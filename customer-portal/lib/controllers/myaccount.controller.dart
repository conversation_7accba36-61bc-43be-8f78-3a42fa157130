import 'package:get/get.dart';
import '../models/card.model.dart';
import '../services/user.service.dart';
import 'app.controller.dart';
import 'loading.controller.dart';

class MyAccountController extends GetxController {
  final UserService _userService;
  MyAccountController(this._userService);
  final AppController _appController = Get.find<AppController>();
  final LoadingController _loadingController = Get.find<LoadingController>();
  int _currentPage = 0;
  bool _canLoadMore = true;
  bool _isLoadingCards = false;
  RxList<CardAccount> cardList = <CardAccount>[].obs;

  Future<void> getCards({int size = 20}) async {
    if (!_canLoadMore || _isLoadingCards) {
      return;
    }
    _isLoadingCards = true;
    _loadingController.showLoading(true);
    try {
      final response = await _userService.getCardsInfo(
        _appController.userProfile.value.id!,
        {
          "page": _currentPage,
          "size": size,
        },
      );
      print('response $response');
      final Map<String, dynamic> data = Map.from(response);
      final List<dynamic> content = data['content'];
      final List<CardAccount> list =
          content.map((v) => CardAccount.fromJson(v)).toList();
      cardList.addAll(list);
      _currentPage++;
      _canLoadMore = list.length == size;
    } catch (e) {
      /** */
      print("catch $e");
    }
    _loadingController.showLoading(false);
    _isLoadingCards = false;
  }

  Future<bool> updateMyAccountProfile(params) async {
    _loadingController.showLoading(true);
    try {
      await _userService.updateUserProfile(params);
      return true;
    } catch (e) {
      /** */
      return false;
    } finally {
      _loadingController.showLoading(false);
    }
  }
}
