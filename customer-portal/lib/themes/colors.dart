import 'package:flutter/material.dart';

const ColorScheme lightScheme = ColorScheme(
  brightness: Brightness.light,
  background: Color(0xffffffff),
  onBackground: Color(0xff212B36),
  primary: Color(0xff165ADC),
  surfaceTint: Color(0xff0554d6),
  onPrimary: Color(0xffffffff),
  primaryContainer: Color(0xffC6DAFF),
  onPrimaryContainer: Color(0xffffffff),
  secondary: Color(0xff7A57DD),
  onSecondary: Color(0xffffffff),
  secondaryContainer: Color(0xffE4DDF8),
  onSecondaryContainer: Color(0xffffffff),
  tertiary: Color(0xffFFAB00),
  onTertiary: Color(0xffffffff),
  tertiaryContainer: Color(0xffFFF5CC),
  onTertiaryContainer: Color(0xff4b2f00),
  error: Color(0xffD0021B),
  onError: Color(0xffffffff),
  errorContainer: Color(0xffFFE1E5),
  onErrorContainer: Color(0xFF920113),
  surface: Color(0xffF4F6F8),
  onSurface: Color(0xff212B36),
  onSurfaceVariant: Color(0xff637381),
  outline: Color(0x3D919EAB),
  outlineVariant: Color(0x29464F60),
  shadow: Color(0xff000000),
  scrim: Color(0xff000000),
  inverseSurface: Color(0xff313030),
  inversePrimary: Color(0xffb3c5ff),
  surfaceContainer: Color(0xffffffff),
);

const currentPageTextColor = Color(0xff171C26);
const totalPageTextColor = Color(0xff687182);
const buttonBackColor = Color(0xff868FA0);
const arrowDownColor = Color(0xff292929);

const success = Color(0xff36B37E);
const successLight = Color(0x2936B37E);
const successDark = Color(0xff1B806A);
