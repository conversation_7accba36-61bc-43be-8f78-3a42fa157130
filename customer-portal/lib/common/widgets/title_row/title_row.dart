import 'package:flutter/material.dart';
import 'package:pacific_2_customer_portal/common/responsive/responsive.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/shadow_button.dart';

class TitleRow extends StatelessWidget {
  final String title;
  final Function()? onBackPressed;

  const TitleRow({super.key, required this.title, this.onBackPressed});

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    return Row(
      children: [
        if (onBackPressed != null)
          Padding(
            padding: const EdgeInsets.only(right: 12),
            child: ShadowButton(
              onPressed: onBackPressed,
              child: Icon(
                Icons.arrow_back_ios_new,
                size: 16,
                color: theme.colorScheme.onSurface,
              ),
            ),
          ),
        Text(
          title,
          style: Responsive.isMobile(context) ? theme.textTheme.headlineMedium : theme.textTheme.headlineLarge,
        )
      ],
    );
  }
}
