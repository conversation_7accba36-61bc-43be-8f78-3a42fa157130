import 'package:flutter/material.dart';
import 'package:pacific_2_customer_portal/common/responsive/responsive.dart';

class FilterDialog extends StatelessWidget {
  const FilterDialog({super.key, required this.child});

  final Widget child;

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    return Container(
      width: Responsive.isDesktop(context) ? 424 : double.infinity,
      color: theme.colorScheme.surfaceContainer,
      child: child,
    );
  }
}

void showFilterDialog(BuildContext context, {required Widget child}) {
  showGeneralDialog(
    context: context,
    barrierDismissible: true,
    barrierLabel: 'filter',
    transitionDuration: Duration.zero,
    transitionBuilder: (_, __, ___, child) => child,
    pageBuilder: (ctx, _, __) => Dialog(
      alignment: Alignment.topRight,
      insetPadding: const EdgeInsets.only(right: 0, top: 0, bottom: 0),
      child: FilterDialog(child: child),
    ),
  );
}
