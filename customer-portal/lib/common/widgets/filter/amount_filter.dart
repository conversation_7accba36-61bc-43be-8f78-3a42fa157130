import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:get/get.dart';
import 'package:pacific_2_customer_portal/common/widgets/text_field_money/money_input_formatter.dart';
import 'package:pacific_2_customer_portal/controllers/app.controller.dart';
import 'package:pacific_2_customer_portal/utils/general_utils.dart';

class AmountFilter extends StatefulWidget {
  const AmountFilter({
    super.key,
    required this.currentFrom,
    required this.currentTo,
    required this.onChangeFrom,
    required this.onChangeTo,
    required this.isError,
  });

  final int? currentFrom;
  final int? currentTo;
  final Function(int? amount) onChangeFrom;
  final Function(int? amount) onChangeTo;
  final bool isError;

  @override
  State<AmountFilter> createState() => _AmountFilterState();
}

class _AmountFilterState extends State<AmountFilter> {
  final FocusNode _focusNodeFrom = FocusNode();
  final FocusNode _focusNodeTo = FocusNode();
  late final TextEditingController _amountFromController = TextEditingController(
      text: widget.currentFrom != null ? formatPrice(widget.currentFrom!, showCurrency: false) : '');
  late final TextEditingController _amountToController =
      TextEditingController(text: widget.currentTo != null ? formatPrice(widget.currentTo!, showCurrency: false) : '');

  bool hasFocus = false;

  @override
  void initState() {
    _focusNodeFrom.addListener(() {
      setState(() => hasFocus = _focusNodeFrom.hasFocus);
    });
    _focusNodeTo.addListener(() {
      setState(() => hasFocus = _focusNodeTo.hasFocus);
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    Color textColor = widget.isError ? theme.colorScheme.error : theme.colorScheme.primary;

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: const EdgeInsets.only(top: 12),
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainer,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: widget.isError ? theme.colorScheme.error : theme.colorScheme.outline,
            ),
            boxShadow: [
              if (hasFocus)
                BoxShadow(
                  color: widget.isError ? const Color(0xFFFFF0F0) : const Color(0xFFF1F1F1),
                  blurRadius: 0,
                  spreadRadius: 4,
                ),
            ],
          ),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  focusNode: _focusNodeFrom,
                  controller: _amountFromController,
                  style: theme.textTheme.bodyMedium!.copyWith(color: textColor),
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.zero,
                    isDense: true,
                    hintText: FlutterI18n.translate(context, 'from'),
                    hintStyle: theme.textTheme.bodyMedium!.copyWith(color: theme.colorScheme.outline.withOpacity(1)),
                    prefixText: '${Get.find<AppController>().tenant.value.settings?.currency?.symbol} ',
                    prefixStyle: theme.textTheme.bodyMedium!.copyWith(color: textColor),
                  ),
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(10),
                    MoneyInputFormatter(),
                  ],
                  onChanged: (value) {
                    widget.onChangeFrom(int.tryParse(value.replaceAll(',', '').replaceAll('.', '')));
                  },
                ),
              ),
              Text(' - ', style: theme.textTheme.bodyMedium),
              Expanded(
                child: TextField(
                  focusNode: _focusNodeTo,
                  controller: _amountToController,
                  style: theme.textTheme.bodyMedium!.copyWith(color: textColor),
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.zero,
                    isDense: true,
                    hintText: FlutterI18n.translate(context, 'to'),
                    hintStyle: theme.textTheme.bodyMedium!.copyWith(color: theme.colorScheme.outline.withOpacity(1)),
                    prefixText: '${Get.find<AppController>().tenant.value.settings?.currency?.symbol} ',
                    prefixStyle: theme.textTheme.bodyMedium!.copyWith(color: textColor),
                  ),
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(10),
                    MoneyInputFormatter(),
                  ],
                  onChanged: (value) {
                    widget.onChangeTo(int.tryParse(value.replaceAll(',', '').replaceAll('.', '')));
                  },
                ),
              ),
            ],
          ),
        ),
        if (widget.isError) ...[
          const SizedBox(height: 4),
          Text(
            FlutterI18n.translate(context, 'byAmountError'),
            style: theme.textTheme.bodySmall!.copyWith(color: theme.colorScheme.error),
          ),
        ]
      ],
    );
  }
}
