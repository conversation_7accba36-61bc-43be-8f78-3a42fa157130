import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:flutter_svg/svg.dart';

class EmptyView extends StatelessWidget {
  const EmptyView({super.key});

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          SvgPicture.asset('assets/images/image-empty.svg'),
          const SizedBox(height: 8),
          Text(
            FlutterI18n.translate(context, 'noData'),
            textAlign: TextAlign.center,
            style: theme.textTheme.bodyLarge!
                .copyWith(color: theme.colorScheme.onSurfaceVariant),
          ),
        ],
      ),
    );
  }
}
