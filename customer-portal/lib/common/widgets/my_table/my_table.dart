import 'package:flutter/material.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/shadow_button.dart';
import 'package:pacific_2_customer_portal/common/widgets/sort_header/sort_header.dart';
import 'package:pacific_2_customer_portal/models/sort.model.dart';

class MyTable extends StatelessWidget {
  const MyTable({
    super.key,
    required this.headers,
    required this.content,
    required this.columnFlexWidths,
    this.cellAllignment,
    required this.currentKey,
    required this.sortOrder,
    required this.sortable,
    required this.onSort,
    required this.currentPage,
    required this.totalPages,
    required this.totalElements,
    required this.onPageChanged,
  });

  final List<SortHeaderData> headers;
  final Widget content;
  final List<int> columnFlexWidths;
  final Map<int, Alignment>? cellAllignment;

  // sort
  final String? currentKey;
  final SortOrder? sortOrder;
  final List<bool> sortable;
  final void Function(String key) onSort;

  // pagination
  final int currentPage; // start from 1
  final int totalPages;
  final int totalElements;
  final Function(int page) onPageChanged;

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    int elementStart = totalElements == 0 ? 0 : (currentPage - 1) * 10 + 1;
    int elementEnd = currentPage >= totalPages ? totalElements : (currentPage - 1) * 10 + 10;

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: theme.colorScheme.surface),
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: headers.asMap().entries.map((entry) {
                final index = entry.key;
                final header = entry.value;

                return Expanded(
                  flex: columnFlexWidths.elementAtOrNull(index) ?? 1,
                  child: Container(
                    margin: EdgeInsets.fromLTRB(index == 0 ? 16 : 0, 12, 0, 12),
                    child: Align(
                      alignment: cellAllignment?[index] ?? Alignment.centerLeft,
                      child: sortable[index]
                          ? SortHeader(
                              header: header,
                              currentKey: currentKey,
                              sortOrder: sortOrder,
                              onTap: onSort,
                            )
                          : Text(
                              header.label,
                              style: theme.textTheme.labelSmall!.copyWith(color: colorActive),
                            ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
          content,
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    '$elementStart-$elementEnd of $totalElements',
                    style: theme.textTheme.labelSmall!.copyWith(color: theme.colorScheme.onSurfaceVariant),
                  ),
                ),
                ShadowButton(
                  width: 24,
                  height: 20,
                  onPressed: () => onPageChanged(currentPage - 1),
                  disabled: currentPage <= 1,
                  child: Icon(
                    Icons.arrow_back_ios_new,
                    size: 10,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                SizedBox(
                  width: 46,
                  child: Center(
                    child: Text(
                      '$currentPage/$totalPages',
                      style: theme.textTheme.labelSmall!.copyWith(color: theme.colorScheme.onSurfaceVariant),
                    ),
                  ),
                ),
                ShadowButton(
                  width: 24,
                  height: 20,
                  onPressed: () => onPageChanged(currentPage + 1),
                  disabled: currentPage >= totalPages,
                  child: Icon(
                    Icons.arrow_forward_ios,
                    size: 10,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
