import 'package:flutter/material.dart';
import 'package:pacific_2_customer_portal/common/responsive/responsive.dart';

class MyBottomSheet extends StatelessWidget {
  const MyBottomSheet({
    super.key,
    required this.isFullHeight,
    required this.child,
  });

  final bool isFullHeight;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 24),
      child: Column(
        mainAxisSize: isFullHeight ? MainAxisSize.max : MainAxisSize.min,
        children: [
          Container(
            width: 50,
            height: 6,
            margin: const EdgeInsets.symmetric(vertical: 24),
            decoration: BoxDecoration(
              color: const Color(0xFF939CB1),
              borderRadius: BorderRadius.circular(3),
            ),
          ),
          isFullHeight ? Expanded(child: child) : child,
        ],
      ),
    );
  }
}

void showMyBottomSheet(BuildContext context, Widget Function() child,
    {bool isFullHeight = true, bool isFullWidth = false}) {
  ThemeData theme = Theme.of(context);
  if (Responsive.isDesktop(context)) {
    showDialog(
      context: context,
      builder: (ctx) {
        return Center(
          child: Container(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(ctx).size.height - 208,
              minWidth: isFullWidth ? 928 : 0,
              maxWidth: 928,
            ),
            child: Dialog(
              backgroundColor: theme.colorScheme.surfaceContainer,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Container(
                padding: const EdgeInsets.all(16),
                child: child(),
              ),
            ),
          ),
        );
      },
    );
  } else {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      constraints: BoxConstraints(
        maxWidth: double.infinity,
        minHeight: isFullHeight ? MediaQuery.of(context).size.height - 96 : 0,
        maxHeight: MediaQuery.of(context).size.height - 96,
      ),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      backgroundColor: theme.colorScheme.surfaceContainer,
      elevation: 0,
      builder: ((context) {
        return MyBottomSheet(
          isFullHeight: isFullHeight,
          child: child(),
        );
      }),
    );
  }
}
