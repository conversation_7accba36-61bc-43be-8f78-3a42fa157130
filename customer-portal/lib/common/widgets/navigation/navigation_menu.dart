import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:pacific_2_customer_portal/common/responsive/responsive.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_button.dart';
import 'package:pacific_2_customer_portal/common/widgets/navigation/navigation_item.dart';
import 'package:pacific_2_customer_portal/common/widgets/user_avatar/user_avatar.dart';
import 'package:pacific_2_customer_portal/controllers/app.controller.dart';
import 'package:pacific_2_customer_portal/controllers/route.controller.dart';
import 'package:pacific_2_customer_portal/core/constants/system_const.dart';
import 'package:pacific_2_customer_portal/models/user_profile.model.dart';
import 'package:pacific_2_customer_portal/utils/general_utils.dart';

class NavigationMenu extends StatefulWidget {
  const NavigationMenu({
    super.key,
    required this.onClose,
    this.backgroundColor,
  });

  final Function() onClose;
  final Color? backgroundColor;

  @override
  State<NavigationMenu> createState() => _NavigationMenuState();
}

class _NavigationMenuState extends State<NavigationMenu> {
  final AppController appController = Get.find<AppController>();
  final RouteController routeController = Get.find<RouteController>();

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final profile = appController.userProfile.value;
      return Responsive.isDesktop(context) ? _renderDesktop(context, profile) : _renderMobile(context, profile);
    });
  }

  Widget _renderDesktop(BuildContext context, UserProfile profile) {
    ThemeData theme = Theme.of(context);
    return Container(
      width: 280,
      decoration: BoxDecoration(
        color: theme.colorScheme.primary,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          bottomLeft: Radius.circular(24),
          bottomRight: Radius.circular(24),
        ),
      ),
      child: Stack(
        children: [
          Positioned(
            top: 0,
            right: 0,
            child: IconButton(
              onPressed: widget.onClose,
              icon: Icon(
                Icons.close_rounded,
                size: 24,
                color: theme.colorScheme.onPrimary,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    UserAvatar(url: profile.avatar?.url, size: 80),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        profile.getFullName(),
                        style: theme.textTheme.headlineSmall!.copyWith(color: theme.colorScheme.onPrimary),
                      ),
                    ),
                  ],
                ),
                ...[SystemConst.TOP_MENU.last, ...SystemConst.BOTTOM_MENU].map(
                  (e) => Container(
                    margin: const EdgeInsets.only(top: 20),
                    width: double.infinity,
                    child: TextButton(
                      onPressed: () {
                        widget.onClose();
                        context.go.call(e.url);
                      },
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                        foregroundColor: theme.colorScheme.onPrimary,
                        backgroundColor: theme.primaryColor,
                      ),
                      child: Row(
                        children: [
                          SvgPicture.asset(e.icon ?? '', width: 24),
                          const SizedBox(width: 16),
                          Text(
                            FlutterI18n.translate(context, e.titleKey),
                            style: theme.textTheme.headlineSmall!.copyWith(
                              color: theme.colorScheme.onPrimary,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _renderMobile(BuildContext context, UserProfile profile) {
    ThemeData theme = Theme.of(context);
    return Container(
      color: widget.backgroundColor ?? theme.colorScheme.surfaceContainer,
      child: Container(
        margin: const EdgeInsets.only(top: 10, left: 16, right: 16, bottom: 16),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: theme.colorScheme.primary,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(24),
            bottomLeft: Radius.circular(24),
            bottomRight: Radius.circular(24),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.max,
          children: [
            Align(
              alignment: Alignment.centerRight,
              child: MainButton(
                width: 70,
                height: 24,
                text: FlutterI18n.translate(context, 'close'),
                onPressed: widget.onClose,
                icon: Icon(
                  Icons.close_rounded,
                  size: 24,
                  color: theme.colorScheme.onPrimary,
                ),
              ),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(8),
                child: Column(
                  children: [
                    Row(
                      children: [
                        UserAvatar(url: profile.avatar?.url, size: 80),
                        const SizedBox(width: 16),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(profile.getFullName(),
                                    style: theme.textTheme.headlineSmall!.copyWith(color: theme.colorScheme.onPrimary)),
                                const SizedBox(height: 4),
                                Text(profile.userGroup?.groupName ?? '',
                                    style: theme.textTheme.bodyMedium!.copyWith(color: theme.colorScheme.onPrimary)),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                    Container(
                      decoration: BoxDecoration(
                        color: theme.colorScheme.shadow.withOpacity(0.5),
                        borderRadius: BorderRadius.circular(24),
                      ),
                      padding: const EdgeInsets.only(left: 16, right: 12, top: 12, bottom: 12),
                      margin: const EdgeInsets.symmetric(vertical: 32),
                      child: Row(
                        children: [
                          SvgPicture.asset(
                            'assets/images/ic-wallet.svg',
                            width: 24,
                          ),
                          const SizedBox(width: 12),
                          Text(
                            appController.tenant.value.settings?.currency?.symbol ?? '\$',
                            style: theme.textTheme.titleMedium!.copyWith(color: theme.colorScheme.tertiary),
                          ),
                          Expanded(
                            child: Text(
                              formatPrice(
                                appController.getDepositWallet().balance ?? 0,
                                showCurrency: false,
                              ),
                              style: theme.textTheme.bodyLarge!.copyWith(color: theme.colorScheme.onPrimary),
                            ),
                          ),
                          MainButton(
                            text: FlutterI18n.translate(context, 'topup'),
                            onPressed: () {
                              widget.onClose();
                              context.go(
                                  '/${SystemConst.ROUTE['WALLET']!['TOPUP']!}?walletId=${appController.getDepositWallet().walletId}');
                            },
                            width: 64,
                            height: 32,
                            textStyle: theme.textTheme.labelSmall,
                          )
                        ],
                      ),
                    ),
                    ...SystemConst.TOP_MENU.map(
                      (e) => NavigationItem(
                        isSelected: routeController.activeRoute.value == e.url,
                        labelKey: e.titleKey,
                        onPressed: () {
                          widget.onClose();
                          context.go.call(e.url);
                        },
                      ),
                    ),
                    Divider(
                      height: 41,
                      thickness: 1,
                      color: theme.colorScheme.surfaceContainer.withOpacity(0.16),
                    ),
                    ...SystemConst.BOTTOM_MENU.map(
                      (e) => NavigationItem(
                        isSelected: routeController.activeRoute.value == e.url,
                        labelKey: e.titleKey,
                        onPressed: () {
                          widget.onClose();
                          context.go(e.url);
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (appController.appVersion.value.isNotEmpty) ...[
              const SizedBox(height: 8),
              Align(
                alignment: Alignment.centerRight,
                child: Text(
                  "Version ${appController.appVersion.value}",
                  style: theme.textTheme.bodySmall!.copyWith(color: theme.colorScheme.onPrimary),
                ),
              ),
            ]
          ],
        ),
      ),
    );
  }
}
