// coverage:ignore-file
import 'package:pacific_2_customer_portal/core/constants/system_const.dart';
import 'package:pacific_2_customer_portal/core/services/http/http.service.dart';

class TenantService {
  final api = SystemConst.API;
  final HttpService _httpService = HttpService();

  Future<dynamic> getTaxConfig() {
    return _httpService.get(api['TENANT']!['TAX']!);
  }

  Future<dynamic> getTermsConditions() {
    return _httpService.get(api['TENANT']!['T&C']!);
  }
}
