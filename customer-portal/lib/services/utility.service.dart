// coverage:ignore-file
import 'package:pacific_2_customer_portal/core/constants/system_const.dart';
import 'package:pacific_2_customer_portal/core/services/http/http.service.dart';

class UtilityService {
  final api = SystemConst.API;
  final HttpService _httpService = HttpService();

  Future<dynamic> getPreSignedUrlUpload(String fileName) {
    return _httpService.get(api['UTILITY']!['IMAGE_UPLOAD_PRE_SIGNED']!,
        params: {"fileName": fileName});
  }

  Future<dynamic> getTimeZoneMetaData() {
    return _httpService.get(api['UTILITY']!['TIMEZONE']!);
  }

  Future<dynamic> getDateFormatMetaData() {
    return _httpService.get(api['UTILITY']!['DATE_FORMAT']!);
  }

  Future<dynamic> getTimeFormatMetaData() {
    return _httpService.get(api['UTILITY']!['TIME_FORMAT']!);
  }
}
