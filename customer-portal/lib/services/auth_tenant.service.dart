// coverage:ignore-file
import 'package:dio/dio.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:pacific_2_customer_portal/core/constants/system_const.dart';

class AuthTenantService {
  final api = SystemConst.API;
  Dio dio = Dio(BaseOptions(
    baseUrl: dotenv.env['BASE_API_URL']!,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    followRedirects: false,
  ));

  Future<dynamic> getTenantInfo() async {
    try {
      final response = await dio.get(api['TENANT']!['INFO']!);
      return response.data;
    } on DioException {
      rethrow;
    }
  }
}
