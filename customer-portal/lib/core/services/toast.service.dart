// coverage:ignore-file
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:pacific_2_customer_portal/common/responsive/responsive.dart';

late FToast fToast;

class ToastService {
  final BuildContext context;

  ToastService(this.context) {
    fToast = FToast();
    fToast.init(context);
  }

  void show(String message, String type) {
    Color bgColor;
    Color color;

    switch (type) {
      case 'success':
        bgColor = const Color(0xFFD8FBDE);
        color = const Color(0xFF36B37E);
        break;
      case 'error':
        bgColor = const Color(0xFFFFE1E5);
        color = const Color(0xFFD0021B);
        break;
      case 'warning':
        bgColor = const Color(0xFFFFF5CC);
        color = const Color(0xFFFFAB00);
        break;
      default:
        bgColor = const Color(0xFFB9F3FF);
        color = const Color(0xFF21B5D5);
        break;
    }

    Widget toast = Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: const BorderRadius.all(Radius.circular(4)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(
            'assets/images/ic-info.svg',
            height: 16,
            width: 16,
            colorFilter: ColorFilter.mode(color, BlendMode.srcIn),
          ),
          const SizedBox(
            width: 8,
          ),
          Container(
            constraints: BoxConstraints(
              maxWidth: Responsive.isMobile(context)
                  ? MediaQuery.of(context).size.width - 120
                  : MediaQuery.of(context).size.width - 260,
            ),
            child: Text(
              message,
              style: Theme.of(context)
                  .textTheme
                  .bodyMedium!
                  .copyWith(color: color),
              softWrap: true,
            ),
          ),
        ],
      ),
    );

    fToast.showToast(
      child: toast,
      gravity: ToastGravity.TOP_RIGHT,
      toastDuration: const Duration(seconds: 3),
      positionedToastBuilder: (context, child) {
        return Positioned(
          top: Responsive.isMobile(context) ? 64.0 : 16.0,
          right: Responsive.isMobile(context) ? 4.0 : 16.0,
          child: child,
        );
      },
    );
  }

  void success(String message) {
    show(message, 'success');
  }

  void error(String message) {
    show(message, 'error');
  }

  void info(String message) {
    show(message, 'info');
  }

  void warning(String message) {
    show(message, 'warning');
  }

  removeToast() {
    fToast.removeCustomToast();
  }
}
