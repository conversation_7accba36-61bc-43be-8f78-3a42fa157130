// ignore_for_file: depend_on_referenced_packages, avoid_print

import 'package:shelf/shelf.dart';
import 'package:shelf/shelf_io.dart' as shelf_io;
import 'package:shelf_proxy/shelf_proxy.dart';

void main() {
  const String targetUrl = 'https://pacific-ii-sit.styl.solutions';

  var pipeline = const Pipeline()
      .addMiddleware(corsMiddleware)
      .addMiddleware(handleOptionsMiddleware)
      .addHandler(proxyHandler(targetUrl));

  shelf_io.serve(pipeline, 'localhost', 8888).then((server) {
    print('Proxy server listening on port ${server.port}');
  });
}

Middleware corsMiddleware = createMiddleware(
  responseHandler: (Response response) => response.change(headers: {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Tenant-Id',
  }),
);

Middleware handleOptionsMiddleware = createMiddleware(
  requestHandler: (Request request) {
    if (request.method == 'OPTIONS') {
      return Response.ok('', headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers':
            'Content-Type, Authorization, X-Tenant-Id',
      });
    }
    return null;
  },
);
