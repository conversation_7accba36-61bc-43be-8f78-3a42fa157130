import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:flutter_svg/svg.dart';
import 'package:pacific_2_customer_portal/common/const/const.asset.dart';
import 'package:pacific_2_customer_portal/common/note_view/note_view.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_button.dart';
import 'package:pacific_2_customer_portal/common/widgets/menu_item_image/menu_item_image.dart';
import 'package:pacific_2_customer_portal/models/order_history.model.dart';
import 'package:pacific_2_customer_portal/pages/principal_account_orders/widgets/order_status_text.dart';
import 'package:pacific_2_customer_portal/utils/general_utils.dart';

class OrderItemBlock extends StatelessWidget {
  const OrderItemBlock({
    super.key,
    required this.item,
    required this.onCancelOrder,
  });

  final OrderHistoryItem item;
  final Function() onCancelOrder;

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    int itemCount = item.lineItems!.fold(0, (prev, cur) => prev + (cur.quantity ?? 0));

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Text(item.mealTime?.name ?? '-', style: theme.textTheme.titleMedium),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  '($itemCount ${FlutterI18n.plural(context, 'item.count', itemCount)})',
                  style: theme.textTheme.bodyLarge,
                ),
              ),
              if (item.cancellationDueAt != null &&
                  item.cancellationDueAt! > DateTime.now().millisecondsSinceEpoch &&
                  item.status != OrderStatus.pending.status &&
                  item.status != OrderStatus.collected.status)
                MainButton(
                  width: 62,
                  height: 32,
                  text: FlutterI18n.translate(context, 'cancel'),
                  textStyle: theme.textTheme.labelSmall,
                  textColor: theme.colorScheme.onSurface,
                  color: const Color(0xFFDFE3E8),
                  onPressed: onCancelOrder,
                )
              else
                OrderStatusText(orderStatus: item.status),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              SvgPicture.asset(ConstAsset.icStore, width: 16),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  item.storeName ?? '-',
                  style: theme.textTheme.bodySmall,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...item.lineItems!.asMap().entries.map((e) => _renderItem(
                context,
                e.value,
                e.key == item.lineItems!.length - 1,
              ))
        ],
      ),
    );
  }

  Widget _renderItem(BuildContext context, LineItems lineItem, bool isLast) {
    ThemeData theme = Theme.of(context);
    final sellingPrice = (lineItem.unitPrice ?? 0) + (lineItem.optionPrice ?? 0);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: MenuItemImage(
                url: lineItem.metadata?.images?.firstOrNull?.image?.url ?? '',
                width: 48,
                height: 40,
              ),
            ),
            const SizedBox(width: 16),
            Text(
              '${lineItem.quantity}x ',
              style: theme.textTheme.titleSmall,
            ),
            Expanded(
              child: Text(
                '${lineItem.productName}',
                style: theme.textTheme.bodyMedium,
              ),
            ),
            const SizedBox(width: 6),
            Text(
              formatPrice(sellingPrice),
              style: theme.textTheme.bodyMedium,
            ),
          ],
        ),
        if (lineItem.option?.isNotEmpty ?? false) ...[
          const SizedBox(height: 8),
          Text(
            lineItem.option!,
            style: theme.textTheme.bodySmall!.copyWith(color: theme.colorScheme.onSurfaceVariant),
          )
        ],
        if (lineItem.note?.isNotEmpty ?? false) ...[const SizedBox(height: 8), NoteView(note: lineItem.note!)],
        if (!isLast)
          Divider(
            height: 16,
            thickness: 1,
            color: theme.colorScheme.outline,
          ),
      ],
    );
  }
}
