import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_button.dart';
import 'package:pacific_2_customer_portal/core/constants/system_const.dart';
import 'package:pacific_2_customer_portal/utils/general_utils.dart';

class DialogCheckoutSuccess extends StatelessWidget {
  const DialogCheckoutSuccess({
    super.key,
    required this.totalPayment,
    required this.onViewDetails,
    required this.onClose,
  });

  final int totalPayment;
  final Function() onViewDetails;
  final Function() onClose;

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: <PERSON><PERSON>(
        children: [
          Positioned(
            top: 8,
            right: 0,
            child: SvgPicture.asset(
              'assets/images/image-checkout-complete.svg',
              width: 132,
              height: 112,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Image.asset(
                  'assets/images/logo-terabite.png',
                  height: 46,
                ),
                const SizedBox(height: 16),
                Text(
                  FlutterI18n.translate(context, 'totalPayment'),
                  style: theme.textTheme.bodyLarge!
                      .copyWith(color: theme.colorScheme.onSurfaceVariant),
                ),
                const SizedBox(height: 4),
                Text(
                  formatPrice(totalPayment),
                  style: theme.textTheme.displaySmall,
                ),
                const SizedBox(height: 16),
                Text(
                  FlutterI18n.translate(context, 'paymentSuccessMessage'),
                  style: theme.textTheme.bodyLarge,
                ),
                const SizedBox(height: 16),
                RichText(
                  text: TextSpan(
                    text: FlutterI18n.translate(context, 'paymentSuccessInfo1'),
                    style: theme.textTheme.bodyMedium!.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                      decoration: TextDecoration.underline,
                    ),
                    recognizer: TapGestureRecognizer()..onTap = onViewDetails,
                    children: <TextSpan>[
                      TextSpan(
                        text: FlutterI18n.translate(
                            context, 'paymentSuccessInfo2'),
                        style: theme.textTheme.bodyMedium!.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                Align(
                  alignment: Alignment.center,
                  child: MainButton(
                    width: 144,
                    height: 40,
                    text: FlutterI18n.translate(context, 'close'),
                    textStyle: theme.textTheme.labelMedium,
                    onPressed: onClose,
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}

// coverage:ignore-start
void showCheckoutSuccessDialog(
  BuildContext context, {
  required int totalPayment,
  required String orderId,
}) {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (_) => AlertDialog(
      content: DialogCheckoutSuccess(
        totalPayment: totalPayment,
        onViewDetails: () {
          context.go(
            '/${SystemConst.ROUTE['ORDER']!['HISTORY']!}',
          );
        },
        onClose: () {
          context.go(
            '/${SystemConst.ROUTE['PRE_ORDER']!['MAIN']!}',
          );
        },
      ),
      scrollable: true,
      backgroundColor: Colors.transparent,
      contentPadding: const EdgeInsets.all(0),
    ),
  );
}
// coverage:ignore-end
