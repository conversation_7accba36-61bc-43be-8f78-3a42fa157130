// coverage:ignore-file
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:pacific_2_customer_portal/common/avatar_profile/avatar_view.dart';
import 'package:pacific_2_customer_portal/common/widgets/banned_item_summary/banned_item_summary.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_button.dart';
import 'package:pacific_2_customer_portal/controllers/route.controller.dart';
import 'package:pacific_2_customer_portal/core/constants/system_const.dart';
import 'package:pacific_2_customer_portal/core/services/toast.service.dart';
import 'package:pacific_2_customer_portal/pages/my_account_general_info/general_information.controller.dart';
import 'package:pacific_2_customer_portal/utils/general_utils.dart';

import '../../common/allergens/allergens.controller.dart';
import '../../common/allergens/allergens.dart';
import '../../common/responsive/responsive.dart';
import '../../controllers/app.controller.dart';
import '../../controllers/loading.controller.dart';
import '../../controllers/subaccount.controller.dart';
import '../../models/allergen.model.dart';
import '../../services/catalog.service.dart';
import '../../services/user.service.dart';
import '../../services/utility.service.dart';
import '../../services/wallet.service.dart';
import '../../common/widgets/dropdown_group/group_drop_down.dart';

class GeneralInformation extends StatefulWidget {
  const GeneralInformation({super.key});

  @override
  State<GeneralInformation> createState() => _GeneralInformationState();
}

class _GeneralInformationState extends State<GeneralInformation> {
  late final SubAccountController _subAccountController =
      Get.put(SubAccountController(UserService(), UtilityService(), WalletService(), CatalogService()));
  final AppController _appController = Get.find<AppController>();
  final LoadingController loadingController = Get.find<LoadingController>();
  late final _routeController = Get.find<RouteController>();

  final AllergensController _allergensController = AllergensController();
  final GeneralInformationController _generalInformationController = GeneralInformationController();
  final TextEditingController _firstNameController = TextEditingController();
  final TextEditingController _lastNameController = TextEditingController();

  StreamSubscription? activeRouteListener;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      waitForAuth(_appController, fetchData);
      activeRouteListener = _routeController.activeRoute.listen((route) {
        if (route == '/${SystemConst.ROUTE['ACCOUNT']!['MAIN']!}') {
          _subAccountController.getBannedItems(_appController.userProfile.value.id!);
        }
      });
    });
  }

  Future<void> fetchData() async {
    _firstNameController.text = _appController.userProfile.value.firstName ?? '';
    _lastNameController.text = _appController.userProfile.value.lastName ?? '';
    await _subAccountController.getAllergensCatalog();
    if (_appController.userProfile.value.id != null) {
      _subAccountController.getBannedItems(_appController.userProfile.value.id!);
      await _subAccountController.getAllergensList(_appController.userProfile.value.id!);
      _allergensController.allergenSelected.value =
          List<AllergenModel>.from(_subAccountController.allergenUserSelectedList.toList());
    }
    setState(() {});
  }

  @override
  void dispose() {
    _subAccountController.clearAllergenList();
    _subAccountController.clearBannedItems();
    _allergensController.hasChangeAllergens = false;
    _firstNameController.dispose();
    _lastNameController.dispose();
    activeRouteListener?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Obx(() {
      return Scaffold(
          body: SingleChildScrollView(
              child: Responsive.isDesktop(context) ? _buildBodyDesktop(theme) : _buildBody(theme)));
    });
  }

  Widget _buildBody(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
      child: Column(
        children: [
          _buildGeneralInformation(theme),
          const SizedBox(height: 12),
          if (_subAccountController.allergenList.toList().isNotEmpty)
            Allergens(
              allergensController: _allergensController,
            ),
          const SizedBox(height: 12),
          _buildBannedItemSummary(context),
          const SizedBox(height: 12),
          _buildBottomButtons(theme),
        ],
      ),
    );
  }

  Widget _buildBodyDesktop(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                flex: 1,
                child: _buildGeneralInformation(theme),
              ),
              const SizedBox(width: 24),
              Expanded(
                flex: 2,
                child: Column(
                  children: [
                    if (_subAccountController.allergenList.toList().isNotEmpty) ...[
                      Allergens(allergensController: _allergensController),
                      const SizedBox(height: 12),
                    ],
                    _buildBannedItemSummary(context),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              SizedBox(
                width: 164,
                child: _buildBottomButtons(theme),
              )
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildGeneralInformation(ThemeData theme) {
    final isNameEnabled = !_appController.hasLinkedAccount.value;

    return Container(
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            FlutterI18n.translate(context, 'generalInfo'),
            style: theme.textTheme.headlineSmall?.copyWith(color: theme.colorScheme.onSurface),
          ),
          const SizedBox(height: 12.0),
          Container(
            padding: const EdgeInsets.all(12.0),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AvatarView(
                  avatarUrl: _appController.userProfile.value.avatar?.url,
                  onAvatarSelected: (fileName, imageBytes, imageFile) {
                    _generalInformationController.fileName = fileName;
                    _generalInformationController.imageBytes = imageBytes;
                    _generalInformationController.imageFile = imageFile;
                  },
                ),
                const SizedBox(height: 16.0),
                Text(FlutterI18n.translate(context, 'firstName'),
                    style: theme.textTheme.bodyMedium?.copyWith(color: theme.colorScheme.onSurface)),
                const SizedBox(height: 4.0),
                SizedBox(
                  height: 42,
                  child: TextField(
                    controller: _firstNameController,
                    enabled: isNameEnabled,
                    style: theme.textTheme.bodyMedium,
                    decoration: InputDecoration(
                      filled: true,
                      fillColor: isNameEnabled ? theme.colorScheme.surfaceContainer : const Color(0xFFF2F3F5),
                      border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide:
                              isNameEnabled ? const BorderSide() : BorderSide(color: theme.colorScheme.outline)),
                    ),
                  ),
                ),
                const SizedBox(height: 8.0),
                Text(FlutterI18n.translate(context, 'lastName'),
                    style: theme.textTheme.bodyMedium?.copyWith(color: theme.colorScheme.onSurface)),
                const SizedBox(height: 4.0),
                SizedBox(
                  height: 42,
                  child: TextField(
                    controller: _lastNameController,
                    enabled: isNameEnabled,
                    style: theme.textTheme.bodyMedium,
                    decoration: InputDecoration(
                      filled: true,
                      fillColor: isNameEnabled ? theme.colorScheme.surfaceContainer : const Color(0xFFF2F3F5),
                      border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide:
                              isNameEnabled ? const BorderSide() : BorderSide(color: theme.colorScheme.outline)),
                    ),
                  ),
                ),
                // TODO: later phases
                // const SizedBox(height: 8.0),
                // Text(FlutterI18n.translate(context, 'group'),
                //     style: theme.textTheme.bodyMedium
                //         ?.copyWith(color: theme.colorScheme.onSurface)),
                // const SizedBox(height: 4.0),
                // GestureDetector(
                //   onTap: () {
                //     onSelectGroupPress();
                //   },
                //   child: Container(
                //     padding: const EdgeInsets.all(12.0),
                //     decoration: BoxDecoration(
                //       borderRadius: BorderRadius.circular(8),
                //       border: Border.all(
                //           color: theme.colorScheme.outline.withOpacity(0.48)),
                //     ),
                //     height: 56,
                //     child: Row(
                //       children: [
                //         Expanded(
                //             child: Text(
                //                 _generalInformationController.selectedGroup?.groupName ??
                //                     _appController.userProfile.value.userGroup
                //                         ?.groupName ??
                //                     FlutterI18n.translate(
                //                         context, 'selectGroup'),
                //                 style: (_generalInformationController.selectedGroup?.groupName != null ||
                //                         _appController.userProfile.value
                //                                 .userGroup?.groupName !=
                //                             null)
                //                     ? theme.textTheme.bodyMedium
                //                         ?.copyWith(color: Color(0xff454F5B))
                //                     : theme.textTheme.bodyMedium?.copyWith(
                //                         color: theme.colorScheme.outline
                //                             .withOpacity(0.48)))),
                //         SizedBox(
                //           width: 16, // Set the desired width
                //           height: 16, // Set the desired height
                //           child: Icon(
                //             Icons.keyboard_arrow_down,
                //             color: arrowDownColor,
                //             size: 16,
                //           ),
                //         ),
                //       ],
                //     ),
                //   ),
                // ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBannedItemSummary(BuildContext context) {
    return BannedItemSummary(
        bannedItems: _subAccountController.bannedProducts.toList(),
        onPressEdit: () {
          context.push(
            '/${SystemConst.ROUTE['ACCOUNT']!['BANNED_ITEMS']!}',
            extra: {
              'userId': _appController.userProfile.value.id,
              'bannedItems': _subAccountController.bannedProducts.toList(),
            },
          );
        });
  }

  Widget _buildBottomButtons(ThemeData theme) {
    return MainButton(
      text: FlutterI18n.translate(context, 'updateProfile'),
      onPressed: () => onSaveChangesPress(context),
    );
  }

  Future<void> onSelectGroupPress() async {
    final result = await showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        builder: (builder) {
          return GroupedDropdown(
              selectedUsergroup:
                  _generalInformationController.selectedGroup ?? _appController.userProfile.value.userGroup,
              onItemSelected: (value) {
                Navigator.pop(context, value); // Close modal and return the result
              });
        });
    if (result != null) {
      setState(() {
        _generalInformationController.selectedGroup = result;
      });
    }
  }

  Future<void> onSaveChangesPress(BuildContext context) async {
    if (_firstNameController.text.isEmpty) {
      loadingController.showAlertDialog(
        context,
        title: FlutterI18n.translate(context, 'alert'),
        message: FlutterI18n.translate(context, 'firstNameError'),
        positiveText: FlutterI18n.translate(context, 'OK'),
        onPositivePress: () {},
      );
      return; // Exit the f
    } else if (_lastNameController.text.isEmpty) {
      loadingController.showAlertDialog(
        context,
        title: FlutterI18n.translate(context, 'alert'),
        message: FlutterI18n.translate(context, 'lastNameError'),
        positiveText: FlutterI18n.translate(context, 'OK'),
        onPositivePress: () {},
      );
      return; //
    }
    bool isUpdateSuccess = false;
    if (_allergensController.hasChangeAllergens) {
      bool allegesUpdated = await _allergensController.updateAllergens(_appController.userProfile.value.id!);
      isUpdateSuccess = allegesUpdated;
    }
    await _generalInformationController.updateAvatar();
    bool updateSuccess = await _generalInformationController.callUpdateUserProfileApi(
        _firstNameController.text,
        _lastNameController.text,
        _appController.userProfile.value.avatar?.path,
        _appController.userProfile.value.userGroup?.id);

    if (updateSuccess) {
      isUpdateSuccess = true;
      _appController.getUserProfile(_appController.userProfile.value.id!);
    }
    if (context.mounted && isUpdateSuccess) {
      _subAccountController.setRefreshState(true);
      ToastService(context).success(FlutterI18n.translate(context, 'profileUpdateSuccess'));
    }
  }
}
