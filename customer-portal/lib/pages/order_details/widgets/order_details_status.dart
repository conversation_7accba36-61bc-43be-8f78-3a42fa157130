import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:pacific_2_customer_portal/common/responsive/responsive.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_button.dart';
import 'package:pacific_2_customer_portal/models/order_history.model.dart';
import 'package:pacific_2_customer_portal/utils/general_utils.dart';

class OrderDetailsStatus extends StatelessWidget {
  const OrderDetailsStatus({
    super.key,
    required this.item,
    required this.onCancelOrder,
  });

  final OrderHistoryItem item;
  final Function() onCancelOrder;

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    return Container(
      padding: Responsive.isDesktop(context)
          ? const EdgeInsets.all(16)
          : const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withOpacity(0.08),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
            child: RichText(
              text: TextSpan(
                text: FlutterI18n.translate(context, 'totalAmount'),
                style: theme.textTheme.bodyMedium,
                children: [
                  const TextSpan(text: ' '),
                  TextSpan(
                    text: formatPrice(item.totalAmount ?? 0),
                    style: theme.textTheme.bodyMedium!.copyWith(color: theme.colorScheme.primary),
                  ),
                ],
              ),
            ),
          ),
          if (item.cancellationDueAt != null &&
              item.cancellationDueAt! > DateTime.now().millisecondsSinceEpoch &&
              (item.type != OrderType.preorder.type || item.status != OrderStatus.pending.status) &&
              item.status != OrderStatus.cancelling.status &&
              item.status != OrderStatus.cancelled.status &&
              item.status != OrderStatus.collected.status)
            MainButton(
              width: 102,
              height: 32,
              text: FlutterI18n.translate(context, 'cancelOrder'),
              textStyle: theme.textTheme.labelSmall,
              onPressed: onCancelOrder,
            )
        ],
      ),
    );
  }
}
