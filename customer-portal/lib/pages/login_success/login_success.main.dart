// coverage:ignore-file
// ignore_for_file: use_build_context_synchronously

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:pacific_2_customer_portal/controllers/auth.controller.dart';
import 'package:pacific_2_customer_portal/pages/not_found/not_found.dart';

class LoginSuccess extends StatefulWidget {
  const LoginSuccess({super.key});

  @override
  State<LoginSuccess> createState() => _LoginSuccessState();
}

class _LoginSuccessState extends State<LoginSuccess> {
  final AuthController _authController = Get.find<AuthController>();
  bool _isError = false;

  Future<void> loginSuccess(BuildContext context) async {
    try {
      if (kIsWeb) {
        final c = await _authController.authenticate();
        if (c != null) {
          context.go('/login');
          return;
        }
      }
      context.go('/');
    } catch (e) {
      setState(() {
        _isError = true;
      });
    }
  }

  @override
  void initState() {
    loginSuccess(context);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return _isError ? const NotFound() : const Center(child: CircularProgressIndicator(color: Colors.black));
  }
}
