import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:pacific_2_customer_portal/common/responsive/responsive.dart';
import 'package:pacific_2_customer_portal/common/widgets/checkbox/my_checkbox.dart';
import 'package:pacific_2_customer_portal/common/widgets/menu_item_image/menu_item_image.dart';
import 'package:pacific_2_customer_portal/common/widgets/my_bottom_sheet/my_bottom_sheet.dart';
import 'package:pacific_2_customer_portal/common/widgets/price_display/price_display.dart';
import 'package:pacific_2_customer_portal/common/widgets/text_underline/text_underline.dart';
import 'package:pacific_2_customer_portal/models/catalog_product.model.dart';
import 'package:pacific_2_customer_portal/models/preorder_menu_item.model.dart';
import 'package:pacific_2_customer_portal/pages/preorder/widgets/menu_item_details.dart';

class MenuItem extends StatelessWidget {
  const MenuItem({
    super.key,
    required this.item,
    required this.requestCatalogProduct,
    this.isInCart = false,
    this.onPressCheckBox,
    this.canOrder = true,
    this.isPurchased = false,
    this.isUnavailable = false,
  });

  final PreOrderMenuItem item;
  final bool isInCart;
  final Function(bool? checked)? onPressCheckBox;
  final Future<CatalogProduct?> Function(String id) requestCatalogProduct;

  final bool canOrder;
  final bool isPurchased;
  final bool isUnavailable;

  Future<void> onPressDetails(BuildContext context, PreOrderMenuItem item) async {
    final product = await requestCatalogProduct(item.product!.id!);
    if (context.mounted && product != null) {
      showMyBottomSheet(
        context,
        () => MenuItemDetails(
          menuItem: item,
          catalogProduct: product,
          canOrder: canOrder,
        ),
        isFullWidth: true,
        isFullHeight: Responsive.isMobile(context) ? true : false,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    final ordered = item.ordered ?? 0;
    final capacity = item.capacity ?? 0;
    final isSoldOut = capacity > 0 && ordered == capacity;

    return Opacity(
      opacity: isUnavailable ? 0.4 : 1,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: theme.colorScheme.surface,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _renderThumbnail(context, ordered, capacity),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: SizedBox(
                  child: Text(
                    '${item.product?.name}',
                    style: theme.textTheme.titleSmall!.copyWith(height: 17.8 / 12),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ),
            PriceDisplay(item: item),
            const SizedBox(height: 8),
            SizedBox(
              height: 20,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  GestureDetector(
                    onTap: () {
                      onPressDetails(context, item);
                    },
                    child: TextUnderline(
                      FlutterI18n.translate(context, 'details'),
                      style: theme.textTheme.bodySmall!.copyWith(color: theme.colorScheme.primary),
                    ),
                  ),
                  if (canOrder && !isSoldOut)
                    MyCheckBox(
                      checked: isInCart,
                      onChanged: isUnavailable ? null : onPressCheckBox,
                    )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _renderThumbnail(BuildContext context, int ordered, int capacity) {
    ThemeData theme = Theme.of(context);

    final ratio = ordered / capacity;
    final showCapacity = canOrder && capacity > 0;

    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: MenuItemImage(
            url: item.product?.images?.firstOrNull?.image?.url,
            width: double.infinity,
            height: 124,
          ),
        ),
        Positioned(
          top: 0,
          left: 0,
          child: Image.network(
            item.product?.healthierChoice?.symbol?.url ?? '',
            width: 32,
            height: 32,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) => const SizedBox.shrink(),
          ),
        ),
        Positioned(
          bottom: 3,
          left: 3,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (isPurchased)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                  decoration: BoxDecoration(
                    color: const Color(0xFF86E8AB),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    FlutterI18n.translate(context, 'purchased'),
                    style: theme.textTheme.bodySmall,
                  ),
                ),
              if (showCapacity) const SizedBox(height: 2),
              if (showCapacity)
                ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 16, sigmaY: 16),
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.surfaceContainer.withOpacity(0.6),
                      ),
                      child: Row(
                        children: [
                          if (ordered == capacity)
                            Text(
                              FlutterI18n.translate(context, 'soldOut'),
                              style: theme.textTheme.bodySmall!.copyWith(color: theme.colorScheme.error),
                            )
                          else ...[
                            Text(
                              ordered.toString(),
                              style: theme.textTheme.bodySmall!.copyWith(
                                  color:
                                      ratio > warningThreshold ? theme.colorScheme.error : theme.colorScheme.primary),
                            ),
                            Text(
                              '/$capacity ${FlutterI18n.translate(context, 'orders')}',
                              style: theme.textTheme.bodySmall,
                            ),
                          ]
                        ],
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }
}
