import 'package:get/get.dart';
import 'package:jiffy/jiffy.dart';
import 'package:pacific_2_customer_portal/controllers/app.controller.dart';
import 'package:pacific_2_customer_portal/controllers/loading.controller.dart';
import 'package:pacific_2_customer_portal/models/payment_transaction.model.dart';
import 'package:pacific_2_customer_portal/models/sort.model.dart';
import 'package:pacific_2_customer_portal/models/user_profile.model.dart';
import 'package:pacific_2_customer_portal/services/payment.service.dart';
import 'package:pacific_2_customer_portal/utils/general_utils.dart';

class PaymentHistoryController extends GetxController {
  final PaymentService _paymentService;
  final LoadingController _loadingController = Get.find<LoadingController>();
  final AppController _appController = Get.find<AppController>();

  PaymentHistoryController(this._paymentService);

  final RxInt currentTransactionPage = 0.obs;
  final RxInt totalPages = 0.obs;
  final RxInt totalElements = 0.obs;
  bool _canLoadMoreTransaction = true;
  bool _isLoadingTransaction = false;
  final RxList<PaymentTransaction> transactionList = <PaymentTransaction>[].obs;

  final Rxn<String> currentKey = Rxn(PaymentHistorySortKeys.createdAt.key);
  final Rxn<SortOrder> sortOrder = Rxn(SortOrder.DESC);

  final Rx<PaymentHistoryFilter> filter = PaymentHistoryFilter.create(UserProfile()).obs;

  void clearData() {
    currentTransactionPage.value = 0;
    totalPages.value = 0;
    totalElements.value = 0;
    _canLoadMoreTransaction = true;
    _isLoadingTransaction = false;
    transactionList.value = [];
  }

  void onSort(String key) {
    if (currentKey.value != key) {
      currentKey.value = key;
      sortOrder.value = SortOrder.DESC;
    } else if (sortOrder.value == SortOrder.DESC) {
      sortOrder.value = SortOrder.ASC;
    } else {
      currentKey.value = null;
      sortOrder.value = null;
    }
    getTransactionList(isRefresh: true);
  }

  Future<void> getTransactionList({
    int size = 10,
    int page = 0,
    bool isRefresh = false,
    bool isSinglePage = false,
  }) async {
    if (_isLoadingTransaction) return;
    if (isRefresh) clearData();
    if (!_canLoadMoreTransaction && !isSinglePage) return;

    _isLoadingTransaction = true;
    _loadingController.showLoading(true);
    try {
      final response = await _paymentService.getPaymentTransactionList({
        'size': size,
        'page': page,
        if (currentKey.value != null) ...{
          'sortDirection': sortOrder.value?.name,
          'sortFields': currentKey.value,
        },
        'filter.byCustomerIds': filter.value.accounts?.map((e) => e.id).toList(),
        'filter.byTransactionType': filter.value.transactionTypes!.map((item) => item.type).toList(),
        'filter.byStatus': filter.value.transactionStatuses!.map((item) => item.status).toList(),
        if (filter.value.createdFrom != null)
          'filter.byCreatedRange.from':
              getTZDateTimeFromUtc(Jiffy.parseFromDateTime(filter.value.createdFrom!).startOf(Unit.day).dateTime)
                  .millisecondsSinceEpoch,
        if (filter.value.createdTo != null)
          'filter.byCreatedRange.to':
              getTZDateTimeFromUtc(Jiffy.parseFromDateTime(filter.value.createdTo!).endOf(Unit.day).dateTime)
                  .millisecondsSinceEpoch,
      });
      final Map<String, dynamic> data = Map.from(response);
      final List<dynamic> transactions = data['content'];
      if (isSinglePage) {
        transactionList.value = transactions.map((e) => PaymentTransaction.fromJson(e)).toList();
      } else {
        transactionList.addAll(transactions.map((e) => PaymentTransaction.fromJson(e)));
      }
      currentTransactionPage.value = data['page'] + 1;
      totalPages.value = data['totalPages'];
      totalElements.value = data['totalElements'];
      _canLoadMoreTransaction = isSinglePage || transactions.length == size;
    } catch (e) {/** */}
    _loadingController.showLoading(false);
    _isLoadingTransaction = false;
  }

  void onFilterChanged(PaymentHistoryFilter newFitler) {
    filter.value = newFitler;
    getTransactionList(isRefresh: true);
  }

  void clearFilter() {
    filter.value = PaymentHistoryFilter.create(_appController.userProfile.value);
  }
}
