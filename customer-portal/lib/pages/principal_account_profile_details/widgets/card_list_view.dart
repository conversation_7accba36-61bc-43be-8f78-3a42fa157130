import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:pacific_2_customer_portal/common/responsive/responsive.dart';
import 'package:pacific_2_customer_portal/common/widgets/card_item/card_item.dart';
import 'package:pacific_2_customer_portal/models/card.model.dart';

class CardListView extends StatelessWidget {
  const CardListView({super.key, required this.cardList});

  final List<CardAccount> cardList;

  @override
  Widget build(BuildContext context) {
    return Responsive.isDesktop(context) ? _renderDesktop(context) : _renderMobile(context);
  }

  Widget _renderDesktop(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(FlutterI18n.translate(context, 'cardList'), style: theme.textTheme.headlineSmall),
          const SizedBox(height: 12),
          GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              mainAxisExtent: 100,
            ),
            primary: false,
            shrinkWrap: true,
            itemCount: cardList.length,
            itemBuilder: (context, index) {
              final item = cardList[index];
              return CardItem(item: item, hasBorder: false);
            },
          )
        ],
      ),
    );
  }

  Widget _renderMobile(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(FlutterI18n.translate(context, 'cardList'), style: theme.textTheme.headlineSmall),
          ...cardList.map(
            (item) => CardItem(
              item: item,
              margin: const EdgeInsets.only(top: 12),
              hasBorder: false,
            ),
          ),
        ],
      ),
    );
  }
}
