import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_button.dart';
import 'package:pacific_2_customer_portal/models/order_summary.model.dart';
import 'package:pacific_2_customer_portal/utils/general_utils.dart';

class TopupPriceSummary extends StatelessWidget {
  const TopupPriceSummary({
    super.key,
    required this.summary,
    required this.onTopup,
    required this.isTopupEnabled,
  });

  final OrderSummary summary;
  final Function() onTopup;
  final bool isTopupEnabled;

  Widget _renderPriceRow(BuildContext context, String label, String value) {
    ThemeData theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: theme.textTheme.bodyMedium),
          Text(value, style: theme.textTheme.bodyMedium),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            FlutterI18n.translate(context, 'topupSummary'),
            style: theme.textTheme.headlineSmall,
          ),
          const SizedBox(height: 6),
          _renderPriceRow(
            context,
            FlutterI18n.translate(context, 'topupAmount'),
            formatPrice(summary.subTotal),
          ),
          Divider(height: 12, thickness: 1, color: theme.colorScheme.outline),
          _renderPriceRow(
            context,
            FlutterI18n.translate(context, 'totalAmount'),
            formatPrice(summary.totalAmount),
          ),
          Divider(height: 12, thickness: 1, color: theme.colorScheme.outline),
          _renderPriceRow(
            context,
            summary.surchargeName,
            formatPrice(summary.surcharge),
          ),
          Divider(height: 12, thickness: 1, color: theme.colorScheme.outline),
          const SizedBox(height: 6),
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${FlutterI18n.translate(context, 'totalPayment')}:',
                style: theme.textTheme.headlineSmall,
              ),
              Text(
                formatPrice(summary.totalPayment),
                style: theme.textTheme.headlineSmall,
              ),
            ],
          ),
          const SizedBox(height: 16),
          MainButton(
            height: 44,
            text: FlutterI18n.translate(context, 'proceedTopup'),
            onPressed: onTopup,
            disabled: !isTopupEnabled,
          ),
        ],
      ),
    );
  }
}
