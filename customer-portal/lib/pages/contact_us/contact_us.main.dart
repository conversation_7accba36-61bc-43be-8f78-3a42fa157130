// coverage:ignore-file
import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:get/get.dart';
import 'package:pacific_2_customer_portal/common/responsive/responsive.dart';
import 'package:pacific_2_customer_portal/common/widgets/title_row/title_row.dart';
import 'package:pacific_2_customer_portal/controllers/app.controller.dart';
import 'package:pacific_2_customer_portal/pages/contact_us/widgets/contact_info.dart';

class ContactUs extends StatefulWidget {
  const ContactUs({super.key});

  @override
  State<ContactUs> createState() => _ContactUsState();
}

class _ContactUsState extends State<ContactUs> {
  final AppController _appController = Get.find<AppController>();

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => ListView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.symmetric(vertical: 16, horizontal: Responsive.isMobile(context) ? 16 : 32),
        children: [
          TitleRow(title: FlutterI18n.translate(context, 'contactUs')),
          const SizedBox(height: 16),
          ContactInfo(tenant: _appController.tenant.value),
        ],
      ),
    );
  }
}
