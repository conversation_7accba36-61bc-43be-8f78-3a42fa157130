// coverage:ignore-file
import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_button.dart';
import 'package:pacific_2_customer_portal/common/widgets/my_bottom_sheet/my_bottom_sheet.dart';
import 'package:pacific_2_customer_portal/common/widgets/sort_header/sort_header.dart';
import 'package:pacific_2_customer_portal/common/widgets/title_row/title_row.dart';
import 'package:pacific_2_customer_portal/controllers/app.controller.dart';
import 'package:pacific_2_customer_portal/controllers/route.controller.dart';
import 'package:pacific_2_customer_portal/controllers/subaccount.controller.dart';
import 'package:pacific_2_customer_portal/models/sort.model.dart';
import 'package:pacific_2_customer_portal/models/sub_account.model.dart';
import 'package:pacific_2_customer_portal/models/user_profile.model.dart';
import 'package:pacific_2_customer_portal/pages/principal_account/widgets/main_account_desktop_item.dart';
import 'package:pacific_2_customer_portal/pages/principal_account/widgets/main_account_item.dart';
import 'package:pacific_2_customer_portal/pages/principal_account/widgets/principal_account_desktop_item.dart';
import 'package:pacific_2_customer_portal/pages/principal_account/widgets/principal_account_item.dart';
import 'package:pacific_2_customer_portal/pages/principal_account_profile_details/principal_account_profile_details.main.dart';
import 'package:pacific_2_customer_portal/services/utility.service.dart';
import 'package:pacific_2_customer_portal/utils/general_utils.dart';
import '../../common/responsive/responsive.dart';
import '../../controllers/loading.controller.dart';
import '../../core/constants/system_const.dart';
import '../../services/catalog.service.dart';
import '../../services/user.service.dart';
import '../../services/wallet.service.dart';

class PrincipalAccount extends StatefulWidget {
  const PrincipalAccount({super.key});

  @override
  State<PrincipalAccount> createState() => _PrincipalAccountState();
}

class _PrincipalAccountState extends State<PrincipalAccount> {
  final LoadingController loadingController = Get.find<LoadingController>();
  final AppController _appController = Get.find<AppController>();
  final RouteController _routeController = Get.find<RouteController>();
  final routes = SystemConst.ROUTE;
  final ScrollController _scrollController = ScrollController();
  late final SubAccountController _subAccountController =
      Get.put(SubAccountController(UserService(), UtilityService(), WalletService(), CatalogService()));

  StreamSubscription? activeRouteListener;

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      waitForAuth(_appController, () {
        _subAccountController.getAccountList();
      });

      activeRouteListener = _routeController.activeRoute.listen((route) {
        if (route == '/${routes['SUB_ACCOUNT']!['MAIN']!}') {
          refreshData();
        }
      });
    });
    super.initState();
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    activeRouteListener?.cancel();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.extentAfter < 500) {
      _subAccountController.getAccountList();
    }
  }

  Future<void> refreshData() async {
    if (_subAccountController.shouldRefreshSubAccountList.value) {
      await _subAccountController.getAccountList(isRefresh: true);
      setState(() {});
      _subAccountController.setRefreshState(false); // Reset the state
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Responsive(
        mobile: _renderMobile(context),
        desktop: _renderDesktop(context),
      ),
    );
  }

  Widget _renderDesktop(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Scaffold(
        body: Column(children: [
      _buildHeaderWidgetDesktop(theme, context),
      _buildContentWidgetDesktop(theme),
    ]));
  }

  Widget _renderMobile(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Scaffold(
        body: Column(children: [
      _buildHeaderWidgetMobile(theme, context),
      _buildContentWidgetMobile(theme),
    ]));
  }

  Widget _buildHeaderWidgetMobile(ThemeData theme, BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TitleRow(
                title: FlutterI18n.translate(context, 'principalAccount'),
              ),
              const SizedBox(height: 8),
              MainButton(
                width: 150,
                height: 40,
                text: FlutterI18n.translate(context, 'addNewMember'),
                onPressed: () {
                  context.push('/${routes['SUB_ACCOUNT']!['ADD']!}');
                },
                icon: SvgPicture.asset(
                  'assets/images/ic-add.svg',
                  height: 20,
                  width: 20,
                ),
                textStyle: theme.textTheme.labelMedium,
                spacing: 10,
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.all(12),
          height: 40,
          width: double.infinity,
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            border: Border(bottom: BorderSide(color: theme.colorScheme.outline)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SortHeader(
                header: SortHeaderData(SortKeys.firstName.key, FlutterI18n.translate(context, 'fullName')),
                currentKey: _subAccountController.currentKey.value,
                sortOrder: _subAccountController.sortOrder.value,
                onTap: (key) => _subAccountController.onSort(key),
              ),
              const SizedBox.shrink(),
              SortHeader(
                header: SortHeaderData(SortKeys.subAccountStatus.key, FlutterI18n.translate(context, 'profileStatus')),
                currentKey: _subAccountController.currentKey.value,
                sortOrder: _subAccountController.sortOrder.value,
                onTap: (key) => _subAccountController.onSort(key),
              ),
            ],
          ),
        )
      ],
    );
  }

  Widget _buildHeaderWidgetDesktop(ThemeData theme, BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(32, 12, 32, 12),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TitleRow(
            title: FlutterI18n.translate(context, 'principalAccount'),
          ),
          MainButton(
            width: 150,
            height: 40,
            text: FlutterI18n.translate(context, 'addNewMember'),
            onPressed: () {
              context.push('/${routes['SUB_ACCOUNT']!['ADD']!}');
            },
            icon: SvgPicture.asset(
              'assets/images/ic-add.svg',
              height: 20,
              width: 20,
            ),
            textStyle: theme.textTheme.labelMedium,
            spacing: 10,
          ),
        ],
      ),
    );
  }

  Widget _buildContentWidgetMobile(ThemeData theme) {
    return Expanded(
      child: _appController.userProfile.value.id != null || _subAccountController.subAccountList.isNotEmpty
          ? _buildListView(theme)
          : _noData(theme),
    );
  }

  Widget _buildContentWidgetDesktop(ThemeData theme) {
    return Expanded(
      child: Padding(
          padding: const EdgeInsets.fromLTRB(32, 12, 32, 12),
          child: _appController.userProfile.value.id != null || _subAccountController.subAccountList.isNotEmpty
              ? _dataViewDesktop(theme)
              : _noData(theme)),
    );
  }

  Widget _dataViewDesktop(ThemeData theme) {
    double padding = 64 + 44 * 3 + 228;
    double gridItemWidth = MediaQuery.of(context).size.width - padding;
    return Container(
      padding: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: theme.colorScheme.surface),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            height: 48,
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding: const EdgeInsets.only(left: 12),
                  width: max(293 * gridItemWidth / (1360 - padding), 120),
                  alignment: Alignment.centerLeft,
                  child: SortHeader(
                    header: SortHeaderData(SortKeys.firstName.key, FlutterI18n.translate(context, 'accounts')),
                    currentKey: _subAccountController.currentKey.value,
                    sortOrder: _subAccountController.sortOrder.value,
                    onTap: (key) => _subAccountController.onSort(key),
                  ),
                ),
                Container(
                    width: 178 * gridItemWidth / (1360 - padding),
                    alignment: Alignment.centerLeft,
                    child: Text(FlutterI18n.translate(context, 'externalID'))),
                Expanded(
                  child: Container(
                    width: 435 * gridItemWidth / (1360 - padding),
                    alignment: Alignment.centerLeft,
                    child: SortHeader(
                      header: SortHeaderData(
                          SortKeys.groupName.key, FlutterI18n.translate(context, 'group').replaceAll(':', '')),
                      currentKey: _subAccountController.currentKey.value,
                      sortOrder: _subAccountController.sortOrder.value,
                      onTap: (key) => _subAccountController.onSort(key),
                    ),
                  ),
                ),
                Container(
                  width: 100,
                  alignment: Alignment.centerLeft,
                  child: SortHeader(
                    header: SortHeaderData(SortKeys.subAccountStatus.key, FlutterI18n.translate(context, 'status')),
                    currentKey: _subAccountController.currentKey.value,
                    sortOrder: _subAccountController.sortOrder.value,
                    onTap: (key) => _subAccountController.onSort(key),
                  ),
                ),
                const SizedBox(width: 44 * 3 + 128),
              ],
            ),
          ),
          Expanded(child: _buildListViewDesktop(theme))
        ],
      ),
    );
  }

  Widget _noData(ThemeData theme) {
    return Container(
      alignment: Alignment.center,
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          SvgPicture.asset(
            'assets/images/image-empty.svg',
            height: 120,
          ),
          const SizedBox(height: 16),
          Text(
            FlutterI18n.translate(context, 'noData'),
            style: Responsive.isDesktop(context)
                ? theme.textTheme.displaySmall?.copyWith(color: theme.colorScheme.onSurfaceVariant)
                : theme.textTheme.headlineLarge?.copyWith(color: theme.colorScheme.onSurfaceVariant),
          ),
          const SizedBox(height: 16),
          Text(
            FlutterI18n.translate(context, 'noDataAccountDes'),
            textAlign: TextAlign.center,
            style: theme.textTheme.bodyLarge?.copyWith(color: theme.colorScheme.onSurfaceVariant),
          ),
        ],
      ),
    );
  }

  Widget _buildListViewDesktop(ThemeData theme) {
    return Container(
        padding: const EdgeInsets.all(1),
        color: Colors.white,
        child: ListView.builder(
          controller: _scrollController,
          physics: const AlwaysScrollableScrollPhysics(),
          itemCount: _subAccountController.subAccountList.length + 1,
          itemBuilder: (context, index) {
            if (index == 0) {
              return MainAccountDesktopItem(
                item: _appController.userProfile.value,
                onPressOrder: () => onMealPreOrderDetailPress(_appController.userProfile.value),
              );
            }
            return _buildListItem(index, theme, true);
          },
        ));
  }

  Widget _buildListView(ThemeData theme) {
    return ListView.builder(
      controller: _scrollController,
      physics: const AlwaysScrollableScrollPhysics(),
      itemCount: _subAccountController.subAccountList.length + 1,
      itemBuilder: (context, index) {
        if (index == 0) {
          return MainAccountItem(
              item: _appController.userProfile.value,
              onPressOrder: () => onMealPreOrderDetailPress(_appController.userProfile.value));
        }
        return _buildListItem(index, theme, false);
      },
    );
  }

  Widget _buildListItem(int index, ThemeData theme, bool isDesktopView) {
    var item = _subAccountController.subAccountList[index - 1];

    return isDesktopView
        ? Column(
            children: [
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.0), // Spacing for divider
                child: Divider(),
              ),
              PrincipalAccountDesktopItem(
                  item: item,
                  onPressDelete: () {
                    loadingController.showAlertDialog(context,
                        title: FlutterI18n.translate(context, 'deleteAccount'),
                        message: FlutterI18n.translate(context, 'deleteAccountConfirm'),
                        positiveText: FlutterI18n.translate(context, 'confirm'),
                        onNegativePress: () {}, onPositivePress: () {
                      deleteSubAccountPress(item);
                    });
                  },
                  onPressDetails: () => onProfileDetailsPress(item),
                  onPressEdit: () => onProfileEditPress(item),
                  onPressOrder: () => onMealPreOrderDetailPress(item.subUser)),
            ],
          )
        : PrincipalAccountItem(
            item: item,
            onPressDelete: () {
              loadingController.showAlertDialog(context,
                  title: FlutterI18n.translate(context, 'deleteAccount'),
                  message: FlutterI18n.translate(context, 'deleteAccountConfirm'),
                  positiveText: FlutterI18n.translate(context, 'confirm'),
                  onNegativePress: () {}, onPositivePress: () {
                deleteSubAccountPress(item);
              });
            },
            onPressDetails: () => onProfileDetailsPress(item),
            onPressEdit: () => onProfileEditPress(item),
            onPressOrder: () => onMealPreOrderDetailPress(item.subUser));
  }

  void onProfileDetailsPress(SubAccount item) {
    showMyBottomSheet(
      context,
      () => PrincipalAccountProfileDetails(selectedAccount: item),
    );
  }

  void onProfileEditPress(SubAccount item) {
    context.push('/${routes['SUB_ACCOUNT']!['EDIT']!}', extra: item);
  }

  Future<void> deleteSubAccountPress(SubAccount item) async {
    if (item.subUser?.id != null) {
      bool isSuccessful = await _subAccountController.deleteSubAccount(item.id!);
      if (isSuccessful) {
        _subAccountController.subAccountList.remove(item);
      }
    }
  }

  void onMealPreOrderDetailPress(UserProfile? profile) {
    context.push('/${routes['SUB_ACCOUNT']!['ORDERS']!}', extra: profile);
  }
}
