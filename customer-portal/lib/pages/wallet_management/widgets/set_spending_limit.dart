import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:pacific_2_customer_portal/common/responsive/responsive.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_button.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_outlined_button.dart';
import 'package:pacific_2_customer_portal/common/widgets/text_field_money/text_field_money.dart';
import 'package:pacific_2_customer_portal/utils/general_utils.dart';

class SetSpendingLimit extends StatefulWidget {
  const SetSpendingLimit({
    super.key,
    required this.currentLimitDay,
    required this.currentLimitWeek,
    required this.currentLimitMonth,
    required this.onSave,
  });

  final int currentLimitDay;
  final int currentLimitWeek;
  final int currentLimitMonth;
  final Function(int limitDay, int limitWeek, int limitMonth) onSave;

  @override
  State<SetSpendingLimit> createState() => _SetSpendingLimitState();
}

class _SetSpendingLimitState extends State<SetSpendingLimit> {
  late String _amountDay = '${widget.currentLimitDay != 0 ? widget.currentLimitDay : ''}';
  late final TextEditingController _textEditingControllerDay = TextEditingController(
      text: widget.currentLimitDay != 0 ? formatPrice(widget.currentLimitDay, showCurrency: false) : '');

  late String _amountWeek = '${widget.currentLimitWeek != 0 ? widget.currentLimitWeek : ''}';
  late final TextEditingController _textEditingControllerWeek = TextEditingController(
      text: widget.currentLimitWeek != 0 ? formatPrice(widget.currentLimitWeek, showCurrency: false) : '');

  late String _amountMonth = '${widget.currentLimitMonth != 0 ? widget.currentLimitMonth : ''}';
  late final TextEditingController _textEditingControllerMonth = TextEditingController(
      text: widget.currentLimitMonth != 0 ? formatPrice(widget.currentLimitMonth, showCurrency: false) : '');

  @override
  Widget build(BuildContext context) {
    return Responsive.isDesktop(context) ? _renderDesktop(context) : _renderMobile(context);
  }

  Widget _renderDesktop(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return SizedBox(
      width: 428,
      child: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Align(
              alignment: Alignment.center,
              child: Text(
                FlutterI18n.translate(context, 'setSpendingLimit'),
                style: theme.textTheme.headlineLarge,
              ),
            ),
            const SizedBox(height: 12),
            ..._renderContent(context),
            const SizedBox(height: 24),
            _renderButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _renderMobile(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 10, 16, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            FlutterI18n.translate(context, 'setSpendingLimit'),
            style: theme.textTheme.headlineSmall,
          ),
          const SizedBox(height: 4),
          Expanded(child: ListView(children: _renderContent(context))),
          _renderButtons(context),
        ],
      ),
    );
  }

  List<Widget> _renderContent(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return [
      Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _renderDot(),
          Expanded(
            child: Text(
              FlutterI18n.translate(context, 'spendingLimitInfo1'),
              style: theme.textTheme.bodyMedium,
            ),
          ),
        ],
      ),
      Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _renderDot(),
          Expanded(
            child: Text(
              FlutterI18n.translate(context, 'spendingLimitInfo2'),
              style: theme.textTheme.bodyMedium,
            ),
          ),
        ],
      ),
      const SizedBox(height: 16),
      Text(
        FlutterI18n.translate(context, 'dailySpendingAmount'),
        style: theme.textTheme.bodyMedium,
      ),
      const SizedBox(height: 4),
      TextFieldMoney(
        controller: _textEditingControllerDay,
        hintText: FlutterI18n.translate(context, 'noLimit'),
        onChanged: (text) => setState(() {
          _amountDay = text.replaceAll(',', '').replaceAll('.', '');
        }),
      ),
      const SizedBox(height: 16),
      Text(
        FlutterI18n.translate(context, 'weeklySpendingAmount'),
        style: theme.textTheme.bodyMedium,
      ),
      const SizedBox(height: 4),
      TextFieldMoney(
        controller: _textEditingControllerWeek,
        hintText: FlutterI18n.translate(context, 'noLimit'),
        onChanged: (text) => setState(() {
          _amountWeek = text.replaceAll(',', '').replaceAll('.', '');
        }),
      ),
      const SizedBox(height: 16),
      Text(
        FlutterI18n.translate(context, 'monthlySpendingAmount'),
        style: theme.textTheme.bodyMedium,
      ),
      const SizedBox(height: 4),
      TextFieldMoney(
        controller: _textEditingControllerMonth,
        hintText: FlutterI18n.translate(context, 'noLimit'),
        onChanged: (text) => setState(() {
          _amountMonth = text.replaceAll(',', '').replaceAll('.', '');
        }),
      ),
    ];
  }

  Widget _renderButtons(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Row(
      children: [
        Expanded(
          child: MainOutlinedButton(
            height: 40,
            text: FlutterI18n.translate(context, 'cancel'),
            textStyle: theme.textTheme.labelMedium,
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: MainButton(
            height: 40,
            text: FlutterI18n.translate(context, 'save'),
            textStyle: theme.textTheme.labelMedium,
            onPressed: () {
              widget.onSave(
                int.tryParse(_amountDay.trim()) ?? 0,
                int.tryParse(_amountWeek.trim()) ?? 0,
                int.tryParse(_amountMonth.trim()) ?? 0,
              );
              Navigator.of(context).pop();
            },
          ),
        )
      ],
    );
  }

  Widget _renderDot() => Container(
        width: 4,
        height: 4,
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.onSurface,
          shape: BoxShape.circle,
        ),
      );
}
