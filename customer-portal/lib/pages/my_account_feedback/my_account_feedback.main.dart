// coverage:ignore-file
import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:get/get.dart';
import 'package:pacific_2_customer_portal/common/responsive/responsive.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_button.dart';
import 'package:pacific_2_customer_portal/core/services/toast.service.dart';
import 'package:pacific_2_customer_portal/pages/my_account_feedback/my_account_feedback.controller.dart';
import 'package:pacific_2_customer_portal/pages/my_account_feedback/widgets/contact_form.dart';
import 'package:pacific_2_customer_portal/services/user.service.dart';

class MyAccountFeedback extends StatefulWidget {
  const MyAccountFeedback({super.key});

  @override
  State<MyAccountFeedback> createState() => _MyAccountFeedbackState();
}

class _MyAccountFeedbackState extends State<MyAccountFeedback> {
  final _myAccountFeedbackController = Get.put(MyAccountFeedbackController(UserService()));

  late final TextEditingController _titleController = TextEditingController();
  late final TextEditingController _desController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _titleController.addListener(() => _myAccountFeedbackController.onTitleChange(_titleController.text));
    _desController.addListener(() => _myAccountFeedbackController.onDescriptionChange(_desController.text));
  }

  @override
  void dispose() {
    _titleController.dispose();
    _desController.dispose();
    Get.delete<MyAccountFeedbackController>();
    super.dispose();
  }

  Future<void> onSendFeedback(BuildContext context) async {
    final result = await _myAccountFeedbackController.sendFeedback();
    if (context.mounted && result) {
      _titleController.clear();
      _desController.clear();
      ToastService(context).success(FlutterI18n.translate(context, 'contactFormSubmitSuccess'));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => ListView(
        padding: EdgeInsets.symmetric(vertical: 16, horizontal: Responsive.isDesktop(context) ? 0 : 16),
        children: [
          ContactForm(
            titleController: _titleController,
            desController: _desController,
          ),
          const SizedBox(height: 24),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              MainButton(
                width: Responsive.isDesktop(context) ? 164 : null,
                text: FlutterI18n.translate(context, 'sendFeedback'),
                disabled: !_myAccountFeedbackController.canSubmit(),
                onPressed: () => onSendFeedback(context),
              )
            ],
          )
        ],
      ),
    );
  }
}
