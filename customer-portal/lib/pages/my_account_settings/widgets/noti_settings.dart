import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:pacific_2_customer_portal/common/responsive/responsive.dart';
import 'package:pacific_2_customer_portal/common/widgets/dashed_line/dashed_line.dart';
import 'package:pacific_2_customer_portal/common/widgets/my_switch/my_switch.dart';
import 'package:pacific_2_customer_portal/models/user_preference.model.dart';

class NotiSettings extends StatelessWidget {
  const NotiSettings({
    super.key,
    required this.paymentSettings,
    required this.orderSettings,
    required this.walletSettings,
    required this.onSavePref,
  });

  final UserPreference? paymentSettings;
  final UserPreference? orderSettings;
  final UserPreference? walletSettings;
  final Function(UserPreferenceKey key) onSavePref;

  void onSwitchSetting(UserPreference pref, UserPreferenceKey key, NotiChannel channel, bool value) {
    if (value) {
      pref.data!.channels!.add(channel.value);
    } else {
      pref.data!.channels!.remove(channel.value);
    }
    onSavePref(key);
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Container(
      padding: EdgeInsets.all(Responsive.isDesktop(context) ? 16 : 12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(FlutterI18n.translate(context, 'notificationConfig'), style: theme.textTheme.headlineSmall),
          const SizedBox(height: 4),
          Text(FlutterI18n.translate(context, 'notificationConfigDes'), style: theme.textTheme.bodySmall),
          const SizedBox(height: 8),
          if (Responsive.isDesktop(context))
            Row(
              children: [
                Expanded(child: _renderNotificationPayment(context)),
                const SizedBox(width: 16),
                Expanded(child: _renderNotificationOrder(context)),
                const SizedBox(width: 16),
                Expanded(child: _renderNotificationWallet(context)),
              ],
            )
          else ...[
            _renderNotificationPayment(context),
            const SizedBox(height: 8),
            _renderNotificationOrder(context),
            const SizedBox(height: 8),
            _renderNotificationWallet(context),
          ]
        ],
      ),
    );
  }

  Widget _renderNotificationPayment(BuildContext context) {
    return _renderSettingBlock(
      context,
      pref: paymentSettings,
      key: UserPreferenceKey.notificationPayment,
      title: FlutterI18n.translate(context, 'paymentSuccessfully'),
    );
  }

  Widget _renderNotificationOrder(BuildContext context) {
    return _renderSettingBlock(
      context,
      pref: orderSettings,
      key: UserPreferenceKey.notificationOrder,
      title: FlutterI18n.translate(context, 'orderStatusUpdate'),
    );
  }

  Widget _renderNotificationWallet(BuildContext context) {
    return _renderSettingBlock(
      context,
      pref: walletSettings,
      key: UserPreferenceKey.notificationWallet,
      title: FlutterI18n.translate(context, 'walletTransactionAlert'),
    );
  }

  Widget _renderSettingBlock(
    BuildContext context, {
    required UserPreference? pref,
    required UserPreferenceKey key,
    required String title,
  }) {
    ThemeData theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.fromLTRB(16, 12, 16, 0),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainer,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(title, style: theme.textTheme.titleSmall),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 12),
            child: Row(
              children: [
                Expanded(
                  child: Text(FlutterI18n.translate(context, 'emailNoti'), style: theme.textTheme.bodyMedium),
                ),
                MySwitch(
                  key: ValueKey('${key.key}_${NotiChannel.email.value}'),
                  value: pref?.data?.channels?.contains(NotiChannel.email.value) ?? false,
                  onChanged: (value) => onSwitchSetting(pref!, key, NotiChannel.email, value),
                )
              ],
            ),
          ),
          // TODO: future version
          // CustomPaint(
          //   size: Size(double.infinity, 1),
          //   painter: DashedLinePainter(color: theme.colorScheme.outline),
          // ),
          // Padding(
          //   padding: const EdgeInsets.symmetric(vertical: 12),
          //   child: Row(
          //     children: [
          //       Expanded(
          //         child: Text(FlutterI18n.translate(context, 'pushNoti'),
          //             style: theme.textTheme.bodyMedium),
          //       ),
          //       MySwitch(
          //         key: ValueKey('${key.key}_${NotiChannel.push.value}'),
          //         value:
          //             pref?.data?.channels?.contains(NotiChannel.push.value) ??
          //                 false,
          //         onChanged: (value) =>
          //             onSwitchSetting(pref!, key, NotiChannel.push, value),
          //       )
          //     ],
          //   ),
          // ),
          DashedLine(color: theme.colorScheme.outline),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 12),
            child: Row(
              children: [
                Expanded(
                  child: Text(FlutterI18n.translate(context, 'inAppNoti'), style: theme.textTheme.bodyMedium),
                ),
                MySwitch(
                  key: ValueKey('${key.key}_${NotiChannel.inApp.value}'),
                  value: pref?.data?.channels?.contains(NotiChannel.inApp.value) ?? false,
                  onChanged: (value) => onSwitchSetting(pref!, key, NotiChannel.inApp, value),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
