// coverage:ignore-file
import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:get/get.dart';
import 'package:pacific_2_customer_portal/common/responsive/responsive.dart';
import 'package:pacific_2_customer_portal/common/widgets/account_list/account_list.controller.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/filter_button.dart';
import 'package:pacific_2_customer_portal/common/widgets/empty_view/empty_view.dart';
import 'package:pacific_2_customer_portal/common/widgets/filter/filter_dialog.dart';
import 'package:pacific_2_customer_portal/common/widgets/my_bottom_sheet/my_bottom_sheet.dart';
import 'package:pacific_2_customer_portal/common/widgets/my_table/my_table.dart';
import 'package:pacific_2_customer_portal/common/widgets/sort_header/sort_header.dart';
import 'package:pacific_2_customer_portal/common/widgets/title_row/title_row.dart';
import 'package:pacific_2_customer_portal/controllers/app.controller.dart';
import 'package:pacific_2_customer_portal/models/controller_tag.model.dart';
import 'package:pacific_2_customer_portal/models/order_history.model.dart';
import 'package:pacific_2_customer_portal/models/sort.model.dart';
import 'package:pacific_2_customer_portal/pages/order_details/order_details.main.dart';
import 'package:pacific_2_customer_portal/pages/order_history/order_history.controller.dart';
import 'package:pacific_2_customer_portal/pages/order_history/widgets/history_item.dart';
import 'package:pacific_2_customer_portal/pages/order_history/widgets/order_filter.dart';
import 'package:pacific_2_customer_portal/pages/order_history/widgets/order_history_table.dart';
import 'package:pacific_2_customer_portal/services/order.service.dart';
import 'package:pacific_2_customer_portal/services/user.service.dart';
import 'package:pacific_2_customer_portal/utils/general_utils.dart';

class OrderHistory extends StatefulWidget {
  const OrderHistory({super.key});

  @override
  State<OrderHistory> createState() => _OrderHistoryState();
}

class _OrderHistoryState extends State<OrderHistory> {
  final ScrollController _scrollController = ScrollController();
  final OrderHistoryController _orderHistoryController = Get.put(OrderHistoryController(OrderService()));
  late final AppController _appController = Get.find<AppController>();

  @override
  void initState() {
    super.initState();
    Get.put(AccountListController(UserService()), tag: ControllerTag.accountListOrder.name);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      waitForAuth(_appController, () async {
        _orderHistoryController.clearFilter();
        await _orderHistoryController.getOrderHistory();
        if (mounted && Responsive.isMobile(context)) {
          _orderHistoryController.getOrderHistory(page: _orderHistoryController.currentOrderPage.value);
        }
      });
    });
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    Get.delete<AccountListController>(tag: ControllerTag.accountListOrder.name);
    Get.delete<OrderHistoryController>();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.extentAfter < 500) {
      _orderHistoryController.getOrderHistory(page: _orderHistoryController.currentOrderPage.value);
    }
  }

  void showDetails(OrderHistoryItem item) {
    showMyBottomSheet(
      context,
      () => OrderDetails(
        orderItem: item,
        onUpdateOrder: _orderHistoryController.onUpdateOrder,
      ),
    );
  }

  void showFilter(BuildContext context) {
    showFilterDialog(
      context,
      child: OrderFilterWidget(
        currentFilter: _orderHistoryController.filter.value,
        onApplyFilter: _orderHistoryController.onFilterChanged,
        onClearFilter: () {
          _orderHistoryController.clearFilter();
          _orderHistoryController.getOrderHistory(isRefresh: true);
        },
        onClose: () => Navigator.of(context).pop(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Responsive(
        mobile: _renderMobile(context),
        desktop: _renderDesktop(context),
      ),
    );
  }

  Widget _renderDesktop(BuildContext context) {
    ThemeData theme = Theme.of(context);
    const columnFlexWidths = [6, 3, 3, 3, 3, 2, 3, 1];

    return Column(
      children: [
        _renderTitleRow(context, padding: 32),
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: MyTable(
              headers: [
                SortHeaderData(OrderHistorySortKeys.orderId.key, FlutterI18n.translate(context, 'orderIdText')),
                SortHeaderData('', FlutterI18n.translate(context, 'user')),
                SortHeaderData('', FlutterI18n.translate(context, 'orderType')),
                SortHeaderData('', FlutterI18n.translate(context, 'orderStatus')),
                SortHeaderData(
                    OrderHistorySortKeys.collectionDate.key, FlutterI18n.translate(context, 'collectionDate')),
                SortHeaderData('', FlutterI18n.translate(context, 'totalItem')),
                SortHeaderData('', FlutterI18n.translate(context, 'paymentStatus')),
                SortHeaderData('', ''),
              ],
              content: _orderHistoryController.orderList.isEmpty
                  ? Container(
                      width: double.infinity, color: theme.colorScheme.surfaceContainer, child: const EmptyView())
                  : OrderHistoryTable(
                      data: _orderHistoryController.orderList.toList(),
                      onPressDetails: showDetails,
                      columnFlexWidths: columnFlexWidths,
                    ),
              columnFlexWidths: columnFlexWidths,
              currentKey: _orderHistoryController.currentKey.value,
              sortOrder: _orderHistoryController.sortOrder.value,
              sortable: const [true, false, false, false, true, false, false, false],
              onSort: (key) => _orderHistoryController.onSort(key),
              currentPage: _orderHistoryController.currentOrderPage.value,
              totalPages: _orderHistoryController.totalPages.value,
              totalElements: _orderHistoryController.totalElements.value,
              onPageChanged: (pageDisplay) =>
                  _orderHistoryController.getOrderHistory(page: pageDisplay - 1, isSinglePage: true),
            ),
          ),
        ),
      ],
    );
  }

  Widget _renderMobile(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Column(
      children: [
        _renderTitleRow(context, padding: 16),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            border: Border(bottom: BorderSide(color: theme.colorScheme.outline)),
          ),
          child: SortHeader(
            header: SortHeaderData(SortKeys.createdAt.key, FlutterI18n.translate(context, 'sortOrderCreate')),
            currentKey: _orderHistoryController.currentKey.value,
            sortOrder: _orderHistoryController.sortOrder.value,
            onTap: (_) {
              _orderHistoryController.onSort(OrderHistorySortKeys.createdAt.key);
            },
          ),
        ),
        Expanded(
          child: _orderHistoryController.orderList.isEmpty
              ? ListView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  children: const [EmptyView()],
                )
              : ListView.builder(
                  controller: _scrollController,
                  physics: const AlwaysScrollableScrollPhysics(),
                  itemCount: _orderHistoryController.orderList.length,
                  itemBuilder: (ctx, index) {
                    var item = _orderHistoryController.orderList[index];
                    return HistoryItem(
                      item: item,
                      onPressDetails: () => showDetails(item),
                    );
                  },
                ),
        )
      ],
    );
  }

  Widget _renderTitleRow(BuildContext context, {required double padding}) {
    return Padding(
      padding: EdgeInsets.fromLTRB(padding, 16, padding, 12),
      child: Row(
        children: [
          Expanded(
            child: TitleRow(
              title: FlutterI18n.translate(context, 'orderHistory'),
            ),
          ),
          FilterButton(
            onPressed: () => showFilter(context),
          )
        ],
      ),
    );
  }
}
