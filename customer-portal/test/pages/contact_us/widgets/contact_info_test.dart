import 'package:flutter_test/flutter_test.dart';
import 'package:pacific_2_customer_portal/models/tenant.model.dart';
import 'package:pacific_2_customer_portal/pages/contact_us/widgets/contact_info.dart';

import '../../../mocks/localized_widget.dart';
import '../../../setup.config.dart';

void main() {
  setupTestEnv();

  final tenant = Tenant(
    email: "test@email",
    phoneNumber: '012345',
    addressLine1: 'address1',
    addressLine2: 'address2',
    city: 'city',
    postalCode: '100000',
  );
  testWidgets("ContactInfo render correctly", (tester) async {
    await tester.pumpWidget(
      renderLocalizedWidget(ContactInfo(tenant: tenant)),
    );
    await tester.pumpAndSettle();

    expect(find.text(tenant.email!), findsOne);
    expect(find.text(tenant.phoneNumber!), findsOne);
    expect(
        find.text(
            '${tenant.addressLine1}, ${tenant.addressLine2}, ${tenant.city}, ${tenant.postalCode}'),
        findsOne);
  });

  testWidgets("ContactInfo render correctly desktop", (tester) async {
    await tester.pumpWidget(
      renderLocalizedWidgetDesktop(ContactInfo(tenant: tenant)),
    );
    await tester.pumpAndSettle();

    expect(find.text(tenant.email!), findsOne);
    expect(find.text(tenant.phoneNumber!), findsOne);
    expect(
        find.text(
            '${tenant.addressLine1}, ${tenant.addressLine2}, ${tenant.city}, ${tenant.postalCode}'),
        findsOne);
  });
}
