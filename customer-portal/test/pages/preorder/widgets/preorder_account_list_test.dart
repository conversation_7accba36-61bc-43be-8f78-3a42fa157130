import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/mockito.dart';
import 'package:pacific_2_customer_portal/controllers/app.controller.dart';
import 'package:pacific_2_customer_portal/models/user_profile.model.dart';
import 'package:pacific_2_customer_portal/pages/preorder/preorder.controller.dart';
import 'package:pacific_2_customer_portal/pages/preorder/widgets/preorder_account_list.dart';

import '../../../mocks/localized_widget.dart';
import '../../../mocks/mocks.mocks.dart';
import '../../../mocks/network_image.mock.dart';
import '../../../mocks/responses/sub_account_list.response.mock.dart';
import '../../../setup.config.dart';

void main() {
  setupTestEnv();
  final profile = UserProfile(id: 'test', firstName: 'Test', lastName: 'User');

  final mockUserService = MockUserService();
  final appController = Get.find<AppController>();
  appController.userProfile.value = profile;
  Get.put(PreOrderController(
      MockOrderService(), mockUserService, MockCatalogService()));

  testWidgets("PreOrderAccountList render correctly", (tester) async {
    mockNetworkImagesFor(() async {
      when(mockUserService.getSubAccountList(any, any))
          .thenAnswer((_) async => mockSubAccountListResponse);

      await tester.pumpWidget(
        renderLocalizedWidget(
          PreOrderAccountList(
            selectedAccount: null,
            onSelectAccount: (_) {},
          ),
        ),
      );
      await tester.pumpAndSettle();

      expect(find.text('Test User'), findsOne);
      verify(mockUserService.getSubAccountList(any, any))
          .called(greaterThan(0));
    });
  });
}
