import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pacific_2_customer_portal/models/preorder_menu_item.model.dart';
import 'package:pacific_2_customer_portal/pages/preorder/widgets/category/category_list.dart';
import 'package:pacific_2_customer_portal/themes/colors.dart';

import '../../../../mocks/localized_widget.dart';

void main() {
  testWidgets(
    'CategoryList should render All item even when data is empty',
    (tester) async {
      await tester.pumpWidget(renderLocalizedWidget(CategoryList(
        data: const [],
        selectedCategory: const [],
        onSelectAll: () {},
        onSelectCategory: (_) {},
      )));
      await tester.pumpAndSettle();

      expect(find.text('All item'), findsOne);
    },
  );

  testWidgets(
    'CategoryList should render correctly with data',
    (tester) async {
      final data = [
        Category(id: '1', name: 'Cat <PERSON>'),
        Category(id: '2', name: '<PERSON> <PERSON>'),
        Category(id: '3', name: '<PERSON> <PERSON>')
      ];
      bool pressedCategory = false;
      bool pressedAll = false;
      await tester.pumpWidget(renderLocalizedWidget(CategoryList(
        data: data,
        selectedCategory: data.sublist(1),
        onSelectAll: () => pressedAll = true,
        onSelectCategory: (_) => pressedCategory = true,
      )));
      await tester.pumpAndSettle();

      expect(find.text('All item'), findsOne);
      expect(find.text('Cat A'), findsOne);
      expect(find.text('Cat B'), findsOne);
      expect(find.text('Cat C'), findsOne);

      expect(
        (tester.firstWidget(find.text('Cat A')) as Text).style?.color,
        lightScheme.onSurface,
      );
      expect(
        (tester.firstWidget(find.text('Cat B')) as Text).style?.color,
        lightScheme.primary,
      );
      expect(
        (tester.firstWidget(find.text('Cat C')) as Text).style?.color,
        lightScheme.primary,
      );

      await tester.tap(find.text('Cat A'));
      await tester.tap(find.text('All item'));
      expect(pressedAll, isTrue);
      expect(pressedCategory, isTrue);
    },
  );
}
