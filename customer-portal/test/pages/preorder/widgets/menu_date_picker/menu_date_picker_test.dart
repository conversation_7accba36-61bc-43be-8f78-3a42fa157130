import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:jiffy/jiffy.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_button.dart';
import 'package:pacific_2_customer_portal/pages/preorder/preorder.controller.dart';
import 'package:pacific_2_customer_portal/pages/preorder/widgets/menu_date_picker/day_component.dart';
import 'package:pacific_2_customer_portal/pages/preorder/widgets/menu_date_picker/menu_date_picker.dart';

import '../../../../mocks/localized_widget.dart';
import '../../../../mocks/mocks.mocks.dart';
import '../../../../setup.config.dart';

void main() {
  setupTestEnv();
  final controller = Get.put(PreOrderController(
      MockOrderService(), MockUserService(), MockCatalogService()));

  testWidgets("MenuDatePicker render correctly", (tester) async {
    DateTime now = DateTime.now();
    String currentMonth = DateFormat.yMMMM().format(now);
    await tester.pumpWidget(
      renderLocalizedWidget(
        MenuDatePicker(
          selectedDate: null,
          onSelectDate: (value) {},
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.text('Today'), findsOne);
    expect(find.text(currentMonth), findsOne);
    expect(find.byKey(ValueKey(currentMonth)), findsOne);
    expect(find.byType(MainButton), findsOne);
    expect(
      (tester.firstWidget(find.byType(MainButton)) as MainButton).disabled,
      isTrue,
    );
  });

  testWidgets("MenuDatePicker render correctly desktop", (tester) async {
    DateTime now = DateTime.now();
    String currentMonth = DateFormat.yMMMM().format(now);
    bool selectDate = false;
    await tester.pumpWidget(
      renderLocalizedWidgetDesktop(
        MenuDatePicker(
          selectedDate: null,
          onSelectDate: (_) => selectDate = true,
          isDesktop: true,
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.text('Today'), findsOne);
    expect(find.text(currentMonth), findsOne);
    expect(find.byKey(ValueKey(currentMonth)), findsOne);
    expect(find.byType(MainButton), findsNothing);

    await tester.tap(find.text('1'));
    await tester.tap(find.text('Today'));
    expect(selectDate, true);
  });

  testWidgets("MenuDatePicker should pick correct day when press today",
      (tester) async {
    DateTime? selectedDate;
    controller.calendarMenuSummary.value = {
      DateFormat('yyyy-MM-dd').format(DateTime.now()): true
    };
    await tester.pumpWidget(
      renderLocalizedWidget(
        MenuDatePicker(
          selectedDate: null,
          onSelectDate: (value) {
            selectedDate = value;
          },
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.text('Today'), findsOne);
    expect(find.byType(MainButton), findsOne);

    await tester.tap(find.text('Today'));
    await tester.pumpAndSettle();
    expect(
      (tester.firstWidget(find.byType(MainButton)) as MainButton).disabled,
      isFalse,
    );

    await tester.tap(find.byType(MainButton));
    await tester.pumpAndSettle();
    expect(selectedDate?.day, DateTime.now().day);
  });

  testWidgets("MenuDatePicker should change month when clicked on arrow",
      (tester) async {
    String currentMonth = DateFormat.yMMMM().format(DateTime.now());
    String nextMonth =
        DateFormat.yMMMM().format(Jiffy.now().add(months: 1).dateTime);

    await tester.pumpWidget(
      renderLocalizedWidget(
        MenuDatePicker(
          selectedDate: null,
          onSelectDate: (value) {},
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.byKey(ValueKey(currentMonth)), findsOne);
    expect(find.byKey(ValueKey(nextMonth)), findsNothing);
    await tester.tap(find.byIcon(Icons.arrow_forward_ios));
    await tester.pumpAndSettle();
    expect(find.byKey(ValueKey(currentMonth)), findsNothing);
    expect(find.byKey(ValueKey(nextMonth)), findsOne);
    await tester.tap(find.byIcon(Icons.arrow_back_ios_new));
    await tester.pumpAndSettle();
    expect(find.byKey(ValueKey(currentMonth)), findsOne);
    expect(find.byKey(ValueKey(nextMonth)), findsNothing);
  });

  testWidgets("MenuDatePicker should select day correctly", (tester) async {
    DateTime day1 = DateTime.now().copyWith(day: 1);
    DateTime day2 = DateTime.now().copyWith(day: 2);
    controller.calendarMenuSummary.value = {
      DateFormat('yyyy-MM-dd').format(day1): true,
      DateFormat('yyyy-MM-dd').format(day2): true
    };
    await tester.pumpWidget(
      renderLocalizedWidget(
        MenuDatePicker(
          selectedDate: day1,
          onSelectDate: (value) {},
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(
      find.byKey(ValueKey('date_${DateFormat.yMd().format(day1)}')),
      findsOne,
    );
    expect(
      (tester.firstWidget(
                  find.byKey(ValueKey('date_${DateFormat.yMd().format(day1)}')))
              as DayComponent)
          .isSelected,
      isTrue,
    );
    await tester.tap(
      find.byKey(ValueKey('date_${DateFormat.yMd().format(day2)}')),
    );
    await tester.pumpAndSettle();
    expect(
      (tester.firstWidget(
                  find.byKey(ValueKey('date_${DateFormat.yMd().format(day2)}')))
              as DayComponent)
          .isSelected,
      isTrue,
    );
  });
}
