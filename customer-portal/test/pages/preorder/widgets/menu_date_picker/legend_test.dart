import 'package:flutter_test/flutter_test.dart';
import 'package:pacific_2_customer_portal/pages/preorder/widgets/menu_date_picker/legend.dart';

import '../../../../mocks/localized_widget.dart';

void main() {
  testWidgets("Legend render correctly", (tester) async {
    await tester.pumpWidget(
      renderLocalizedWidget(
        const Legend(),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.text('Available Day'), findsOne);
    expect(find.text('Order created'), findsOne);
    expect(find.text('Item added'), findsOne);
    expect(find.text('Cafe closed'), findsOne);
  });
}
