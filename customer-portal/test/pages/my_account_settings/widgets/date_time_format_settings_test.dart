import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:pacific_2_customer_portal/models/common.model.dart';
import 'package:pacific_2_customer_portal/models/user_preference.model.dart';
import 'package:pacific_2_customer_portal/pages/my_account_settings/my_account_settings.controller.dart';
import 'package:pacific_2_customer_portal/pages/my_account_settings/widgets/date_time_format_settings.dart';

import '../../../mocks/localized_widget.dart';
import '../../../mocks/mocks.mocks.dart';
import '../../../mocks/responses/metadata.response.mock.dart';
import '../../../mocks/responses/user_preference.response.mock.dart';
import '../../../setup.config.dart';

void main() {
  setupTestEnv();
  final controller = Get.put(
      MyAccountSettingsController(MockUserService(), MockUtilityService()));
  controller.dateFormatList = mockDateFormatResponse.cast<String>();
  controller.timeFormatList = mockTimeFormatResponse.cast<String>();
  controller.timeZonelist =
      mockTimeZoneResponse.map((item) => TimeZone.fromJson(item)).toList();

  testWidgets("DateTimeFormatSettings render correctly", (tester) async {
    FlutterError.onError = ignoreOverflowErrors;
    final pref = UserPreference.fromJson(mockDateTimePrefResponse);
    bool pressedSave = false;
    bool refreshed = false;
    await tester.pumpWidget(
      renderLocalizedWidget(
        DateTimeFormatSettings(
          pref: pref,
          onSavePref: () => pressedSave = true,
          onRefresh: () => refreshed = true,
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.text(pref.data!.dateFormat!), findsOne);
    expect(find.text(pref.data!.timeFormat!), findsOne);
    expect(find.text(pref.data!.timeZone!.displayName!), findsOne);

    await tester.tap(find.text('Save Change'));

    expect(pressedSave, true);

    await tester.tap(find.text(pref.data!.dateFormat!));
    await tester.pumpAndSettle();
    await tester.tap(find.text(mockDateFormatResponse[1]));
    await tester.pumpAndSettle();
    await tester.tap(find.text('Select'));
    await tester.pumpAndSettle();

    expect(pref.data?.dateFormat, mockDateFormatResponse[1]);

    await tester.tap(find.text(pref.data!.timeFormat!));
    await tester.pumpAndSettle();
    await tester.tap(find.text(mockTimeFormatResponse[0]));
    await tester.pumpAndSettle();
    await tester.tap(find.text('Select'));
    await tester.pumpAndSettle();

    expect(pref.data?.timeFormat, mockTimeFormatResponse[0]);

    await tester.tap(find.text(pref.data!.timeZone!.displayName!));
    await tester.pumpAndSettle();
    await tester.tap(find.text(mockTimeZoneResponse[0]['displayName']));
    await tester.pumpAndSettle();
    await tester.tap(find.text('Select'));
    await tester.pumpAndSettle();

    expect(pref.data?.timeZone?.zoneId, mockTimeZoneResponse[0]['zoneId']);

    await tester.tap(find.text('Save Change'));
    await tester.pumpAndSettle();

    expect(find.text('Timezone Change Detected'), findsOne);

    await tester.tap(find.text('Confirm'));
    await tester.pumpAndSettle();

    expect(find.text('Timezone Change Detected'), findsNothing);

    expect(refreshed, true);
  });

  testWidgets("DateTimeFormatSettings render correctly desktop",
      (tester) async {
    FlutterError.onError = ignoreOverflowErrors;
    final pref = UserPreference.fromJson(mockDateTimePrefResponse);
    bool pressedSave = false;
    bool refreshed = false;
    await tester.pumpWidget(
      renderLocalizedWidgetDesktop(
        DateTimeFormatSettings(
          pref: pref,
          onSavePref: () => pressedSave = true,
          onRefresh: () => refreshed = true,
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.text(pref.data!.dateFormat!), findsOne);
    expect(find.text(pref.data!.timeFormat!), findsOne);
    expect(find.text(pref.data!.timeZone!.displayName!), findsOne);

    await tester.tap(find.text('Save Change'));

    expect(pressedSave, true);

    await tester.tap(find.text(pref.data!.dateFormat!));
    await tester.pumpAndSettle();
    await tester.tap(find.text(mockDateFormatResponse[1]));
    await tester.pumpAndSettle();
    await tester.tap(find.text('Select'));
    await tester.pumpAndSettle();

    expect(pref.data?.dateFormat, mockDateFormatResponse[1]);

    await tester.tap(find.text(pref.data!.timeFormat!));
    await tester.pumpAndSettle();
    await tester.tap(find.text(mockTimeFormatResponse[0]));
    await tester.pumpAndSettle();
    await tester.tap(find.text('Select'));
    await tester.pumpAndSettle();

    expect(pref.data?.timeFormat, mockTimeFormatResponse[0]);

    await tester.tap(find.text(pref.data!.timeZone!.displayName!));
    await tester.pumpAndSettle();
    await tester.tap(find.text(mockTimeZoneResponse[0]['displayName']));
    await tester.pumpAndSettle();
    await tester.tap(find.text('Select'));
    await tester.pumpAndSettle();

    expect(pref.data?.timeZone?.zoneId, mockTimeZoneResponse[0]['zoneId']);

    await tester.tap(find.text('Save Change'));
    await tester.pumpAndSettle();

    expect(find.text('Timezone Change Detected'), findsOne);

    await tester.tap(find.text('Cancel'));
    await tester.pumpAndSettle();

    expect(find.text('Timezone Change Detected'), findsNothing);

    expect(refreshed, true);
  });
}
