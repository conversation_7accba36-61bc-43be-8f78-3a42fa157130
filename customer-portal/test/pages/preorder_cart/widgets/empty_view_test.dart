import 'package:flutter_test/flutter_test.dart';
import 'package:pacific_2_customer_portal/pages/preorder_cart/widgets/empty_view.dart';

import '../../../mocks/localized_widget.dart';

void main() {
  testWidgets("EmptyCartView render correctly", (tester) async {
    await tester.pumpWidget(
      renderLocalizedWidget(
        const EmptyCartView(),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.text('Cart empty'), findsOne);
  });
}
