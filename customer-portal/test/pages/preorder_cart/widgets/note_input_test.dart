import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_button.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_outlined_button.dart';
import 'package:pacific_2_customer_portal/pages/preorder_cart/widgets/note_input.dart';

import '../../../mocks/localized_widget.dart';

void main() {
  testWidgets("NoteInput render correctly", (tester) async {
    String note = '';
    await tester.pumpWidget(
      renderLocalizedWidget(
        NoteInput(
          currentNote: '',
          onSaveNote: (newNote) {
            note = newNote;
          },
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.text('Add note'), findsOne);
    expect(find.byType(MainButton), findsOne);
    expect(
      (tester.firstWidget(find.byType(MainButton)) as MainButton).disabled,
      isTrue,
    );

    await tester.enterText(find.byType(TextField).first, "test");
    await tester.pumpAndSettle();

    expect(find.text('test'), findsOne);
    expect(
      (tester.firstWidget(find.byType(MainButton)) as MainButton).disabled,
      isFalse,
    );

    await tester.tap(find.byType(MainButton));
    await tester.pumpAndSettle();

    expect(note, 'test');
  });

  testWidgets("NoteInput render correctly desktop 1", (tester) async {
    String note = '';
    await tester.pumpWidget(
      renderLocalizedWidgetDesktop(
        NoteInput(
          currentNote: '',
          onSaveNote: (newNote) {
            note = newNote;
          },
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.text('Add note'), findsOne);
    expect(find.byType(MainButton), findsOne);
    expect(find.byType(MainOutlinedButton), findsOne);
    expect(
      (tester.firstWidget(find.byType(MainButton)) as MainButton).disabled,
      isTrue,
    );

    await tester.enterText(find.byType(TextField).first, "test");
    await tester.pumpAndSettle();

    expect(find.text('test'), findsOne);
    expect(
      (tester.firstWidget(find.byType(MainButton)) as MainButton).disabled,
      isFalse,
    );

    await tester.tap(find.byType(MainButton));
    await tester.pumpAndSettle();

    expect(note, 'test');
  });

  testWidgets("NoteInput render correctly desktop 2", (tester) async {
    await tester.pumpWidget(
      renderLocalizedWidgetDesktop(
        NoteInput(currentNote: '', onSaveNote: (_) {}),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.text('Add note'), findsOne);
    expect(find.byType(MainOutlinedButton), findsOne);
    await tester.tap(find.byType(MainOutlinedButton));
    await tester.pumpAndSettle();
    expect(find.text('Add note'), findsNothing);
  });
}
