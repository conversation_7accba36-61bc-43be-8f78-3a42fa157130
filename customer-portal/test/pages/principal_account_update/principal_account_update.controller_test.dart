import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/mockito.dart';
import 'package:pacific_2_customer_portal/controllers/subaccount.controller.dart';
import 'package:pacific_2_customer_portal/pages/principal_account_update/principal_account_update.controller.dart';
import '../../mocks/mocks.mocks.dart';
import '../../setup.config.dart';

void main() {
  setupTestEnv();
  final mockUserService = MockUserService();
  final mockWalletService = MockWalletService();
  final mockCatalogService = MockCatalogService();
  final mockUtilityService = MockUtilityService();
  final mockSubAccountController = SubAccountController(mockUserService,
      mockUtilityService, mockWalletService, mockCatalogService);
  Get.put<SubAccountController>(mockSubAccountController);
  late PrincipalAccountUpdateController controller;

  setUp(() {
    controller = PrincipalAccountUpdateController();
    when(mockUserService.updateSubAccountProfile('', any))
        .thenAnswer((_) async => {});
  });

  tearDown(() {
    reset(mockUserService);
    reset(mockWalletService);
    reset(mockCatalogService);
    reset(mockUtilityService);
  });

  test('GeneralInformationController should update avatar correctly', () async {
    // Mock file name
    const mockFileName = 'testFileName';

    // Set up mock dependencies
    controller.fileName = mockFileName; // Ensure fileName is initialized
    controller.imageBytes = Uint8List(10);
    when(mockUtilityService.getPreSignedUrlUpload(mockFileName)).thenAnswer(
        (_) async =>
            {'filePath': 'mockFilePath', 'presignedUrl': 'mockPresignedUrl'});

    // Call the method
    await controller.updateAvatar();

    // Verify interactions
    verify(mockUtilityService.getPreSignedUrlUpload(mockFileName)).called(1);

    // Assert the results
    expect(controller.presignedUrl, 'mockPresignedUrl');
    expect(controller.filePath, 'mockFilePath');
  });

  test(
      'GeneralInformationController should call update user profile api correctly',
      () async {
    bool isSuccess =
        await controller.callUpdateUserProfileApi('', '', '', '', '');
    expect(isSuccess, true);
  });
}
