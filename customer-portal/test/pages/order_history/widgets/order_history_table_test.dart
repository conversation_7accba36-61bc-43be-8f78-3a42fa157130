import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pacific_2_customer_portal/models/order_history.model.dart';
import 'package:pacific_2_customer_portal/pages/order_history/widgets/order_history_table.dart';

import '../../../mocks/localized_widget.dart';
import '../../../mocks/responses/order_hitory.response.mock.dart';
import '../../../setup.config.dart';

void main() {
  setupTestEnv();
  testWidgets('OrderHistoryTable render correctly', (tester) async {
    FlutterError.onError = ignoreOverflowErrors;

    bool pressedDetails = false;
    final item =
        OrderHistoryItem.fromJson(mockOrderHistoryResponse['content'][0]);

    await tester.pumpWidget(
      renderLocalizedWidgetDesktop(
        OrderHistoryTable(
          data: [item],
          onPressDetails: (_) => pressedDetails = true,
          columnFlexWidths: [1, 1, 1, 1, 1, 1, 1, 1],
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.text(item.id!), findsOne);
    expect(find.byType(IconButton), findsOne);
    await tester.tap(find.byType(IconButton));
    expect(pressedDetails, true);
  });
}
