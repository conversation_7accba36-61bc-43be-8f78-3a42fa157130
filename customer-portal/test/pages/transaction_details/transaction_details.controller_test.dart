import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:pacific_2_customer_portal/pages/transaction_details/transaction_details.controller.dart';

import '../../mocks/mocks.mocks.dart';
import '../../mocks/responses/transaction_history.response.mock.dart';
import '../../setup.config.dart';

void main() {
  setupTestEnv();

  final mockWalletService = MockWalletService();
  late TransactionDetailsController transactionHistoryController;

  setUp(() {
    transactionHistoryController =
        TransactionDetailsController(mockWalletService);
    when(mockWalletService.getTransactionDetails(any))
        .thenAnswer((_) async => mockTransactionHistoryResponse['content'][0]);
  });

  tearDown(() {
    reset(mockWalletService);
  });

  test('Initial values for the TransactionDetailsController', () {
    expect(transactionHistoryController.transactionId, '');
    expect(transactionHistoryController.transactionItem.value, null);
  });

  test(
    'TransactionDetailsController should set transaction id correctly',
    () async {
      transactionHistoryController.setTransactionId('test');
      expect(transactionHistoryController.transactionId, 'test');
    },
  );

  test(
    'TransactionDetailsController should get transaction details correctly',
    () async {
      await transactionHistoryController.getTransactionDetails();
      expect(
        transactionHistoryController.transactionItem.value?.transactionId,
        mockTransactionHistoryResponse['content'][0]['transactionId'],
      );
      verify(mockWalletService.getTransactionDetails(any)).called(1);
    },
  );
}
