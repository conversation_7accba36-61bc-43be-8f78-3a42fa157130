import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pacific_2_customer_portal/models/wallet.model.dart';
import 'package:pacific_2_customer_portal/pages/wallet_management/widgets/deposit_wallet_item.dart';
import 'package:pacific_2_customer_portal/utils/general_utils.dart';

import '../../../mocks/localized_widget.dart';
import '../../../mocks/responses/wallet_list.response.mock.dart';
import '../../../setup.config.dart';

void main() {
  setupTestEnv();
  testWidgets("DepositWalletItem render correctly", (tester) async {
    Wallet wallet = Wallet.fromJson(mockWalletListResponse['content'][0]);
    bool pressedTopup = false;
    bool pressedAction = false;
    await tester.pumpWidget(
      renderLocalizedWidget(
        DepositWalletItem(
          wallet: wallet,
          onPressTopUp: () => pressedTopup = true,
          onPressActions: () => pressedAction = true,
          onPressSpendingLimit: () => {},
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.text('#${wallet.walletId}'), findsOne);
    expect(find.text(formatPrice(wallet.balance!)), findsOne);

    await tester.tap(find.text('Top-up'));
    await tester.tap(find.byType(IconButton));

    expect(pressedTopup, isTrue);
    expect(pressedAction, isTrue);
  });

  testWidgets("DepositWalletItem render correctly desktop", (tester) async {
    Wallet wallet = Wallet.fromJson(mockWalletListResponse['content'][0]);
    bool pressedTopup = false;
    bool pressedSpendingLimit = true;
    await tester.pumpWidget(
      renderLocalizedWidgetDesktop(
        DepositWalletItem(
          wallet: wallet,
          onPressTopUp: () => pressedTopup = true,
          onPressActions: () => {},
          onPressSpendingLimit: () => pressedSpendingLimit = true,
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.text('#${wallet.walletId}'), findsOne);
    expect(find.text(formatPrice(wallet.balance!)), findsOne);

    expect(find.byType(IconButton), findsNothing);
    expect(find.text('Set Spending Limit'), findsOne);

    await tester.tap(find.text('Top-up'));
    await tester.tap(find.text('Set Spending Limit'));

    expect(pressedTopup, isTrue);
    expect(pressedSpendingLimit, isTrue);
  });
}
