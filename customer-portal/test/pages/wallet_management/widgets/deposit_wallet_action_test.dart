import 'package:flutter_test/flutter_test.dart';
import 'package:pacific_2_customer_portal/pages/wallet_management/widgets/deposit_wallet_action.dart';

import '../../../mocks/localized_widget.dart';
import '../../../setup.config.dart';

void main() {
  setupTestEnv();
  testWidgets("DepositWalletAction render correctly", (tester) async {
    bool pressedSpendingLimit = false;
    await tester.pumpWidget(
      renderLocalizedWidget(
        DepositWalletAction(
            onPressSpendingLimit: () => pressedSpendingLimit = true),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.text('Set Spending Limit'), findsOne);
    await tester.tap(find.text('Set Spending Limit'));
    expect(pressedSpendingLimit, isTrue);
  });
}
