import 'package:flutter_test/flutter_test.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_button.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_outlined_button.dart';
import 'package:pacific_2_customer_portal/models/order_summary.model.dart';
import 'package:pacific_2_customer_portal/models/payment_method.model.dart';
import 'package:pacific_2_customer_portal/models/service_charge.model.dart';
import 'package:pacific_2_customer_portal/pages/checkout/widgets/order_price_summary.dart';
import 'package:pacific_2_customer_portal/utils/general_utils.dart';

import '../../../mocks/localized_widget.dart';
import '../../../setup.config.dart';

void main() {
  setupTestEnv();
  final summary = OrderSummary(
    totalItem: 10,
    subTotal: 1000,
    serviceCharges: [
      ServiceCharge(name: 'Test service charge 1')..setAmount(300),
      ServiceCharge(name: 'Test service charge 2')..setAmount(400),
    ],
    surchargeRate: 0.03,
    fixedSurcharge: 50,
    surcharge: 100,
    surchargeName: 'Test surcharge',
    taxName: 'Test tax',
    tax: 200,
    taxRate: 0.08,
    totalAmount: 2000,
    totalPayment: 3000,
  );

  testWidgets("OrderPriceSummary render correctly", (tester) async {
    bool pressedCheckout = false;
    bool pressedBackToCart = false;
    await tester.pumpWidget(
      renderLocalizedWidget(
        OrderPriceSummary(
          summary: summary,
          selectedPaymentMethod: PaymentMethod(),
          onCheckout: () => pressedCheckout = true,
          onBackToCart: () => pressedBackToCart = true,
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.text(summary.totalItem.toString()), findsOne);
    expect(find.text(formatPrice(summary.subTotal)), findsOne);
    expect(find.text(summary.serviceCharges[0].name!), findsOne);
    expect(find.text(formatPrice(summary.serviceCharges[0].amount)), findsOne);
    expect(find.text(summary.serviceCharges[1].name!), findsOne);
    expect(find.text(formatPrice(summary.serviceCharges[1].amount)), findsOne);
    expect(find.text(formatPrice(summary.surcharge)), findsOne);
    expect(find.text(formatPrice(summary.tax)), findsOne);
    expect(find.text(formatPrice(summary.totalAmount)), findsOne);
    expect(find.text(formatPrice(summary.totalPayment)), findsOne);
    expect(find.text(summary.taxName), findsOne);

    await tester.tap(find.byType(MainButton));
    await tester.tap(find.byType(MainOutlinedButton));
    await tester.pumpAndSettle();

    expect(pressedCheckout, isTrue);
    expect(pressedBackToCart, isTrue);
  });
}
