import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pacific_2_customer_portal/common/responsive/responsive.dart';

void main() {
  Widget renderWidget(Widget child) {
    return Directionality(
      textDirection: TextDirection.ltr,
      child: MaterialApp(home: Material(child: child)),
    );
  }

  Widget responsive = Responsive(
    mobile: Container(
      color: Colors.transparent,
      child: const Text('Mobile View'),
    ),
    desktop: Container(
      color: Colors.transparent,
      child: const Text('Desktop View'),
    ),
  );

  group('Responsive widget should render correctly with screen size', () {
    testWidgets('Responsive widget should render correctly with mobile size',
        (tester) async {
      await tester.binding.setSurfaceSize(const Size(375, 812));
      await tester.pumpWidget(
        renderWidget(responsive),
      );

      // Verify that Mobile widget is displayed initially.
      expect(find.text('Mobile View'), findsOne);
      expect(find.text('Desktop View'), findsNothing);
    });

    testWidgets('Responsive widget should render correctly with desktop size',
        (tester) async {
      await tester.binding.setSurfaceSize(const Size(1440, 1024));
      await tester.pumpWidget(
        renderWidget(responsive),
      );

      // Verify that Desktop widget is displayed initially.
      expect(find.text('Mobile View'), findsNothing);
      expect(find.text('Desktop View'), findsOne);
    });
  });

  group(
      'Responsive widget should return value correctly when checking screen size',
      () {
    Widget makeTestableWidget({required Widget child, required Size size}) {
      return MaterialApp(
        home: MediaQuery(
          data: MediaQueryData(size: size),
          child: child,
        ),
      );
    }

    testWidgets("Function isMobile should return true when width < 1024",
        (tester) async {
      final testableWidget = makeTestableWidget(
        child: Container(),
        size: const Size(375, 821),
      );

      await tester.pumpWidget(renderWidget(testableWidget));
      final BuildContext context = tester.element(find.byType(Container));

      expect(Responsive.isMobile(context), true);
    });

    testWidgets('Function isMobile should return false when width >= 1024',
        (WidgetTester tester) async {
      final testableWidget = makeTestableWidget(
        child: Container(),
        size: const Size(1440, 1024),
      );

      await tester.pumpWidget(renderWidget(testableWidget));
      final BuildContext context = tester.element(find.byType(Container));

      expect(Responsive.isMobile(context), false);
    });

    testWidgets('Function isDesktop should return true when width >= 1024',
        (WidgetTester tester) async {
      final testableWidget = makeTestableWidget(
        child: Container(),
        size: const Size(1440, 1024),
      );

      await tester.pumpWidget(renderWidget(testableWidget));
      final BuildContext context = tester.element(find.byType(Container));

      expect(Responsive.isDesktop(context), true);
    });

    testWidgets('Function isDesktop should return false when width < 1024',
        (WidgetTester tester) async {
      final testableWidget = makeTestableWidget(
        child: Container(),
        size: const Size(768, 1024),
      );

      await tester.pumpWidget(renderWidget(testableWidget));
      final BuildContext context = tester.element(find.byType(Container));

      expect(Responsive.isDesktop(context), false);
    });
  });
}
