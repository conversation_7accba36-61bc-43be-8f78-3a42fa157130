import 'package:flutter_svg/svg.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pacific_2_customer_portal/models/cart.model.dart';
import 'package:pacific_2_customer_portal/common/widgets/day_list/day_list.dart';
import 'package:pacific_2_customer_portal/utils/general_utils.dart';

import '../../../mocks/localized_widget.dart';
import '../../../setup.config.dart';

void main() {
  setupTestEnv();
  final dayList = [
    CartDate(date: DateTime.now()),
    CartDate(date: DateTime.now().add(const Duration(days: 1))),
    CartDate(date: DateTime.now().add(const Duration(days: 2))),
  ];

  testWidgets("DayList render correctly", (tester) async {
    CartDate cDate = dayList[0];

    await tester.pumpWidget(
      renderLocalizedWidget(
        DayList(
          dayList: dayList,
          selectedDate: dayList[0],
          onSelectDate: (date) {
            cDate = date;
          },
          isCartDateUnavailable: (_) => false,
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.text(formatDate(dayList[0].date)), findsOne);
    expect(find.text(formatDate(dayList[1].date)), findsOne);
    expect(find.text(formatDate(dayList[2].date)), findsOne);

    await tester.tap(find.text(formatDate(dayList[1].date)));
    await tester.pumpAndSettle();

    expect(cDate, dayList[1]);
  });

  testWidgets("DayList render correctly 2", (tester) async {
    await tester.pumpWidget(
      renderLocalizedWidget(
        DayList(
          dayList: dayList,
          selectedDate: dayList[0],
          onSelectDate: (date) {},
          isCartDateUnavailable: (_) => true,
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.byType(SvgPicture), findsAny);
  });
}
