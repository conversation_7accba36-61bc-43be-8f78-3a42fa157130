import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:pacific_2_customer_portal/common/widgets/navigation/navigation_menu.dart';
import 'package:pacific_2_customer_portal/controllers/app.controller.dart';

import '../../../mocks/localized_widget.dart';
import '../../../mocks/network_image.mock.dart';
import '../../../setup.config.dart';

void main() {
  setupTestEnv();

  final appController = Get.find<AppController>();
  appController.appVersion.value = '1.0.0';

  testWidgets("NavigationMenu render correctly", (tester) async {
    FlutterError.onError = ignoreOverflowErrors;

    tester.view.physicalSize = const Size(1000, 2000);
    tester.view.devicePixelRatio = 1.0;

    mockNetworkImagesFor(() async {
      int pressed = 0;
      await tester.pumpWidget(
        renderLocalizedWidget(
          Stack(
            children: [
              NavigationMenu(
                onClose: () => pressed++,
              ),
            ],
          ),
        ),
      );
      await tester.pumpAndSettle();

      expect(find.text('Dashboard'), findsOne);
      expect(find.text('My Account'), findsOne);
      expect(find.text('Principal Account'), findsOne);
      expect(find.text('Wallet'), findsOne);
      expect(find.text('Logout'), findsOne);
      expect(find.text('Version 1.0.0'), findsOne);
      await tester.tap(find.text('Close'));
      await tester.tap(find.text('Dashboard'));
      await tester.tap(find.text('My Account'));
      await tester.tap(find.text('Top-up'));
      expect(pressed, 4);
    });
  });

  testWidgets("NavigationMenu render correctly on desktop", (tester) async {
    FlutterError.onError = ignoreOverflowErrors;

    await tester.binding.setSurfaceSize(const Size(2000, 1000));

    mockNetworkImagesFor(() async {
      int pressed = 0;
      await tester.pumpWidget(
        renderLocalizedWidget(
          Stack(
            children: [
              NavigationMenu(
                onClose: () => pressed++,
              ),
            ],
          ),
          size: const Size(2000, 1000),
        ),
      );
      await tester.pumpAndSettle();

      expect(find.text('Contact Us'), findsOne);
      expect(find.text('My Account'), findsOne);
      expect(find.text('Logout'), findsOne);
      await tester.tap(find.byIcon(Icons.close_rounded));
      await tester.tap(find.text('My Account'));
      expect(pressed, 2);
    });
  });
}
