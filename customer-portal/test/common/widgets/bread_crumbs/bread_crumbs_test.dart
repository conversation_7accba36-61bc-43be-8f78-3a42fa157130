import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pacific_2_customer_portal/common/widgets/bread_crumbs/bread_crumbs.dart';

void main() {
  Widget renderWidget(Widget child) {
    return Directionality(
      textDirection: TextDirection.ltr,
      child: MaterialApp(home: Material(child: child)),
    );
  }

  testWidgets("BreadCrumbs render correctly", (tester) async {
    await tester.pumpWidget(
      renderWidget(
        const BreadCrumbs(path: ['Test 1', 'Test 2']),
      ),
    );

    expect(find.text('Test 1'), findsOne);
    expect(find.text('Test 2'), findsOne);
  });
}
