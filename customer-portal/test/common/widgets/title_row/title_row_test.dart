import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pacific_2_customer_portal/common/widgets/title_row/title_row.dart';

void main() {
  Widget renderWidget(Widget child) {
    return Directionality(
      textDirection: TextDirection.ltr,
      child: MaterialApp(home: Material(child: child)),
    );
  }

  testWidgets("TitleRow render correctly 1", (tester) async {
    bool buttonPressed = false;
    await tester.pumpWidget(
      renderWidget(
        TitleRow(
          title: "Test",
          onBackPressed: () {
            buttonPressed = true;
          },
        ),
      ),
    );

    expect(find.text("Test"), findsOne);
    expect(find.byType(TextButton), findsOne);
    await tester.tap(find.byType(TextButton));
    expect(buttonPressed, isTrue);
  });

  testWidgets("TitleRow render correctly 2", (tester) async {
    await tester.pumpWidget(
      renderWidget(
        const TitleRow(title: "Test"),
      ),
    );

    expect(find.text("Test"), findsOne);
    expect(find.byType(TextButton), findsNothing);
  });

  testWidgets("TitleRow render correctly desktop", (tester) async {
    await tester.pumpWidget(
      MediaQuery(
        data: const MediaQueryData(size: Size(2000, 1000)),
        child: renderWidget(
          const TitleRow(title: "Test"),
        ),
      ),
    );

    expect(find.text("Test"), findsOne);
    expect(find.byType(TextButton), findsNothing);
  });
}
