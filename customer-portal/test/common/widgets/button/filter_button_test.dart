import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/filter_button.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/shadow_button.dart';

void main() {
  Widget renderWidget(Widget child) {
    return Directionality(
      textDirection: TextDirection.ltr,
      child: MaterialApp(home: Material(child: child)),
    );
  }

  testWidgets("FilterButton render correctly", (tester) async {
    bool buttonPressed = false;
    await tester.pumpWidget(
      renderWidget(
        FilterButton(onPressed: () => {buttonPressed = true}),
      ),
    );

    expect(find.byType(SvgPicture), findsOne);
    await tester.tap(find.byType(ShadowButton));
    expect(buttonPressed, isTrue);
  });
}
