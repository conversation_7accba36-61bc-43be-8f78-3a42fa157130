import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pacific_2_customer_portal/models/payment_method.model.dart';
import 'package:pacific_2_customer_portal/common/widgets/select_payment_method/select_payment_method.dart';

import '../../../mocks/localized_widget.dart';
import '../../../mocks/responses/payment_methods.response.mock.dart';

void main() {
  final List<dynamic> methodList = mockPaymentMethodsResponse['content'];
  final List<PaymentMethod> methods =
      methodList.map((method) => PaymentMethod.fromJson(method)).toList();

  testWidgets("SelectPaymentMethod render correctly", (tester) async {
    PaymentMethod selectedMethod = methods[0];

    await tester.pumpWidget(
      renderLocalizedWidget(
        SelectPaymentMethod(
          paymentMethods: methods,
          selectedPaymentMethod: selectedMethod,
          onSelectPaymentMethod: (method) {
            selectedMethod = method;
          },
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.text(methods[0].displayName!), findsAny);
    expect(find.text(methods[0].paymentInstruction!), findsAny);
    expect(find.text(methods[1].displayName!), findsAny);
    expect(find.text(methods[1].paymentInstruction!), findsAny);
    expect(find.byType(Image), findsOne);

    await tester.tap(find.text(methods[1].displayName!));
    await tester.pumpAndSettle();

    expect(selectedMethod.id, methods[1].id);
  });
}
