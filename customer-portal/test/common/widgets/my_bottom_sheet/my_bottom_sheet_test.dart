import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pacific_2_customer_portal/common/widgets/my_bottom_sheet/my_bottom_sheet.dart';

void main() {
  testWidgets('show MyBottomSheet correctly', (tester) async {
    await tester.pumpWidget(MaterialApp(home: Material(child: Container())));
    final BuildContext context = tester.element(find.byType(Container));

    showMyBottomSheet(context, () => const Text('test'));
    await tester.pumpAndSettle();

    expect(find.text('test'), findsOne);
  });

  testWidgets('show dialog correctly desktop', (tester) async {
    await tester.pumpWidget(
      MediaQuery(
        data: const MediaQueryData(size: Size(2000, 1000)),
        child: MaterialApp(home: Material(child: Container())),
      ),
    );
    final BuildContext context = tester.element(find.byType(Container));

    showMyBottomSheet(context, () => const Text('test'));
    await tester.pumpAndSettle();

    expect(find.text('test'), findsOne);
  });
}
