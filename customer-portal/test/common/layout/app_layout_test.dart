import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:pacific_2_customer_portal/common/layout/app_layout.dart';
import 'package:pacific_2_customer_portal/common/widgets/navigation/notification_view.dart';
import 'package:pacific_2_customer_portal/controllers/app.controller.dart';

import '../../mocks/localized_widget.dart';
import '../../mocks/network_image.mock.dart';
import '../../setup.config.dart';

void main() {
  setupTestEnv();

  final appController = Get.find<AppController>();
  appController.appVersion.value = '1.0.0';

  testWidgets("AppLayout render correctly", (tester) async {
    FlutterError.onError = ignoreOverflowErrors;
    mockNetworkImagesFor(() async {
      await tester.pumpWidget(
        renderLocalizedWidget(
          const AppLayout(
            child: Column(children: []),
          ),
        ),
      );
      await tester.pumpAndSettle();

      expect(find.byType(TextButton), findsOne);
      await tester.tap(find.byType(TextButton));
      await tester.pumpAndSettle();
      expect(find.text('Dashboard'), findsOne);
      await tester.tap(find.text('Close'));
      await tester.pumpAndSettle();
      expect(find.text('Dashboard'), findsNothing);

      await tester.tap(find.byType(NotificationView));
      await tester.pumpAndSettle();
      expect(find.text('Notifications'), findsOne);
      await tester.tap(find.byIcon(Icons.close_rounded));
      await tester.pumpAndSettle();
      expect(find.text('Notifications'), findsNothing);
    });
  });

  testWidgets("AppLayout render correctly on desktop", (tester) async {
    FlutterError.onError = ignoreOverflowErrors;

    tester.view.physicalSize = const Size(1920, 1080);
    tester.view.devicePixelRatio = 1.0;

    mockNetworkImagesFor(() async {
      await tester.pumpWidget(
        renderLocalizedWidgetDesktop(
          const AppLayout(
            child: Column(children: []),
          ),
        ),
      );
      await tester.pumpAndSettle();

      expect(find.text('Version 1.0.0'), findsOne);
      await tester.tap(find.text('Account'));
      await tester.pumpAndSettle();
      expect(find.text('My Account'), findsOne);
      await tester.tap(find.byIcon(Icons.close_rounded));
      await tester.pumpAndSettle();
      expect(find.text('My Account'), findsNothing);

      await tester.tap(find.byType(NotificationView));
      await tester.pumpAndSettle();
      expect(find.text('Notifications'), findsOne);
    });
  });
}
