import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/mockito.dart';
import 'package:pacific_2_customer_portal/controllers/app.controller.dart';
import 'package:pacific_2_customer_portal/controllers/subaccount.controller.dart';
import 'package:pacific_2_customer_portal/models/sort.model.dart';
import 'package:pacific_2_customer_portal/models/sub_account.model.dart';
import 'package:pacific_2_customer_portal/models/user_profile.model.dart';
import 'package:pacific_2_customer_portal/models/wallet.model.dart';

import '../mocks/mocks.mocks.dart';
import '../mocks/responses/allergens_list_response.mock.dart';
import '../mocks/responses/group.response.mock.dart';
import '../mocks/responses/sub_account.response.mock.dart';
import '../mocks/responses/sub_account_list.response.mock.dart';
import '../mocks/responses/wallet_list.response.mock.dart';
import '../setup.config.dart';

void main() {
  setupTestEnv();
  final appController = Get.find<AppController>();
  appController.userProfile.value = UserProfile(id: '1');
  final mockUserService = MockUserService();
  final mockWalletService = MockWalletService();
  final mockCatalogService = MockCatalogService();
  final mockUtilityService = MockUtilityService();
  late SubAccountController controller;

  setUp(() {
    controller = SubAccountController(mockUserService, mockUtilityService, mockWalletService, mockCatalogService);
    when(mockUserService.getSubAccountList(any, any)).thenAnswer((_) async => mockSubAccountListResponse);
    when(mockUserService.deleteSubAccount(any)).thenAnswer((_) async => {});
    when(mockUserService.getGroup()).thenAnswer((_) async => mockGroupResponse);
    when(mockUserService.getBannedItems(any)).thenAnswer((_) async => [{}]);
    when(mockWalletService.getWalletList(any)).thenAnswer((_) async => mockWalletListResponse);
    when(mockUserService.addSubAccount(any)).thenAnswer((_) async => mockSubAccountResponse);
    when(mockUserService.linkSubAccount(any)).thenAnswer((_) async => mockSubAccountResponse);
    when(mockUtilityService.getPreSignedUrlUpload('')).thenAnswer((_) async => {'file': ''});
    when(mockUserService.updateSubAccountProfile('', any)).thenAnswer((_) async => {});
    when(mockCatalogService.getAllergens()).thenAnswer((_) async => mockAllergenCatalogListResponse);
    when(mockWalletService.getWalletFind(any)).thenAnswer((_) async => mockWalletListResponse['content'][0]);
  });

  tearDown(() {
    reset(mockUserService);
    reset(mockWalletService);
    reset(mockCatalogService);
    reset(mockUtilityService);
  });

  test('Initial values for the SubAccountController', () {
    expect(controller.subAccountList.toList(), []);
  });

  test('SubAccountController should get sub account list correctly', () async {
    await controller.getAccountList();
    expect(
      controller.subAccountList.length,
      mockSubAccountListResponse['content'].length,
    );
    expect(
      controller.subAccountList.first.id,
      mockSubAccountListResponse['content'][0]['id'],
    );
    verify(mockUserService.getSubAccountList(any, any)).called(1);
  });

  test('SubAccountController should get group list correctly', () async {
    await controller.getGroup();
    expect(
      controller.groupList.length,
      mockGroupResponse.length,
    );
    verify(mockUserService.getGroup()).called(1);
  });

  test('should set refresh state correctly', () async {
    controller.setRefreshState(true);
    expect(controller.shouldRefreshSubAccountList.value, true);
  });

  test('should clear allergens list correctly', () async {
    controller.clearAllergenList();
    expect(controller.allergenUserSelectedList.toList(), []);
  });

  test('SubAccountController should delete sub account correctly', () async {
    bool isSuccessful = await controller.deleteSubAccount('');
    expect(isSuccessful, false);
    verify(mockUserService.deleteSubAccount(any)).called(1);
  });

  test('SubAccountController should add sub account correctly', () async {
    SubAccount? newSubAccount = await controller.addSubAccount('');
    expect(newSubAccount?.id, mockSubAccountResponse['id']);
    verify(mockUserService.addSubAccount(any)).called(1);
  });

  test('SubAccountController should link sub account correctly', () async {
    SubAccount? newSubAccount = await controller.linkSubAccount('');
    expect(newSubAccount?.id, mockSubAccountResponse['id']);
    verify(mockUserService.linkSubAccount(any)).called(1);
  });

  test('SubAccountController should get pre url upload correctly', () async {
    Map<String, String>? response = await controller.getPreSignedUrlUpload('');
    expect(response?['file'], '');
    verify(mockUtilityService.getPreSignedUrlUpload(any)).called(1);
  });

  test('SubAccountController should update sub account profile correctly', () async {
    bool? response = await controller.updateSubAccountProfile('', '');
    expect(response, true);
    verify(mockUserService.updateSubAccountProfile('', any)).called(1);
  });

  test('SubAccountController should get allergen list correctly', () async {
    // Mock the response
    when(mockUserService.getAllergens(any)).thenAnswer((_) async => mockAllergenListResponse);

    // Call the method under test
    await controller.getAllergensList('testId');

    // Verify the updateAllergens method was called with correct arguments
    verify(mockUserService.getAllergens('testId')).called(1);
    // Validate the state update
    expect(controller.allergenUserSelectedList.length, mockAllergenListResponse.length);
    expect(controller.allergenUserSelectedList.first.id, mockAllergenListResponse[0]['id']);
  });

  test('SubAccountController should update allergen list correctly', () async {
    // Mock the response
    when(mockUserService.updateAllergens(any, any)).thenAnswer((_) async => mockAllergenListResponse);

    // Call the method under test
    final result = await controller.updateAllergens('testId', mockAllergenListResponse);

    // Verify the updateAllergens method was called with correct arguments
    verify(mockUserService.updateAllergens('testId', {"items": mockAllergenListResponse})).called(1);

    // Check the result of the method
    expect(result, isTrue);

    // Validate the state update
    expect(controller.allergenUserSelectedList.length, mockAllergenListResponse.length);
    expect(controller.allergenUserSelectedList.first.id, mockAllergenListResponse[0]['id']);
  });

  test('SubAccountController should get allergens catalog correctly', () async {
    await controller.getAllergensCatalog();
    expect(
      controller.allergenList.length,
      mockAllergenCatalogListResponse['content'].length,
    );
    expect(
      controller.allergenList.first.allergenId,
      mockAllergenCatalogListResponse['content'][0]['id'],
    );
    verify(mockCatalogService.getAllergens()).called(1);
  });

  test('SubAccountController should get wallet info correctly', () async {
    Wallet? wallet = await controller.getWalletInfo('');
    expect(
      wallet?.walletId,
      mockWalletListResponse['content'][0]['walletId'],
    );
    verify(mockWalletService.getWalletFind(any)).called(1);
  });

  test('SubAccountController should set sort correctly', () async {
    controller.onSort('');
    expect(controller.currentKey.value, '');
    expect(controller.sortOrder.value, SortOrder.ASC);
    controller.onSort('');
    expect(controller.sortOrder.value, SortOrder.DESC);
  });

  test('SubAccountController should get banned items correctly', () async {
    await controller.getBannedItems('');
    expect(controller.bannedProducts.length, 1);
    verify(mockUserService.getBannedItems(any)).called(1);

    controller.clearBannedItems();
    expect(controller.bannedProducts.length, 0);
  });
}
