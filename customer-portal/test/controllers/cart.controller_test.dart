import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:pacific_2_customer_portal/controllers/cart.controller.dart';
import 'package:pacific_2_customer_portal/models/cart.model.dart';
import 'package:pacific_2_customer_portal/models/preorder_menu_item.model.dart';
import 'package:pacific_2_customer_portal/models/user_profile.model.dart';

import '../mocks/mocks.mocks.dart';
import '../mocks/responses/catalog_product.response.mock.dart';
import '../mocks/responses/menu_item_list.response.mock.dart';
import '../mocks/responses/sub_account_list.response.mock.dart';
import '../mocks/storage/storage.service.mock.mocks.dart'
    if (dart.library.io) '../mocks/storage/storage.service.mobile.mock.mocks.dart';
import '../setup.config.dart';

void main() {
  setupTestEnv();

  final mockStorageService = MockStorageService();
  final mockOrderService = MockOrderService();
  final mockCatalogService = MockCatalogService();
  late CartController cartController;

  final item =
      PreOrderMenuItem.fromJson(mockMenuItemListResponse['content'][0]);
  final profile =
      UserProfile.fromJson(mockSubAccountListResponse['content'][0]['subUser']);
  final now = DateTime.now();
  final groupCriteria = CartGroupCriteria(cartDate: CartDate(date: now));
  final cartItem = CartItem(item: item, groupCriteria: groupCriteria);

  setUp(() {
    cartController = CartController(
        mockStorageService, mockOrderService, mockCatalogService);
    when(mockStorageService.getCart()).thenAnswer((_) async => {});
    when(mockOrderService.getPreOrderMenuItems(any, any))
        .thenAnswer((_) async => {'content': []});
    when(mockCatalogService.getCatalogProduct(any))
        .thenAnswer((_) async => mockCatalogProductResponse);
  });

  tearDown(() {
    reset(mockStorageService);
    reset(mockOrderService);
  });

  test('CartController initial value', () async {
    expect(cartController.cart.value, {});
    expect(cartController.checkedAccounts, []);
    expect(cartController.selectedAccount.value, null);
  });

  test('CartController get cart correctly', () async {
    reset(mockStorageService);
    final cAccount = CartAccount(profile: profile);
    when(mockStorageService.getCart()).thenAnswer(
      (_) async => {
        cAccount: [
          cartItem,
          cartItem,
          CartItem(
              groupCriteria: CartGroupCriteria(
                  cartDate:
                      CartDate(date: now.subtract(const Duration(days: 1)))),
              item: item),
        ]
      },
    );
    await cartController.getCart();
    expect(cartController.cart.value[cAccount]?.length, 2);
    verify(mockStorageService.getCart()).called(1);
  });

  test('CartController get refresh requests correctly', () async {
    cartController.cart.value = {
      CartAccount(profile: UserProfile()): [
        CartItem(
            item: PreOrderMenuItem(id: 'test1', preOrderMenuId: 'test_menu'),
            groupCriteria: groupCriteria),
        CartItem(
            item: PreOrderMenuItem(id: 'test2', preOrderMenuId: 'test_menu'),
            groupCriteria: groupCriteria)
      ]
    };
    final result = cartController.getRefreshCartRequests();
    expect(result['test_menu'], isNotNull);
    expect(result['test_menu']!.contains('test1'), true);
    expect(result['test_menu']!.contains('test2'), true);
  });

  test('CartController get update cart correctly', () async {
    cartController.cart.value = {
      CartAccount(profile: profile): [
        CartItem(
            item: PreOrderMenuItem(id: 'test1'), groupCriteria: groupCriteria),
      ]
    };
    cartController
        .updateCart({'test1': PreOrderMenuItem(product: Product(id: 'test'))});
    expect(
        cartController.cart.value.values.first.first.item.product?.id, 'test');
  });

  test('CartController refresh cart correctly', () async {
    cartController.cart.value = {
      CartAccount(profile: UserProfile()): [
        CartItem(
            item: PreOrderMenuItem(id: 'test1', preOrderMenuId: 'test_menu_1'),
            groupCriteria: groupCriteria),
        CartItem(
            item: PreOrderMenuItem(id: 'test2', preOrderMenuId: 'test_menu_2'),
            groupCriteria: groupCriteria)
      ]
    };
    await cartController.refreshCart();
    verify(mockOrderService.getPreOrderMenuItems(any, any)).called(2);
  });

  test('CartController clear cart correctly', () async {
    cartController.clearCart();
    expect(cartController.cart.value, {});
  });

  test('CartController add and remove item correctly', () async {
    cartController.addToCart(cartItem, profile);
    expect(cartController.cart.value.length, 1);
    cartController.removeFromCart(item, profile);
    expect(cartController.cart.value.isEmpty, isTrue);
  });

  test('CartController add and clear item correctly', () async {
    UserProfile profile2 = UserProfile.fromJson(
        mockSubAccountListResponse['content'][0]['subUser']);
    profile2.id = "id2";
    final cartItem2 = CartItem(
      groupCriteria: CartGroupCriteria(
          cartDate: CartDate(date: now.add(const Duration(days: 1)))),
      item: item,
    );

    cartController.addToCart(cartItem, profile);
    cartController.addToCart(cartItem, profile);
    cartController.addToCart(cartItem2, profile);
    cartController.addToCart(cartItem, profile2);

    expect(cartController.cart.value.length, 2);
    expect(cartController.cart.value[CartAccount(profile: profile)], isNotNull);
    expect(cartController.isInCart(item, now, profile), isTrue);
    expect(CartDate(date: now).toString(), isIn(cartController.getDayList()));
    expect(cartController.getNumOfDay(), 2);
    expect(
        cartController.getTotalItemCount(), item.product!.inventory!.step! * 4);
    expect(cartController.getSubTotal(),
        item.product!.unitPrice! * cartController.getTotalItemCount());

    cartController.clearCart();
    expect(cartController.cart.value.isEmpty, isTrue);
  });

  test('CartController check and uncheck account correctly', () async {
    UserProfile profile2 = UserProfile.fromJson(
        mockSubAccountListResponse['content'][0]['subUser']);
    profile2.id = "id2";

    CartAccount cAccount = CartAccount(profile: profile);

    cartController.addToCart(cartItem, profile);
    expect(
      cartController.isAccountChecked(CartAccount(profile: profile)),
      isNull,
    );
    cartController.addToCart(cartItem, profile2);
    expect(
      cartController.isAccountChecked(cAccount),
      isFalse,
    );
    cartController.checkAccount(cAccount, true);
    expect(
      cartController.isAccountChecked(cAccount),
      isTrue,
    );
    cartController.checkAccount(cAccount, false);
    expect(
      cartController.isAccountChecked(cAccount),
      isFalse,
    );
    cartController.clearCheckedAccount();
    expect(cartController.checkedAccounts, []);
  });

  test('CartController remove checked account correctly', () async {
    CartAccount cAccount = CartAccount(profile: profile);
    cartController.addToCart(cartItem, profile);
    expect(cartController.cart.value.length, 1);
    cartController.checkAccount(cAccount, true);
    cartController.removeCheckedAccount();
    expect(cartController.cart.value.length, 0);
    expect(cartController.checkedAccounts, []);
  });

  test('CartController handle unavailable items correctly', () async {
    final item2 =
        PreOrderMenuItem.fromJson(mockMenuItemListResponse['content'][1]);
    DateTime tomorrow = now.add(const Duration(days: 1));
    cartController.addToCart(cartItem, profile);
    cartController.addToCart(
      CartItem(
        item: item2,
        groupCriteria: CartGroupCriteria(
            cartDate: CartDate(date: now.add(const Duration(days: 1)))),
      ),
      profile,
    );
    cartController.setUnavailableItems([item.id!]);
    expect(
      cartController.isCartDateUnavailable(
          CartAccount(profile: profile), CartDate(date: now)),
      isTrue,
    );
    expect(
      cartController.isCartDateUnavailable(
          CartAccount(profile: profile), CartDate(date: tomorrow)),
      false,
    );
    expect(
      cartController.isCartAccountUnavailable(CartAccount(profile: profile)),
      true,
    );
  });

  test('CartController update quantity correctly', () async {
    CartItem cItem = CartItem(item: item, groupCriteria: groupCriteria);
    expect(cItem.quantity, 1);
    cartController.updateQuantity(cItem, true);
    expect(cItem.quantity, 2);
    cartController.updateQuantity(cItem, false);
    expect(cItem.quantity, 1);
  });

  test('CartController update options correctly', () async {
    CartItem cItem = CartItem(item: item, groupCriteria: groupCriteria);
    CartItem newCItem =
        CartItem(item: item, groupCriteria: groupCriteria, quantity: 2);
    newCItem.addNotes('test');

    expect(cItem.quantity, 1);
    expect(cItem.notes, '');
    cartController.updateOptions(cItem, newCItem);
    expect(cItem.quantity, 2);
    expect(cItem.notes, 'test');
  });

  // single checkout
  test('CartController select & delete account correctly', () async {
    cartController.addToCart(cartItem, profile);
    cartController.onSelectAccount(CartAccount(profile: profile));
    expect(
      cartController.selectedAccount.value!.profile.id,
      profile.id,
    );
    cartController.deleteSelectedAccount();
    expect(cartController.cart.value.isEmpty, true);
    expect(cartController.selectedAccount.value, null);
  });

  // catalog
  test('CartController should get catalog product correctly', () async {
    final product = await cartController.getCatalogProduct('');
    expect(product?.id, mockCatalogProductResponse['id']);

    verify(mockCatalogService.getCatalogProduct(any)).called(1);
  });
}
