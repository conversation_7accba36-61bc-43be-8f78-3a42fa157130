final Map<String, dynamic> mockUserProfileResponse = {
  "id": "test",
  "ssoId": "string",
  "externalId": "string",
  "firstName": "Test",
  "lastName": "User",
  "email": "<EMAIL>",
  "userType": "CUSTOMER",
  "realmId": "customer-portal",
  "avatar": {"path": "string", "url": "string"},
  "userStatus": "CREATED",
  "phoneNumber": "string",
  "totalSubAccounts": 0,
  "totalSponsors": 0,
  "userGroup": {
    "id": "string",
    "tenantId": "string",
    "path": "string",
    "groupName": "string",
    "parent": null,
    "description": "string",
    "createdAt": 0,
    "updatedAt": 0
  },
  "userNonCompletedActions": ["KEYCLOAK_REGISTRATION"],
  "defaultDateFormat": "string",
  "defaultTimeFormat": "string",
  "defaultTimezone": {
    "zoneId": "string",
    "gtmOffset": "string",
    "displayName": "string"
  },
  "permissions": [
    {
      "tenantId": "string",
      "userId": "string",
      "userRoleId": "string",
      "permissionStatus": "CREATED"
    }
  ],
  "createdAt": 0,
  "updatedAt": 0
};
