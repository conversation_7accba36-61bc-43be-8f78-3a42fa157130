final Map<String, dynamic> mockPaymentMethodsResponse = {
  "content": [
    {
      "id": "1",
      "processorId": "STRIPE_WEB_PAYMENT",
      "tenantId": "string",
      "displayName": "string",
      "icon": {"path": "string", "url": "string"},
      "description": "string test test test",
      "isActive": true,
      "paymentInstruction": "string test test test",
      "surchargeRate": "0.03",
      "fixedSurcharge": 700,
      "currency": {
        "displayName": "string",
        "numericCode": 0,
        "currencyCode": "string",
        "symbol": "string",
        "fractionDigits": 0
      },
      "surchargeTitle": "string",
      "processorConfig": {
        "processorId": "STRIPE_WEB_PAYMENT",
        "netsFamilyCardType": "NETS_CARD"
      },
      "acceptedApplications": ["POS"],
      "createdAt": 0,
      "updatedAt": 0,
      "createdBy": "string",
      "updatedBy": "string"
    },
    {
      "id": "2",
      "processorId": "STRIPE_WEB_PAYMENT",
      "tenantId": "string",
      "displayName": "string 2",
      "icon": null,
      "description": "string",
      "isActive": true,
      "paymentInstruction": "string",
      "surchargeRate": "0.03",
      "fixedSurcharge": 0,
      "currency": {
        "displayName": "string",
        "numericCode": 0,
        "currencyCode": "string",
        "symbol": "string",
        "fractionDigits": 0
      },
      "surchargeTitle": "string",
      "processorConfig": {
        "processorId": "STRIPE_WEB_PAYMENT",
        "netsFamilyCardType": "NETS_CARD"
      },
      "acceptedApplications": ["POS"],
      "createdAt": 0,
      "updatedAt": 0,
      "createdBy": "string",
      "updatedBy": "string"
    }
  ],
  "totalElements": 0,
  "totalPages": 0,
  "page": 0,
  "sort": ["string"]
};
