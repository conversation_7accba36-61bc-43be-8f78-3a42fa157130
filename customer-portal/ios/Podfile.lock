PODS:
  - Flutter (1.0.0)
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 5.0)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 5.0)
  - flutter_secure_storage (6.0.0):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - OrderedSet (5.0.0)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - Toast (4.1.1)
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - Flutter (from `Flutter`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - OrderedSet
    - Toast

EXTERNAL SOURCES:
  Flutter:
    :path: Flutter
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_inappwebview_ios: 97215cf7d4677db55df76782dbd2930c5e1c1ea0
  flutter_secure_storage: d33dac7ae2ea08509be337e775f6b59f1ff45f12
  fluttertoast: 9f2f8e81bb5ce18facb9748d7855bf5a756fe3db
  OrderedSet: aaeb196f7fef5a9edf55d89760da9176ad40b93c
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  Toast: 1f5ea13423a1e6674c4abdac5be53587ae481c4e
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe

PODFILE CHECKSUM: 819463e6a0290f5a72f145ba7cde16e8b6ef0796

COCOAPODS: 1.15.2
