{"slogan": "Simplify mealtime, one tap at a time", "dashboard": "Dashboard", "principalAccount": "Principal Account", "wallet": "Wallet", "mealPreorder": "Meal Pre-order", "orderHistory": "Order History", "contactUs": "Contact Us", "myAccount": "My Account", "logout": "Logout", "login": "<PERSON><PERSON>", "topup": "Top-up", "close": "Close", "tokenExpiredMessage": "Your session has expired! Please login again.", "commonErrorMessage": "An unknown error has occurred. Please retry again later. Error code {errorCode}.", "pageNotFound": "Page not found!", "alert": "<PERSON><PERSON>", "error": "Error", "addNewMember": "New Member", "addNew": "Add New", "groupSearchPlaceholder": "Search group, name, ID...", "fullName": "Full Name", "profileStatus": "Profile Status", "noDataAccountDes": "Information not available, please add \" New Member\" to the Principal Account.", "active": "Active", "inactive": "Inactive", "group": "Group:", "externalID": "External ID", "viewPreorder": "View Pre-order", "preorderDetails": "Pre-order Details", "noOrder": "No Order", "orderEmpty": "No orders found for this day. Please create a new order or choose a different date.", "rowPerPage": "Rows per page:", "deleteAccount": "Delete Account", "deleteAccountConfirm": "Are you sure you want to delete this account?", "confirm": "Confirm", "userType": "User Type", "userTypeNew": "Totally New Member", "userTypeNewDes": "Create a new user account to begin management.", "userTypeLink": "<PERSON> To An Existing Account", "userTypeLinkDes": "Allow an existing account on the platform to connect and be managed by the main user profile.", "informationNeeded": "Information Needed", "firstName": "First Name", "firstNamePlaceholder": "Input first name", "firstNameError": "Please enter the first name", "lastName": "Last Name", "lastNamePlaceholder": "Input last name", "lastNameError": "Please enter the last name", "emailAddress": "Email Address", "emailAddressPlaceholder": "Input email address", "emailAddressError": "Please enter the email address", "confirmTermPolicy": "I confirm that I have read and agreed to", "termPolicy": "Terms of Use and Policy.", "save": "Save", "update": "Update", "updateInfo": "Update Information", "accountCreated": "Account created successfully", "otherInfo": "Other Information", "allergens": "Allergens", "allergensDetail": "Customers cannot place orders for items that contain matching allergens at site. Additionally, pre-order items with matching allergens cannot be accepted.", "allergensRestrict": "Restrict", "allergensWarn": "<PERSON><PERSON>", "saveUpdate": "Save Update", "skip": "<PERSON><PERSON>", "profileDetail": "Profile Details", "generalInfo": "General Information", "fundedWallet": "FUNDED WALLET", "expiryDate": "Expiry date", "cardList": "Card List", "updateProfile": "Update Profile", "editInfo": "Edit Information", "createPreorder": "Create Pre-order", "accounts": "Accounts", "selectAccount": "Select Account", "mainUser": "Main User", "selectDate": "Select Date", "select": "Select", "today": "Today", "availableDay": "Available Day", "orderCreated": "Order created", "itemAdded": "<PERSON><PERSON> added", "cafeClosed": "Cafe closed", "allItem": "All item", "menuSearchHint": "Search item name...", "noData": "No data", "menuEmpty": "Information not available, please select an Account and a Day to view the menu", "purchased": "Purchased", "purchasedDialogTitle": "Add to cart", "purchasedDialogMessage": "You have already pre-ordered this item for {mealTime}. Are you sure you want to pre-order it again?", "allergenWarningDialogTitle": "Allergen Warning", "allergenWarningDialogMessage": "There are matching Allergens: {allergens}. Do you still want to continue?", "allergenRestrictDialogTitle": "Allergen Restricted", "allergenRestrictDialogMessage": "There are matching restricted Allergens: {allergens}.", "yes": "Yes", "orders": "orders", "details": "Details", "itemNotAvailable": "Item not available", "itemDetails": "<PERSON><PERSON>", "healthierChoice": "Healthier Choice", "menu": "<PERSON><PERSON>", "collection": "Collection", "delivery": "Delivery", "category": "Category", "ingredients": "Ingredients", "nutritionFacts": "Nutrition Facts", "cutOffTime": "Cut Off Time", "cutOffByTime": "By {time}", "cutOffDays": {"day-0": "same day", "day-1": "{day} day prior", "day-2": "{day} days prior"}, "daySelected": "Day selected", "total": "Total", "item": {"count-0": "item", "count-2": "items"}, "soldOut": "Sold out", "cart": "<PERSON><PERSON>", "clearCart": "Clear Cart", "clearCartMessage": "Are you sure you want to clear the cart?", "totalItem": "Total Items", "subTotal": "Sub Total", "continueShopping": "Continue Shopping", "proceedPayment": "Proceed Payment", "addNote": "Add note", "addNoteHint": "Add note here...", "saveNote": "Save Note", "selected": "selected", "deleteSelection": "Delete Selection", "notAvailable": "Not available", "cartEmpty": "Cart empty", "checkboxRequire": "Please check this box if you want to proceed.", "payment": "Payment", "selectPaymentMethod": "Select Payment Method", "noPaymentRequired": "No payment required", "orderSummary": "Order Summary", "serviceCharge": "{rate}% Service Charge:", "totalAmount": "Total Amount:", "totalPayment": "Total Payment", "checkout": "Check Out", "backToCart": "Back To Cart", "view": "View", "itemSelected": "<PERSON><PERSON> Selected", "serviceFee": "Service Fee", "errorWalletBalance": "Insufficient wallet balance", "paymentConfirmation": "Payment Confirmation", "paymentWalletMessage": " will be deducted from your wallet #{walletId}", "accept": "Accept", "paymentSuccessMessage": "Thank you for your order. Your payment and order have been successfully processed.", "paymentSuccessInfo1": "Click here", "paymentSuccessInfo2": " for more information on your pre-orders.", "depositWallet": "<PERSON><PERSON><PERSON><PERSON>", "willExpireOn": " will be expired on {date}", "setSpendingLimit": "Set Spending Limit", "spendingLimitInfo1": "Limit your daily/weekly/monthly spending", "spendingLimitInfo2": "Leave the amount field empty for unlimited spending.", "dailySpendingAmount": "Daily Spending Amount", "weeklySpendingAmount": "Weekly Spending Amount", "monthlySpendingAmount": "Monthly Spending Amount", "spendingLimit": "Spending Limit", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "noLimit": "No limit", "isSpendingLimit": " is the daily spending limit.", "cancel": "Cancel", "walletDelegateTitle": "Allow to deduct to main user's wallet", "walletDelegateMessage": "Sub-accounts will be permitted to use the main user's wallet for payments, subject to their daily spending limit.", "transactionHistory": "Transaction History", "allTransactions": "All Transactions", "filter": "Filter", "account": "Account", "all": "All", "transactionType": "Transaction Type", "transactionCategory": "Transaction Category", "refund": "Refund", "transfer": "Transfer", "withdraw": "Withdraw", "byAmount": "By Amount", "byAmountError": "End value must be greater than or equal to start value", "byDate": "By Date", "byDateError": "End date must be after or the same as start date", "from": "From", "to": "To", "last7": "Last 7 days", "last30": "Last 30 days", "last60": "Last 60 days", "byDateRange": "By date range", "startDate": "Start date", "endDate": "End date", "apply": "Apply", "clearFilter": "Clear Filter", "applyFilter": "Apply Fitler", "incoming": "Incoming", "outgoing": "Outgoing", "activity": "Activity", "paymentSession": "Payment Session", "oldBalance": "Old Balance", "newBalance": "New Balance", "transactionId": "Transaction ID", "activityTopup": "Top-up to deposit wallet", "activityPurchase": "Paid for order #{orderId}", "topupSuccess": "Top-up successfully", "selectTopupAmount": "Select Top-up amount", "otherAmount": "Other Amount", "enterAmount": "Enter Amount", "min": "Min", "max": "Max", "topupSummary": "Top-up Summary", "topupAmount": "Top-up Amount:", "proceedTopup": "Proceed Top-up", "topupAmountError": "Please select or enter a valid top-up amount", "personalInfo": "Personal Information", "card": "Card", "settings": "Settings", "sendFeedback": "Send Feedback", "saveChange": "Save Change", "cutoffErrorMessage": "The pre-order deadline has already passed. Please remove unavailable items from your cart to proceed.", "insufficientErrorMessage": "The available capacity for the pre-order item is insufficient. Please adjust your order by removing unavailable items to continue.", "itemUnavailableMessage": "The pre-order item is currently unavailable. Please adjust your order by removing unavailable items to continue.", "invalidPreorderItemInfoMessage": "The pre-order item's info has been updated. Please review your order again before continuing.", "invalidPreorderMessage": "TThe pre-order's info has been updated. Please review your order again before continuing.", "orderId": "Order #{orderId}", "orderIdText": "Order ID", "preorder": "Pre-order", "instantOrder": "Instant Order", "onsiteOrder": "On-site Order", "collectionDate": "Collection Date", "paymentStatus": "Payment Status", "createdDate": "Created Date", "orderType": "Order Type", "user": "User", "orderStatus": "Order Status", "byOrderDate": "By Order Date", "byCollectionDate": "By Collection Date", "paid": "Paid", "completed": "Completed", "pending": "Pending", "created": "Created", "cancelling": "Cancelling", "cancelled": "Cancelled", "confirmed": "Confirmed", "preparing": "Preparing", "collected": "Collected", "failed": "Failed", "refunding": "Refunding", "refunded": "Refunded", "unimplemented": "-", "sortOrderCreate": "Recent Order", "dateTimeFormat": "Date-Time Format", "date": "Date", "time": "Time", "timezone": "Timezone", "timezoneChangeTitle": "Timezone Change Detected", "timezoneChangeMessage": "A new timezone has been detected. Would you like to update your timezone to {timezone}?", "settingsSaved": "Setting<PERSON> saved successfully", "notificationConfig": "Notifications Config", "notificationConfigDes": "Users choose how they want to receive notifications.", "paymentSuccessfully": "Payment Successfully", "orderStatusUpdate": "Order Status Update", "walletTransactionAlert": "Wallet Transaction Alert", "emailNoti": "Email Notifications", "pushNoti": "Push Notifications", "inAppNoti": "In-app Notifications", "security": "Security", "password": "Password", "changePasswordDes": "Select 'Change Password' to update your password.", "changePassword": "Change Password", "mfa": "MFA", "mfaDes": "(Multi-factor authentication)", "alias": "<PERSON><PERSON>", "selectGroup": "Select group", "profileUpdateSuccess": "Profile updated!", "orderDetails": "Order Details", "paymentMethod": "Payment Method", "walletId": "Wallet ID", "cancelOrder": "Cancel Order", "cancelOrderTitle": "New Update Confirmation", "cancelOrderMessage": "You have already placed this order. Are you sure you want to cancel your purchase?", "note": "Note", "cancelOrderNote": " will be transferred back to your wallet.", "cancelOrderSuccess": "Cancel order successfully", "contactFormTitle": "Title", "contactFormTitleHint": "Enter your title...", "contactFormTitleError": "Please enter a valid title", "contactFormDescription": "Description", "contactFormDescriptionHint": "Enter a description...", "contactFormDescriptionError": "Please enter a valid description", "contactFormSubmitSuccess": "Send feedback success", "email": "Email", "phone": "Phone", "address": "Address", "paymentHistory": "Payment History", "recentPaymentHistory": "Recent Payment History", "transactionStatus": "Transaction Status", "status": "Status", "purchase": "Purchase", "succeeded": "Succeeded", "paidAt": "<PERSON><PERSON>", "amount": "Amount", "paymentHistoryDetails": "Payment History Details", "paymentMethodId": "Payment Method ID", "paymentProcessorId": "Payment Processor ID", "description": "Description", "paymentReference": "Payment Reference", "customerInfo": "Customer Information", "customerId": "Customer ID", "customerEmail": "Customer <PERSON><PERSON>", "customerName": "Customer Name", "billInfo": "Bill Information", "fee": "Fee", "netAmount": "Net Amount", "systemSource": "System Source", "merchantName": "Merchant Name", "appliedSurchargeRate": "Applied Surcharge Rate", "appliedFixedSurcharge": "Applied Fixed Surcharge", "createdAt": "Create At", "termsAndConditions": "Terms & Conditions", "addNewMemberTitle": "Add New Member", "pleaseProvideInfo": "Please provide the following information", "invitationTitle": "Link Account Invitation", "invitationMessage1": "You have received a link account invitation from ", "invitationMessage2": "Accepting this invitation will link your account to theirs, allowing them to manage it.", "decline": "Decline", "awaitLinkAccountAccept": "Awaiting link account acceptance", "bannedItem": "Banned Item", "bannedItemDes": "Users are not allowed to purchase banned items.", "bannedItemMore": {"count-0": "more item selected", "count-2": "more items selected"}, "bannedItemAdd": "Add Banned Items", "bannedItemSearchHint": "Search item...", "result": "Result", "numberSelected": "{number} selected", "noResult": "No result found", "add": "Add", "items": "Items", "clearAll": "Clear All", "options": "Options", "amountOptional": "(Optional: Select up to {amount})", "amountRequired": "(Required: Choose {amount})", "addToCart": "Add to Cart", "notifications": "Notifications", "markAllAsRead": "Mark all as read ({count})", "markAsRead": "<PERSON> as read", "deleteNotification": "Delete notification", "noNotification": "No notification found", "notiWelcomeUser": "Welcome", "notiAddSubAccount": "Add Sub Account", "notiBalanceChange": "Balance Change", "notiPaymentSuccess": "Payment Success", "notiSubAccountIvitation": "Sub Account Invitation", "notiUpdateOrderStatus": "Order Status Update", "notiOther": "Notification"}