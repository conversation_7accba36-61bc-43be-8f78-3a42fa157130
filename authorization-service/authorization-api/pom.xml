<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.styl.pacific.authz.service</groupId>
        <artifactId>authorization-service</artifactId>
        <version>1.2.4</version>
    </parent>
    <artifactId>authorization-api</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>common-mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>common-rest</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.authz.service</groupId>
            <artifactId>authorization-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.authz.service</groupId>
            <artifactId>authorization-shared</artifactId>
        </dependency>
    </dependencies>
</project>
