/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.authz.service.core.features.featurenode.loaders;

import com.styl.pacific.authz.service.core.features.featurenode.entities.FeatureNode;
import com.styl.pacific.authz.service.core.features.featurenode.utils.FeatureTreeUtils;
import com.styl.pacific.authz.shared.constants.FeatureTreeConstants;
import com.styl.pacific.authz.shared.exceptions.FeatureNodeInvalidException;
import com.styl.pacific.domain.permissions.FeatureNodeType;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
@NoArgsConstructor
public class EnumFeatureTreeNodeValidator {
	public void validateFeatureNode(List<FeatureNode> featureNodes) {
		((Consumer<List<FeatureNode>>) nodes -> {
			final var invalidNodes = nodes.stream()
					.collect(Collectors.groupingBy(FeatureNode::getId))
					.entrySet()
					.stream()
					.filter(it -> it.getValue()
							.size() > 1)
					.toList();
			if (!invalidNodes.isEmpty()) {
				final var errorMessage = String.format("Duplicated ID: %s ", invalidNodes.stream()
						.map(it -> it.getValue()
								.stream()
								.map(n -> n.getId()
										.getValue() + "-"
										+ n.getExternalId()
												.name())
								.collect(Collectors.joining(", ")))
						.collect(Collectors.joining()));
				log.error(errorMessage);
				throw new FeatureNodeInvalidException(errorMessage);
			}

		}).andThen(nodes -> {
			final var invalidNodes = nodes.stream()
					.collect(Collectors.groupingBy(FeatureNode::getNodePath))
					.entrySet()
					.stream()
					.filter(it -> it.getValue()
							.size() > 1)
					.toList();
			if (!invalidNodes.isEmpty()) {
				final var errorMessage = String.format("Duplicated Node Path: %s ", invalidNodes.stream()
						.map(Map.Entry::getValue)
						.flatMap(List::stream)
						.map(it -> it.getId()
								.getValue() + "-"
								+ it.getNodePath()
								+ "-"
								+ it.getExternalId()
										.name())
						.collect(Collectors.joining(", ")));
				log.error(errorMessage);
				throw new FeatureNodeInvalidException(errorMessage);
			}
		})
				.andThen(nodes -> {
					final var invalidNodes = nodes.stream()
							.filter(it -> StringUtils.isBlank(it.getNodePath()) || !it.getNodePath()
									.startsWith(FeatureTreeConstants.ROOT))
							.toList();
					if (!invalidNodes.isEmpty()) {
						final var errorMessage = String.format(
								"Invalid node path. Path is required and start with root: %s ", invalidNodes.stream()
										.map(it -> it.getNodePath() + "-"
												+ it.getExternalId())
										.collect(Collectors.joining(", ")));
						log.error(errorMessage);
						throw new FeatureNodeInvalidException(errorMessage);
					}
				})
				.andThen(nodes -> {
					final var moduleNodes = nodes.stream()
							.filter(it -> FeatureNodeType.MODULE.equals(it.getNodeType()))
							.toList();

					final var moduleTree = FeatureTreeUtils.buildTree(moduleNodes);
					final var invalidModules = moduleTree.stream()
							.filter(module -> !module.getChildren()
									.isEmpty())
							.flatMap(module -> Stream.concat(Stream.of(module), module.getChildren()
									.stream()))
							.toList();
					if (!invalidModules.isEmpty()) {
						final var errorMessage = "Module Path must be unique and not started with others : %s."
								.formatted(invalidModules.stream()
										.map(it -> "[%s]-%s".formatted(it.getExternalId(), it.getNodePath()))
										.collect(Collectors.joining(", ")));
						log.error(errorMessage);
						throw new FeatureNodeInvalidException(errorMessage);
					}
				})
				.accept(featureNodes);

	}
}
