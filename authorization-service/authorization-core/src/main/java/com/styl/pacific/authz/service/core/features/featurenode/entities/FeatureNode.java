/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.authz.service.core.features.featurenode.entities;

import com.styl.pacific.domain.entity.BaseEntity;
import com.styl.pacific.domain.enums.UserType;
import com.styl.pacific.domain.permissions.FeatureNodeType;
import com.styl.pacific.domain.permissions.PacificApiPermissionKey;
import com.styl.pacific.domain.valueobject.FeatureNodeId;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.With;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

@Getter
@Builder
@With
@RequiredArgsConstructor
public class FeatureNode extends BaseEntity<FeatureNodeId> {
	private final FeatureNodeId id;
	private final PacificApiPermissionKey externalId;
	private final String nodeTitle;
	private final FeatureNodeType nodeType;
	private final String nodePath;
	private final Integer displayOrder;
	private final List<UserType> allowedUserTypes;

	@Builder.Default
	private final List<FeatureNode> children = new ArrayList<>();

	@Override
	public boolean equals(Object o) {
		if (this == o) {
			return true;
		}

		if (!(o instanceof FeatureNode that)) {
			return false;
		}

		return new EqualsBuilder().appendSuper(super.equals(o))
				.append(id, that.id)
				.append(externalId, that.externalId)
				.append(nodeTitle, that.nodeTitle)
				.append(nodeType, that.nodeType)
				.append(nodePath, that.nodePath)
				.append(displayOrder, that.displayOrder)
				.append(children, that.children)
				.append(allowedUserTypes, that.allowedUserTypes)
				.isEquals();
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder(17, 37).appendSuper(super.hashCode())
				.append(id)
				.append(externalId)
				.append(nodeTitle)
				.append(nodeType)
				.append(nodePath)
				.append(displayOrder)
				.append(children)
				.append(allowedUserTypes)
				.toHashCode();
	}

	public FeatureNode cloneNode() {
		return FeatureNode.builder()
				.id(id)
				.externalId(externalId)
				.nodeTitle(nodeTitle)
				.nodeType(nodeType)
				.nodePath(nodePath)
				.displayOrder(displayOrder)
				.children(Optional.ofNullable(children)
						.map(it -> new ArrayList<>(children))
						.orElse(new ArrayList<>()))
				.allowedUserTypes(Optional.ofNullable(allowedUserTypes)
						.map(it -> new ArrayList<>(allowedUserTypes))
						.orElse(new ArrayList<>()))
				.build();
	}
}
