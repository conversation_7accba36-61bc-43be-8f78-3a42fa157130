/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.authz.service.data.access.relational.userroles.repository;

import com.querydsl.core.types.Predicate;
import com.styl.pacific.authz.service.data.access.relational.userroles.entities.QUserRoleEntity;
import com.styl.pacific.authz.service.data.access.relational.userroles.entities.UserRoleEntity;
import com.styl.pacific.data.access.jpa.querydsl.BaseQuerydslRepository;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

public interface JpaUserRoleRepository extends BaseQuerydslRepository<UserRoleEntity, Long> {

	@EntityGraph(attributePaths = { "permissions" })
	List<UserRoleEntity> getAllByIdIn(Collection<Long> roleIds);

	@EntityGraph(attributePaths = { "permissions" })
	List<UserRoleEntity> getAllByTenantIdAndExternalIdIn(Long tenantId, Collection<String> roleExternalIds);

	@EntityGraph(attributePaths = { "permissions" })
	Optional<UserRoleEntity> getOneByIdAndDeletedAtIsNull(Long userRoleId);

	@EntityGraph(attributePaths = { "permissions" })
	Optional<UserRoleEntity> getOneByTenantIdAndExternalIdAndDeletedAtIsNull(Long tenantId, String externalId);

	@EntityGraph(attributePaths = { "permissions" })
	Optional<UserRoleEntity> getOneByIdAndTenantIdAndDeletedAtIsNull(Long id, Long tenantId);

	@Override
	default Predicate softDeletionPredicate() {
		return QUserRoleEntity.userRoleEntity.deletedAt.isNull();
	}

	@Transactional
	@Modifying
	@Query("UPDATE UserRoleEntity e SET e.deletedAt = current_timestamp WHERE e.id = ?1")
	void softDeleteUserRoleByUserRoleId(Long id);

}
