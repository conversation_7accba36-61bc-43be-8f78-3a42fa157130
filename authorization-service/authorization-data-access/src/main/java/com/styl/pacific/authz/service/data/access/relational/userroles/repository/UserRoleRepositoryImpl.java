/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.authz.service.data.access.relational.userroles.repository;

import com.styl.pacific.authz.service.core.features.featurenode.entities.FeatureNode;
import com.styl.pacific.authz.service.core.features.userroles.UserRoleRepository;
import com.styl.pacific.authz.service.core.features.userroles.entities.UserRole;
import com.styl.pacific.authz.service.core.features.userroles.idgenerator.UserRoleIdGenerator;
import com.styl.pacific.authz.service.core.features.userroles.request.DeleteSingleUserRoleCommand;
import com.styl.pacific.authz.service.core.features.userroles.request.GetSingleUserRoleQuery;
import com.styl.pacific.authz.service.core.features.userroles.request.UserRoleFilter;
import com.styl.pacific.authz.service.core.features.userroles.request.UserRolePaginationQuery;
import com.styl.pacific.authz.service.data.access.relational.featurenode.repository.InMemoryFeatureNodeRepository;
import com.styl.pacific.authz.service.data.access.relational.permissions.entities.FeaturePermissionEntity;
import com.styl.pacific.authz.service.data.access.relational.userroles.entities.QUserRoleEntity;
import com.styl.pacific.authz.service.data.access.relational.userroles.entities.UserRoleEntity;
import com.styl.pacific.authz.service.data.access.relational.userroles.mapper.UserRoleEntityMapper;
import com.styl.pacific.data.access.jpa.querydsl.WhereBuilder;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.range.InstantDateTimeRange;
import com.styl.pacific.domain.valueobject.BaseId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserRoleId;
import com.styl.pacific.utils.fallback.FallbackFlow;
import jakarta.validation.constraints.NotNull;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@RequiredArgsConstructor
public class UserRoleRepositoryImpl implements UserRoleRepository {

	private final JpaUserRoleRepository jpaUserRoleRepository;
	private final InMemoryFeatureNodeRepository featureNodeRepository;
	private final UserRoleIdGenerator keyGenerator;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public List<UserRole> saveAll(@NotNull TenantId tenantId, List<UserRole> roles) {
		final var roleIds = roles.stream()
				.map(UserRole::getId)
				.filter(Objects::nonNull)
				.map(BaseId::getValue)
				.filter(Objects::nonNull)
				.collect(Collectors.toSet());

		final var roleExternalIds = roles.stream()
				.map(UserRole::getExternalId)
				.collect(Collectors.toSet());

		final var existingUserRoleEntities = Stream.concat(!roleIds.isEmpty()
				? jpaUserRoleRepository.getAllByIdIn(roleIds)
						.stream()
				: Stream.empty(), !roleExternalIds.isEmpty()
						? jpaUserRoleRepository.getAllByTenantIdAndExternalIdIn(tenantId.getValue(), roleExternalIds)
								.stream()
						: Stream.empty())
				.toList();

		final var featureNodeMap = featureNodeRepository.findAllByIds(roles.stream()
				.map(UserRole::getPermissions)
				.flatMap(Collection::stream)
				.map(FeatureNode::getId)
				.collect(Collectors.toSet()))
				.stream()
				.collect(Collectors.toMap(FeatureNode::getId, Function.identity()));

		final Function<UserRole, UserRoleEntity> userRoleEntityGetter = role -> FallbackFlow
				.<UserRole, UserRoleEntity>builder()
				.addFallBack(userRole -> userRole.getId() != null, userRole -> existingUserRoleEntities.stream()
						.filter(it -> Objects.equals(userRole.getId()
								.getValue(), it.getId()))
						.findFirst()
						.orElse(null))
				.addFallBack(userRole -> StringUtils.isNotBlank(userRole.getExternalId()),
						userRole -> existingUserRoleEntities.stream()
								.filter(it -> Objects.equals(userRole.getExternalId(), it.getExternalId()))
								.findFirst()
								.orElse(null))
				.execute(role);

		final var updatedUserRoles = roles.stream()
				.map(role -> Optional.ofNullable(userRoleEntityGetter.apply(role))
						.map(existing -> {
							existing.setRoleTitle(role.getRoleTitle());
							existing.getPermissions()
									.clear();
							existing.getPermissions()
									.addAll(role.getPermissions()
											.stream()
											.map(node -> featureNodeMap.get(node.getId()))
											.map(featureNode -> FeaturePermissionEntity.builder()
													.tenantId(tenantId.getValue())
													.userRoleEntity(existing)
													.featureNodeId(featureNode.getId()
															.getValue())
													.build())
											.toList());
							return existing;
						})
						.orElseGet(() -> {
							final var newUserRoleEntity = UserRoleEntityMapper.INSTANCE.toNewEntity(role);
							newUserRoleEntity.getPermissions()
									.addAll(role.getPermissions()
											.stream()
											.map(node -> featureNodeMap.get(node.getId()))
											.map(featureNode -> FeaturePermissionEntity.builder()
													.tenantId(tenantId.getValue())
													.userRoleEntity(newUserRoleEntity)
													.featureNodeId(featureNode.getId()
															.getValue())
													.build())
											.toList());
							newUserRoleEntity.setId(Optional.ofNullable(newUserRoleEntity.getId())
									.orElseGet(keyGenerator::generateId));
							return newUserRoleEntity;
						}))
				.toList();
		return jpaUserRoleRepository.saveAll(updatedUserRoles)
				.stream()
				.map(it -> UserRoleEntityMapper.INSTANCE.toModel(it, featureNodeRepository))
				.toList();
	}

	@Override
	public Paging<UserRole> findPaginationUserRoles(UserRolePaginationQuery query) {

		final var pageable = PageRequest.of(query.getPage(), query.getSize(), Sort.Direction.valueOf(query
				.getSortDirection()), query.getSortFields()
						.toArray(String[]::new));

		final var condition = query.getFilter();
		final var pageResult = jpaUserRoleRepository.findAllWithBase(WhereBuilder.build()
				.applyIf(Optional.ofNullable(condition)
						.map(UserRoleFilter::getByUserRoleId)
						.map(BaseId::getValue)
						.isPresent(), where -> where.and(QUserRoleEntity.userRoleEntity.id.eq(condition
								.getByUserRoleId()
								.getValue())))
				.applyIf(Optional.ofNullable(condition)
						.map(UserRoleFilter::getByTenantId)
						.map(BaseId::getValue)
						.isPresent(), where -> where.and(QUserRoleEntity.userRoleEntity.tenantId.eq(condition
								.getByTenantId()
								.getValue())))
				.applyIf(Optional.ofNullable(condition)
						.map(UserRoleFilter::getByExternalId)
						.filter(StringUtils::isNoneBlank)
						.isPresent(), where -> where.and(QUserRoleEntity.userRoleEntity.externalId.eq(condition
								.getByExternalId())))
				.applyIf(Optional.ofNullable(condition)
						.map(UserRoleFilter::getExcludeExternalIds)
						.filter(it -> !it.isEmpty())
						.isPresent(), where -> where.and(QUserRoleEntity.userRoleEntity.externalId.isNull()
								.or(QUserRoleEntity.userRoleEntity.externalId.notIn(condition
										.getExcludeExternalIds()))))
				.applyIf(Optional.ofNullable(condition)
						.map(UserRoleFilter::getByRoleTitle)
						.filter(StringUtils::isNotBlank)
						.isPresent(), where -> where.and(QUserRoleEntity.userRoleEntity.roleTitle.containsIgnoreCase(
								condition.getByRoleTitle())))
				.applyIf(Optional.ofNullable(condition)
						.map(UserRoleFilter::getIncludeRoleIds)
						.filter(it -> !it.isEmpty())
						.isPresent(), where -> where.and(QUserRoleEntity.userRoleEntity.id.in(condition
								.getIncludeRoleIds()
								.stream()
								.map(BaseId::getValue)
								.collect(Collectors.toSet()))))
				.applyIf(Optional.ofNullable(condition)
						.map(UserRoleFilter::getByCreatedRange)
						.map(InstantDateTimeRange::from)
						.isPresent(), where -> where.and(QUserRoleEntity.userRoleEntity.createdAt.after(condition
								.getByCreatedRange()
								.from())))
				.applyIf(Optional.ofNullable(condition)
						.map(UserRoleFilter::getByCreatedRange)
						.map(InstantDateTimeRange::to)
						.isPresent(), where -> where.and(QUserRoleEntity.userRoleEntity.createdAt.before(condition
								.getByCreatedRange()
								.to()))), pageable);

		return new Paging<>(pageResult.getContent()
				.stream()
				.map(UserRoleEntityMapper.INSTANCE::toLiteModel)
				.toList(), pageResult.getTotalElements(), pageResult.getTotalPages(), pageResult.getPageable()
						.getPageNumber(), pageResult.getSort()
								.stream()
								.map(Sort.Order::toString)
								.toList());
	}

	@Override
	public Optional<UserRole> findSingleUserRoleDetail(GetSingleUserRoleQuery query) {
		return FallbackFlow.<GetSingleUserRoleQuery, Optional<UserRoleEntity>>builder()
				.addFallBack(q -> Optional.ofNullable(q.getUserRoleId())
						.map(BaseId::getValue)
						.isPresent() && Optional.ofNullable(q.getTenantId())
								.map(BaseId::getValue)
								.isPresent(), q -> jpaUserRoleRepository.getOneByIdAndTenantIdAndDeletedAtIsNull(q
										.getUserRoleId()
										.getValue(), q.getTenantId()
												.getValue()))
				.addFallBack(q -> Optional.ofNullable(q.getUserRoleId())
						.map(BaseId::getValue)
						.isPresent(), q -> jpaUserRoleRepository.getOneByIdAndDeletedAtIsNull(q.getUserRoleId()
								.getValue()))
				.addFallBack(q -> StringUtils.isNotBlank(q.getExternalId()) && query.getTenantId() != null,
						q -> jpaUserRoleRepository.getOneByTenantIdAndExternalIdAndDeletedAtIsNull(q.getTenantId()
								.getValue(), q.getExternalId()))
				.execute(query)
				.map(source -> UserRoleEntityMapper.INSTANCE.toModel(source, featureNodeRepository));
	}

	@Override
	public void deleteUserRole(DeleteSingleUserRoleCommand command) {
		jpaUserRoleRepository.softDeleteUserRoleByUserRoleId(command.getUserRoleId()
				.getValue());
	}

	@Override
	public Optional<UserRole> findUserRoleById(UserRoleId userRoleId) {
		return jpaUserRoleRepository.findOne(QUserRoleEntity.userRoleEntity.id.eq(userRoleId.getValue()))
				.map(UserRoleEntityMapper.INSTANCE::toLiteModel);
	}

	@Override
	public boolean existsUserRole(GetSingleUserRoleQuery query) {
		return jpaUserRoleRepository.exists(WhereBuilder.build()
				.and(QUserRoleEntity.userRoleEntity.id.eq(query.getUserRoleId()
						.getValue()))
				.and(QUserRoleEntity.userRoleEntity.tenantId.eq(query.getTenantId()
						.getValue()))
				.and(QUserRoleEntity.userRoleEntity.externalId.eq(query.getExternalId())));

	}
}
