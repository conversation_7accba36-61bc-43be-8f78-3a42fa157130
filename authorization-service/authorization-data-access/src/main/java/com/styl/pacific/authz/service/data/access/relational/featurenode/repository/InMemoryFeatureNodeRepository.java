/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.authz.service.data.access.relational.featurenode.repository;

import com.styl.pacific.authz.service.core.features.featurenode.FeatureNodeRepository;
import com.styl.pacific.authz.service.core.features.featurenode.entities.FeatureNode;
import com.styl.pacific.domain.valueobject.FeatureNodeId;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component("inMemoryFeatureNodeRepository")
@RequiredArgsConstructor
@Slf4j
public class InMemoryFeatureNodeRepository implements FeatureNodeRepository {

	private final Map<FeatureNodeId, FeatureNode> featureNodeByIdMap = new ConcurrentHashMap<>();
	private final Map<String, FeatureNode> featureNodeByPathMap = new ConcurrentHashMap<>();

	@Override
	public void saveAll(List<FeatureNode> nodes) {
		featureNodeByIdMap.putAll(nodes.stream()
				.collect(Collectors.toMap(FeatureNode::getId, Function.identity())));
		featureNodeByPathMap.putAll(nodes.stream()
				.collect(Collectors.toMap(FeatureNode::getNodePath, Function.identity())));
	}

	@Override
	public List<FeatureNode> getFeatureNodeTree() {
		return featureNodeByPathMap.values()
				.stream()
				.toList();

	}

	@Override
	public List<FeatureNode> findFeatureNodesByPaths(Collection<String> paths) {
		if (CollectionUtils.isEmpty(paths)) {
			return Collections.emptyList();
		}
		return paths.stream()
				.map(featureNodeByPathMap::get)
				.filter(Objects::nonNull)
				.toList();
	}

	@Override
	public List<FeatureNode> getChildrenByParentPaths(Collection<String> parentPaths) {
		if (CollectionUtils.isEmpty(parentPaths)) {
			return Collections.emptyList();
		}
		return parentPaths.stream()
				.map(parentPath -> featureNodeByPathMap.values()
						.stream()
						.filter(node -> !parentPath.equals(node.getNodePath()) && node.getNodePath()
								.startsWith(parentPath))
						.toList())
				.flatMap(List::stream)
				.toList();
	}

	public List<FeatureNode> findAllByIds(Set<FeatureNodeId> featureNodes) {
		if (CollectionUtils.isEmpty(featureNodes)) {
			return Collections.emptyList();
		}
		return featureNodes.stream()
				.filter(it -> it != null && it.getValue() != null)
				.map(featureNodeByIdMap::get)
				.filter(Objects::nonNull)
				.toList();
	}
}
