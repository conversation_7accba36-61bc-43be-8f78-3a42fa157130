--Always runs << CREATE EXTENSION IF NOT EXISTS ltree SCHEMA "authz-service"; >> under Postgres Owner User
CREATE
    TABLE
        IF NOT EXISTS features_tree(
            id BIGSERIAL NOT NULL CONSTRAINT features_tree_pk PRIMARY KEY,
            external_id VARCHAR(50) NOT NULL,
            node_title VARCHAR(150),
            node_type VARCHAR(20) NOT NULL,
            node_path LTREE NOT NULL,
            is_root BOOL DEFAULT FALSE NOT NULL,
            is_leaf BOOL NULL,
            api_mapping JSONB,
            created_at TIMESTAMP(6) DEFAULT NOW(),
            updated_at TIMESTAMP(6) DEFAULT NOW(),
            deleted_at TIMESTAMP(6),
            display_order INTEGER,
            CONSTRAINT features_tree_node_external_id_uidx UNIQUE(external_id)
        );

CREATE
    INDEX IF NOT EXISTS features_tree_node_path_idx ON
    features_tree(node_path);

CREATE
    INDEX IF NOT EXISTS features_tree_node_path_gtidx ON
    features_tree
        USING GIST(
        node_path gist_ltree_ops(
            siglen = 200
        )
    );

CREATE
    TABLE
        IF NOT EXISTS user_roles(
            id BIGSERIAL NOT NULL CONSTRAINT user_roles_pk PRIMARY KEY,
            external_id VARCHAR(50) NOT NULL,
            role_title VARCHAR(150),
            tenant_id BIGINT NOT NULL,
            created_at TIMESTAMP(6) DEFAULT NOW(),
            created_by BIGINT NULL,
            updated_at TIMESTAMP(6) DEFAULT NOW(),
            updated_by BIGINT NULL,
            deleted_at TIMESTAMP(6),
            deleted_by BIGINT
        );

CREATE
    UNIQUE INDEX IF NOT EXISTS user_roles_external_id_uidx ON
    user_roles(external_id);

CREATE
    TABLE
        IF NOT EXISTS feature_permissions(
            id BIGSERIAL NOT NULL CONSTRAINT feature_permissions_pk PRIMARY KEY,
            allowed_access_path LTREE NOT NULL,
            feature_node_id BIGINT NOT NULL,
            user_role_id BIGINT NOT NULL,
            created_at TIMESTAMP(6) DEFAULT NOW(),
            updated_at TIMESTAMP(6) DEFAULT NOW(),
            CONSTRAINT feature_permission_features_tree_fk FOREIGN KEY(feature_node_id) REFERENCES features_tree(id),
            CONSTRAINT feature_permission_user_roles_fk FOREIGN KEY(user_role_id) REFERENCES user_roles(id)
        );

CREATE
    INDEX IF NOT EXISTS feature_permissions_allowed_access_path_gtidx ON
    feature_permissions
        USING GIST(
        allowed_access_path gist_ltree_ops(
            siglen = 200
        )
    );

CREATE
    TABLE
        IF NOT EXISTS content_ingestion_tracking(
            id BIGSERIAL NOT NULL CONSTRAINT content_ingestion_tracking_pk PRIMARY KEY,
            app_content_ingestion_key VARCHAR(50) NOT NULL,
            app_content_version VARCHAR(50) NOT NULL,
            checksum VARCHAR(15) NOT NULL,
            status VARCHAR(15) NOT NULL,
            stacktrace VARCHAR(200),
            created_at TIMESTAMP(6) DEFAULT NOW(),
            updated_at TIMESTAMP(6) DEFAULT NOW()
        );

CREATE
    INDEX IF NOT EXISTS content_ingestion_tracking_key_version_idx ON
    content_ingestion_tracking(
        app_content_ingestion_key,
        app_content_version
    );
