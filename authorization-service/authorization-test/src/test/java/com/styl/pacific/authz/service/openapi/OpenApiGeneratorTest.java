/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.authz.service.openapi;

import com.styl.pacific.authz.service.TenantCreatedEventKafkaConsumer;
import com.styl.pacific.authz.service.config.IntegrationTestConfiguration;
import com.styl.pacific.authz.service.core.features.featurenode.initializer.FeatureNodeInitializer;
import com.styl.pacific.authz.service.core.features.userroles.initializer.DefaultAdminRoleSystemInitializer;
import com.styl.pacific.common.test.openapi.AbstractOpenApiGenerator;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.bean.override.mockito.MockitoBeans;

/**
 * <AUTHOR>
 */

@SuppressWarnings("java:S2187")
@MockitoBeans(value = { @MockitoBean(types = TenantCreatedEventKafkaConsumer.class),
		@MockitoBean(types = FeatureNodeInitializer.class),
		@MockitoBean(types = DefaultAdminRoleSystemInitializer.class) })
@ContextConfiguration(classes = { IntegrationTestConfiguration.class })
class OpenAPIGeneratorTest extends AbstractOpenApiGenerator {

	@Value("${spring.application.name}")
	private String applicationName;

	protected String getServiceName() {
		return applicationName;
	}

}
