/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.authz.service.controller.userroles;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.application.rest.dto.ErrorResponse;
import com.styl.pacific.authz.service.TenantCreatedEventKafkaConsumer;
import com.styl.pacific.authz.service.config.IntegrationTestConfiguration;
import com.styl.pacific.authz.service.core.features.featurenode.FeatureNodeQueryService;
import com.styl.pacific.authz.service.core.features.featurenode.entities.FeatureNode;
import com.styl.pacific.authz.service.core.features.userroles.UserRoleCommandService;
import com.styl.pacific.authz.service.core.features.userroles.UserRoleQueryService;
import com.styl.pacific.authz.service.core.features.userroles.request.GetSingleUserRoleQuery;
import com.styl.pacific.authz.service.core.features.userroles.request.UpsertUserRoleCommand;
import com.styl.pacific.authz.service.core.features.userroles.request.UserRoleFilter;
import com.styl.pacific.authz.service.core.features.userroles.request.UserRolePaginationQuery;
import com.styl.pacific.authz.service.data.access.clients.users.UserPermissionClient;
import com.styl.pacific.authz.shared.http.requests.CreateUserRoleRequest;
import com.styl.pacific.authz.shared.http.requests.FilterUserRoleRequest;
import com.styl.pacific.authz.shared.http.requests.QueryUserRolePaginationRequest;
import com.styl.pacific.authz.shared.http.requests.UpdateUserRoleRequest;
import com.styl.pacific.authz.shared.http.responses.FeatureNodeResponse;
import com.styl.pacific.authz.shared.http.responses.UserRoleResponse;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.test.BaseWebClientWithDbTest;
import com.styl.pacific.common.test.utils.GenerateHttpHeader;
import com.styl.pacific.common.test.utils.HeaderGenerator;
import com.styl.pacific.domain.constants.CommonDomainConstants;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.enums.DefaultSystemRole;
import com.styl.pacific.domain.permissions.FeatureNodeType;
import com.styl.pacific.domain.permissions.PacificApiPermissionKey;
import com.styl.pacific.domain.range.LongDateTimeRange;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.user.shared.http.permissions.response.CountUserPermissionResponse;
import java.time.Instant;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.util.LinkedMultiValueMap;

@MockBean(value = { TenantCreatedEventKafkaConsumer.class })
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = IntegrationTestConfiguration.class)
class UserRoleIntegrationControllerTest extends BaseWebClientWithDbTest {

	@Autowired
	private ObjectMapper objectMapper;

	@Autowired
	private UserRoleCommandService userRoleCommandService;

	@Autowired
	private UserRoleQueryService userRoleQueryService;

	@Autowired
	private FeatureNodeQueryService featureNodeQueryService;

	@MockBean
	private UserPermissionClient userPermissionClient;

	@Test
	void testSystemAdminRoleDefaultWhenStartUpApplication() {
		final var actualSystemAdminRole = userRoleQueryService.getUserRoleDetail(GetSingleUserRoleQuery.builder()
				.tenantId(CommonDomainConstants.SYSTEM_TENANT_ID)
				.userRoleId(MapstructCommonDomainMapper.INSTANCE.longToUserRoleId(DefaultSystemRole.SYSTEM_ADMIN_ROLE
						.getFixedRoleId()))
				.externalId(DefaultSystemRole.SYSTEM_ADMIN_ROLE.getExternalId())
				.build())
				.orElse(null);

		Assertions.assertNotNull(actualSystemAdminRole);
		Assertions.assertEquals(DefaultSystemRole.SYSTEM_ADMIN_ROLE.getRoleTitle(), actualSystemAdminRole
				.getRoleTitle());
		Assertions.assertEquals(DefaultSystemRole.SYSTEM_ADMIN_ROLE.getExternalId(), actualSystemAdminRole
				.getExternalId());
		Assertions.assertEquals(DefaultSystemRole.SYSTEM_ADMIN_ROLE.getFixedRoleId(), actualSystemAdminRole.getId()
				.getValue());

		assertThat(actualSystemAdminRole.getPermissions()
				.stream()
				.map(FeatureNode::getExternalId)
				.toList(), Matchers.allOf(Matchers.hasSize(Arrays.stream(PacificApiPermissionKey.values())
						.filter(it -> FeatureNodeType.API.equals(it.getNodeType()))
						.toArray().length), Matchers.containsInAnyOrder(Arrays.stream(PacificApiPermissionKey.values())
								.filter(it -> FeatureNodeType.API.equals(it.getNodeType()))
								.toArray())));
	}

	@Test
	void testServiceOperatorRoleDefaultWhenStartUpApplication() {
		final var actualServiceOperatorRole = userRoleQueryService.getUserRoleDetail(GetSingleUserRoleQuery.builder()
				.tenantId(CommonDomainConstants.SYSTEM_TENANT_ID)
				.userRoleId(MapstructCommonDomainMapper.INSTANCE.longToUserRoleId(
						DefaultSystemRole.SERVICE_OPERATOR_ROLE.getFixedRoleId()))
				.externalId(DefaultSystemRole.SERVICE_OPERATOR_ROLE.getExternalId())
				.build())
				.orElse(null);

		Assertions.assertNotNull(actualServiceOperatorRole);
		Assertions.assertEquals(DefaultSystemRole.SERVICE_OPERATOR_ROLE.getRoleTitle(), actualServiceOperatorRole
				.getRoleTitle());
		Assertions.assertEquals(DefaultSystemRole.SERVICE_OPERATOR_ROLE.getExternalId(), actualServiceOperatorRole
				.getExternalId());
		Assertions.assertEquals(DefaultSystemRole.SERVICE_OPERATOR_ROLE.getFixedRoleId(), actualServiceOperatorRole
				.getId()
				.getValue());

		final var expectedPermissions = featureNodeQueryService.getChildrenByParentPaths(
				DefaultSystemRole.SERVICE_OPERATOR_ROLE.getAllowedPermissions()
						.stream()
						.map(PacificApiPermissionKey::getNodePath)
						.collect(Collectors.toSet()));

		assertThat(actualServiceOperatorRole.getPermissions()
				.stream()
				.map(FeatureNode::getExternalId)
				.toList(), Matchers.allOf(Matchers.hasSize(expectedPermissions.size()), Matchers.containsInAnyOrder(
						expectedPermissions.stream()
								.map(FeatureNode::getExternalId)
								.toArray())));
	}

	@Test
	void testGetUserRoleWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var tenantId = new TenantId(1L);
		final var permissions = Set.of(PacificApiPermissionKey.TENANT_MGMT,
				PacificApiPermissionKey.TENANT_MGMT_SETTING_UPDATE, PacificApiPermissionKey.PRODUCT_MGMT,
				PacificApiPermissionKey.CUSTOMER_MGMT);
		final var userRole = userRoleCommandService.upsertUserRoles(tenantId, List.of(UpsertUserRoleCommand.builder()
				.roleTitle("User Role 12345")
				.allowedPermissionPaths(permissions.stream()
						.map(PacificApiPermissionKey::getNodePath)
						.collect(Collectors.toSet()))
				.build()))
				.getFirst();

		final var childrenPermissions = featureNodeQueryService.getChildrenByParentPaths(permissions.stream()
				.map(PacificApiPermissionKey::getNodePath)
				.collect(Collectors.toSet()));

		// Act & Assert
		webClient.get()
				.uri("/api/authz/roles/{userRoleId}", userRole.getId()
						.getValue())
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(UserRoleResponse.class)
				.consumeWith(response -> {
					final var userRoleResponse = response.getResponseBody();

					Assertions.assertNotNull(userRoleResponse);
					Assertions.assertNotNull(userRoleResponse.getId());
					Assertions.assertEquals(userRole.getRoleTitle(), userRoleResponse.getRoleTitle());
					Assertions.assertNull(userRoleResponse.getExternalId());

					assertThat(userRoleResponse.getPermissions()
							.stream()
							.map(FeatureNodeResponse::getExternalId)
							.toList(), Matchers.allOf(Matchers.hasSize(childrenPermissions.size()), Matchers
									.containsInAnyOrder(childrenPermissions.stream()
											.map(FeatureNode::getExternalId)
											.map(Enum::name)
											.toArray())));

				});

	}

	@Test
	void testCreateUserRoleWithModulePathsWhenValidRequestThenReturnSuccessfully() throws Exception {
		// Arrange
		final var tenantId = new TenantId(1L);
		final var permissions = Set.of(PacificApiPermissionKey.TENANT_MGMT,
				PacificApiPermissionKey.TENANT_MGMT_SETTING_UPDATE, PacificApiPermissionKey.CUSTOMER_MGMT);
		final var request = CreateUserRoleRequest.builder()
				.roleTitle("New Role 1")
				.allowedPermissionPaths(permissions.stream()
						.map(PacificApiPermissionKey::getNodePath)
						.collect(Collectors.toSet()))
				.build();

		final var childrenPermissions = featureNodeQueryService.getChildrenByParentPaths(permissions.stream()
				.map(PacificApiPermissionKey::getNodePath)
				.collect(Collectors.toSet()));

		// Act & Assert
		webClient.post()
				.uri("/api/authz/roles")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(objectMapper.writeValueAsString(request))
				.headers(httpHeaders -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(httpHeaders)
						.objectMapper(objectMapper)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isCreated()
				.expectBody(UserRoleResponse.class)
				.consumeWith(response -> {
					final var userRoleResponse = response.getResponseBody();

					Assertions.assertNotNull(userRoleResponse);
					Assertions.assertNotNull(userRoleResponse.getId());
					Assertions.assertEquals(request.getRoleTitle(), userRoleResponse.getRoleTitle());
					Assertions.assertNull(userRoleResponse.getExternalId());

					assertThat(userRoleResponse.getPermissions()
							.stream()
							.map(FeatureNodeResponse::getExternalId)
							.toList(), Matchers.allOf(Matchers.hasSize(childrenPermissions.size()), Matchers
									.containsInAnyOrder(childrenPermissions.stream()
											.map(FeatureNode::getExternalId)
											.map(Enum::name)
											.toArray())));
				});

	}

	@Test
	void testCreateUserRoleWithModuleAndLeafPathsWhenValidRequestThenReturnSuccessfully() throws Exception {
		// Arrange
		final var tenantId = new TenantId(1L);
		final var permissions = Set.of(PacificApiPermissionKey.TENANT_MGMT,
				PacificApiPermissionKey.TENANT_MGMT_SETTING_UPDATE, PacificApiPermissionKey.CUSTOMER_MGMT,
				PacificApiPermissionKey.ROLE_MGMT_DELETE, PacificApiPermissionKey.ROLE_MGMT_UPDATE);
		final var request = CreateUserRoleRequest.builder()
				.roleTitle("New Role 1")
				.allowedPermissionPaths(permissions.stream()
						.map(PacificApiPermissionKey::getNodePath)
						.collect(Collectors.toSet()))
				.build();

		final var childrenPermissions = Stream.concat(featureNodeQueryService.getChildrenByParentPaths(permissions
				.stream()
				.map(PacificApiPermissionKey::getNodePath)
				.collect(Collectors.toSet()))
				.stream(), featureNodeQueryService.getFeatureNodesByPaths(Set.of(
						PacificApiPermissionKey.ROLE_MGMT_DELETE, PacificApiPermissionKey.ROLE_MGMT_UPDATE)
						.stream()
						.map(PacificApiPermissionKey::getNodePath)
						.toList())
						.stream())
				.toList();

		// Act & Assert
		webClient.post()
				.uri("/api/authz/roles")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(objectMapper.writeValueAsString(request))
				.headers(httpHeaders -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(httpHeaders)
						.objectMapper(objectMapper)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isCreated()
				.expectBody(UserRoleResponse.class)
				.consumeWith(response -> {
					final var userRoleResponse = response.getResponseBody();

					Assertions.assertNotNull(userRoleResponse);
					Assertions.assertNotNull(userRoleResponse.getId());
					Assertions.assertEquals(request.getRoleTitle(), userRoleResponse.getRoleTitle());
					Assertions.assertNull(userRoleResponse.getExternalId());

					assertThat(userRoleResponse.getPermissions()
							.stream()
							.map(FeatureNodeResponse::getExternalId)
							.toList(), Matchers.allOf(Matchers.hasSize(childrenPermissions.size()), Matchers
									.containsInAnyOrder(childrenPermissions.stream()
											.map(FeatureNode::getExternalId)
											.map(Enum::name)
											.toArray())));
				});

	}

	@Test
	void testGetUserRoleByMultipleConditionsWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var tenantId = new TenantId(10002L);
		final var permissions = Set.of(PacificApiPermissionKey.TENANT_MGMT,
				PacificApiPermissionKey.TENANT_MGMT_SETTING_UPDATE, PacificApiPermissionKey.CUSTOMER_MGMT,
				PacificApiPermissionKey.ROLE_MGMT_DELETE, PacificApiPermissionKey.ROLE_MGMT_UPDATE);

		final var childrenPermissions = Stream.concat(featureNodeQueryService.getChildrenByParentPaths(permissions
				.stream()
				.map(PacificApiPermissionKey::getNodePath)
				.collect(Collectors.toSet()))
				.stream(), featureNodeQueryService.getFeatureNodesByPaths(Set.of(
						PacificApiPermissionKey.ROLE_MGMT_DELETE, PacificApiPermissionKey.ROLE_MGMT_UPDATE)
						.stream()
						.map(PacificApiPermissionKey::getNodePath)
						.toList())
						.stream())
				.toList();

		final var userRole = userRoleCommandService.upsertUserRoles(tenantId, List.of(UpsertUserRoleCommand.builder()
				.roleTitle("New Role 1")
				.allowedPermissionPaths(permissions.stream()
						.map(PacificApiPermissionKey::getNodePath)
						.collect(Collectors.toSet()))
				.build()))
				.getFirst();

		final Map<String, String> map = new HashMap<>();

		map.put("userRoleId", userRole.getId()
				.getValue()
				.toString());
		map.put("tenantId", userRole.getTenantId()
				.getValue()
				.toString());
		map.put("flatten", "false");

		// Act & Assert
		webClient.get()
				.uri(uriBuilder -> {
					final var requestParams = new LinkedMultiValueMap<>(map.entrySet()
							.stream()
							.collect(Collectors.toMap(Map.Entry::getKey, it -> List.of(it.getValue()))));
					uriBuilder.path("/api/authz/roles");
					uriBuilder.queryParams(requestParams);
					return uriBuilder.build();
				})
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(UserRoleResponse.class)
				.consumeWith(response -> {
					final var userRoleResponse = response.getResponseBody();

					Assertions.assertNotNull(userRoleResponse);
					Assertions.assertNotNull(userRoleResponse.getId());
					Assertions.assertEquals(userRole.getRoleTitle(), userRoleResponse.getRoleTitle());
					Assertions.assertNull(userRoleResponse.getExternalId());
				});

	}

	@Test
	void testUpdateUserRoleWhenValidRequestThenReturnSuccessfully() throws Exception {
		// Arrange
		final var tenantId = new TenantId(10003L);
		final var userRole = userRoleCommandService.upsertUserRoles(tenantId, List.of(UpsertUserRoleCommand.builder()
				.roleTitle("New Role 1")
				.allowedPermissionPaths(Set.of(PacificApiPermissionKey.TENANT_MGMT,
						PacificApiPermissionKey.TENANT_MGMT_SETTING_UPDATE, PacificApiPermissionKey.CUSTOMER_MGMT,
						PacificApiPermissionKey.ROLE_MGMT_DELETE, PacificApiPermissionKey.ROLE_MGMT_UPDATE)
						.stream()
						.map(PacificApiPermissionKey::getNodePath)
						.collect(Collectors.toSet()))
				.build()))
				.getFirst();

		final var updatedPermissions = Set.of(PacificApiPermissionKey.TENANT_MGMT,
				PacificApiPermissionKey.ROLE_MGMT_DELETE, PacificApiPermissionKey.ROLE_MGMT_UPDATE);

		final var childrenPermissions = Stream.concat(featureNodeQueryService.getChildrenByParentPaths(
				updatedPermissions.stream()
						.map(PacificApiPermissionKey::getNodePath)
						.collect(Collectors.toSet()))
				.stream(), featureNodeQueryService.getFeatureNodesByPaths(Set.of(
						PacificApiPermissionKey.ROLE_MGMT_DELETE, PacificApiPermissionKey.ROLE_MGMT_UPDATE)
						.stream()
						.map(PacificApiPermissionKey::getNodePath)
						.toList())
						.stream())
				.toList();

		final var request = UpdateUserRoleRequest.builder()
				.id(userRole.getId()
						.getValue()
						.toString())
				.roleTitle("New Role 12345")
				.allowedPermissionPaths(updatedPermissions.stream()
						.map(PacificApiPermissionKey::getNodePath)
						.collect(Collectors.toSet()))
				.build();

		// Act & Assert
		webClient.put()
				.uri("/api/authz/roles")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(objectMapper.writeValueAsString(request))
				.headers(httpHeaders -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(httpHeaders)
						.objectMapper(objectMapper)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(UserRoleResponse.class)
				.consumeWith(response -> {
					final var userRoleResponse = response.getResponseBody();

					Assertions.assertNotNull(userRoleResponse);
					Assertions.assertNotNull(userRoleResponse.getId());
					Assertions.assertEquals(request.getRoleTitle(), userRoleResponse.getRoleTitle());
					Assertions.assertNull(userRoleResponse.getExternalId());

					assertThat(userRoleResponse.getPermissions()
							.stream()
							.map(FeatureNodeResponse::getExternalId)
							.toList(), Matchers.allOf(Matchers.hasSize(childrenPermissions.size()), Matchers
									.containsInAnyOrder(childrenPermissions.stream()
											.map(FeatureNode::getExternalId)
											.map(Enum::name)
											.toArray())));
				});

	}

	@Test
	void testQueryUserRoleWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var tenantId = new TenantId(10005L);
		final var permissions = Set.of(PacificApiPermissionKey.TENANT_MGMT,
				PacificApiPermissionKey.TENANT_MGMT_SETTING_UPDATE, PacificApiPermissionKey.CUSTOMER_MGMT,
				PacificApiPermissionKey.ROLE_MGMT_DELETE, PacificApiPermissionKey.ROLE_MGMT_UPDATE);

		final var childrenPermissions = Stream.concat(featureNodeQueryService.getChildrenByParentPaths(permissions
				.stream()
				.map(PacificApiPermissionKey::getNodePath)
				.collect(Collectors.toSet()))
				.stream(), featureNodeQueryService.getFeatureNodesByPaths(Set.of(
						PacificApiPermissionKey.ROLE_MGMT_DELETE, PacificApiPermissionKey.ROLE_MGMT_UPDATE)
						.stream()
						.map(PacificApiPermissionKey::getNodePath)
						.toList())
						.stream())
				.toList();

		final var userRole = userRoleCommandService.upsertUserRoles(tenantId, List.of(UpsertUserRoleCommand.builder()
				.roleTitle("New Role 2")
				.allowedPermissionPaths(permissions.stream()
						.map(PacificApiPermissionKey::getNodePath)
						.collect(Collectors.toSet()))
				.build()))
				.getFirst();

		final var request = QueryUserRolePaginationRequest.builder()
				.filter(FilterUserRoleRequest.builder()
						.byUserRoleId(userRole.getId()
								.getValue()
								.toString())
						.byTenantId(userRole.getTenantId()
								.getValue()
								.toString())
						.build())
				.size(10)
				.page(0)
				.sortDirection("ASC")
				.sortFields(List.of("id"))
				.build();

		final Map<String, String> map = new HashMap<>();
		map.put("filter.byUserRoleId", request.getFilter()
				.getByUserRoleId());
		map.put("filter.byTenantId", request.getFilter()
				.getByTenantId());
		map.put("size", String.valueOf(request.getSize()));
		map.put("page", String.valueOf(request.getPage()));
		map.put("sortDirection", request.getSortDirection());
		map.put("sortFields", request.getSortFields()
				.stream()
				.findFirst()
				.orElse(null));

		// Act & Assert
		webClient.get()
				.uri(uriBuilder -> {
					final var requestParams = new LinkedMultiValueMap<>(map.entrySet()
							.stream()
							.collect(Collectors.toMap(Map.Entry::getKey, it -> List.of(it.getValue()))));
					uriBuilder.path("/api/authz/roles/query");
					uriBuilder.queryParams(requestParams);
					return uriBuilder.build();
				})
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(Paging.class)
				.consumeWith(response -> {
					Assertions.assertNotNull(response.getResponseBody());
					final var userRoleResponse = (UserRoleResponse) objectMapper.convertValue(response.getResponseBody()
							.getContent()
							.getFirst(), UserRoleResponse.class);

					Assertions.assertNotNull(userRoleResponse);
					Assertions.assertNotNull(userRoleResponse.getId());
					Assertions.assertEquals(userRole.getRoleTitle(), userRoleResponse.getRoleTitle());
					Assertions.assertNull(userRoleResponse.getExternalId());
				});
	}

	@Test
	void testQueryUserRoleExcludeExternalIdsExcludeWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var tenantId = new TenantId(1111L);
		final var permissions = Set.of(PacificApiPermissionKey.TENANT_MGMT,
				PacificApiPermissionKey.TENANT_MGMT_SETTING_UPDATE, PacificApiPermissionKey.CUSTOMER_MGMT,
				PacificApiPermissionKey.ROLE_MGMT_DELETE, PacificApiPermissionKey.ROLE_MGMT_UPDATE);

		final var userRole1 = userRoleCommandService.upsertUserRoles(tenantId, List.of(UpsertUserRoleCommand.builder()
				.roleTitle("New Role 1")
				.externalId("External_Id_1")
				.allowedPermissionPaths(permissions.stream()
						.map(PacificApiPermissionKey::getNodePath)
						.collect(Collectors.toSet()))
				.build()))
				.getFirst();

		final var userRole2 = userRoleCommandService.upsertUserRoles(tenantId, List.of(UpsertUserRoleCommand.builder()
				.roleTitle("New Role 2")
				.externalId("External_Id_2")
				.allowedPermissionPaths(permissions.stream()
						.map(PacificApiPermissionKey::getNodePath)
						.collect(Collectors.toSet()))
				.build()))
				.getFirst();

		final var userRole3 = userRoleCommandService.upsertUserRoles(tenantId, List.of(UpsertUserRoleCommand.builder()
				.roleTitle("New Role 2")
				.allowedPermissionPaths(permissions.stream()
						.map(PacificApiPermissionKey::getNodePath)
						.collect(Collectors.toSet()))
				.build()))
				.getFirst();

		final var request = QueryUserRolePaginationRequest.builder()
				.filter(FilterUserRoleRequest.builder()
						.byTenantId(tenantId.getValue()
								.toString())
						.excludeExternalIds(Set.of(userRole1.getExternalId(), userRole2.getExternalId()))
						.build())
				.size(10)
				.page(0)
				.sortDirection("ASC")
				.sortFields(List.of("id"))
				.build();

		final Map<String, String> map = new HashMap<>();
		map.put("filter.byTenantId", request.getFilter()
				.getByTenantId());
		map.put("filter.excludeExternalIds", String.join(",", request.getFilter()
				.getExcludeExternalIds()));
		map.put("size", String.valueOf(request.getSize()));
		map.put("page", String.valueOf(request.getPage()));
		map.put("sortDirection", request.getSortDirection());
		map.put("sortFields", request.getSortFields()
				.stream()
				.findFirst()
				.orElse(null));

		// Act & Assert
		webClient.get()
				.uri(uriBuilder -> {
					final var requestParams = new LinkedMultiValueMap<>(map.entrySet()
							.stream()
							.collect(Collectors.toMap(Map.Entry::getKey, it -> List.of(it.getValue()))));
					uriBuilder.path("/api/authz/roles/query");
					uriBuilder.queryParams(requestParams);
					return uriBuilder.build();
				})
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(Paging.class)
				.consumeWith(response -> {
					Assertions.assertNotNull(response.getResponseBody());
					final var userRoleResponses = Optional.ofNullable(response.getResponseBody())
							.map(Paging::getContent)
							.map(ls -> (List<UserRoleResponse>) ls.stream()
									.map(it -> objectMapper.convertValue(it, UserRoleResponse.class))
									.toList())
							.orElseGet(List::of);

					Assertions.assertEquals(tenantId.getValue()
							.toString(), userRoleResponses.getFirst()
									.getTenantId());
					Assertions.assertTrue(userRoleResponses.stream()
							.map(UserRoleResponse::getId)
							.toList()
							.stream()
							.noneMatch(it -> Set.of(userRole1.getId()
									.toString(), userRole2.getId()
											.getValue(), userRole3.getId()
													.getValue())
									.contains(it)));
				});
	}

	@Test
	void testQueryUserRoleIncludeIdsWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var tenantId = new TenantId(2222L);

		final var permissions = Set.of(PacificApiPermissionKey.TENANT_MGMT,
				PacificApiPermissionKey.TENANT_MGMT_SETTING_UPDATE, PacificApiPermissionKey.CUSTOMER_MGMT,
				PacificApiPermissionKey.ROLE_MGMT_DELETE, PacificApiPermissionKey.ROLE_MGMT_UPDATE);

		final var userRole1 = userRoleCommandService.upsertUserRoles(tenantId, List.of(UpsertUserRoleCommand.builder()
				.roleTitle("New Role 1")
				.externalId("External_Id_1")
				.allowedPermissionPaths(permissions.stream()
						.map(PacificApiPermissionKey::getNodePath)
						.collect(Collectors.toSet()))
				.build()))
				.getFirst();

		final var userRole2 = userRoleCommandService.upsertUserRoles(tenantId, List.of(UpsertUserRoleCommand.builder()
				.roleTitle("New Role 2")
				.externalId("External_Id_2")
				.allowedPermissionPaths(permissions.stream()
						.map(PacificApiPermissionKey::getNodePath)
						.collect(Collectors.toSet()))
				.build()))
				.getFirst();

		userRoleCommandService.upsertUserRoles(tenantId, List.of(UpsertUserRoleCommand.builder()
				.roleTitle("New Role 3")
				.externalId("External_Id_2")
				.allowedPermissionPaths(permissions.stream()
						.map(PacificApiPermissionKey::getNodePath)
						.collect(Collectors.toSet()))
				.build()))
				.getFirst();

		final var request = QueryUserRolePaginationRequest.builder()
				.filter(FilterUserRoleRequest.builder()
						.byTenantId(tenantId.getValue()
								.toString())
						.includeRoleIds(Set.of(userRole1.getId()
								.getValue()
								.toString(), userRole2.getId()
										.getValue()
										.toString()))
						.build())
				.size(10)
				.page(0)
				.sortDirection("ASC")
				.sortFields(List.of("id"))
				.build();

		final Map<String, String> map = new HashMap<>();
		map.put("filter.byTenantId", request.getFilter()
				.getByTenantId());
		map.put("filter.includeRoleIds", String.join(",", request.getFilter()
				.getIncludeRoleIds()));
		map.put("size", String.valueOf(request.getSize()));
		map.put("page", String.valueOf(request.getPage()));
		map.put("sortDirection", request.getSortDirection());
		map.put("sortFields", request.getSortFields()
				.stream()
				.findFirst()
				.orElse(null));

		// Act & Assert
		webClient.get()
				.uri(uriBuilder -> {
					final var requestParams = new LinkedMultiValueMap<>(map.entrySet()
							.stream()
							.collect(Collectors.toMap(Map.Entry::getKey, it -> List.of(it.getValue()))));
					uriBuilder.path("/api/authz/roles/query");
					uriBuilder.queryParams(requestParams);
					return uriBuilder.build();
				})
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(Paging.class)
				.consumeWith(response -> {
					Assertions.assertNotNull(response.getResponseBody());
					final var userRoleResponses = Optional.ofNullable(response.getResponseBody())
							.map(Paging::getContent)
							.map(ls -> (List<UserRoleResponse>) ls.stream()
									.map(it -> objectMapper.convertValue(it, UserRoleResponse.class))
									.toList())
							.orElseGet(List::of);

					Assertions.assertEquals(tenantId.getValue()
							.toString(), userRoleResponses.getFirst()
									.getTenantId());
					Assertions.assertEquals(2, userRoleResponses.size());

					Assertions.assertTrue(Set.of(userRole1.getExternalId(), userRole2.getExternalId())
							.containsAll(userRoleResponses.stream()
									.map(UserRoleResponse::getExternalId)
									.toList()));
				});
	}

	@Test
	void testQueryUserRoleCreatedRangeWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var tenantId = new TenantId(2222L);
		final var from = Instant.now();

		final var permissions = Set.of(PacificApiPermissionKey.TENANT_MGMT,
				PacificApiPermissionKey.TENANT_MGMT_SETTING_UPDATE, PacificApiPermissionKey.CUSTOMER_MGMT,
				PacificApiPermissionKey.ROLE_MGMT_DELETE, PacificApiPermissionKey.ROLE_MGMT_UPDATE);

		final var userRole1 = userRoleCommandService.upsertUserRoles(tenantId, List.of(UpsertUserRoleCommand.builder()
				.roleTitle("New Role 1")
				.externalId("External_Id_1")
				.allowedPermissionPaths(permissions.stream()
						.map(PacificApiPermissionKey::getNodePath)
						.collect(Collectors.toSet()))
				.build()))
				.getFirst();

		final var userRole2 = userRoleCommandService.upsertUserRoles(tenantId, List.of(UpsertUserRoleCommand.builder()
				.roleTitle("New Role 2")
				.externalId("External_Id_2")
				.allowedPermissionPaths(permissions.stream()
						.map(PacificApiPermissionKey::getNodePath)
						.collect(Collectors.toSet()))
				.build()))
				.getFirst();

		final var userRole3 = userRoleCommandService.upsertUserRoles(tenantId, List.of(UpsertUserRoleCommand.builder()
				.roleTitle("New Role 3")
				.externalId("External_Id_3")
				.allowedPermissionPaths(permissions.stream()
						.map(PacificApiPermissionKey::getNodePath)
						.collect(Collectors.toSet()))
				.build()))
				.getFirst();

		final var to = Instant.now();

		final var request = QueryUserRolePaginationRequest.builder()
				.filter(FilterUserRoleRequest.builder()
						.byTenantId(tenantId.getValue()
								.toString())
						.byCreatedRange(LongDateTimeRange.builder()
								.from(from.toEpochMilli())
								.to(to.toEpochMilli())
								.build())
						.build())
				.size(10)
				.page(0)
				.sortDirection("ASC")
				.sortFields(List.of("id"))
				.build();

		final Map<String, String> map = new HashMap<>();
		map.put("filter.byTenantId", request.getFilter()
				.getByTenantId());
		map.put("filter.byCreatedRange.from", String.valueOf(from.toEpochMilli()));
		map.put("filter.byCreatedRange.to", String.valueOf(to.toEpochMilli()));

		map.put("size", String.valueOf(request.getSize()));
		map.put("page", String.valueOf(request.getPage()));
		map.put("sortDirection", request.getSortDirection());
		map.put("sortFields", request.getSortFields()
				.stream()
				.findFirst()
				.orElse(null));

		// Act & Assert
		webClient.get()
				.uri(uriBuilder -> {
					final var requestParams = new LinkedMultiValueMap<>(map.entrySet()
							.stream()
							.collect(Collectors.toMap(Map.Entry::getKey, it -> List.of(it.getValue()))));
					uriBuilder.path("/api/authz/roles/query");
					uriBuilder.queryParams(requestParams);
					return uriBuilder.build();
				})
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(Paging.class)
				.consumeWith(response -> {
					Assertions.assertNotNull(response.getResponseBody());
					final var userRoleResponses = Optional.ofNullable(response.getResponseBody())
							.map(Paging::getContent)
							.map(ls -> (List<UserRoleResponse>) ls.stream()
									.map(it -> objectMapper.convertValue(it, UserRoleResponse.class))
									.toList())
							.orElseGet(List::of);

					Assertions.assertEquals(3, userRoleResponses.size());
					Assertions.assertEquals(tenantId.getValue()
							.toString(), userRoleResponses.getFirst()
									.getTenantId());
					Assertions.assertTrue(Set.of(userRole1.getExternalId(), userRole2.getExternalId(), userRole3
							.getExternalId())
							.containsAll(userRoleResponses.stream()
									.map(UserRoleResponse::getExternalId)
									.toList()));

				});
	}

	@Test
	void testQueryUserRoleNoFilterWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var tenantId = new TenantId(533L);

		final var permissions = Set.of(PacificApiPermissionKey.TENANT_MGMT,
				PacificApiPermissionKey.TENANT_MGMT_SETTING_UPDATE, PacificApiPermissionKey.CUSTOMER_MGMT,
				PacificApiPermissionKey.ROLE_MGMT_DELETE, PacificApiPermissionKey.ROLE_MGMT_UPDATE);

		final var userRole1 = userRoleCommandService.upsertUserRoles(tenantId, List.of(UpsertUserRoleCommand.builder()
				.roleTitle("New Role 1333")
				.externalId("External_Id_1333")
				.allowedPermissionPaths(permissions.stream()
						.map(PacificApiPermissionKey::getNodePath)
						.collect(Collectors.toSet()))
				.build()))
				.getFirst();

		final var userRole2 = userRoleCommandService.upsertUserRoles(tenantId, List.of(UpsertUserRoleCommand.builder()
				.roleTitle("New Role 2333")
				.externalId("External_Id_2333")
				.allowedPermissionPaths(permissions.stream()
						.map(PacificApiPermissionKey::getNodePath)
						.collect(Collectors.toSet()))
				.build()))
				.getFirst();

		final var userRole3 = userRoleCommandService.upsertUserRoles(tenantId, List.of(UpsertUserRoleCommand.builder()
				.roleTitle("New Role 3333")
				.externalId("External_Id_3333")
				.allowedPermissionPaths(permissions.stream()
						.map(PacificApiPermissionKey::getNodePath)
						.collect(Collectors.toSet()))
				.build()))
				.getFirst();

		final var request = QueryUserRolePaginationRequest.builder()
				.filter(FilterUserRoleRequest.builder()
						.byTenantId(tenantId.getValue()
								.toString())
						.build())
				.size(100)
				.page(0)
				.sortDirection("ASC")
				.sortFields(List.of("id"))
				.build();

		final Map<String, String> map = new HashMap<>();
		map.put("filter.byTenantId", request.getFilter()
				.getByTenantId());
		map.put("size", String.valueOf(request.getSize()));
		map.put("page", String.valueOf(request.getPage()));
		map.put("sortDirection", request.getSortDirection());
		map.put("sortFields", request.getSortFields()
				.stream()
				.findFirst()
				.orElse(null));

		// Act & Assert
		webClient.get()
				.uri(uriBuilder -> {
					final var requestParams = new LinkedMultiValueMap<>(map.entrySet()
							.stream()
							.collect(Collectors.toMap(Map.Entry::getKey, it -> List.of(it.getValue()))));
					uriBuilder.path("/api/authz/roles/query");
					uriBuilder.queryParams(requestParams);
					return uriBuilder.build();
				})
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(Paging.class)
				.consumeWith(response -> {
					Assertions.assertNotNull(response.getResponseBody());
					final var userRoleResponses = Optional.ofNullable(response.getResponseBody())
							.map(Paging::getContent)
							.map(ls -> (List<UserRoleResponse>) ls.stream()
									.map(it -> objectMapper.convertValue(it, UserRoleResponse.class))
									.toList())
							.orElseGet(List::of);
					Assertions.assertEquals(3, userRoleResponses.size());
					Assertions.assertTrue(userRoleResponses.stream()
							.map(UserRoleResponse::getId)
							.toList()
							.containsAll(Set.of(userRole1.getId(), userRole2.getId(), userRole3.getId())
									.stream()
									.map(it -> it.getValue()
											.toString())
									.toList()));
				});
	}

	@Test
	void testQueryUserRoleByRoleTitleWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var tenantId = new TenantId(335L);
		final var permissions = Set.of(PacificApiPermissionKey.TENANT_MGMT,
				PacificApiPermissionKey.TENANT_MGMT_SETTING_UPDATE, PacificApiPermissionKey.CUSTOMER_MGMT,
				PacificApiPermissionKey.ROLE_MGMT_DELETE, PacificApiPermissionKey.ROLE_MGMT_UPDATE);

		final var userRole1 = userRoleCommandService.upsertUserRoles(tenantId, List.of(UpsertUserRoleCommand.builder()
				.roleTitle("New Role 4444")
				.externalId("External_Id_1333")
				.allowedPermissionPaths(permissions.stream()
						.map(PacificApiPermissionKey::getNodePath)
						.collect(Collectors.toSet()))
				.build()))
				.getFirst();

		final var userRole2 = userRoleCommandService.upsertUserRoles(tenantId, List.of(UpsertUserRoleCommand.builder()
				.roleTitle("New Role 44442")
				.externalId("External_Id_2333")
				.allowedPermissionPaths(permissions.stream()
						.map(PacificApiPermissionKey::getNodePath)
						.collect(Collectors.toSet()))
				.build()))
				.getFirst();

		final var request = QueryUserRolePaginationRequest.builder()
				.filter(FilterUserRoleRequest.builder()
						.byRoleTitle("New Role 4444")
						.build())
				.size(100)
				.page(0)
				.sortDirection("ASC")
				.sortFields(List.of("id"))
				.build();

		final Map<String, String> map = new HashMap<>();
		map.put("filter.byRoleTitle", request.getFilter()
				.getByRoleTitle());

		map.put("size", String.valueOf(request.getSize()));
		map.put("page", String.valueOf(request.getPage()));
		map.put("sortDirection", request.getSortDirection());
		map.put("sortFields", request.getSortFields()
				.stream()
				.findFirst()
				.orElse(null));

		// Act & Assert
		webClient.get()
				.uri(uriBuilder -> {
					final var requestParams = new LinkedMultiValueMap<>(map.entrySet()
							.stream()
							.collect(Collectors.toMap(Map.Entry::getKey, it -> List.of(it.getValue()))));
					uriBuilder.path("/api/authz/roles/query");
					uriBuilder.queryParams(requestParams);
					return uriBuilder.build();
				})
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(Paging.class)
				.consumeWith(response -> {
					Assertions.assertNotNull(response.getResponseBody());
					final var userRoleResponses = Optional.ofNullable(response.getResponseBody())
							.map(Paging::getContent)
							.map(ls -> (List<UserRoleResponse>) ls.stream()
									.map(it -> objectMapper.convertValue(it, UserRoleResponse.class))
									.toList())
							.orElseGet(List::of);

					Assertions.assertEquals(2, userRoleResponses.size());

					Assertions.assertTrue(Set.of(userRole1.getExternalId(), userRole2.getExternalId())
							.containsAll(userRoleResponses.stream()
									.map(UserRoleResponse::getExternalId)
									.toList()));
				});
	}

	@Test
	void testDeleteUserRoleWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var tenantId = new TenantId(10006L);
		final var permissions = Set.of(PacificApiPermissionKey.TENANT_MGMT,
				PacificApiPermissionKey.TENANT_MGMT_SETTING_UPDATE, PacificApiPermissionKey.CUSTOMER_MGMT,
				PacificApiPermissionKey.ROLE_MGMT_DELETE, PacificApiPermissionKey.ROLE_MGMT_UPDATE);

		final var userRole = userRoleCommandService.upsertUserRoles(tenantId, List.of(UpsertUserRoleCommand.builder()
				.roleTitle("New Role 2")
				.allowedPermissionPaths(permissions.stream()
						.map(PacificApiPermissionKey::getNodePath)
						.collect(Collectors.toSet()))
				.build()))
				.getFirst();

		when(userPermissionClient.countUserPermissions(any())).thenReturn(CountUserPermissionResponse.builder()
				.countUserRoleResult(Map.of(userRole.getId()
						.getValue(), CountUserPermissionResponse.UserRoleAggregationResult.builder()
								.totalUsers(0L)
								.build()))
				.build());

		// Act & Assert
		webClient.delete()
				.uri("/api/authz/roles/{userRoleId}", userRole.getId()
						.getValue()
						.toString())
				.exchange()
				.expectStatus()
				.isOk();

	}

	@Test
	void testThrowExceptionWhenDeleteSystemUserRole() {
		// Act & Assert
		webClient.delete()
				.uri("/api/authz/roles/{userRoleId}", DefaultSystemRole.SYSTEM_ADMIN_ROLE.getFixedRoleId())
				.exchange()
				.expectStatus()
				.isBadRequest()
				.expectBody(ErrorResponse.class)
				.consumeWith(response -> {
					final var error = response.getResponseBody();
					Assertions.assertNotNull(error);
					Assertions.assertTrue(error.getDetails()
							.contains("Cannot delete system user role"));
				});

	}

	@Test
	void testThrowExceptionWhenDeleteUserRoleHavingLinkingUsers() {
		// Arrange
		final var tenantId = new TenantId(10008L);
		final var permissions = Set.of(PacificApiPermissionKey.TENANT_MGMT,
				PacificApiPermissionKey.TENANT_MGMT_SETTING_UPDATE, PacificApiPermissionKey.CUSTOMER_MGMT,
				PacificApiPermissionKey.ROLE_MGMT_DELETE, PacificApiPermissionKey.ROLE_MGMT_UPDATE);

		final var userRole = userRoleCommandService.upsertUserRoles(tenantId, List.of(UpsertUserRoleCommand.builder()
				.roleTitle("New Role 2")
				.allowedPermissionPaths(permissions.stream()
						.map(PacificApiPermissionKey::getNodePath)
						.collect(Collectors.toSet()))
				.build()))
				.getFirst();

		when(userPermissionClient.countUserPermissions(any())).thenReturn(CountUserPermissionResponse.builder()
				.countUserRoleResult(Map.of(userRole.getId()
						.getValue(), CountUserPermissionResponse.UserRoleAggregationResult.builder()
								.totalUsers(1L)
								.build()))
				.build());

		// Act & Assert
		webClient.delete()
				.uri("/api/authz/roles/{userRoleId}", userRole.getId()
						.getValue())
				.exchange()
				.expectStatus()
				.isBadRequest()
				.expectBody(ErrorResponse.class)
				.consumeWith(response -> {
					final var error = response.getResponseBody();
					Assertions.assertNotNull(error);
					Assertions.assertTrue(error.getDetails()
							.contains("Cannot delete user role because the role is linking with the available users"));
				});

	}

	@Test
	void initializeTenantUserRolesWhenTenantOnboarding() {
		// Arrange
		final var tenantId1 = new TenantId(50001L);
		final var tenantId2 = new TenantId(50002L);
		final var tenantId3 = new TenantId(50003L);
		final var tenants = Set.of(tenantId1, tenantId2, tenantId3);

		final var expectedRoles = Arrays.stream(DefaultSystemRole.values())
				.filter(it -> !it.isDefaultSystemTenant() && it.getFixedRoleId() == null)
				.collect(Collectors.toSet());

		// Act
		tenants.forEach(tenantId -> userRoleCommandService.initializeTenantUserRoles(tenantId));

		// Assert
		tenants.forEach(tenant -> {
			final var page = userRoleQueryService.queryUserRoles(UserRolePaginationQuery.builder()
					.filter(UserRoleFilter.builder()
							.byTenantId(tenant)
							.build())
					.page(0)
					.size(10)
					.build());
			Assertions.assertEquals(expectedRoles.size(), page.getContent()
					.size());

			expectedRoles.forEach(defaultExpectedRole -> {
				final var actualRole = page.getContent()
						.stream()
						.filter(it -> defaultExpectedRole.getExternalId()
								.equals(it.getExternalId()))
						.findFirst()
						.orElse(null);
				Assertions.assertNotNull(actualRole);
				Assertions.assertEquals(actualRole.getRoleTitle(), defaultExpectedRole.getRoleTitle());

				final var actualRoleDetail = userRoleQueryService.getUserRoleDetail(GetSingleUserRoleQuery.builder()
						.tenantId(tenant)
						.userRoleId(actualRole.getId())
						.externalId(actualRole.getExternalId())
						.build())
						.orElse(null);
				Assertions.assertNotNull(actualRoleDetail);

				final var expectedRolePermissions = Stream.concat(featureNodeQueryService.getChildrenByParentPaths(
						defaultExpectedRole.getAllowedPermissions()
								.stream()
								.filter(it -> FeatureNodeType.MODULE.equals(it.getNodeType()))
								.map(PacificApiPermissionKey::getNodePath)
								.collect(Collectors.toSet())

				)
						.stream(), featureNodeQueryService.getFeatureNodesByPaths(defaultExpectedRole
								.getAllowedPermissions()
								.stream()
								.filter(it -> FeatureNodeType.API.equals(it.getNodeType()))
								.map(PacificApiPermissionKey::getNodePath)
								.collect(Collectors.toSet()))
								.stream())
						.toList();
				assertThat(actualRoleDetail.getPermissions()
						.stream()
						.map(FeatureNode::getExternalId)
						.toList(), Matchers.allOf(Matchers.hasSize(expectedRolePermissions.size()), Matchers
								.containsInAnyOrder(expectedRolePermissions.stream()
										.map(FeatureNode::getExternalId)
										.toArray())));
			});

		});

	}

	@Test
	void initializeTenantUserRolesByTwiceWhenTenantOnboarding() {
		// Arrange
		final var tenantId1 = new TenantId(50001L);
		final var tenantId2 = new TenantId(50001L);
		final var tenants = List.of(tenantId1, tenantId2);

		final var expectedRoles = Arrays.stream(DefaultSystemRole.values())
				.filter(it -> !it.isDefaultSystemTenant() && it.getFixedRoleId() == null)
				.collect(Collectors.toSet());

		// Act
		tenants.forEach(tenantId -> userRoleCommandService.initializeTenantUserRoles(tenantId));

		// Assert
		tenants.forEach(tenant -> {
			final var page = userRoleQueryService.queryUserRoles(UserRolePaginationQuery.builder()
					.filter(UserRoleFilter.builder()
							.byTenantId(tenant)
							.build())
					.page(0)
					.size(10)
					.build());
			Assertions.assertEquals(expectedRoles.size(), page.getContent()
					.size());

			expectedRoles.forEach(defaultExpectedRole -> {
				final var actualRole = page.getContent()
						.stream()
						.filter(it -> defaultExpectedRole.getExternalId()
								.equals(it.getExternalId()))
						.findFirst()
						.orElse(null);
				Assertions.assertNotNull(actualRole);
				Assertions.assertEquals(actualRole.getRoleTitle(), defaultExpectedRole.getRoleTitle());

				final var actualRoleDetail = userRoleQueryService.getUserRoleDetail(GetSingleUserRoleQuery.builder()
						.tenantId(tenant)
						.userRoleId(actualRole.getId())
						.externalId(actualRole.getExternalId())
						.build())
						.orElse(null);
				Assertions.assertNotNull(actualRoleDetail);

				final var expectedRolePermissions = Stream.concat(featureNodeQueryService.getChildrenByParentPaths(
						defaultExpectedRole.getAllowedPermissions()
								.stream()
								.filter(it -> FeatureNodeType.MODULE.equals(it.getNodeType()))
								.map(PacificApiPermissionKey::getNodePath)
								.collect(Collectors.toSet())

				)
						.stream(), featureNodeQueryService.getFeatureNodesByPaths(defaultExpectedRole
								.getAllowedPermissions()
								.stream()
								.filter(it -> FeatureNodeType.API.equals(it.getNodeType()))
								.map(PacificApiPermissionKey::getNodePath)
								.collect(Collectors.toSet()))
								.stream())
						.toList();
				assertThat(actualRoleDetail.getPermissions()
						.stream()
						.map(FeatureNode::getExternalId)
						.toList(), Matchers.allOf(Matchers.hasSize(expectedRolePermissions.size()), Matchers
								.containsInAnyOrder(expectedRolePermissions.stream()
										.map(FeatureNode::getExternalId)
										.toArray())));
			});

		});

	}
}
