{"openapi": "3.1.0", "info": {"title": "store-service", "description": "Build time: unknown", "contact": {"name": "<EMAIL>", "email": "<EMAIL>"}, "version": "@project.version@"}, "servers": [{"url": "http://localhost:9204", "description": "Generated server url"}, {"url": "http://pacific-ii-sit.styl.solutions"}], "paths": {"/api/store/stores/{id}": {"get": {"tags": ["store-controller"], "operationId": "getStore", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/StoreResponse"}}}}}}, "put": {"tags": ["store-controller"], "operationId": "updateStore", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateStoreRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/StoreResponse"}}}}}}, "delete": {"tags": ["store-controller"], "operationId": "deleteStore", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK"}}}}, "/api/store/stores/{id}/suspend": {"put": {"tags": ["store-controller"], "operationId": "suspendStore", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuspendStoreRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/StoreResponse"}}}}}}}, "/api/store/stores/{id}/active": {"put": {"tags": ["store-controller"], "operationId": "activeStore", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/StoreResponse"}}}}}}}, "/api/store/staffs/{staffId}/pin/reset": {"put": {"tags": ["staff-controller"], "operationId": "resetPinCode", "parameters": [{"name": "staffId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPinCodeRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK"}}}}, "/api/store/staffs/{id}": {"get": {"tags": ["staff-controller"], "operationId": "getStaff", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/StaffResponse"}}}}}}, "put": {"tags": ["staff-controller"], "operationId": "updateStaff", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateStaffRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/StaffResponse"}}}}}}}, "/api/store/staffs/{id}/inactivate": {"put": {"tags": ["staff-controller"], "operationId": "inActivateStaff", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/StaffResponse"}}}}}}}, "/api/store/staffs/{id}/archive": {"put": {"tags": ["staff-controller"], "operationId": "archiveStaff", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK"}}}}, "/api/store/staffs/{id}/activate": {"put": {"tags": ["staff-controller"], "operationId": "activateStaff", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/StaffResponse"}}}}}}}, "/api/store/devices/{id}": {"get": {"tags": ["device-controller"], "operationId": "getDevice", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DeviceResponse"}}}}}}, "put": {"tags": ["device-controller"], "operationId": "updateDevice", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDeviceRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DeviceResponse"}}}}}}}, "/api/store/devices/{deviceId}/unassign": {"put": {"tags": ["device-controller"], "operationId": "unassignDevice", "parameters": [{"name": "deviceId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnassignDeviceRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK"}}}}, "/api/store/devices/{deviceId}/ping": {"put": {"tags": ["device-controller"], "operationId": "ping", "parameters": [{"name": "deviceId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PingDeviceRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK"}}}}, "/api/store/devices/{deviceId}/assign": {"put": {"tags": ["device-controller"], "operationId": "assignDevice", "parameters": [{"name": "deviceId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssignDeviceRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK"}}}}, "/api/store/cash-floats/{id}": {"get": {"tags": ["cash-float-controller"], "operationId": "getCashFloat", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CashFloatResponse"}}}}}}, "put": {"tags": ["cash-float-controller"], "operationId": "updateCashFloat", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCashFloatRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CashFloatResponse"}}}}}}}, "/api/store/stores": {"get": {"tags": ["store-controller"], "operationId": "findStores", "parameters": [{"name": "filter.storeIds", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "filter.tenantId", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "filter.name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.statuses", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["PENDING", "ACTIVE", "SUSPENDED", "TERMINATED"]}}}, {"name": "filter.location", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.email", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.phone", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.migrationId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "sortDirection", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortFields", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ListStoresResponse"}}}}}}, "post": {"tags": ["store-controller"], "operationId": "createStore", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateStoreRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/StoreResponse"}}}}}}}, "/api/store/staffs": {"get": {"tags": ["staff-controller"], "operationId": "find<PERSON>taff", "parameters": [{"name": "filter.tenantId", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "filter.storeId", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "filter.staffId", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "filter.name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.cardId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.statuses", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "ARCHIVED"]}}}, {"name": "filter.type", "in": "query", "required": false, "schema": {"type": "string", "enum": ["CASHIER", "SUPERVISOR"]}}, {"name": "filter.staffCode", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.staffCodeEq", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.email", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "sortDirection", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortFields", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ListStaffResponse"}}}}}}, "post": {"tags": ["staff-controller"], "operationId": "createStaff", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateStaffRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/StaffResponse"}}}}}}}, "/api/store/staffs/pin/verify": {"post": {"tags": ["staff-controller"], "operationId": "verifyStaff", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyStaffRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/StaffResponse"}}}}}}}, "/api/store/staffs/card/verify": {"post": {"tags": ["staff-controller"], "operationId": "verifyStaffCard", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyStaffCardRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/StaffResponse"}}}}}}}, "/api/store/devices/{deviceId}/init-session": {"post": {"tags": ["device-controller"], "operationId": "initDeviceSession", "parameters": [{"name": "deviceId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InitDeviceRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DeviceSessionResponse"}}}}}}}, "/api/store/devices/{deviceId}/deactivate": {"post": {"tags": ["device-controller"], "operationId": "deactivateDevice", "parameters": [{"name": "deviceId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DeviceResponse"}}}}}}}, "/api/store/devices/{deviceId}/activate": {"post": {"tags": ["device-controller"], "operationId": "activateDevice", "parameters": [{"name": "deviceId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DeviceResponse"}}}}}}}, "/api/store/cash-floats": {"get": {"tags": ["cash-float-controller"], "operationId": "findCashFloat", "parameters": [{"name": "filter.storeId", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "filter.staffId", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "filter.staffName", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.staffCode", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.fromTime", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "filter.toTime", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "sortDirection", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortFields", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ListCashFloatResponse"}}}}}}, "post": {"tags": ["cash-float-controller"], "operationId": "createCashFloat", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCashFloatRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CashFloatResponse"}}}}}}}, "/api/store/stores/{storeId}/devices/{deviceId}/setting": {"get": {"tags": ["device-controller"], "operationId": "getDeviceSetting", "parameters": [{"name": "storeId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "deviceId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DeviceSettingResponse"}}}}}}}, "/api/store/staffs/assignments": {"get": {"tags": ["staff-controller"], "operationId": "getStaffAssignment", "parameters": [{"name": "request", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/StaffAssignmentRequest"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StaffAssignmentResponse"}}}}}}}}, "/api/store/devices": {"get": {"tags": ["device-controller"], "operationId": "findDevices", "parameters": [{"name": "filter.storeId", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "filter.tenantId", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "filter.name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.deviceIds", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "filter.serialNumber", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.type", "in": "query", "required": false, "schema": {"type": "string", "enum": ["POS", "KDS", "QMS", "SOK", "TOP_UP_KIOSK"]}}, {"name": "filter.statuses", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "BROKEN", "AVAILABLE"]}}}, {"name": "filter.model", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.firmwareVersion", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.lastOnlineAfter", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "sortDirection", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortFields", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ListDevicesResponse"}}}}}}}, "/api/store/devices/session/{sessionId}": {"get": {"tags": ["device-controller"], "operationId": "getDeviceSession", "parameters": [{"name": "sessionId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DeviceSessionResponse"}}}}}}}}, "components": {"schemas": {"ErrorResponse": {"type": "object", "properties": {"error": {"type": "string"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "string"}}}}, "UpdateStoreRequest": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}, "email": {"type": "string"}, "phoneNumber": {"type": "string"}, "addressLine1": {"type": "string"}, "addressLine2": {"type": "string"}, "city": {"type": "string"}, "country": {"type": "string"}, "postalCode": {"type": "string"}, "workingHour": {"type": "string"}}}, "StoreResponse": {"type": "object", "properties": {"storeId": {"type": "integer", "format": "int64"}, "tenantId": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "email": {"type": "string"}, "phoneNumber": {"type": "string"}, "addressLine1": {"type": "string"}, "addressLine2": {"type": "string"}, "city": {"type": "string"}, "country": {"type": "string"}, "status": {"type": "string", "enum": ["PENDING", "ACTIVE", "SUSPENDED", "TERMINATED"]}, "postalCode": {"type": "string"}, "workingHour": {"type": "string"}, "createdAt": {"type": "integer", "format": "int64"}, "migrationId": {"type": "string"}}}, "SuspendStoreRequest": {"type": "object", "properties": {"suspendType": {"type": "string", "enum": ["RENOVATION_OR_RELOCATION", "SEASONAL_OR_TEMPORARY_CLOSURES", "NON_COMPLIANCE_OR_REGULATORY_ISSUES"]}, "note": {"type": "string"}}, "required": ["suspendType"]}, "ResetPinCodeRequest": {"type": "object", "properties": {"pinCode": {"type": "string"}}, "required": ["pinCode"]}, "UpdateStaffRequest": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}, "storeIds": {"type": "array", "items": {"type": "string"}}, "type": {"type": "string", "enum": ["CASHIER", "SUPERVISOR"]}, "cardId": {"type": "string"}, "email": {"type": "string"}}, "required": ["type"]}, "StaffResponse": {"type": "object", "properties": {"staffId": {"type": "integer", "format": "int64"}, "tenantId": {"type": "integer", "format": "int64"}, "cardId": {"type": "string"}, "staffCode": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "type": {"type": "string", "enum": ["CASHIER", "SUPERVISOR"]}, "status": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "ARCHIVED"]}, "createdAt": {"type": "integer", "format": "int64"}}}, "UpdateDeviceRequest": {"type": "object", "properties": {"storeId": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "receiptFooter": {"type": "string"}, "receiptHeader": {"type": "string"}}, "required": ["storeId"]}, "DeviceResponse": {"type": "object", "properties": {"deviceId": {"type": "string"}, "storeId": {"type": "integer", "format": "int64"}, "tenantId": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "serialNumber": {"type": "string"}, "type": {"type": "string", "enum": ["POS", "KDS", "QMS", "SOK", "TOP_UP_KIOSK"]}, "status": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "BROKEN", "AVAILABLE"]}, "firmwareVersion": {"type": "string"}, "osVersion": {"type": "string"}, "model": {"type": "string"}, "assignedDate": {"type": "integer", "format": "int64"}, "lastOnline": {"type": "integer", "format": "int64"}, "createdAt": {"type": "integer", "format": "int64"}}}, "UnassignDeviceRequest": {"type": "object", "properties": {"tenantId": {"type": "integer", "format": "int64"}, "storeId": {"type": "integer", "format": "int64"}}, "required": ["storeId", "tenantId"]}, "PingDeviceRequest": {"type": "object", "properties": {"storeId": {"type": "string", "minLength": 1}}}, "AssignDeviceRequest": {"type": "object", "properties": {"tenantId": {"type": "integer", "format": "int64"}, "storeId": {"type": "integer", "format": "int64"}}, "required": ["storeId", "tenantId"]}, "UpdateCashFloatRequest": {"type": "object", "properties": {"staffId": {"type": "integer", "format": "int64"}, "storeId": {"type": "integer", "format": "int64"}, "deviceId": {"type": "string", "minLength": 1}, "closingAmount": {"type": "integer"}, "desiredAmount": {"type": "integer"}, "closingTime": {"type": "integer", "format": "int64"}, "reason": {"type": "string"}}, "required": ["closingTime", "staffId", "storeId"]}, "CashFloatResponse": {"type": "object", "properties": {"cashFloatId": {"type": "integer", "format": "int64"}, "tenantId": {"type": "integer", "format": "int64"}, "storeId": {"type": "integer", "format": "int64"}, "deviceId": {"type": "string"}, "staffId": {"type": "integer", "format": "int64"}, "staffName": {"type": "string"}, "staffCode": {"type": "string"}, "openingAmount": {"type": "integer"}, "closingAmount": {"type": "integer"}, "desiredAmount": {"type": "integer"}, "openingTime": {"type": "integer", "format": "int64"}, "closingTime": {"type": "integer", "format": "int64"}, "currency": {"$ref": "#/components/schemas/CurrencyResponse"}, "reason": {"type": "string"}}}, "CurrencyResponse": {"type": "object", "properties": {"displayName": {"type": "string"}, "numericCode": {"type": "integer", "format": "int32"}, "currencyCode": {"type": "string"}, "symbol": {"type": "string"}, "fractionDigits": {"type": "integer", "format": "int32"}}}, "CreateStoreRequest": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}, "email": {"type": "string"}, "phoneNumber": {"type": "string"}, "addressLine1": {"type": "string"}, "addressLine2": {"type": "string"}, "city": {"type": "string"}, "country": {"type": "string"}, "postalCode": {"type": "string"}, "workingHour": {"type": "string"}, "migrationId": {"type": "string"}}}, "CreateStaffRequest": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}, "staffCode": {"type": "string", "minLength": 1}, "pinCode": {"type": "string", "minLength": 1}, "storeIds": {"type": "array", "items": {"type": "string"}}, "type": {"type": "string", "enum": ["CASHIER", "SUPERVISOR"]}, "cardId": {"type": "string"}, "email": {"type": "string"}}, "required": ["type"]}, "VerifyStaffRequest": {"type": "object", "properties": {"staffCode": {"type": "string", "minLength": 1}, "pinCode": {"type": "string", "minLength": 1}, "storeId": {"type": "integer", "format": "int64"}}, "required": ["storeId"]}, "VerifyStaffCardRequest": {"type": "object", "properties": {"cardId": {"type": "string", "minLength": 1}, "storeId": {"type": "integer", "format": "int64"}}, "required": ["storeId"]}, "InitDeviceRequest": {"type": "object", "properties": {"type": {"type": "string", "enum": ["POS", "KDS", "QMS", "SOK", "TOP_UP_KIOSK"]}, "firmwareVersion": {"type": "string"}, "osVersion": {"type": "string"}}, "required": ["type"]}, "DeviceSessionResponse": {"type": "object", "properties": {"sessionId": {"type": "string"}, "deviceId": {"type": "string"}, "storeId": {"type": "integer", "format": "int64"}, "tenantId": {"type": "integer", "format": "int64"}, "firmwareVersion": {"type": "string"}, "osVersion": {"type": "string"}, "status": {"type": "string"}}}, "CreateCashFloatRequest": {"type": "object", "properties": {"storeId": {"type": "integer", "format": "int64"}, "staffId": {"type": "integer", "format": "int64"}, "deviceId": {"type": "string", "minLength": 1}, "openingAmount": {"type": "integer"}, "closingAmount": {"type": "integer"}, "desiredAmount": {"type": "integer"}, "currency": {"type": "string"}, "reason": {"type": "string"}, "openingTime": {"type": "integer", "format": "int64"}, "closingTime": {"type": "integer", "format": "int64"}}, "required": ["staffId", "storeId"]}, "ListStoresResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/StoreResponse"}}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"type": "string"}}}}, "DeviceSettingResponse": {"type": "object", "properties": {"type": {"type": "string", "enum": ["POS", "KDS", "QMS", "SOK", "TOP_UP_KIOSK"]}, "deviceId": {"type": "string"}, "deviceName": {"type": "string"}, "storeId": {"type": "integer", "format": "int64"}, "tenantId": {"type": "integer", "format": "int64"}, "storeName": {"type": "string"}, "email": {"type": "string"}, "phoneNumber": {"type": "string"}, "addressLine1": {"type": "string"}, "addressLine2": {"type": "string"}, "city": {"type": "string"}, "country": {"type": "string"}, "postalCode": {"type": "string"}, "receiptHeader": {"type": "string"}, "receiptFooter": {"type": "string"}}}, "ListStaffResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/StaffResponse"}}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"type": "string"}}}}, "StaffAssignmentRequest": {"type": "object", "properties": {"staffIds": {"type": "array", "items": {"type": "string"}, "minItems": 1}}}, "StaffAssignmentResponse": {"type": "object", "properties": {"staffId": {"type": "string"}, "storeId": {"type": "string"}}}, "ListDevicesResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/DeviceResponse"}}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"type": "string"}}}}, "ListCashFloatResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/CashFloatResponse"}}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"type": "string"}}}}}}}