CREATE
    TABLE
        tb_feedback_email(
            id BIGINT PRIMARY KEY NOT NULL,
            tenant_id BIGINT NOT NULL,
            email VARCHAR(255),
            created_at TIMESTAMP(6) WITH TIME ZONE NOT NULL,
            updated_at TIMESTAMP(6) WITH TIME ZONE NOT NULL,
            deleted_at TIMESTAMP(6) WITH TIME ZONE
        );

CREATE
    UNIQUE INDEX IF NOT EXISTS tb_feedback_email_tenant_id_uidx ON
    tb_feedback_email(tenant_id);