/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.data.access.entity;

import com.styl.pacific.data.access.entity.AuditableEntity;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.Objects;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */

@Getter
@Setter
@Entity
@NoArgsConstructor
@Table(name = "tb_tenant_tax")
public class TaxEntity extends AuditableEntity {
	@Id
	private Long id;
	private Long tenantId;
	private String name;
	private String taxRegNo;
	private BigDecimal rate;
	private String description;
	private Boolean enabled;
	private Boolean includeInPrice;
	private Instant effectiveDate;

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (!(o instanceof TaxEntity taxEntity))
			return false;
		if (!super.equals(o))
			return false;
		return Objects.equals(id, taxEntity.id) && Objects.equals(tenantId, taxEntity.tenantId) && Objects.equals(name,
				taxEntity.name) && Objects.equals(taxRegNo, taxEntity.taxRegNo) && Objects.equals(rate, taxEntity.rate)
				&& Objects.equals(description, taxEntity.description) && Objects.equals(enabled, taxEntity.enabled)
				&& Objects.equals(includeInPrice, taxEntity.includeInPrice) && Objects.equals(effectiveDate,
						taxEntity.effectiveDate);
	}

	@Override
	public int hashCode() {
		return Objects.hash(super.hashCode(), id, tenantId, name, taxRegNo, rate, description, enabled, includeInPrice,
				effectiveDate);
	}
}
