/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.data.access.specification;

import com.styl.pacific.data.access.jpa.specification.BaseSpecification;
import com.styl.pacific.tenant.service.data.access.entity.TenantDomainNameEntity;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import lombok.Builder;

/**
 * <AUTHOR>
 */
@Builder
public class TenantDomainNameEntitySpecification extends BaseSpecification<TenantDomainNameEntity> {

	private Long tenantId;
	private String tenantName;
	private String domainName;
	private List<String> statuses;

	@Override
	public Predicate toPredicate(Root<TenantDomainNameEntity> root, CriteriaQuery<?> query,
			CriteriaBuilder criteriaBuilder) {
		List<Predicate> predicates = new ArrayList<>();

		if (isNotBlank(domainName)) {
			predicates.add(like(criteriaBuilder, root.get("domainName"), domainName));
		}

		if (isNotNull(tenantId)) {
			predicates.add(equals(criteriaBuilder, root.get("tenant")
					.get("id"), tenantId));
		}

		if (isNotBlank(tenantName)) {
			predicates.add(like(criteriaBuilder, root.get("tenant")
					.get("name"), tenantName));
		}

		if (isNotEmpty(statuses)) {
			predicates.add(inStringList(root.get("status"), statuses));
		}

		return and(criteriaBuilder, predicates);
	}
}
