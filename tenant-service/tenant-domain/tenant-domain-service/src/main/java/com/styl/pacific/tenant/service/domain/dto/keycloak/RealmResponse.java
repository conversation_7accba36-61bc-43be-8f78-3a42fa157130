/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.domain.dto.keycloak;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RealmResponse {

	private String realm;
	private String displayName;
	private boolean enabled;
	private String loginTheme;
	private boolean registrationAllowed;
	private boolean registrationEmailAsUsername;
	private boolean rememberMe;
	private boolean resetPasswordAllowed;
	private boolean verifyEmail;
	private String browserFlow;
	private String registrationFlow;
	private List<String> eventsListeners;
	private List<ClientDTO> clients;
	private List<AuthenticationFlowDTO> authenticationFlows;
	private List<AuthenticatorConfigDTO> authenticatorConfig;
	private List<RequiredAction> requiredActions;
	private Components components;

}
