/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.domain.dto;

import com.styl.pacific.common.validator.currency.Currency;
import com.styl.pacific.common.validator.dateformat.DateFormat;
import com.styl.pacific.common.validator.timeformat.TimeFormat;
import com.styl.pacific.common.validator.timezone.TimeZone;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class UpdateTenantSettingsCommand {

	@NotNull
	private final Long tenantId;

	@NotBlank
	private final String defaultDomain;

	@NotBlank
	@Currency
	private final String currency;

	@TimeZone
	@NotBlank
	@Length(max = 100)
	private final String timeZone;

	@NotBlank
	@DateFormat
	private final String dateFormat;

	@NotBlank
	@TimeFormat
	private final String timeFormat;

}
