/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.tenant.service.domain.dto.UpdateTenantCommand;
import com.styl.pacific.tenant.service.rest.mapper.TenantControllerMapper;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.UpdateTenantRequest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class TenantControllerMapperTest {

	@Test
	void testUpdateTenantRequestToUpdateTenantCommandWhenValidRequestThenReturnCommand() {
		// Arrange
		UpdateTenantRequest request = UpdateTenantRequest.builder()
				.name("Updated Tenant")
				.email("<EMAIL>")
				.phoneNumber("0987654321")
				.addressLine1("456 Updated St")
				.addressLine2("Apt 5")
				.city("Updated City")
				.country("Updated Country")
				.postalCode("54321")
				.build();

		TenantId tenantId = new TenantId(1L);

		// Act
		UpdateTenantCommand result = TenantControllerMapper.INSTANCE.toUpdateTenantCommand(request, tenantId);

		// Assert
		assertNotNull(result);
		assertEquals(tenantId.getValue(), result.getTenantId());
		assertEquals(request.getName(), result.getName());
		assertEquals(request.getEmail(), result.getEmail());
		assertEquals(request.getPhoneNumber(), result.getPhoneNumber());
		assertEquals(request.getAddressLine1(), result.getAddressLine1());
		assertEquals(request.getAddressLine2(), result.getAddressLine2());
		assertEquals(request.getCity(), result.getCity());
		assertEquals(request.getCountry(), result.getCountry());
		assertEquals(request.getPostalCode(), result.getPostalCode());
	}

	@Test
	void testUpdateTenantRequestToUpdateTenantCommandWhenNullAddressLine2ThenReturnCommand() {
		// Arrange
		UpdateTenantRequest request = UpdateTenantRequest.builder()
				.name("Updated Tenant")
				.email("<EMAIL>")
				.phoneNumber("0987654321")
				.addressLine1("456 Updated St")
				.addressLine2(null)
				.city("Updated City")
				.country("Updated Country")
				.postalCode("54321")
				.build();

		TenantId tenantId = new TenantId(1L);

		// Act
		UpdateTenantCommand result = TenantControllerMapper.INSTANCE.toUpdateTenantCommand(request, tenantId);

		// Assert
		assertNotNull(result);
		assertEquals(tenantId.getValue(), result.getTenantId());
		assertEquals(request.getName(), result.getName());
		assertEquals(request.getEmail(), result.getEmail());
		assertEquals(request.getPhoneNumber(), result.getPhoneNumber());
		assertEquals(request.getAddressLine1(), result.getAddressLine1());
		assertEquals(request.getAddressLine2(), result.getAddressLine2());
		assertEquals(request.getCity(), result.getCity());
		assertEquals(request.getCountry(), result.getCountry());
		assertEquals(request.getPostalCode(), result.getPostalCode());
	}

	@Test
	void testUpdateTenantRequestToUpdateTenantCommandWhenEmptyPostalCodeThenReturnCommand() {
		// Arrange
		UpdateTenantRequest request = UpdateTenantRequest.builder()
				.name("Updated Tenant")
				.email("<EMAIL>")
				.phoneNumber("0987654321")
				.addressLine1("456 Updated St")
				.addressLine2("Apt 5")
				.city("Updated City")
				.country("Updated Country")
				.postalCode("")
				.build();

		TenantId tenantId = new TenantId(1L);

		// Act
		UpdateTenantCommand result = TenantControllerMapper.INSTANCE.toUpdateTenantCommand(request, tenantId);

		// Assert
		assertNotNull(result);
		assertEquals(tenantId.getValue(), result.getTenantId());
		assertEquals(request.getName(), result.getName());
		assertEquals(request.getEmail(), result.getEmail());
		assertEquals(request.getPhoneNumber(), result.getPhoneNumber());
		assertEquals(request.getAddressLine1(), result.getAddressLine1());
		assertEquals(request.getAddressLine2(), result.getAddressLine2());
		assertEquals(request.getCity(), result.getCity());
		assertEquals(request.getCountry(), result.getCountry());
		assertEquals(request.getPostalCode(), result.getPostalCode());
	}

	@Test
	void testUpdateTenantRequestToUpdateTenantCommandWhenNullRequestThenReturnNull() {
		// Act
		UpdateTenantCommand result = TenantControllerMapper.INSTANCE.toUpdateTenantCommand(null, null);

		// Assert
		Assertions.assertNull(result);
	}

}