/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.domain.entity;

import static org.assertj.core.api.Assertions.assertThat;

import com.styl.pacific.domain.valueobject.Address;
import com.styl.pacific.domain.valueobject.TenantId;
import java.time.Instant;
import java.util.TimeZone;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class TenantTest {

	private final String name = "Test Tenant";
	private final String email = "<EMAIL>";
	private final String phoneNumber = "**********";
	private final Address address = Address.builder()
			.addressLine1("123 Main St")
			.addressLine2("Apt 4B")
			.city("Test City")
			.country("Test Country")
			.postalCode("12345")
			.build();
	private final TimeZone timeZone = TimeZone.getTimeZone("UTC");
	private final Instant createdAt = Instant.now();
	private final Instant updatedAt = Instant.now();
	private final TenantId tenantId = new TenantId(1L);
	private Tenant tenant;
	private Tenant tenantWithNullName;

	@BeforeEach
	public void setUp() {
		tenant = Tenant.builder()
				.name(name)
				.email(email)
				.phoneNumber(phoneNumber)
				.address(address)
				.createdAt(createdAt)
				.updatedAt(updatedAt)
				.id(tenantId)
				.build();

		tenantWithNullName = Tenant.builder()
				.name(null)
				.email(email)
				.phoneNumber(phoneNumber)
				.address(address)
				.createdAt(createdAt)
				.updatedAt(updatedAt)
				.id(tenantId)
				.build();
	}

	@Test
	void testGetNameWhenNameIsSetThenReturnCorrectName() {
		// Act
		String result = tenant.getName();

		// Assert
		assertThat(result).isEqualTo(name);
	}

	@Test
	void testGetNameWhenNameIsNullThenReturnNull() {
		// Act
		String result = tenantWithNullName.getName();

		// Assert
		assertThat(result).isNull();
	}

	@Test
	void testGetEmailWhenEmailIsSetThenReturnCorrectEmail() {
		// Act
		String result = tenant.getEmail();

		// Assert
		assertThat(result).isEqualTo(email);
	}

	@Test
	void testGetPhoneNumberWhenPhoneNumberIsSetThenReturnCorrectPhoneNumber() {
		// Act
		String result = tenant.getPhoneNumber();

		// Assert
		assertThat(result).isEqualTo(phoneNumber);
	}

	@Test
	void testGetAddressWhenAddressIsSetThenReturnCorrectAddress() {
		// Act
		Address result = tenant.getAddress();

		// Assert
		assertThat(result).isEqualTo(address);
	}

	@Test
	void testGetCreatedAtWhenCreatedAtIsSetThenReturnCorrectCreatedAt() {
		// Act
		Instant result = tenant.getCreatedAt();

		// Assert
		assertThat(result).isEqualTo(createdAt);
	}

	@Test
	void testGetUpdatedAtWhenUpdatedAtIsSetThenReturnCorrectUpdatedAt() {
		// Act
		Instant result = tenant.getUpdatedAt();

		// Assert
		assertThat(result).isEqualTo(updatedAt);
	}

	@Test
	void testEqualsWhenDifferentObjectThenReturnFalse() {
		// Arrange
		Tenant anotherTenant = Tenant.builder()
				.name("Another Tenant")
				.email("<EMAIL>")
				.phoneNumber("0987654321")
				.address(address)
				.createdAt(createdAt)
				.updatedAt(updatedAt)
				.id(new TenantId(2L))
				.build();

		// Act & Assert
		assertThat(tenant).isNotEqualTo(anotherTenant);
	}

	@Test
	void testEqualsWhenNullThenReturnFalse() {
		// Act & Assert
		assertThat(tenant).isNotEqualTo(null);
	}

	@Test
	void testEqualsWhenDifferentClassThenReturnFalse() {
		// Act & Assert
		assertThat(tenant).isNotEqualTo(new Object());
	}

	@Test
	void testEqualsWhenSameFieldsThenReturnTrue() {
		// Arrange
		Tenant anotherTenant = Tenant.builder()
				.name(name)
				.email(email)
				.phoneNumber(phoneNumber)
				.address(address)
				.createdAt(createdAt)
				.updatedAt(updatedAt)
				.id(tenantId)
				.build();

		// Act & Assert
		assertThat(tenant).isEqualTo(anotherTenant);
	}
}
