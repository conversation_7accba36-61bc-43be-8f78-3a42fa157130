/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.tenant.service.domain.KeycloakRealmGenerator;
import com.styl.pacific.tenant.service.domain.config.KeycloakConfigProperties;
import com.styl.pacific.tenant.service.domain.dto.GetTenantQuery;
import com.styl.pacific.tenant.service.domain.dto.keycloak.ClientDTO;
import com.styl.pacific.tenant.service.domain.dto.keycloak.ProtocolMapperDTO;
import com.styl.pacific.tenant.service.domain.dto.keycloak.RealmResponse;
import com.styl.pacific.tenant.service.domain.entity.Tenant;
import com.styl.pacific.tenant.service.domain.exception.TenantNotFoundException;
import com.styl.pacific.tenant.service.domain.output.repository.TenantRepository;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class KeycloakRealmGeneratorTest {

	private final TenantRepository tenantRepository;

	private final KeycloakRealmGenerator keycloakRealmGenerator;

	private final Tenant tenant;

	public KeycloakRealmGeneratorTest(@Mock TenantRepository tenantRepository) {
		this.tenantRepository = tenantRepository;
		tenant = Tenant.builder()
				.id(new TenantId(1L))
				.realmId("realm-01")
				.name("Test Tenant")
				.email("<EMAIL>")
				.phoneNumber("1234567890")
				.address(null)
				.createdAt(Instant.now())
				.updatedAt(Instant.now())
				.build();

		KeycloakConfigProperties keycloakConfigProperties = new KeycloakConfigProperties();
		KeycloakConfigProperties.ProtocolMapper protocolMapper = new KeycloakConfigProperties.ProtocolMapper();
		protocolMapper.setId("protocol-mapper-id");
		protocolMapper.setName("protocol-mapper-name");
		protocolMapper.setUri("protocol-mapper-uri");

		KeycloakConfigProperties.URIProperty invitationValidation = new KeycloakConfigProperties.URIProperty();
		invitationValidation.setUri("invitation-validation-uri");

		KeycloakConfigProperties.Theme theme = new KeycloakConfigProperties.Theme();
		theme.setLogin("login-theme");

		keycloakConfigProperties.setEventListeners(List.of("event-listener-1", "event-listener-2"));
		keycloakConfigProperties.setProtocolMapper(protocolMapper);
		keycloakConfigProperties.setInvitationValidation(invitationValidation);
		keycloakConfigProperties.setTheme(theme);

		keycloakRealmGenerator = new KeycloakRealmGenerator(tenantRepository, keycloakConfigProperties,
				new ObjectMapper());
	}

	@Test
	void testGenerateWhenValidTenantIdThenReturnRealmProfileResponse() throws JsonProcessingException {
		// Arrange
		when(tenantRepository.getTenant(any(GetTenantQuery.class))).thenReturn(Optional.of(tenant));

		// Act
		String response = keycloakRealmGenerator.generate(1L);
		RealmResponse result = new ObjectMapper().readValue(response, RealmResponse.class);

		// Assert
		assertNotNull(result);
		assertEquals("realm-01", result.getRealm());
		assertEquals("Test Tenant", result.getDisplayName());
		assertTrue(result.isEnabled());
		assertEquals("login-theme", result.getLoginTheme());
		assertTrue(result.isRegistrationAllowed());
		assertTrue(result.isRegistrationEmailAsUsername());
		assertTrue(result.isRememberMe());
		assertTrue(result.isResetPasswordAllowed());
		assertEquals(KeycloakRealmGenerator.REGISTRATION_FLOW_WITH_INVITATION, result.getRegistrationFlow());
		assertEquals(List.of("event-listener-1", "event-listener-2"), result.getEventsListeners());
		assertClients(result);
		assertNotNull(result.getAuthenticationFlows());
		assertNotNull(result.getAuthenticatorConfig());
	}

	private void assertClients(RealmResponse response) {
		assertNotNull(response.getClients());
		assertEquals(2, response.getClients()
				.size());
		ClientDTO client1 = response.getClients()
				.getFirst();
		assertEquals("customer-portal", client1.getClientId());
		assertEquals("Customer Portal", client1.getName());
		assertEquals(List.of("*"), client1.getRedirectUris());
		ProtocolMapperDTO protocolMapper1 = client1.getProtocolMappers()
				.getFirst();
		assertNotNull(protocolMapper1);
		assertEquals("protocol-mapper-id", protocolMapper1.getProtocolMapper());
		assertEquals("protocol-mapper-name", protocolMapper1.getName());
		assertEquals("protocol-mapper-uri", protocolMapper1.getConfig()
				.get("URI"));

		ClientDTO client2 = response.getClients()
				.getLast();
		assertEquals("go-app", client2.getClientId());
		assertEquals("Go App", client2.getName());
		assertEquals(List.of("*"), client2.getRedirectUris());
		ProtocolMapperDTO protocolMapper2 = client2.getProtocolMappers()
				.getFirst();
		assertNotNull(protocolMapper2);
		assertEquals("protocol-mapper-id", protocolMapper2.getProtocolMapper());
		assertEquals("protocol-mapper-name", protocolMapper2.getName());
		assertEquals("protocol-mapper-uri", protocolMapper2.getConfig()
				.get("URI"));
	}

	@Test
	void testGenerateWhenTenantNotFoundThenThrowTenantNotFoundException() {
		// Arrange
		when(tenantRepository.getTenant(any(GetTenantQuery.class))).thenReturn(Optional.empty());

		// Act & Assert
		assertThrows(TenantNotFoundException.class, () -> keycloakRealmGenerator.generate(1L));
	}

}