/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.messaging.publisher;

import com.styl.pacific.kafka.notification.avro.model.NotificationAvroChannel;
import com.styl.pacific.kafka.notification.avro.model.NotificationCreatedAvroEvent;
import com.styl.pacific.notification.publisher.ApplicationNotificationEventPublisher;
import com.styl.pacific.notification.service.constant.Action;
import com.styl.pacific.tenant.service.domain.entity.Tenant;
import com.styl.pacific.tenant.service.domain.output.publisher.TenantNotificationQueuePublisher;
import com.styl.pacific.user.shared.http.users.response.UserResponse;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class TenantNotificationPublisher implements TenantNotificationQueuePublisher {
	private final ApplicationNotificationEventPublisher publisher;

	@Override
	public void publishSubmitTenantEvent(Tenant tenant, List<UserResponse> list) {
		for (UserResponse user : list) {
			NotificationCreatedAvroEvent notificationCreatedAvroEvent = NotificationCreatedAvroEvent.newBuilder()
					.setId(UUID.randomUUID())
					.setUserId(Long.valueOf(user.getId()))
					.setTenantId(tenant.getId()
							.getValue())
					.setAction(Action.TENANT_SUBMIT_REVIEW)
					.setSource("tenant-service")
					.setChannels(List.of(NotificationAvroChannel.IN_APP))
					.setData(Map.of("id", tenant.getId()
							.toString(), "name", tenant.getName()))
					.setCreatedAt(Instant.now()
							.toEpochMilli())
					.build();
			publisher.publish(notificationCreatedAvroEvent);
		}
	}

	@Override
	public void publishActivateTenantEvent(Tenant tenant, List<UserResponse> list) {
		for (UserResponse user : list) {
			NotificationCreatedAvroEvent notificationCreatedAvroEvent = NotificationCreatedAvroEvent.newBuilder()
					.setId(UUID.randomUUID())
					.setUserId(Long.valueOf(user.getId()))
					.setTenantId(tenant.getId()
							.getValue())
					.setAction(Action.WELCOME_TENANT)
					.setSource("tenant-service")
					.setChannels(List.of(NotificationAvroChannel.IN_APP))
					.setData(Map.of("id", tenant.getId()
							.toString(), "name", tenant.getName()))
					.setCreatedAt(Instant.now()
							.toEpochMilli())
					.build();
			publisher.publish(notificationCreatedAvroEvent);
		}
	}
}