/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.shared.http.api.dto.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.styl.pacific.domain.dto.CountryResponse;
import com.styl.pacific.domain.dto.FileResponse;
import java.util.List;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@Builder
public class TenantInfoResponse {

	@JsonSerialize(using = ToStringSerializer.class)
	private final Long tenantId;

	private final String name;

	private final String businessType;

	private final String status;

	private final FileResponse logo;

	private final String realmId;

	private final String email;

	private final String phoneNumber;

	private final String contactRemarks;

	private final String addressLine1;

	private final String addressLine2;

	private final String city;

	private final CountryResponse country;

	private final String postalCode;

	private final TenantSettingsResponse settings;

	private final List<BusinessFeatureResponse> businessFeatures;

}
