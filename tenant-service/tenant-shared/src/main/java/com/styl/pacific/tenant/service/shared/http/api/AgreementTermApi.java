/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.shared.http.api;

import com.styl.pacific.domain.permissions.PacificApiAuthorized;
import com.styl.pacific.domain.permissions.PacificApiPermissionKey;
import com.styl.pacific.domain.permissions.PlatformApiSecurityLevel;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.SaveAgreementTermRequest;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.AgreementTermResponse;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface AgreementTermApi {
	@GetMapping(path = "/api/tenant/agreement-terms")
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.TENANT_MGMT_TERM_CONDITION_VIEW, isAllowedCustomerAccess = true)
	AgreementTermResponse getAgreementTerm();

	@PostMapping(path = "/api/tenant/agreement-terms")
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.LABEL_TEMPLATE_MGMT_UPDATE)
	AgreementTermResponse saveAgreementTerm(@RequestBody @Valid SaveAgreementTermRequest request);
}
