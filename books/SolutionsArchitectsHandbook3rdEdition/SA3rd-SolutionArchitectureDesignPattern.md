# Solution Architecture Design Pattern

## 3-Tier Architecture
1. Web Tier
2. Application Tier
3. Database Tier

![3tierArchitecture.png](assets/3tierArchitecture.png)

## Multi-Tenancy Architecture

### Isolations
1. Database Isolation: Two tenants have separate databases **(use tenantId at database level)**
2. Table Isolation: Two tenants have separate tables **(use tenantId at table level)**
3. Row Isolation: Two tenants have separate rows **(use tenantId column)**

![MultiTenancySaaSArchitectureIsolation.png](assets/MultiTenancySaaSArchitectureIsolation.png)


## SOA Pattern (Service Oriented Architecture)
Concept: "Modular, reusable, and loosely coupled services"
- Decompose Monolithic service into smaller services
- Make small services working independently, loosely coupled, and reusable
- Parallelizable for development, deployment, operation

Cons:
- Require a robust solution for governance, security.

**There are multiple implementation of SOA**:
1. SOAP (Simple Object Access Protocol). XML-based protocol
2. RESTful service (Representational State Transfer). JSON-based protocol
2. gRPC (Google Remote Procedure Call). Protocol Buffers-based protocol


