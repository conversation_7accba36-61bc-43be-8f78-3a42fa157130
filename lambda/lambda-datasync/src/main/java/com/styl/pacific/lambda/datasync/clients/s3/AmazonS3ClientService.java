/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.lambda.datasync.clients.s3;

import com.styl.pacific.lambda.datasync.clients.s3.entities.DownloadedFileMetadata;
import com.styl.pacific.lambda.datasync.commons.domains.QueueJobMessage;
import java.nio.file.Path;

public interface AmazonS3ClientService {
	DownloadedFileMetadata downloadFileToTmp(String bucket, String objectKey);

	void uploadFolderToDirectoryKey(Path localDirectoryPath, String bucket, String targetDirectoryKey);

	String moveObjectToDirectoryKey(String bucket, String directoryKey, String objectKey);

	void pushQueueDirectory(String targetDirectoryKey, String directoryKey, QueueJobMessage build);

	boolean existObjectKey(String bucket, String key);
}
