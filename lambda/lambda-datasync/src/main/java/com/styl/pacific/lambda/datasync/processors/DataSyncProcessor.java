/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.lambda.datasync.processors;

import com.styl.pacific.lambda.datasync.chains.DataSyncChainFlowData;
import com.styl.pacific.lambda.datasync.exceptions.DataSyncDomainException;
import java.util.function.Function;

public interface DataSyncProcessor extends Function<DataSyncChainFlowData, DataSyncChainFlowData> {

	@Override
	default DataSyncChainFlowData apply(DataSyncChainFlowData data) {
		try {
			return process(data);
		} catch (DataSyncDomainException exception) {
			handleError(data, exception);
			throw exception;
		} catch (Exception exception) {
			handleError(data, exception);
			throw new DataSyncDomainException(exception.getMessage(), exception);
		}
	}

	DataSyncChainFlowData process(DataSyncChainFlowData data);

	default void handleError(DataSyncChainFlowData data, Exception exception) {
	}
}
