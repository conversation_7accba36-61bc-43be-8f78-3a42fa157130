/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.lambda.datasync.commons.domains;

import com.styl.pacific.lambda.datasync.commons.checksum.ChecksumAlgorithm;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class QueueJobMessage {
	private final Long jobId;
	private final String tenantId;
	private final String zipFileHashValue;
	private final ChecksumAlgorithm checksumAlgorithm;
	private final Boolean isValidChecksum;
	private final String archivedFileDirectory;
	private final String archivedZipFilePath;
	private final String archivedChecksumFilePath;
}
