{"namespace": "com.styl.pacific.kafka.inventory.avro.model", "type": "record", "name": "InventoryReservedAvroEvent", "fields": [{"name": "id", "type": {"type": "string", "logicalType": "uuid"}}, {"name": "tenantId", "type": {"type": "long"}}, {"name": "storeId", "type": {"type": "long"}}, {"name": "orderId", "type": {"type": "long"}}, {"name": "reservations", "type": {"type": "array", "items": {"type": "record", "name": "ProductQuantityAvro", "fields": [{"name": "productId", "type": {"type": "long"}}, {"name": "quantity", "type": {"type": "long"}}]}}}, {"name": "eventTime", "type": {"type": "long"}}]}