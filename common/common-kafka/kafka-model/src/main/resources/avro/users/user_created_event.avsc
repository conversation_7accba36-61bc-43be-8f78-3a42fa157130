{"name": "UserCreatedEvent", "namespace": "com.styl.pacific.kafka.users.avro.model", "type": "record", "fields": [{"name": "id", "type": {"type": "string", "logicalType": "uuid"}}, {"name": "userId", "type": {"type": "long"}}, {"name": "ssoId", "type": ["null", {"type": "string"}]}, {"name": "userType", "type": {"type": "enum", "symbols": ["SYSTEM_ADMIN", "BACK_OFFICE", "CUSTOMER"], "name": "UserType"}}, {"name": "userStatus", "type": {"type": "enum", "symbols": ["CREATED", "ACTIVE", "ARCHIVED", "BLOCKED", "DELETING"], "name": "UserStatus"}}, {"name": "firstName", "type": ["null", {"type": "string"}]}, {"name": "lastName", "type": ["null", {"type": "string"}]}, {"name": "email", "type": ["null", {"type": "string"}]}, {"name": "phoneNumber", "type": ["null", {"type": "string"}]}, {"name": "realmId", "type": {"type": "string"}}, {"name": "avat<PERSON><PERSON><PERSON>", "type": ["null", {"type": "string"}]}, {"name": "userNonCompletedActions", "type": ["null", {"type": "array", "items": {"type": "enum", "name": "UserNonCompletedAction", "symbols": ["KEYCLOAK_REGISTRATION"]}}]}, {"name": "permissions", "type": ["null", {"type": "array", "items": {"type": "record", "name": "UserPermission", "fields": [{"name": "tenantId", "type": "long"}, {"name": "userRoleId", "type": "long"}, {"name": "permissionStatus", "type": {"type": "enum", "symbols": ["CREATED", "ACTIVE", "IN_ACTIVE"], "name": "PermissionStatus"}}]}}]}, {"name": "createdAt", "type": "long"}, {"name": "created<PERSON>y", "type": ["null", {"type": "long"}], "default": null}, {"name": "updatedAt", "type": "long"}, {"name": "updatedBy", "type": ["null", {"type": "long"}], "default": null}]}