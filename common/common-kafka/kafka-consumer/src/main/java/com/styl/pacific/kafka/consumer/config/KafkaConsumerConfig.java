/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.kafka.consumer.config;

import com.styl.pacific.kafka.config.KafkaConfigProps;
import com.styl.pacific.kafka.config.KafkaConsumerConfigProps;
import io.micrometer.core.instrument.MeterRegistry;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import org.apache.avro.specific.SpecificRecordBase;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.core.MicrometerConsumerListener;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;

/**
 * <AUTHOR>
 */

@Configuration
public class KafkaConsumerConfig<K extends Serializable, V extends SpecificRecordBase> {

	private final KafkaConfigProps kafkaConfigProps;

	private final KafkaConsumerConfigProps kafkaConsumerConfigProps;

	private final MeterRegistry meterRegistry;

	public KafkaConsumerConfig(KafkaConfigProps kafkaConfigProps, KafkaConsumerConfigProps kafkaConsumerConfigProps,
			MeterRegistry meterRegistry) {
		this.kafkaConfigProps = kafkaConfigProps;
		this.kafkaConsumerConfigProps = kafkaConsumerConfigProps;
		this.meterRegistry = meterRegistry;
	}

	@Bean
	public Map<String, Object> consumerConfigs() {
		Map<String, Object> props = new HashMap<>();
		props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaConfigProps.getBootstrapServers());
		props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, kafkaConsumerConfigProps.getKeyDeserializer());
		props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, kafkaConsumerConfigProps.getValueDeserializer());
		props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, kafkaConsumerConfigProps.getAutoOffsetReset());
		props.put(kafkaConfigProps.getSchemaRegistryUrlKey(), kafkaConfigProps.getSchemaRegistryUrl());
		props.put(kafkaConsumerConfigProps.getSpecificAvroReaderKey(), kafkaConsumerConfigProps
				.getSpecificAvroReader());
		props.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, kafkaConsumerConfigProps.getSessionTimeoutMs());
		props.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, kafkaConsumerConfigProps.getHeartbeatIntervalMs());
		props.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, kafkaConsumerConfigProps.getMaxPollIntervalMs());
		props.put(ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG, kafkaConsumerConfigProps
				.getMaxPartitionFetchBytesDefault() * kafkaConsumerConfigProps.getMaxPartitionFetchBytesBoostFactor());
		props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, kafkaConsumerConfigProps.getMaxPollRecords());
		return props;
	}

	@Bean
	public ConsumerFactory<K, V> consumerFactory() {
		ConsumerFactory<K, V> factory = new DefaultKafkaConsumerFactory<>(consumerConfigs());
		factory.addListener(new MicrometerConsumerListener<>(meterRegistry));
		return factory;
	}

	@Bean
	public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<K, V>> kafkaListenerContainerFactory() {
		ConcurrentKafkaListenerContainerFactory<K, V> factory = new ConcurrentKafkaListenerContainerFactory<>();
		factory.setConsumerFactory(consumerFactory());
		factory.setBatchListener(false); // To adapt micrometer observation
		factory.setConcurrency(kafkaConsumerConfigProps.getConcurrencyLevel());
		factory.setAutoStartup(kafkaConsumerConfigProps.getAutoStartup());
		factory.getContainerProperties()
				.setPollTimeout(kafkaConsumerConfigProps.getPollTimeoutMs());
		factory.getContainerProperties()
				.setObservationEnabled(true);
		return factory;
	}

}
