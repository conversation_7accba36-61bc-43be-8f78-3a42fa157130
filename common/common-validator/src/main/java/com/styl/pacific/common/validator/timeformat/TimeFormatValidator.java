/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.common.validator.timeformat;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */

public class TimeFormatValidator implements ConstraintValidator<TimeFormat, String> {

	public static Set<String> getAvailableTimeFormats() {
		HashSet<String> dateFormats = new HashSet<>();
		dateFormats.add("HH:mm");
		dateFormats.add("hh:mm a");
		dateFormats.add("HH:mm:ss");
		dateFormats.add("hh:mm:ss a");
		return dateFormats;
	}

	@Override
	public boolean isValid(String value, ConstraintValidatorContext context) {
		if (value == null) {
			return true;
		}

		return getAvailableTimeFormats().contains(value);
	}
}