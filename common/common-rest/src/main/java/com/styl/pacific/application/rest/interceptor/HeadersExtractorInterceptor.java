/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.application.rest.interceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.application.rest.context.RequestContext;
import com.styl.pacific.common.constant.PacificRestConstants;
import com.styl.pacific.domain.tokenclaims.UserTokenClaim;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Enumeration;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * <AUTHOR>
 */

@Component
public final class HeadersExtractorInterceptor implements HandlerInterceptor {

	private final Logger logger = LoggerFactory.getLogger(HeadersExtractorInterceptor.class);

	private final RequestContext requestContext;
	private final ObjectMapper objectMapper = new ObjectMapper();

	public HeadersExtractorInterceptor(RequestContext requestContext) {
		this.requestContext = requestContext;
	}

	@Override
	public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
			@NonNull Object handler) throws Exception {

		if (request.getRequestURI()
				.startsWith("/api/")) {

			Optional.ofNullable(extractHeader(request, PacificRestConstants.PlatformHeader.HEADER_X_REQUEST_ID))
					.filter(StringUtils::hasText)
					.ifPresentOrElse(requestContext::setRequestId, () -> logger.warn("Missing {} in headers!",
							PacificRestConstants.PlatformHeader.HEADER_X_REQUEST_ID));

			Optional.ofNullable(extractHeader(request, PacificRestConstants.PlatformHeader.HEADER_X_TENANT_ID))
					.filter(StringUtils::hasText)
					.map(Long::parseLong)
					.ifPresentOrElse(requestContext::setTenantId, () -> logger.warn("Missing {} in Request ID: {}",
							PacificRestConstants.PlatformHeader.HEADER_X_TENANT_ID, requestContext.getRequestId()));

			Optional.ofNullable(extractHeader(request, HttpHeaders.AUTHORIZATION))
					.filter(StringUtils::hasText)
					.ifPresent(requestContext::setRawUserToken);

			Optional.ofNullable(extractHeader(request, HttpHeaders.AUTHORIZATION))
					.filter(StringUtils::hasText)
					.map(authToken -> {
						try {
							return objectMapper.readValue(Base64.getUrlDecoder()
									.decode((authToken.split("\\.")[1]).getBytes(StandardCharsets.UTF_8)),
									UserTokenClaim.class);
						} catch (IOException e) {
							logger.error(e.getMessage(), e);
							return null;
						}
					})
					.ifPresent(requestContext::setTokenClaim);
		}

		return HandlerInterceptor.super.preHandle(request, response, handler);
	}

	private String extractHeader(HttpServletRequest request, String headerName) {
		Enumeration<String> headerNames = request.getHeaderNames();
		String result = null;
		while (headerNames.hasMoreElements()) {
			String header = headerNames.nextElement();
			if (headerName.equalsIgnoreCase(header)) {
				result = request.getHeader(header);
			}
		}
		return result;
	}

}
