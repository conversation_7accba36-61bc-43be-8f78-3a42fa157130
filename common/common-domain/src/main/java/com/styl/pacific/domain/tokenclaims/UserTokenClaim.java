/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.domain.tokenclaims;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.styl.pacific.domain.dto.TokenClaim;
import com.styl.pacific.domain.enums.UserType;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@ToString
public class UserTokenClaim implements TokenClaim {
	private Long exp;
	private Long iat;

	@JsonProperty("auth_time")
	private long authTime;

	private String jti;
	private String iss;
	private String aud;

	// This is user's ssoId
	private String sub;
	private String typ;
	private String azp;

	// This is a user token session
	private String sid;
	private String acr;

	private String scope;

	@JsonProperty("email_verified")
	private Boolean emailVerified;
	private String name;
	private String userId;
	private String alternativeUserId;
	private UserType userType;
	private Set<TenantRolePermission> permissions;

	@JsonProperty("given_name")
	private String givenName;

	@JsonProperty("family_name")
	private String familyName;
	private String email;

	@Getter
	@JsonIgnoreProperties(ignoreUnknown = true)
	@JsonInclude(JsonInclude.Include.NON_NULL)
	public static class TenantRolePermission {
		private final String tenantId;
		private final String userRoleId;

		@Builder
		@JsonCreator
		public TenantRolePermission(@JsonProperty("tenantId") String tenantId,
				@JsonProperty("userRoleId") String userRoleId) {
			this.tenantId = tenantId;
			this.userRoleId = userRoleId;
		}

		@Override
		public final boolean equals(Object o) {
			if (this == o) {
				return true;
			}
			if (!(o instanceof TenantRolePermission that)) {
				return false;
			}

			return tenantId.equals(that.tenantId) && userRoleId.equals(that.userRoleId);
		}

		@Override
		public int hashCode() {
			int result = tenantId.hashCode();
			result = 31 * result + userRoleId.hashCode();
			return result;
		}
	}
}
