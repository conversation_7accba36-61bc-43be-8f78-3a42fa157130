/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.aws.dynamodb.config;

import java.net.URI;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;
import software.amazon.awssdk.regions.Region;

/**
 * <AUTHOR>
 */

@Data
@Configuration
@ConfigurationProperties(prefix = "pacific.aws.dynamodb")
public class DynamoDbConfigProperties {

	private String endpoint;
	private String region;
	private String accessKey;
	private String secretKey;

	public URI getEndpoint() {
		if (StringUtils.hasText(endpoint)) {
			return URI.create(endpoint);
		}
		return null;
	}

	public Region getRegion() {
		return Region.of(region);
	}

}