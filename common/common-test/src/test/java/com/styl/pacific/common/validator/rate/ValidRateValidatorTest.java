/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.common.validator.rate;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.Payload;
import java.lang.annotation.Annotation;
import java.math.BigDecimal;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class ValidRateValidatorTest {
	@Mock
	private ConstraintValidatorContext context;

	@Mock
	private ConstraintValidatorContext.ConstraintViolationBuilder violationBuilder;

	private ValidRateValidator validRateValidator;

	@BeforeEach
	public void setUp() {
		MockitoAnnotations.openMocks(this);
		validRateValidator = new ValidRateValidator();
	}

	private static Stream<Arguments> generateRate() {
		return IntStream.range(0, 10001)
				.mapToObj(v -> BigDecimal.valueOf(0.0001)
						.multiply(BigDecimal.valueOf(v))
						.stripTrailingZeros())
				.map(Arguments::of);
	}

	@Test
	void testIsValidWhenValueHigherOneThenReturnFalse() {
		// Arrange
		final var captor = ArgumentCaptor.forClass(String.class);

		when(context.buildConstraintViolationWithTemplate(anyString())).thenReturn(violationBuilder);

		final var rate = new BigDecimal("1.01");
		// Act
		boolean result = validRateValidator.isValid(rate, context);

		verify(context, times(1)).buildConstraintViolationWithTemplate(captor.capture());

		final var errorMessage = captor.getValue();

		// Assert
		Assertions.assertFalse(result);
		Assertions.assertEquals("Rate must be lower 1", errorMessage);
	}

	@Test
	void testIsValidWhenNumberOfCharactersLessThenReturnFalse() {
		// Arrange
		final var captor = ArgumentCaptor.forClass(String.class);

		when(context.buildConstraintViolationWithTemplate(anyString())).thenReturn(violationBuilder);

		final var rate = new BigDecimal("0.12345");
		// Act
		boolean result = validRateValidator.isValid(rate, context);

		verify(context, times(1)).buildConstraintViolationWithTemplate(captor.capture());

		final var errorMessage = captor.getValue();

		// Assert
		Assertions.assertFalse(result);
		Assertions.assertEquals("Rate must be lesser 5 characters", errorMessage);
	}

	@Test
	void testIsValidWhenFractionalDigitsHigherFractionThenReturnFalse() {
		// Arrange
		final var captor = ArgumentCaptor.forClass(String.class);

		when(context.buildConstraintViolationWithTemplate(anyString())).thenReturn(violationBuilder);

		final var rate = new BigDecimal("0.1234");

		final var validator = new ValidRateValidator();
		validator.initialize(new ValidRate() {

			@Override
			public Class<? extends Annotation> annotationType() {
				return null;
			}

			@Override
			public String message() {
				return "must be valid percentage rule";
			}

			@Override
			public int fraction() {
				return 2;
			}

			@Override
			public int number() {
				return 6;

			}

			@Override
			public int inclusiveMax() {
				return 1;

			}

			@Override
			public Class<?>[] groups() {
				return new Class[0];

			}

			@Override
			public Class<? extends Payload>[] payload() {
				return new Class[0];

			}
		});

		// Act
		boolean result = validator.isValid(rate, context);
		verify(context, times(1)).buildConstraintViolationWithTemplate(captor.capture());
		final var errorMessage = captor.getValue();

		// Assert
		Assertions.assertFalse(result);
		Assertions.assertEquals("Rate must be lower 2 fractional digits", errorMessage);
	}

	@Test
	void testIsValidWhenValueIsNegativeThenReturnFalse() {
		// Arrange
		final var captor = ArgumentCaptor.forClass(String.class);

		when(context.buildConstraintViolationWithTemplate(anyString())).thenReturn(violationBuilder);

		final var rate = new BigDecimal("-0.1234");
		// Act
		boolean result = validRateValidator.isValid(rate, context);

		verify(context, times(1)).buildConstraintViolationWithTemplate(captor.capture());

		final var errorMessage = captor.getValue();

		// Assert
		Assertions.assertFalse(result);
		Assertions.assertEquals("Rate must be positive", errorMessage);
	}

	@ParameterizedTest
	@MethodSource("generateRate")
	void testIsValidWhenCalledWithNumericStringAndNegativeThenFalse(BigDecimal value) {
		// Act
		boolean result = validRateValidator.isValid(value, context);

		// Assert
		Assertions.assertTrue(result);
	}

}
