/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tracing;

import com.styl.pacific.tracing.config.TracingConfigProperties;
import io.micrometer.context.ContextExecutorService;
import io.micrometer.context.ContextScheduledExecutorService;
import io.micrometer.context.ContextSnapshotFactory;
import io.micrometer.observation.ObservationPredicate;
import io.opentelemetry.exporter.otlp.trace.OtlpGrpcSpanExporter;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;
import lombok.AllArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.lang.NonNull;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Configuration
@ConditionalOnProperty(prefix = "pacific.tracing", name = "enabled", havingValue = "true")
public class TracingConfiguration {

	private final TracingConfigProperties tracingConfigProperties;

	private static final String ACTUATOR_PATH_PREFIX = "/actuator";

	@Bean
	public OtlpGrpcSpanExporter otlpGrpcSpanExporter() {
		return OtlpGrpcSpanExporter.builder()
				.setEndpoint(tracingConfigProperties.getTracing()
						.getOtlp()
						.getEndpoint())
				.build();
	}

	@Bean
	ObservationPredicate ignoreActuator() {
		return (name, context) -> {
			if (context instanceof org.springframework.http.server.reactive.observation.ServerRequestObservationContext reactive) {
				return !reactive.getCarrier()
						.getURI()
						.getPath()
						.startsWith(ACTUATOR_PATH_PREFIX);
			}
			if (context instanceof org.springframework.http.server.observation.ServerRequestObservationContext srCtx) {
				return !srCtx.getCarrier()
						.getRequestURI()
						.startsWith(ACTUATOR_PATH_PREFIX);
			}
			return true;
		};
	}

	@Bean(name = "taskExecutor", destroyMethod = "shutdown")
	ThreadPoolTaskScheduler threadPoolTaskScheduler() {
		ThreadPoolTaskScheduler threadPoolTaskScheduler = new ThreadPoolTaskScheduler() {
			@Override
			protected @NonNull ExecutorService initializeExecutor(@NonNull ThreadFactory threadFactory,
					@NonNull RejectedExecutionHandler rejectedExecutionHandler) {
				ExecutorService executorService = super.initializeExecutor(threadFactory, rejectedExecutionHandler);
				return ContextExecutorService.wrap(executorService, ContextSnapshotFactory.builder()
						.build()::captureAll);
			}

			@Override
			public @NonNull ScheduledExecutorService getScheduledExecutor() throws IllegalStateException {
				return ContextScheduledExecutorService.wrap(super.getScheduledExecutor());
			}
		};
		threadPoolTaskScheduler.initialize();
		return threadPoolTaskScheduler;
	}
}