/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package io.vinta.containerbase.data.access.relational.dashboard.entities;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.vinta.containerbase.common.enums.UserType;
import java.util.Set;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.With;

@Getter
@Builder
@With
@RequiredArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DashboardAccessDataPolicy {
	public static final String FIELD_NAME_ALLOWED_TENANT_IDS = "allowedTenantIds";
	public static final String FIELD_NAME_ALLOWED_USER_TYPES = "allowedUserTypes";
	public static final String FIELD_NAME_ALLOWED_USER_IDS = "allowedUserIds";

	private final Set<Long> allowedTenantIds;
	private final Set<UserType> allowedUserTypes;
	private final Set<Long> allowedUserIds;
}
