/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.users.handler;

import com.styl.pacific.domain.constants.CommonDomainConstants;
import com.styl.pacific.domain.enums.UserType;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.user.service.core.features.groups.UserGroupQueryService;
import com.styl.pacific.user.service.core.features.notifications.request.ControlNotificationCommand;
import com.styl.pacific.user.service.core.features.preferences.UserPreferenceCommandService;
import com.styl.pacific.user.service.core.features.tenants.TenantQueryService;
import com.styl.pacific.user.service.core.features.userroles.UserRoleQueryService;
import com.styl.pacific.user.service.core.features.users.UserRepository;
import com.styl.pacific.user.service.core.features.users.entities.User;
import com.styl.pacific.user.service.core.features.users.events.NewUserAddedEvent;
import com.styl.pacific.user.service.core.features.users.request.UpsertTenantUserCommand;
import com.styl.pacific.user.shared.enums.UserPermissionStatus;
import com.styl.pacific.user.shared.enums.UserStatus;
import com.styl.pacific.user.shared.exceptions.UserAlreadyExistingException;
import com.styl.pacific.user.shared.exceptions.UserExternalIdDuplicatedException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
public class SystemUserTenantCommandHandler extends UserTypeCommandHandler {

	public SystemUserTenantCommandHandler(UserRepository userRepository, TenantQueryService tenantQueryService,
			UserRoleQueryService roleQueryService, ApplicationEventPublisher eventPublisher,
			UserGroupQueryService userGroupQueryService, UserPreferenceCommandService preferenceCommandService) {
		super(userRepository, tenantQueryService, roleQueryService, eventPublisher, userGroupQueryService,
				preferenceCommandService);
	}

	@Override
	public boolean isSupportedUserType(UserType userType) {
		return UserType.SYSTEM_ADMIN.equals(userType);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public User addSingleUser(UpsertTenantUserCommand command) {
		final var request = command.withTenantId(CommonDomainConstants.SYSTEM_TENANT_ID)
				.withRealmId(CommonDomainConstants.BACK_OFFICE_REALM_ID);

		if (userRepository.existUserByRealmIdAndEmail(request.realmId(), request.email())) {
			throw new UserAlreadyExistingException("User has existed");
		}

		if (StringUtils.isNotBlank(command.uniqueExternalId()) && userRepository.existUserByUniqueExternalId(command
				.uniqueExternalId())) {
			throw new UserExternalIdDuplicatedException("External Id has been existed");
		}

		return createNewUser(request.withUserStatus(UserStatus.CREATED)
				.withPermissionStatus(UserPermissionStatus.CREATED));
	}

	@Override
	public void postAddSingleUser(TenantId tenantId, User user, ControlNotificationCommand controlNotificationCommand) {
		eventPublisher.publishEvent(NewUserAddedEvent.builder()
				.newUser(user)
				.tenantId(tenantId)
				.build());
	}
}
