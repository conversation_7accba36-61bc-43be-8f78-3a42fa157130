/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.requiredactions;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.domain.valueobject.UserRequiredActionId;
import com.styl.pacific.domain.valueobject.UserSubAccountId;
import com.styl.pacific.user.service.core.features.requiredactions.entities.UserRequiredAction;
import com.styl.pacific.user.service.core.features.requiredactions.request.ExistUserRequireActionQuery;
import com.styl.pacific.user.service.core.features.requiredactions.request.UserRequiredActionPaginationQuery;
import com.styl.pacific.user.shared.enums.UserRequiredActionType;
import java.util.Optional;
import java.util.Set;

public interface UserRequiredActionRepository {
	UserRequiredAction save(UserRequiredAction entity);

	boolean existsUserRequiredAction(ExistUserRequireActionQuery query);

	Optional<UserRequiredAction> getUserRequiredActionByUserIdAndId(UserId userId,
			UserRequiredActionId userRequiredActionId);

	void deleteUserRequiredActionById(UserRequiredActionId id);

	void deleteUserRequiredActionByUserAndActionTypeAndTenantIds(UserId userId, UserRequiredActionType actionType,
			Set<TenantId> tenantIds);

	void deleteUserRequiredActionByMetadataSubAccountId(UserSubAccountId subAccountId);

	Paging<UserRequiredAction> queryUserGroups(UserRequiredActionPaginationQuery query);

	void deleteUserRequiredActionByUserIds(Set<UserId> userIds);
}
