/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.term;

import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.user.service.core.features.agreementterm.AgreementTermRepository;
import com.styl.pacific.user.service.core.features.term.dto.SaveUserAgreementTermCommand;
import com.styl.pacific.user.service.core.features.term.entity.UserAgreementTerm;
import com.styl.pacific.user.service.core.features.term.input.UserAgreementTermService;
import com.styl.pacific.user.service.core.features.term.mapper.AgreementTermDataMapper;
import com.styl.pacific.user.service.core.features.users.UserRepository;
import com.styl.pacific.user.service.core.features.users.request.FilterUserQuery;
import com.styl.pacific.user.service.core.features.users.valueobject.UserFetchRule;
import com.styl.pacific.user.shared.exceptions.UserNotFoundException;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */

@Service
@AllArgsConstructor
public class UserAgreementTermImpl implements UserAgreementTermService {

	private final AgreementTermRepository agreementTermRepository;

	private final AgreementTermIdGenerator agreementTermIdGenerator;

	private final UserRepository userRepository;

	@Override
	public UserAgreementTerm getUserAgreementTerm(Long userId, Long tenantId) {
		return agreementTermRepository.getAgreementTerm(userId, tenantId)
				.orElse(buildAgreementTermDefault());
	}

	@Override
	public UserAgreementTerm saveAgreementTerm(SaveUserAgreementTermCommand command) {
		final var user = userRepository.getUserByCondition(FilterUserQuery.builder()
				.byUserId(new UserId(command.getUserId()))
				.build(), UserFetchRule.builder()
						.build())
				.orElseThrow(() -> new UserNotFoundException("User not found"));
		Optional<UserAgreementTerm> agreementTermOptional = agreementTermRepository.getAgreementTerm(command
				.getUserId(), command.getTenantId());
		if (agreementTermOptional.isEmpty()) {
			return command.isEnabled()
					? agreementTermRepository.saveAgreementTerm(AgreementTermDataMapper.INSTANCE
							.saveCommandToAgreementTerm(command, user))
					: buildAgreementTermDefault();
		}
		if (!command.isEnabled()) {
			agreementTermRepository.deleteAgreementTerm(command.getUserId(), command.getTenantId());
			return buildAgreementTermDefault();
		}
		return agreementTermOptional.get();
	}

	private UserAgreementTerm buildAgreementTermDefault() {
		return UserAgreementTerm.builder()
				.enabled(false)
				.build();
	}
}
