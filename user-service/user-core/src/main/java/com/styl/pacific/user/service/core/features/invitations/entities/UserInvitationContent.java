/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.invitations.entities;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.styl.pacific.user.shared.constants.UserInvitationConstants;
import com.styl.pacific.user.shared.enums.UserInvitationContentType;
import lombok.Getter;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = UserInvitationContent.TYPE_FIELD_NAME, visible = true)
@JsonSubTypes({
		@JsonSubTypes.Type(value = UserInvitationContentEmpty.class, name = UserInvitationConstants.UserInvitationContentConstants.CONTENT_EMPTY),
		@JsonSubTypes.Type(value = UserInvitationContentCode.class, name = UserInvitationConstants.UserInvitationContentConstants.CONTENT_CODE),
		@JsonSubTypes.Type(value = UserInvitationSubAccountContent.class, name = UserInvitationConstants.UserInvitationContentConstants.CONTENT_SUB_ACCOUNT_INVITE), })
@Getter
public class UserInvitationContent {
	public static final String TYPE_FIELD_NAME = "contentType";
	private final UserInvitationContentType contentType;

	protected UserInvitationContent(UserInvitationContentType contentType) {
		this.contentType = contentType;
	}
}
