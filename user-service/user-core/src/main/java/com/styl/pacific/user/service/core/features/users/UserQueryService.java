/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.users;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.user.service.core.features.users.entities.User;
import com.styl.pacific.user.service.core.features.users.request.FilterUserQuery;
import com.styl.pacific.user.service.core.features.users.request.UserPaginationQuery;
import com.styl.pacific.user.service.core.features.users.valueobject.UserFetchRule;
import java.util.Optional;

public interface UserQueryService {
	Optional<User> getSingleUserByRealmIdAndSsoId(String realmId, String ssoId);

	Optional<User> getSingleUserById(UserId id);

	Optional<User> getUserByConditions(FilterUserQuery request, UserFetchRule fetchRule);

	Paging<User> queryUsers(UserPaginationQuery request);

	long countUsersByConditions(FilterUserQuery query);
}
