/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.invitations.handler;

import com.styl.pacific.user.service.core.features.invitations.UserInvitationRepository;
import com.styl.pacific.user.service.core.features.invitations.entities.UserInvitation;
import com.styl.pacific.user.service.core.features.invitations.entities.UserInvitationSubAccountContent;
import com.styl.pacific.user.service.core.features.invitations.request.SendUserInvitationCommand;
import com.styl.pacific.user.service.core.features.invitations.request.SendUserSubAccountInvitationCommand;
import com.styl.pacific.user.service.core.features.users.UserQueryService;
import com.styl.pacific.user.service.core.features.users.entities.User;
import com.styl.pacific.user.shared.enums.UserInvitationType;
import jakarta.validation.Valid;
import org.springframework.stereotype.Component;

@Component
public class UserInvitationSubAccountHandler extends UserInvitationHandler {

	public UserInvitationSubAccountHandler(UserInvitationRepository invitationRepository,
			UserQueryService userQueryService) {
		super(invitationRepository, userQueryService);
	}

	@Override
	public boolean isSupportedType(UserInvitationType type) {
		return UserInvitationType.USER_SUB_ACCOUNT_INVITE.equals(type);
	}

	@Override
	protected UserInvitation buildUserInvitation(@Valid SendUserInvitationCommand command) {
		final var subAccountInvitation = (SendUserSubAccountInvitationCommand) command;
		final var subAccount = subAccountInvitation.getUserSubAccount();
		return UserInvitation.builder()
				.user(User.builder()
						.id(command.getUserId())
						.build())
				.realmId(subAccount.getSubUser()
						.getRealmId())
				.type(subAccountInvitation.getType())
				.tenantId(subAccountInvitation.getTenantId())
				.email(subAccount.getSubUser()
						.getEmail())
				.content(UserInvitationSubAccountContent.builder()
						.subAccountId(subAccount.getId()
								.getValue())
						.sponsorUserId(subAccount.getSponsorUser()
								.getId()
								.getValue())
						.build())
				.build();
	}
}
