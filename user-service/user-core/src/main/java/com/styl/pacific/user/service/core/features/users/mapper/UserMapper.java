/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.users.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.domain.enums.UserType;
import com.styl.pacific.user.service.core.features.permissions.mapper.UserPermissionMapper;
import com.styl.pacific.user.service.core.features.users.entities.User;
import com.styl.pacific.user.service.core.features.users.generators.CustomerUniqueExternalIdGenerator;
import com.styl.pacific.user.service.core.features.users.request.UpdateUserCommand;
import com.styl.pacific.user.service.core.features.users.request.UpsertTenantUserCommand;
import com.styl.pacific.user.shared.enums.UserStatus;
import java.util.List;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class, UserPermissionMapper.class })
public interface UserMapper {
	UserMapper INSTANCE = Mappers.getMapper(UserMapper.class);

	@Mapping(target = "id", source = "userId")
	@Mapping(target = "fullName", expression = "java(UserMappingFunctionSupport.buildFullName(source.firstName(), source.lastName()))")
	@Mapping(target = "deletedAt", ignore = true)
	@Mapping(target = "schedulingDeletedAt", ignore = true)
	@Mapping(target = "createdBy", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "updatedBy", ignore = true)
	@Mapping(target = "updatedAt", ignore = true)
	@Mapping(target = "totalSubAccounts", ignore = true)
	@Mapping(target = "totalSponsors", ignore = true)
	@Mapping(target = "permissions", ignore = true)
	@Mapping(target = "userGroup.id", source = "userGroupId")
	@Mapping(target = "isAlternativeUser", source = "isAlternativeUser", defaultExpression = "java(false)")
	User toEntity(UpsertTenantUserCommand source);

	@AfterMapping
	default void afterToModelDetailMapping(@MappingTarget User.UserBuilder builder, UpsertTenantUserCommand source) {
		builder.permissions(List.of(UserPermissionMapper.INSTANCE.toEntity(source)));
	}

	@Mapping(target = "id", source = "user.id")
	@Mapping(target = "userType", source = "user.userType")
	@Mapping(target = "realmId", source = "user.realmId")
	@Mapping(target = "email", source = "command.email")
	@Mapping(target = "ssoId", source = "command.ssoId")
	@Mapping(target = "externalId", source = "command.externalId")
	@Mapping(target = "uniqueExternalId", expression = "java(buildUniqueExternalId(user, command))")
	@Mapping(target = "firstName", source = "command.firstName")
	@Mapping(target = "lastName", source = "command.lastName")
	@Mapping(target = "fullName", expression = "java(UserMappingFunctionSupport.buildFullName(command.getFirstName(), command.getLastName()))")
	@Mapping(target = "phoneNumber", source = "command.phoneNumber")
	@Mapping(target = "avatarPath", source = "command.avatarPath")
	@Mapping(target = "avatarHash", source = "command.avatarHash")
	@Mapping(target = "userStatus", source = "command.userStatus")
	@Mapping(target = "trustedSource", expression = "java(command.isInternalUpdating() ? command.getTrustedSource() : user.getTrustedSource())")
	@Mapping(target = "permissions", source = "command.updatePermissions")
	@Mapping(target = "userGroup.id", source = "command.userGroupId")
	@Mapping(target = "deletedAt", source = "user.deletedAt")
	@Mapping(target = "migrationId", source = "user.migrationId")
	@Mapping(target = "familyCode", source = "user.familyCode")
	@Mapping(target = "schedulingDeletedAt", source = "user.schedulingDeletedAt")
	@Mapping(target = "isAlternativeUser", source = "user.isAlternativeUser")
	@Mapping(target = "createdBy", source = "user.createdBy")
	@Mapping(target = "createdAt", source = "user.createdAt")
	@Mapping(target = "updatedBy", source = "user.updatedBy")
	@Mapping(target = "updatedAt", source = "user.updatedAt")
	@Mapping(target = "totalSubAccounts", ignore = true)
	@Mapping(target = "totalSponsors", ignore = true)
	User toUpdateEntity(User user, UpdateUserCommand command);

	@AfterMapping
	default void afterUpdateEntityMapping(@MappingTarget User.UserBuilder builder, UpdateUserCommand command) {
		if (!UserStatus.ARCHIVED.equals(command.getUserStatus())) {
			builder.schedulingDeletedAt(null);
		}
	}

	default String buildUniqueExternalId(User user, UpdateUserCommand command) {
		return UserType.CUSTOMER.equals(user.getUserType())
				? CustomerUniqueExternalIdGenerator.buildCustomerUniqueExternalId(command.getUpdatePermissions()
						.getFirst()
						.getTenantId(), command.getExternalId())
				: command.getExternalId();
	}

	@Mapping(target = "userGroupId", source = "userGroup.id")
	@Mapping(target = "updatePermissions", source = "permissions")
	@Mapping(target = "isInternalUpdating", ignore = true)
	UpdateUserCommand toUpdateCommand(User user);

}
