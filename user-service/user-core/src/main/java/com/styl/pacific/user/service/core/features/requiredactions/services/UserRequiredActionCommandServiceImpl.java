/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.requiredactions.services;

import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.domain.valueobject.UserSubAccountId;
import com.styl.pacific.user.service.core.features.requiredactions.UserRequiredActionCommandService;
import com.styl.pacific.user.service.core.features.requiredactions.UserRequiredActionRepository;
import com.styl.pacific.user.service.core.features.requiredactions.entities.UserRequiredAction;
import com.styl.pacific.user.service.core.features.requiredactions.handlers.UserRequiredActionHandler;
import com.styl.pacific.user.service.core.features.requiredactions.request.CreateUserRequiredActionCommand;
import com.styl.pacific.user.service.core.features.users.request.ReplyUserRequiredActionCommand;
import com.styl.pacific.user.shared.enums.UserRequiredActionType;
import com.styl.pacific.user.shared.exceptions.UserRequiredActionException;
import com.styl.pacific.user.shared.exceptions.UserRequiredActionNotFoundException;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class UserRequiredActionCommandServiceImpl implements UserRequiredActionCommandService {

	private final List<UserRequiredActionHandler> handlers;
	private final UserRequiredActionRepository repository;

	@Override
	public UserRequiredAction createRequiredAction(CreateUserRequiredActionCommand command) {
		final var handler = getUserRequiredActionHandler(command.getActionType());
		handler.validate(command);
		return handler.createRequiredAction(command);
	}

	private UserRequiredActionHandler getUserRequiredActionHandler(UserRequiredActionType command) {
		return handlers.stream()
				.filter(it -> it.isSupported(command))
				.findFirst()
				.orElseThrow(() -> new UserRequiredActionException("Action type has not supported"));
	}

	@Override
	public void deleteUserRequiredActionByUserAndActionTypeAndTenantIds(UserId userId,
			UserRequiredActionType actionType, Set<TenantId> tenantIds) {
		if (CollectionUtils.isEmpty(tenantIds)) {
			return;
		}
		repository.deleteUserRequiredActionByUserAndActionTypeAndTenantIds(userId, actionType, tenantIds);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void replyUserRequiredAction(ReplyUserRequiredActionCommand command) {
		final var userRequiredAction = repository.getUserRequiredActionByUserIdAndId(command.getUserId(), command
				.getUserRequiredActionId())
				.orElseThrow(() -> new UserRequiredActionNotFoundException("User required action not found"));
		final var handler = getUserRequiredActionHandler(userRequiredAction.getActionType());

		handler.validateValidOption(command.getRepliedOption());
		handler.replyUserRequiredAction(userRequiredAction, command.getRepliedOption());
	}

	@Override
	public void deleteUserRequiredActionByMetadataSubAccountId(UserSubAccountId subAccountId) {
		repository.deleteUserRequiredActionByMetadataSubAccountId(subAccountId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteUserRequiredActionByUserIds(Set<UserId> userIds) {
		repository.deleteUserRequiredActionByUserIds(userIds);
	}
}
