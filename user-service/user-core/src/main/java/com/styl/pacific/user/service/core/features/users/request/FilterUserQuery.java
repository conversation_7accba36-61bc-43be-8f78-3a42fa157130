/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.users.request;

import com.styl.pacific.domain.enums.UserType;
import com.styl.pacific.domain.range.InstantDateTimeRange;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserGroupId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.domain.valueobject.UserRoleId;
import com.styl.pacific.user.shared.enums.UserStatus;
import java.util.Set;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.With;

@With
@Getter
@Builder
@RequiredArgsConstructor
public class FilterUserQuery {
	private final String bySsoId;
	private final UserId byUserId;
	private final TenantId byTenantId;
	private final Set<UserRoleId> byRoleIds;
	private final Set<UserType> byUserTypes;
	private final Set<UserStatus> byUserStatuses;
	private final Set<UserId> byUserIds;
	private final Set<String> byMigrationIds;
	private final String byPhoneNumber;
	private final String byEmail;
	private final String byContainingEmail;
	private final String byName;
	private final String byRealmId;
	private final String byExternalId;
	private final String byContainingExternalId;
	private final InstantDateTimeRange byCreatedRange;
	private final UserGroupId byUserGroupId;
	private final String byUserGroupPath;
	private final String byUserCardId;
	private final String byContainingUserCardId;
	private final String byFamilyCode;
	private final Boolean isAlternativeUser;
	private final Integer groupLevel;

	private final boolean doesExcludeBackOfficeUser;
	private final boolean onlyDeletingUser;
}
