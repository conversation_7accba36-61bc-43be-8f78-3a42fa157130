/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.users.entities;

import com.styl.pacific.domain.entity.BaseEntity;
import com.styl.pacific.domain.enums.UserType;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.user.service.core.features.groups.entities.UserGroup;
import com.styl.pacific.user.service.core.features.permissions.entities.UserPermission;
import com.styl.pacific.user.shared.enums.UserNonCompletedAction;
import com.styl.pacific.user.shared.enums.UserStatus;
import java.time.Instant;
import java.util.List;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.With;
import org.apache.commons.lang3.StringUtils;

@Getter
@Builder
@With
@RequiredArgsConstructor
public class User extends BaseEntity<UserId> {
	private final UserId id;
	private final String ssoId;
	private final UserType userType;
	private final UserStatus userStatus;
	private final String externalId;
	private final String firstName;
	private final String lastName;
	private final String fullName;
	private final String email;
	private final String phoneNumber;
	private final String realmId;
	private final String avatarPath;
	private final String avatarHash;
	private final List<UserNonCompletedAction> userNonCompletedActions;
	private final List<UserPermission> permissions;
	private final Long totalSubAccounts;
	private final Long totalSponsors;
	private final UserGroup userGroup;
	private final String uniqueExternalId;
	private final String trustedSource;
	private final String migrationId;
	private final String familyCode;

	private final Boolean isAlternativeUser;

	private final Instant createdAt;
	private final Long createdBy;
	private final Instant updatedAt;
	private final Long updatedBy;
	private final Instant deletedAt;
	private final Instant schedulingDeletedAt;

	public boolean isTrustedSourceAccess() {
		return StringUtils.isNotBlank(trustedSource);
	}
}