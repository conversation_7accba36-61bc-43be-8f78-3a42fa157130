/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.subaccounts.request;

import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.domain.valueobject.UserSubAccountId;
import com.styl.pacific.user.shared.enums.UserSubAccountStatus;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;

@Builder
public record ChangeStatusSubAccountCommand(@NotNull UserSubAccountId subAccountId,
		@NotNull UserSubAccountStatus subAccountStatus,
		UserId sponsorId) {
}
