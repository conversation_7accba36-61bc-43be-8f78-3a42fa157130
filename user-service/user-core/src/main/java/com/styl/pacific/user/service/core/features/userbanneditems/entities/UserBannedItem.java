/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.userbanneditems.entities;

import com.styl.pacific.domain.entity.BaseEntity;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserBannedItemId;
import com.styl.pacific.user.service.core.features.users.entities.User;
import java.time.Instant;
import java.util.Objects;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.With;

@Getter
@Builder
@With
@RequiredArgsConstructor
public class UserBannedItem extends BaseEntity<UserBannedItemId> {
	private final UserBannedItemId id;
	private final User user;
	private final TenantId tenantId;
	private final String productName;
	private final Instant createdAt;
	private final Instant updatedAt;

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (!(o instanceof UserBannedItem that))
			return false;
		if (!super.equals(o))
			return false;
		return Objects.equals(id, that.id) && Objects.equals(user, that.user) && Objects.equals(tenantId, that.tenantId)
				&& Objects.equals(productName, that.productName) && Objects.equals(createdAt, that.createdAt) && Objects
						.equals(updatedAt, that.updatedAt);
	}

	@Override
	public int hashCode() {
		return Objects.hash(super.hashCode(), id, user, tenantId, productName, createdAt, updatedAt);
	}
}