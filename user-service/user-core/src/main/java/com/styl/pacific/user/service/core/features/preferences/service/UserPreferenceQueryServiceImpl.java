/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.preferences.service;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.user.service.core.features.preferences.UserPreferenceQueryService;
import com.styl.pacific.user.service.core.features.preferences.UserPreferenceRepository;
import com.styl.pacific.user.service.core.features.preferences.entities.UserPreference;
import com.styl.pacific.user.service.core.features.preferences.handlers.UserPreferenceHandler;
import com.styl.pacific.user.service.core.features.preferences.request.FindOneUserPreference;
import com.styl.pacific.user.service.core.features.preferences.request.FindUserPreferenceQuery;
import com.styl.pacific.user.service.core.features.preferences.request.UserPreferencePaginationQuery;
import com.styl.pacific.user.shared.exceptions.UserPreferenceException;
import com.styl.pacific.user.shared.exceptions.UserPreferenceNotFoundException;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class UserPreferenceQueryServiceImpl implements UserPreferenceQueryService {

	private final UserPreferenceRepository userPreferenceRepository;

	private final List<UserPreferenceHandler> handlers;

	@Override
	@Transactional
	public UserPreference getUserPreference(FindUserPreferenceQuery query) {
		final var userPreference = userPreferenceRepository.getOneUserPreference(FindOneUserPreference.builder()
				.byTenantId(query.getByTenantId())
				.byUserId(query.getByUserId())
				.byId(query.getById())
				.byKey(query.getByKey())
				.build());

		if (userPreference.isPresent()) {
			return userPreference.get();
		}

		if (query.getById() != null || (query.getByKey() == null && !query.isUseTemplateIfNonExisted())) {
			throw new UserPreferenceNotFoundException();
		}

		return handlers.stream()
				.filter(handler -> handler.isSupported(query.getByKey()))
				.findFirst()
				.map(handler -> handler.getDefaultPreference(query.getByTenantId(), query.getByUserId(), query
						.getByKey()))
				.orElseThrow(() -> new UserPreferenceException("Unsupported preference key"));
	}

	@Override
	@Transactional
	public Paging<UserPreference> queryUserPreferences(UserPreferencePaginationQuery query) {
		return userPreferenceRepository.queryUserPreferences(query);
	}
}
