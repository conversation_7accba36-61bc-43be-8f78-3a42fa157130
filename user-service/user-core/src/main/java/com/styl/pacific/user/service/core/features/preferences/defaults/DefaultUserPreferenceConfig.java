/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.preferences.defaults;

import com.styl.pacific.notification.service.enums.NotificationChannel;
import com.styl.pacific.user.shared.enums.UserPreferenceKey;
import java.util.List;
import java.util.Set;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "pacific.user-preferences.default")
public class DefaultUserPreferenceConfig {

	private List<DefaultNotificationPreferenceConfig> notifications;
	private Boolean enabledBackOfficeUserEmailOtp;

	@Getter
	@Builder
	@RequiredArgsConstructor
	public static class DefaultNotificationPreferenceConfig {
		private final UserPreferenceKey key;
		private final Set<NotificationChannel> channels;
	}
}
