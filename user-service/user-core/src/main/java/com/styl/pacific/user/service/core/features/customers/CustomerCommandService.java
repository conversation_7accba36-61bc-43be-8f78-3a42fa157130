/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.customers;

import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.user.service.core.features.customers.request.AssignCustomersToGroupCommand;
import com.styl.pacific.user.service.core.features.customers.request.UnAssignCustomersFromGroupCommand;
import com.styl.pacific.user.service.core.features.customers.valueobject.CustomerSelfRegistrationLink;

public interface CustomerCommandService {
	CustomerSelfRegistrationLink generateSelfRegistrationAccess(TenantId tenantId, String redirectUrl);

	void assignCustomersToGroup(TenantId tenantId, AssignCustomersToGroupCommand command);

	void unAssignCustomersFromGroup(UnAssignCustomersFromGroupCommand command);
}
