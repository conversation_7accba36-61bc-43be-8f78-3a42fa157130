/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.groups.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.user.service.core.features.groups.entities.UserGroup;
import com.styl.pacific.user.service.core.features.groups.generator.UserGroupIdGenerator;
import com.styl.pacific.user.service.core.features.groups.request.UpsertUserGroupCommand;
import com.styl.pacific.user.service.core.features.groups.utils.UserGroupPathSupporter;
import java.util.Optional;
import org.mapstruct.AfterMapping;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface UserGroupMapper {
	UserGroupMapper INSTANCE = Mappers.getMapper(UserGroupMapper.class);

	@Mapping(target = "id", source = "source.userGroupId")
	@Mapping(target = "tenantId", source = "source.tenantId")
	@Mapping(target = "groupName", source = "source.groupName")
	@Mapping(target = "description", source = "source.description")
	@Mapping(target = "migrationId", source = "source.migrationId")
	@Mapping(target = "parent", source = "parent")
	@Mapping(target = "path", ignore = true)
	@Mapping(target = "createdBy", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "updatedBy", ignore = true)
	@Mapping(target = "updatedAt", ignore = true)
	UserGroup toEntity(UpsertUserGroupCommand source, UserGroup parent, @Context UserGroupIdGenerator idGenerator);

	@AfterMapping
	default void afterMappingEntity(UpsertUserGroupCommand source, UserGroup parent,
			@MappingTarget UserGroup.UserGroupBuilder builder, @Context UserGroupIdGenerator idGenerator) {
		final var id = Optional.ofNullable(source.userGroupId())
				.orElseGet(idGenerator::generate);
		builder.id(id);
		builder.path(UserGroupPathSupporter.generatePath(id, parent));
	}

	@Mapping(target = "tenantId", source = "tenantId")
	@Mapping(target = "groupName", source = "source.groupName")
	@Mapping(target = "description", source = "source.description")
	@Mapping(target = "id", ignore = true)
	@Mapping(target = "parent", ignore = true)
	@Mapping(target = "path", ignore = true)
	@Mapping(target = "createdBy", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "updatedBy", ignore = true)
	@Mapping(target = "updatedAt", ignore = true)
	UserGroup toEntityFromTemplate(UserGroup source, TenantId tenantId, @Context UserGroupIdGenerator idGenerator);

	@AfterMapping
	default void afterMappingTemplateEntity(TenantId tenantId, @MappingTarget UserGroup.UserGroupBuilder builder,
			@Context UserGroupIdGenerator idGenerator) {
		final var id = idGenerator.generate();
		builder.id(id);
		builder.tenantId(tenantId);
		builder.path(UserGroupPathSupporter.generatePath(id, null));
	}
}
