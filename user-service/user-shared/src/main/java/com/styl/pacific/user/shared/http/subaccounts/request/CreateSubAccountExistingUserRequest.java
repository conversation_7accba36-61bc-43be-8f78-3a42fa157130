/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.shared.http.subaccounts.request;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.styl.pacific.user.shared.constants.UserSubAccountConstants;
import com.styl.pacific.user.shared.enums.UserSubAccountCreationContext;
import jakarta.validation.constraints.Email;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@JsonTypeName(UserSubAccountConstants.SubAccountCreationContext.EXISTING_CUSTOMER)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateSubAccountExistingUserRequest extends CreateSubAccountRequest {

	private final Long userId;

	@Email
	private final String email;

	@Length(max = 120)
	private final String firstName;

	@Length(max = 120)
	private final String lastName;

	@Builder
	@JsonCreator
	public CreateSubAccountExistingUserRequest(@JsonProperty("userId") Long userId, @JsonProperty("email") String email,
			@JsonProperty("sponsorUserId") Long sponsorUserId, @JsonProperty("firstName") String firstName,
			@JsonProperty("lastName") String lastName) {
		super(UserSubAccountCreationContext.EXISTING_CUSTOMER, sponsorUserId);
		this.userId = userId;
		this.email = email;
		this.firstName = firstName;
		this.lastName = lastName;
	}
}