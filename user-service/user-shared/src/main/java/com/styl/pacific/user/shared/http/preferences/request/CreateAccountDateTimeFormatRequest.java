/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.shared.http.preferences.request;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.styl.pacific.common.validator.dateformat.DateFormat;
import com.styl.pacific.common.validator.timeformat.TimeFormat;
import com.styl.pacific.common.validator.timezone.TimeZone;
import com.styl.pacific.user.shared.constants.UserPreferenceKeyConstants;
import com.styl.pacific.user.shared.enums.UserPreferenceKey;
import jakarta.validation.constraints.NotBlank;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@JsonTypeName(UserPreferenceKeyConstants.ACCOUNT_DATE_TIME_FORMAT)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateAccountDateTimeFormatRequest extends CreateUserPreferenceRequest {

	private final String timeZone;

	private final String dateFormat;

	private final String timeFormat;

	@JsonCreator
	@Builder
	public CreateAccountDateTimeFormatRequest(@TimeZone @NotBlank @Length(max = 100) String timeZone,
			@NotBlank @Length(max = 20) @DateFormat String dateFormat,
			@NotBlank @Length(max = 20) @TimeFormat String timeFormat) {
		super(UserPreferenceKey.ACCOUNT_DATE_TIME_FORMAT);
		this.timeZone = timeZone;
		this.dateFormat = dateFormat;
		this.timeFormat = timeFormat;
	}
}
