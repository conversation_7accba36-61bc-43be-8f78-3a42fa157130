/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.shared.http.users.request;

import com.styl.pacific.common.validator.filepath.FilePath;
import com.styl.pacific.common.validator.filepath.FileType;
import com.styl.pacific.common.validator.phonenumber.PhoneNumber;
import com.styl.pacific.user.shared.enums.UserStatus;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.hibernate.validator.constraints.Length;

@Getter
@Builder
@RequiredArgsConstructor
public class UpdateUserRequest {
	@NotNull
	private final Long id;

	@Length(max = 50)
	private final String ssoId;

	@Length(max = 120)
	private final String firstName;

	@Email
	@Length(max = 50)
	private final String email;

	@Length(max = 120)
	private final String lastName;

	@Length(max = 36)
	private final String externalId;

	@PhoneNumber
	@Length(max = 20)
	private final String phoneNumber;

	private final Long userGroupId;

	@NotNull
	private final UserStatus userStatus;

	@FilePath(fileType = FileType.IMAGE)
	@Length(max = 250)
	private final String avatarPath;

	@Length(max = 256)
	private final String avatarHash;

	@NotEmpty
	private final List<UpdateUserPermissionRequest> updatePermissions;
}
