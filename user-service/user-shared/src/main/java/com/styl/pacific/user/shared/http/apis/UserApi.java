/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.shared.http.apis;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.permissions.PacificApiAuthorized;
import com.styl.pacific.domain.permissions.PacificApiPermissionKey;
import com.styl.pacific.domain.permissions.PlatformApiSecurityLevel;
import com.styl.pacific.user.shared.http.customers.request.NotificationControlRequest;
import com.styl.pacific.user.shared.http.users.request.AddUserRequest;
import com.styl.pacific.user.shared.http.users.request.DeleteUserRequest;
import com.styl.pacific.user.shared.http.users.request.QueryUserPaginationRequest;
import com.styl.pacific.user.shared.http.users.request.UpdateUserRequest;
import com.styl.pacific.user.shared.http.users.response.UserResponse;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;

public interface UserApi {

	@PostMapping(path = "/api/user/users")
	@ResponseStatus(HttpStatus.CREATED)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = {
			PacificApiPermissionKey.USER_MGMT_ADD, PacificApiPermissionKey.CUSTOMER_MGMT_ADD })
	UserResponse addNewUser(@RequestBody @Valid AddUserRequest request,
			@SpringQueryMap @ModelAttribute @Valid NotificationControlRequest notificationControl);

	@DeleteMapping(path = "/api/user/users")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.USER_MGMT_DELETE)
	void deleteUsers(@RequestBody @Valid DeleteUserRequest request);

	@PutMapping(path = "/api/user/users")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.USER_MGMT_UPDATE)
	UserResponse updateSingleUser(@RequestBody @Valid UpdateUserRequest request);

	@GetMapping(path = "/api/user/users")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	Paging<UserResponse> queryUsers(@SpringQueryMap @ModelAttribute @Valid QueryUserPaginationRequest request);

	@PutMapping(path = "/api/user/users/{userId}/roles/{userRoleId}/change")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.USER_MGMT_UPDATE)
	UserResponse changeUserRole(@PathVariable("userId") Long userId, @PathVariable("userRoleId") Long userRoleId);

}
