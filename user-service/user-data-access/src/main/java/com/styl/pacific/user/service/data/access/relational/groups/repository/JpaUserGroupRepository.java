/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.data.access.relational.groups.repository;

import com.styl.pacific.data.access.jpa.querydsl.BaseQuerydslRepository;
import com.styl.pacific.user.service.data.access.relational.groups.entities.UserGroupEntity;
import java.util.Optional;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface JpaUserGroupRepository extends BaseQuerydslRepository<UserGroupEntity, Long> {

	@Modifying
	@Query(nativeQuery = true, value = "UPDATE user_group_paths gr " + "SET user_group_path = :newParentPath "
			+ "                 || subpath(gr.user_group_path, nlevel(text2ltree(:parentPath))) "
			+ "WHERE gr.tenant_id = :tenantId "
			+ "  AND gr.user_group_path <@ text2ltree(:parentPath) "
			+ "  AND gr.user_group_path != text2ltree(:parentPath) ")
	void updateChildrenPath(@Param("tenantId") Long tenantId, @Param("parentPath") String parentPath,
			@Param("newParentPath") String newParentPath);

	Optional<UserGroupEntity> findByTenantIdAndId(Long tenantId, Long id);
}
