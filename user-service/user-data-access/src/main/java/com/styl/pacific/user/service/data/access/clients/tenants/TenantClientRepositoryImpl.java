/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.data.access.clients.tenants;

import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.domain.constants.CommonDomainConstants;
import com.styl.pacific.domain.valueobject.BaseId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantResponse;
import com.styl.pacific.user.service.core.features.tenants.entities.Tenant;
import com.styl.pacific.user.service.core.features.tenants.entities.TenantSetting;
import com.styl.pacific.user.service.core.features.tenants.service.TenantRepository;
import com.styl.pacific.user.service.data.access.clients.tenants.configs.TenantDefaultTimeFormatConfig;
import com.styl.pacific.user.service.data.access.clients.tenants.mapper.TenantClientMapper;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class TenantClientRepositoryImpl implements TenantRepository {

	private final TenantClient tenantClient;

	private final TenantDefaultTimeFormatConfig defaultTimeFormatConfig;

	@Override
	public Optional<Tenant> findTenantByTenantId(TenantId tenantId) {
		return Optional.ofNullable(tenantId)
				.map(BaseId::getValue)
				.map(tenantClient::getTenant)
				.map(ResponseEntity::getBody)
				.map(TenantClientMapper.INSTANCE::toTenant);
	}

	@Override
	public Optional<TenantSetting> findTenantSettingByTenantId(TenantId tenantId) {
		final var tenantSettings = Optional.ofNullable(tenantId)
				.map(BaseId::getValue)
				.map(tenantClient::getTenant)
				.map(ResponseEntity::getBody)
				.map(TenantResponse::getSettings)
				.map(TenantClientMapper.INSTANCE::toTenantSetting);

		if (CommonDomainConstants.SYSTEM_TENANT_ID.equals(tenantId) && tenantSettings.isEmpty()) {
			return Optional.of(TenantSetting.builder()
					.timeFormat(defaultTimeFormatConfig.getTimeFormat())
					.currency(MapstructCommonMapper.INSTANCE.currencyCodeToResponse(defaultTimeFormatConfig
							.getCurrency()))
					.dateFormat(defaultTimeFormatConfig.getDateFormat())
					.timeZone(MapstructCommonMapper.INSTANCE.timezoneToTimeZoneResponse(MapstructCommonMapper.INSTANCE
							.stringToTimeZone(defaultTimeFormatConfig.getTimeZone()))

					)
					.build());
		}

		return tenantSettings;
	}
}
