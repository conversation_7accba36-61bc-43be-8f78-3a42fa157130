/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.data.access.relational.users.projections;

import com.styl.pacific.domain.enums.UserType;
import com.styl.pacific.user.shared.enums.UserNonCompletedAction;
import com.styl.pacific.user.shared.enums.UserStatus;
import java.time.Instant;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.With;

@Getter
@Builder
@RequiredArgsConstructor
@With
public class UserFetchProjection {
	private final Long id;
	private final String ssoId;
	private final String realmId;
	private final String externalId;
	private final String uniqueExternalId;
	private final UserType userType;
	private final UserStatus userStatus;
	private final String firstName;
	private final String lastName;
	private final String fullName;
	private final String email;
	private final String phoneNumber;
	private final String avatarPath;
	private final String avatarHash;
	private final String trustedSource;
	private final String migrationId;
	private final Boolean isAlternativeUser;
	private final String familyCode;
	private final UserNonCompletedAction[] nonCompletedActions;
	private final Long createdBy;
	private final Long updatedBy;
	private final Instant createdAt;
	private final Instant updatedAt;
	private final Instant deletedAt;
	private final Instant schedulingDeletedAt;
	// User entity fetch
	private final Long userGroupId;
	private final String userGroupKey;
	private final String userGroupName;
	private final String userGroupPath;
	private final String description;
	private final String userGroupMigrationId;
	private final Long parentId;
	private final Long tenantId;
}
