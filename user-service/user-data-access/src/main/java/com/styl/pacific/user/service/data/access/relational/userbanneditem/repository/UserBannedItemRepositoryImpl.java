/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.data.access.relational.userbanneditem.repository;

import com.styl.pacific.domain.valueobject.BaseId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.user.service.core.features.userbanneditems.UserBannedItemRepository;
import com.styl.pacific.user.service.core.features.userbanneditems.entities.UserBannedItem;
import com.styl.pacific.user.service.core.features.users.entities.User;
import com.styl.pacific.user.service.data.access.relational.userbanneditem.mapper.UserBannedItemEntityMapper;
import com.styl.pacific.user.service.data.access.relational.users.repository.JpaUserRepository;
import com.styl.pacific.user.shared.exceptions.UserBannedItemException;
import com.styl.pacific.user.shared.exceptions.UserNotFoundException;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class UserBannedItemRepositoryImpl implements UserBannedItemRepository {

	private final JpaUserRepository jpaUserRepository;
	private final JpaUserBannedItemRepository jpaUserBannedItemRepository;

	@Override
	public UserBannedItem save(UserBannedItem userBannedItem) {
		final var userId = Optional.ofNullable(userBannedItem.getUser())
				.map(User::getId)
				.map(BaseId::getValue)
				.orElseThrow(() -> new UserBannedItemException("UserId is required"));
		final var userEntity = jpaUserRepository.findById(userId)
				.orElseThrow(() -> new UserNotFoundException("User is not found"));
		return UserBannedItemEntityMapper.INSTANCE.toModel(jpaUserBannedItemRepository.save(
				UserBannedItemEntityMapper.INSTANCE.toCreateEntity(userEntity, userBannedItem)));
	}

	@Override
	public List<UserBannedItem> getListUserBannedItemByUserIdAndTenantId(UserId userId, TenantId tenantId) {
		return jpaUserBannedItemRepository.findByUserEntityIdAndTenantId(userId.getValue(), tenantId.getValue())
				.stream()
				.map(UserBannedItemEntityMapper.INSTANCE::toModel)
				.toList();
	}

	@Override
	public void deleteUserBannedItemsNotIn(TenantId tenantId, UserId userId, List<Long> ids) {
		jpaUserBannedItemRepository.deleteHardAllNotIn(userId.getValue(), tenantId.getValue(), ids);
	}
}
