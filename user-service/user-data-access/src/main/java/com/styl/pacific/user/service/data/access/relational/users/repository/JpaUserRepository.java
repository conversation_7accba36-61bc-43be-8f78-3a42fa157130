/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.data.access.relational.users.repository;

import com.querydsl.core.types.Predicate;
import com.styl.pacific.data.access.jpa.querydsl.BaseQuerydslRepository;
import com.styl.pacific.user.service.data.access.relational.users.entities.QUserEntity;
import com.styl.pacific.user.service.data.access.relational.users.entities.UserEntity;
import com.styl.pacific.user.shared.enums.UserStatus;
import java.time.Instant;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

public interface JpaUserRepository extends BaseQuerydslRepository<UserEntity, Long>, AdvanceQueryDslUserRepository {

	@Override
	default Predicate softDeletionPredicate() {
		return softDeletionPredicate(QUserEntity.userEntity);
	}

	default Predicate softDeletionPredicate(QUserEntity qUserEntity) {
		return qUserEntity.deletedAt.isNull();
	}

	@EntityGraph(attributePaths = { "permissions", "userGroupEntity" })
	List<UserEntity> getAllByDeletedAtIsNullAndIdIn(Collection<Long> ids);

	@EntityGraph(attributePaths = { "permissions", "userGroupEntity" })
	List<UserEntity> getAllByDeletedAtIsNullAndRealmIdAndEmailIn(String realmId, Collection<String> emails);

	@EntityGraph(attributePaths = { "permissions", "userGroupEntity" })
	Optional<UserEntity> getOneByDeletedAtIsNullAndRealmIdAndEmail(String realmId, String email);

	@EntityGraph(attributePaths = { "permissions", "userGroupEntity" })
	Optional<UserEntity> getOneByDeletedAtIsNullAndRealmIdAndSsoId(String realmId, String ssoId);

	@EntityGraph(attributePaths = { "permissions", "userGroupEntity" })
	Optional<UserEntity> getOneByDeletedAtIsNullAndId(Long id);

	@Modifying
	@Query("UPDATE UserEntity e " + "SET "
			+ "e.externalId = null, "
			+ "e.firstName = null, "
			+ "e.lastName = null, "
			+ "e.fullName = null, "
			+ "e.email = null, "
			+ "e.phoneNumber = null, "
			+ "e.uniqueExternalId = null, "
			+ "e.avatarPath = null, "
			+ "e.migrationId = null, "
			+ "e.deletedAt = current_timestamp,"
			+ "e.userStatus = 'ARCHIVED'"
			+ " WHERE e.id = ?1")
	void deleteUserById(Long id);

	@Transactional
	@Modifying
	@Query("UPDATE UserEntity e " + "SET e.schedulingDeletedAt = ?3,"
			+ "e.userStatus = 'ARCHIVED' "
			+ "WHERE EXISTS (SELECT 1 FROM UserPermissionEntity p WHERE p.userEntity = e AND p.tenantId = ?1) AND e.id IN ?2 ")
	void deleteUserByIdsInSchedule(Long tenantId, Set<Long> userIds, Instant schedulingAt);

	@Transactional
	@Modifying
	@Query("UPDATE UserEntity e " + "SET e.userStatus = ?1 "
			+ "WHERE e.id IN ( "
			+ "SELECT i.id "
			+ "FROM UserEntity i "
			+ "WHERE i.userStatus = 'ARCHIVED' AND i.schedulingDeletedAt <= current_timestamp "
			+ "ORDER BY i.schedulingDeletedAt ASC LIMIT ?2 "
			+ ")")
	int changeUserIntoDeletingProgress(UserStatus userStatus, int limitUserAffected);
}
