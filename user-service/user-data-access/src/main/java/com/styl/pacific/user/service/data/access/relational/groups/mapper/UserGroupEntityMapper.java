/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.data.access.relational.groups.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.user.service.core.features.groups.entities.UserGroup;
import com.styl.pacific.user.service.data.access.relational.groups.entities.UserGroupEntity;
import com.styl.pacific.user.service.data.access.relational.groups.projections.UserGroupLiteProjection;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class, })
public interface UserGroupEntityMapper {
	UserGroupEntityMapper INSTANCE = Mappers.getMapper(UserGroupEntityMapper.class);

	@Mapping(target = "id", source = "source.id", qualifiedByName = "userGroupIdToLong")
	@Mapping(target = "userGroupPathEntity.id", source = "source.id", qualifiedByName = "userGroupIdToLong")
	@Mapping(target = "userGroupPathEntity.tenantId", source = "source.tenantId", qualifiedByName = "tenantIdToLong")
	@Mapping(target = "userGroupPathEntity.path", source = "source.path")
	@Mapping(target = "groupName", source = "source.groupName")
	@Mapping(target = "description", source = "source.description")
	@Mapping(target = "tenantId", source = "source.tenantId", qualifiedByName = "tenantIdToLong")
	@Mapping(target = "groupKey", source = "source.groupKey")
	@Mapping(target = "migrationId", source = "source.migrationId")
	@Mapping(target = "createdBy", ignore = true)
	@Mapping(target = "updatedBy", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "updatedAt", ignore = true)
	@Mapping(target = "parentEntity", source = "parent")
	UserGroupEntity toNewEntity(UserGroup source, UserGroupEntity parent);

	@Mapping(target = "id", source = "id", qualifiedByName = "longToUserGroupId")
	@Mapping(target = "path", source = "userGroupPathEntity.path")
	@Mapping(target = "tenantId", source = "tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "parent", source = "parentEntity")
	@Mapping(target = "children", ignore = true)
	UserGroup toModel(UserGroupEntity entity);

	@Mapping(target = "id", source = "id", qualifiedByName = "longToUserGroupId")
	@Mapping(target = "tenantId", source = "tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "parent.id", source = "parentId", qualifiedByName = "longToUserGroupId")
	@Mapping(target = "children", ignore = true)
	@Mapping(target = "updatedBy", ignore = true)
	@Mapping(target = "updatedAt", ignore = true)
	@Mapping(target = "createdBy", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	UserGroup toModel(UserGroupLiteProjection projection);

}
