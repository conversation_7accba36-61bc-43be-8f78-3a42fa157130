CREATE
    TABLE
        IF NOT EXISTS user_groups(
            id BIGSERIAL NOT NULL CONSTRAINT user_groups_pk PRIMARY KEY,
            tenant_id BIGINT NOT NULL,
            user_group_path LTREE NOT NULL,
            group_name VARCHAR(20) NOT NULL,
            description VARCHAR(20) NOT NULL,
            parent_id BIGINT,
            created_at TIMESTAMP(6) DEFAULT NOW(),
            updated_at TIMESTAMP(6) DEFAULT NOW(),
            created_by BIGINT,
            updated_by BIGINT
        );

CREATE
    INDEX IF NOT EXISTS user_groups_user_group_path_idx ON
    user_groups(
        tenant_id,
        user_group_path
    );

CREATE
    INDEX IF NOT EXISTS user_groups_user_group_path_gtidx ON
    user_groups
        USING GIST(
        user_group_path gist_ltree_ops(
            siglen = 200
        )
    );
