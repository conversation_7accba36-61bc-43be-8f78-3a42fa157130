CREATE
    TABLE
        IF NOT EXISTS alternative_user_accesses(
            user_id BIGINT NOT NULL,
            tenant_id BIGINT NOT NULL,
            alternative_user_id BIGINT NOT NULL,
            created_at TIMESTAMP(6) WITH TIME ZONE,
            updated_at TIMESTAMP(6) WITH TIME ZONE,
            CONSTRAINT alternative_user_accesses_user_id_fk FOREIGN KEY(user_id) REFERENCES users(id),
            CONSTRAINT alternative_user_accesses_alternative_user_id_fk FOREIGN KEY(alternative_user_id) REFERENCES users(id),
            PRIMARY KEY(
                user_id,
                tenant_id,
                alternative_user_id
            )
        );