CREATE
    TABLE
        IF NOT EXISTS user_group_paths(
            id BIGSERIAL NOT NULL CONSTRAINT user_group_path_pk PRIMARY KEY,
            tenant_id BIGINT NOT NULL,
            user_group_path LTREE NOT NULL
        );

CREATE
    INDEX IF NOT EXISTS user_groups_group_path_idx ON
    user_group_paths(
        tenant_id,
        user_group_path
    );

CREATE
    INDEX IF NOT EXISTS user_groups_group_path_gtidx ON
    user_group_paths
        USING GIST(
        user_group_path gist_ltree_ops(
            siglen = 200
        )
    );

INSERT
    INTO
        user_group_paths(
            id,
            tenant_id,
            user_group_path
        ) SELECT
            gp.id,
            gp.tenant_id,
            gp.user_group_path
        FROM
            user_groups gp;

DROP
    INDEX IF EXISTS user_groups_user_group_path_idx CASCADE;

DROP
    INDEX IF EXISTS user_groups_user_group_path_gtidx CASCADE;

ALTER TABLE
    user_groups DROP
        COLUMN IF EXISTS user_group_path;

ALTER TABLE
    IF EXISTS user_groups ADD COLUMN IF NOT EXISTS user_group_path_id BIGINT;

ALTER TABLE
    IF EXISTS user_groups ADD CONSTRAINT fk_user_groups_user_group_path_id FOREIGN KEY(user_group_path_id) REFERENCES user_group_paths(id);

CREATE
    INDEX IF NOT EXISTS user_group_user_group_path_id_idx ON
    user_groups(user_group_path_id);

UPDATE
    user_groups
SET
    user_group_path_id = id;