<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.styl.pacific.user.service</groupId>
        <artifactId>user-service</artifactId>
        <version>1.2.4</version>
    </parent>
    <artifactId>user-application</artifactId>

    <properties>
        <docker.skip>true</docker.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>common-application</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>common-feign</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>common-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.user.service</groupId>
            <artifactId>user-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.user.service</groupId>
            <artifactId>user-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.user.service</groupId>
            <artifactId>user-data-access</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.user.service</groupId>
            <artifactId>user-messaging</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.user.service</groupId>
            <artifactId>user-scheduling</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                    <image>
                        <name>${project.groupId}:${project.version}</name>
                        <createdDate>${maven.build.timestamp}</createdDate>
                    </image>
                </configuration>
                <executions>
                    <execution>
                        <id>build-info</id>
                        <goals>
                            <goal>build-info</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                    <execution>
                        <goals>
                            <goal>build-image</goal>
                        </goals>
                        <phase>package</phase>
                        <configuration>
                            <skip>${docker.skip}</skip>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
