/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.http.controller.users;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrowsExactly;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.application.rest.dto.ErrorResponse;
import com.styl.pacific.authz.shared.http.responses.UserRoleResponse;
import com.styl.pacific.common.feign.exception.FeignBadRequestException;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.common.test.BaseWebClientWithDbTest;
import com.styl.pacific.common.test.utils.GenerateHttpHeader;
import com.styl.pacific.common.test.utils.HeaderGenerator;
import com.styl.pacific.domain.constants.CommonDomainConstants;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.enums.DefaultSystemRole;
import com.styl.pacific.domain.enums.UserType;
import com.styl.pacific.domain.exception.GlobalErrorCode;
import com.styl.pacific.domain.range.LongDateTimeRange;
import com.styl.pacific.domain.tokenclaims.UserTokenClaim;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserGroupId;
import com.styl.pacific.domain.valueobject.UserRoleId;
import com.styl.pacific.notification.service.constant.Action;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantSettingsResponse;
import com.styl.pacific.user.service.config.IntegrationTestConfiguration;
import com.styl.pacific.user.service.config.UserInitializationIntegrationSupporter;
import com.styl.pacific.user.service.config.UserIntegrationTestContainer;
import com.styl.pacific.user.service.core.features.alternativeaccess.AlternativeUserAccessCommandService;
import com.styl.pacific.user.service.core.features.alternativeaccess.request.CreateAlternativeUserAccessCommand;
import com.styl.pacific.user.service.core.features.cards.UserCardCommandService;
import com.styl.pacific.user.service.core.features.cards.UserCardQueryService;
import com.styl.pacific.user.service.core.features.cards.request.CreateUserCardCommand;
import com.styl.pacific.user.service.core.features.cards.request.FilterUserCardPaginationQuery;
import com.styl.pacific.user.service.core.features.cards.request.UserCardPaginationQuery;
import com.styl.pacific.user.service.core.features.groups.UserGroupCommandService;
import com.styl.pacific.user.service.core.features.groups.UserGroupQueryService;
import com.styl.pacific.user.service.core.features.groups.constants.UserGroupConstants;
import com.styl.pacific.user.service.core.features.groups.request.GetUserGroupQuery;
import com.styl.pacific.user.service.core.features.groups.request.UpsertUserGroupCommand;
import com.styl.pacific.user.service.core.features.invitations.UserInvitationQueryService;
import com.styl.pacific.user.service.core.features.invitations.request.GetUserInvitationQuery;
import com.styl.pacific.user.service.core.features.notifications.NotificationPublisher;
import com.styl.pacific.user.service.core.features.notifications.request.ControlNotificationCommand;
import com.styl.pacific.user.service.core.features.preferences.UserPreferenceQueryService;
import com.styl.pacific.user.service.core.features.preferences.entities.DateTimeFormatPreferenceData;
import com.styl.pacific.user.service.core.features.preferences.request.FindUserPreferenceQuery;
import com.styl.pacific.user.service.core.features.requiredactions.UserRequiredActionQueryService;
import com.styl.pacific.user.service.core.features.requiredactions.request.UserRequiredActionFilter;
import com.styl.pacific.user.service.core.features.requiredactions.request.UserRequiredActionPaginationQuery;
import com.styl.pacific.user.service.core.features.subaccounts.UserSubAccountCommandService;
import com.styl.pacific.user.service.core.features.subaccounts.UserSubAccountRepository;
import com.styl.pacific.user.service.core.features.subaccounts.request.CreateSubAccountExistingUserCommand;
import com.styl.pacific.user.service.core.features.subaccounts.request.CreateSubAccountNewUserCommand;
import com.styl.pacific.user.service.core.features.users.UserCommandService;
import com.styl.pacific.user.service.core.features.users.UserQueryService;
import com.styl.pacific.user.service.core.features.users.entities.User;
import com.styl.pacific.user.service.core.features.users.generators.CustomerUniqueExternalIdGenerator;
import com.styl.pacific.user.service.core.features.users.mapper.UserMappingFunctionSupport;
import com.styl.pacific.user.service.core.features.users.request.ActivateUserTenantCommand;
import com.styl.pacific.user.service.core.features.users.request.DeleteUserCommand;
import com.styl.pacific.user.service.core.features.users.request.FilterUserQuery;
import com.styl.pacific.user.service.core.features.users.request.UpsertTenantUserCommand;
import com.styl.pacific.user.service.core.features.users.request.UserPaginationQuery;
import com.styl.pacific.user.service.data.access.clients.catalogs.AllergenClient;
import com.styl.pacific.user.service.data.access.clients.keycloak.KeycloakServiceAccountClient;
import com.styl.pacific.user.service.data.access.clients.keycloak.configs.KeycloakServiceAccountConfigProperties;
import com.styl.pacific.user.service.data.access.clients.keycloak.response.KeycloakAccessTokenResponse;
import com.styl.pacific.user.service.data.access.clients.tenants.TenantClient;
import com.styl.pacific.user.service.data.access.clients.tenants.configs.TenantDefaultTimeFormatConfig;
import com.styl.pacific.user.service.data.access.clients.userroles.UserRoleClient;
import com.styl.pacific.user.service.messaging.notifications.publisher.kafka.NotificationCreatedEventKafkaPublisher;
import com.styl.pacific.user.service.messaging.users.publisher.kafka.UserCreatedEventKafkaPublisher;
import com.styl.pacific.user.service.scheduling.features.users.DeletingUserScheduler;
import com.styl.pacific.user.shared.enums.UserCardStatus;
import com.styl.pacific.user.shared.enums.UserInvitationType;
import com.styl.pacific.user.shared.enums.UserNonCompletedAction;
import com.styl.pacific.user.shared.enums.UserPermissionStatus;
import com.styl.pacific.user.shared.enums.UserPreferenceKey;
import com.styl.pacific.user.shared.enums.UserRequiredActionType;
import com.styl.pacific.user.shared.enums.UserStatus;
import com.styl.pacific.user.shared.exceptions.UserInvitationNotFoundException;
import com.styl.pacific.user.shared.exceptions.UserNotFoundException;
import com.styl.pacific.user.shared.http.users.request.AddBackOfficeUserRequest;
import com.styl.pacific.user.shared.http.users.request.AddCustomerUserRequest;
import com.styl.pacific.user.shared.http.users.request.AddSystemAdminUserRequest;
import com.styl.pacific.user.shared.http.users.request.DeleteUserRequest;
import com.styl.pacific.user.shared.http.users.request.QueryUserPaginationRequest;
import com.styl.pacific.user.shared.http.users.request.QueryUserRequest;
import com.styl.pacific.user.shared.http.users.request.UpdateUserMeRequest;
import com.styl.pacific.user.shared.http.users.request.UpdateUserPermissionRequest;
import com.styl.pacific.user.shared.http.users.request.UpdateUserRequest;
import com.styl.pacific.user.shared.http.users.response.UserResponse;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.apache.commons.lang3.StringUtils;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.util.LinkedMultiValueMap;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = IntegrationTestConfiguration.class)
class UserControllerIntegrationTest extends BaseWebClientWithDbTest implements UserIntegrationTestContainer {

	@Autowired
	private ObjectMapper objectMapper;

	@MockitoBean
	private UserRoleClient userRoleClient;

	@MockitoBean
	private TenantClient tenantClient;

	@MockitoBean
	private AllergenClient allergenClient;

	@MockitoBean
	private KeycloakServiceAccountClient keycloakServiceAccountClient;

	@MockitoBean
	private NotificationCreatedEventKafkaPublisher notificationCreatedEventKafkaPublisher;

	@MockitoBean
	private UserCreatedEventKafkaPublisher userCreatedEventKafkaPublisher;

	@Autowired
	private UserCommandService userCommandService;

	@Autowired
	private UserQueryService userQueryService;

	@Autowired
	private UserInvitationQueryService userInvitationQueryService;

	@Autowired
	private UserRequiredActionQueryService userRequiredActionQueryService;

	@Autowired
	private UserGroupCommandService groupCommandService;

	@Autowired
	private UserGroupQueryService groupQueryService;

	@Autowired
	private UserCardCommandService cardCommandService;

	@Autowired
	private NotificationPublisher notificationPublisher;

	@Autowired
	private UserCardQueryService cardQueryService;

	@Autowired
	private AlternativeUserAccessCommandService alternativeUserAccessCommandService;

	@Autowired
	private UserSubAccountCommandService subAccountCommandService;

	@Autowired
	private UserSubAccountRepository subAccountRepository;

	@Autowired
	private UserPreferenceQueryService userPreferenceQueryService;

	@Autowired
	private TenantDefaultTimeFormatConfig defaultTimeFormatConfig;

	@Autowired
	private DeletingUserScheduler deletingUserScheduler;

	private UserInitializationIntegrationSupporter userSupporter;

	@BeforeEach
	void setUp() {
		userSupporter = new UserInitializationIntegrationSupporter(userRoleClient, tenantClient, userCommandService);
	}

	@Test
	void testAddBackOfficeWhenValidRequestThenReturnUserResponse() {
		// Arrange
		final var request = AddBackOfficeUserRequest.builder()
				.userRoleId(1L)
				.email("<EMAIL>")
				.tenantId(1L)
				.firstName("First Name")
				.lastName("Last Name")
				.externalId("External ID 11000")
				.phoneNumber("**********")
				.build();

		final var userCreatedEventCaptor = ArgumentCaptor.forClass(
				com.styl.pacific.kafka.users.avro.model.UserCreatedEvent.class);
		final var now = Instant.now();
		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.BACK_OFFICE)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();

		when(userRoleClient.getUserRole(1L, null, 1L, true)).thenReturn(UserRoleResponse.builder()
				.tenantId("1")
				.id("1")
				.build());

		final var notificationEventCaptor = ArgumentCaptor.forClass(
				com.styl.pacific.kafka.notification.avro.model.NotificationCreatedAvroEvent.class);

		final var tenantSettings = TenantSettingsResponse.builder()
				.timeZone(MapstructCommonMapper.INSTANCE.timezoneToTimeZoneResponse(TimeZone.getTimeZone(
						"Asia/Ho_Chi_Minh")))
				.dateFormat("YYYY-MM-dd")
				.timeFormat("HH:mm:ss")
				.build();

		when(tenantClient.getTenant(1L)).thenReturn(ResponseEntity.ok(TenantResponse.builder()
				.tenantId(1L)
				.settings(tenantSettings)
				.build()));

		// Act & Assert
		webClient.post()
				.uri("/api/user/users")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(1L)
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isCreated()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {
					final var userResponse = response.getResponseBody();
					Assertions.assertNotNull(userResponse);

					Assertions.assertEquals(UserType.BACK_OFFICE, userResponse.getUserType());
					Assertions.assertEquals(request.getEmail(), userResponse.getEmail());
					Assertions.assertEquals(request.getTenantId()
							.toString(), userResponse.getPermissions()
									.getFirst()
									.tenantId());
					Assertions.assertEquals(request.getLastName(), userResponse.getLastName());
					Assertions.assertEquals(request.getFirstName(), userResponse.getFirstName());
					Assertions.assertEquals(request.getExternalId(), userResponse.getExternalId());
					assertFalse(userResponse.getIsAlternativeUser());
					Assertions.assertEquals(UserMappingFunctionSupport.buildFullName(request.getFirstName(), request
							.getLastName()), userResponse.getFullName());

					Assertions.assertDoesNotThrow(() -> userInvitationQueryService.getTenantUserInvitation(
							GetUserInvitationQuery.builder()
									.byUserId(MapstructCommonDomainMapper.INSTANCE.stringToUserId(userResponse.getId()))
									.byType(UserInvitationType.USER_REGISTRATION)
									.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
									.build()));

					verify(notificationCreatedEventKafkaPublisher, times(1)).publish(notificationEventCaptor.capture());
					final var notificationEvent = notificationEventCaptor.getValue();
					Assertions.assertEquals(Action.INVITE_USER, notificationEvent.getAction());
					assertThat(notificationEvent.getChannels(), Matchers.containsInAnyOrder(List.of(
							com.styl.pacific.kafka.notification.avro.model.NotificationAvroChannel.EMAIL)
							.toArray()));
					Assertions.assertNotNull(notificationEvent.getData()
							.get("invitationLink"));
					Assertions.assertTrue(notificationEvent.getData()
							.get("invitationLink")
							.contains(URLEncoder.encode("pacific-backoffice.com", StandardCharsets.UTF_8)));

					verify(userCreatedEventKafkaPublisher, times(1)).publish(userCreatedEventCaptor.capture());
					final var actualUserCreatedEvent = userCreatedEventCaptor.getValue();

					Assertions.assertEquals(userResponse.getId(), String.valueOf(actualUserCreatedEvent.getUserId()));
					Assertions.assertEquals(userResponse.getEmail(), actualUserCreatedEvent.getEmail());
					Assertions.assertEquals(userResponse.getFirstName(), actualUserCreatedEvent.getFirstName());
					Assertions.assertEquals(userResponse.getLastName(), actualUserCreatedEvent.getLastName());

					final var userPreferenceDateTimeFormat = userPreferenceQueryService.getUserPreference(
							FindUserPreferenceQuery.builder()
									.byTenantId(new TenantId(1L))
									.byUserId(MapstructCommonDomainMapper.INSTANCE.stringToUserId(userResponse.getId()))
									.byKey(UserPreferenceKey.ACCOUNT_DATE_TIME_FORMAT)
									.build());
					final var dateTimeFormat = (DateTimeFormatPreferenceData) userPreferenceDateTimeFormat.getData();
					Assertions.assertEquals(tenantSettings.getDateFormat(), dateTimeFormat.getDateFormat());
					Assertions.assertEquals(tenantSettings.getTimeFormat(), dateTimeFormat.getTimeFormat());
					Assertions.assertEquals(tenantSettings.getTimeZone()
							.getZoneId(), dateTimeFormat.getTimeZone()
									.getID());

					Assertions.assertEquals(userResponse.getPhoneNumber(), actualUserCreatedEvent.getPhoneNumber());
					Assertions.assertEquals(userResponse.getRealmId(), actualUserCreatedEvent.getRealmId());

					actualUserCreatedEvent.getPermissions()
							.forEach(actualPermission -> {
								final var firstPermission = userResponse.getPermissions()
										.getFirst();
								Assertions.assertEquals(firstPermission.tenantId(), String.valueOf(actualPermission
										.getTenantId()));
								Assertions.assertEquals(firstPermission.permissionStatus()
										.name(), actualPermission.getPermissionStatus()
												.name());

							});
				});
	}

	@Test
	void testAddSystemAdminWhenValidRequestThenReturnUserResponse() {
		// Arrange
		final var request = AddSystemAdminUserRequest.builder()
				.userRoleId(1L)
				.email("<EMAIL>")
				.tenantId(1L)
				.firstName("First Name")
				.lastName("Last Name")
				.externalId("Admin External ID 111000")
				.phoneNumber("**********")
				.build();

		final var userCreatedEventCaptor = ArgumentCaptor.forClass(
				com.styl.pacific.kafka.users.avro.model.UserCreatedEvent.class);

		final var now = Instant.now();
		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.SYSTEM_ADMIN)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();

		when(userRoleClient.getUserRole(null, DefaultSystemRole.SYSTEM_ADMIN_ROLE.getExternalId(),
				CommonDomainConstants.SYSTEM_TENANT_ID.getValue(), true)).thenReturn(UserRoleResponse.builder()
						.tenantId("0")
						.id("0")
						.build());

		final var notificationEventCaptor = ArgumentCaptor.forClass(
				com.styl.pacific.kafka.notification.avro.model.NotificationCreatedAvroEvent.class);

		final var tenantSettings = TenantSettingsResponse.builder()
				.timeZone(MapstructCommonMapper.INSTANCE.timezoneToTimeZoneResponse(TimeZone.getTimeZone(
						"Asia/Ho_Chi_Minh")))
				.dateFormat("YYYY-MM-dd")
				.timeFormat("HH:mm:ss")
				.build();

		when(tenantClient.getTenant(CommonDomainConstants.SYSTEM_TENANT_ID.getValue())).thenReturn(ResponseEntity.ok(
				TenantResponse.builder()
						.tenantId(CommonDomainConstants.SYSTEM_TENANT_ID.getValue())
						.realmId(CommonDomainConstants.SYSTEM_TENANT_ID.toString())
						.settings(tenantSettings)
						.build()));

		// Act & Assert
		webClient.post()
				.uri("/api/user/users")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(1L)
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isCreated()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {
					final var userResponse = response.getResponseBody();
					Assertions.assertNotNull(userResponse);

					Assertions.assertEquals(UserType.SYSTEM_ADMIN, userResponse.getUserType());
					Assertions.assertEquals(request.getEmail(), userResponse.getEmail());
					Assertions.assertEquals(CommonDomainConstants.SYSTEM_TENANT_ID.getValue()
							.toString(), userResponse.getPermissions()
									.getFirst()
									.tenantId());
					Assertions.assertEquals(request.getUserRoleId()
							.toString(), userResponse.getPermissions()
									.getFirst()
									.userRoleId());
					Assertions.assertEquals(request.getLastName(), userResponse.getLastName());
					Assertions.assertEquals(request.getFirstName(), userResponse.getFirstName());
					Assertions.assertEquals(request.getExternalId(), userResponse.getExternalId());
					assertFalse(userResponse.getIsAlternativeUser());
					Assertions.assertEquals(UserMappingFunctionSupport.buildFullName(request.getFirstName(), request
							.getLastName()), userResponse.getFullName());

					Assertions.assertDoesNotThrow(() -> userInvitationQueryService.getTenantUserInvitation(
							GetUserInvitationQuery.builder()
									.byUserId(MapstructCommonDomainMapper.INSTANCE.stringToUserId(userResponse.getId()))
									.byType(UserInvitationType.USER_REGISTRATION)
									.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
									.build()));

					verify(notificationCreatedEventKafkaPublisher, times(1)).publish(notificationEventCaptor.capture());
					final var notificationEvent = notificationEventCaptor.getValue();
					Assertions.assertEquals(Action.INVITE_USER, notificationEvent.getAction());
					assertThat(notificationEvent.getChannels(), Matchers.containsInAnyOrder(List.of(
							com.styl.pacific.kafka.notification.avro.model.NotificationAvroChannel.EMAIL)
							.toArray()));
					Assertions.assertNotNull(notificationEvent.getData()
							.get("invitationLink"));
					Assertions.assertTrue(notificationEvent.getData()
							.get("invitationLink")
							.contains(URLEncoder.encode("pacific-backoffice.com", StandardCharsets.UTF_8)));

					verify(userCreatedEventKafkaPublisher, times(1)).publish(userCreatedEventCaptor.capture());
					final var actualUserCreatedEvent = userCreatedEventCaptor.getValue();

					Assertions.assertEquals(userResponse.getId(), String.valueOf(actualUserCreatedEvent.getUserId()));
					Assertions.assertEquals(userResponse.getEmail(), actualUserCreatedEvent.getEmail());
					Assertions.assertEquals(userResponse.getFirstName(), actualUserCreatedEvent.getFirstName());
					Assertions.assertEquals(userResponse.getLastName(), actualUserCreatedEvent.getLastName());

					final var userPreferenceDateTimeFormat = userPreferenceQueryService.getUserPreference(
							FindUserPreferenceQuery.builder()
									.byTenantId(CommonDomainConstants.SYSTEM_TENANT_ID)
									.byUserId(MapstructCommonDomainMapper.INSTANCE.stringToUserId(userResponse.getId()))
									.byKey(UserPreferenceKey.ACCOUNT_DATE_TIME_FORMAT)
									.build());
					final var dateTimeFormat = (DateTimeFormatPreferenceData) userPreferenceDateTimeFormat.getData();
					Assertions.assertEquals(tenantSettings.getDateFormat(), dateTimeFormat.getDateFormat());
					Assertions.assertEquals(tenantSettings.getTimeFormat(), dateTimeFormat.getTimeFormat());
					Assertions.assertEquals(tenantSettings.getTimeZone()
							.getZoneId(), dateTimeFormat.getTimeZone()
									.getID());

					Assertions.assertEquals(userResponse.getPhoneNumber(), actualUserCreatedEvent.getPhoneNumber());
					Assertions.assertEquals(userResponse.getRealmId(), actualUserCreatedEvent.getRealmId());

					actualUserCreatedEvent.getPermissions()
							.forEach(actualPermission -> {
								final var firstPermission = userResponse.getPermissions()
										.getFirst();
								Assertions.assertEquals(firstPermission.tenantId(), String.valueOf(actualPermission
										.getTenantId()));
								Assertions.assertEquals(firstPermission.permissionStatus()
										.name(), actualPermission.getPermissionStatus()
												.name());

							});

				});
	}

	@Test
	void testAddCustomerWithEmailWhenValidRequestThenReturnUserResponse() {
		// Arrange
		final var request = AddCustomerUserRequest.builder()
				.userRoleId(1L)
				.email("<EMAIL>")
				.tenantId(1L)
				.firstName("First Name")
				.lastName("Last Name")
				.externalId("External ID 1")
				.phoneNumber("**********")
				.build();

		final var userCreatedEventCaptor = ArgumentCaptor.forClass(
				com.styl.pacific.kafka.users.avro.model.UserCreatedEvent.class);

		final var notificationEventCaptor = ArgumentCaptor.forClass(
				com.styl.pacific.kafka.notification.avro.model.NotificationCreatedAvroEvent.class);

		final var now = Instant.now();
		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.BACK_OFFICE)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();

		when(userRoleClient.getUserRole(1L, null, 1L, true)).thenReturn(UserRoleResponse.builder()
				.tenantId("1")
				.id("1")
				.build());

		when(userRoleClient.getUserRole(null, DefaultSystemRole.CUSTOMER_ROLE.getExternalId(), 1L, true)).thenReturn(
				UserRoleResponse.builder()
						.tenantId("1")
						.id("1")
						.build());

		final var tenantSettings = TenantSettingsResponse.builder()
				.timeZone(MapstructCommonMapper.INSTANCE.timezoneToTimeZoneResponse(TimeZone.getTimeZone(
						"Asia/Ho_Chi_Minh")))
				.dateFormat("YYYY-MM-dd")
				.timeFormat("HH:mm:ss")
				.defaultDomain("tenancy-cportal.com")
				.build();
		when(tenantClient.getTenant(1L)).thenReturn(ResponseEntity.ok(TenantResponse.builder()
				.tenantId(1L)
				.realmId("realm-001")
				.settings(tenantSettings)
				.build()));

		// Act & Assert
		webClient.post()
				.uri("/api/user/users")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(1L)
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isCreated()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {
					final var userResponse = response.getResponseBody();
					Assertions.assertNotNull(userResponse);

					Assertions.assertEquals(UserType.CUSTOMER, userResponse.getUserType());
					Assertions.assertEquals(request.getEmail(), userResponse.getEmail());
					Assertions.assertEquals(request.getTenantId()
							.toString(), userResponse.getPermissions()
									.getFirst()
									.tenantId());
					Assertions.assertEquals(request.getUserRoleId()
							.toString(), userResponse.getPermissions()
									.getFirst()
									.userRoleId());
					Assertions.assertEquals(request.getLastName(), userResponse.getLastName());
					Assertions.assertEquals(request.getFirstName(), userResponse.getFirstName());
					Assertions.assertEquals(UserMappingFunctionSupport.buildFullName(request.getFirstName(), request
							.getLastName()), userResponse.getFullName());
					Assertions.assertEquals(request.getExternalId(), userResponse.getExternalId());
					assertFalse(userResponse.getIsAlternativeUser());
					Assertions.assertEquals(CustomerUniqueExternalIdGenerator.buildCustomerUniqueExternalId(
							MapstructCommonDomainMapper.INSTANCE.longToTenantId(request.getTenantId()), request
									.getExternalId()), userResponse.getUniqueExternalId());

					Assertions.assertDoesNotThrow(() -> userInvitationQueryService.getTenantUserInvitation(
							GetUserInvitationQuery.builder()
									.byUserId(MapstructCommonDomainMapper.INSTANCE.stringToUserId(userResponse.getId()))
									.byType(UserInvitationType.USER_REGISTRATION)
									.realmId("realm-001")
									.build()));

					verify(notificationCreatedEventKafkaPublisher, times(1)).publish(notificationEventCaptor.capture());
					final var notificationEvent = notificationEventCaptor.getValue();
					Assertions.assertNotNull(notificationEvent.getData()
							.get("invitationLink"));
					Assertions.assertTrue(notificationEvent.getData()
							.get("invitationLink")
							.contains(URLEncoder.encode("tenancy-cportal.com", StandardCharsets.UTF_8)));

					verify(userCreatedEventKafkaPublisher, times(1)).publish(userCreatedEventCaptor.capture());
					final var actualUserCreatedEvent = userCreatedEventCaptor.getValue();

					Assertions.assertEquals(userResponse.getId(), String.valueOf(actualUserCreatedEvent.getUserId()));
					Assertions.assertEquals(userResponse.getEmail(), actualUserCreatedEvent.getEmail());
					Assertions.assertEquals(userResponse.getFirstName(), actualUserCreatedEvent.getFirstName());
					Assertions.assertEquals(userResponse.getLastName(), actualUserCreatedEvent.getLastName());

					final var userPreferenceDateTimeFormat = userPreferenceQueryService.getUserPreference(
							FindUserPreferenceQuery.builder()
									.byTenantId(new TenantId(1L))
									.byUserId(MapstructCommonDomainMapper.INSTANCE.stringToUserId(userResponse.getId()))
									.byKey(UserPreferenceKey.ACCOUNT_DATE_TIME_FORMAT)
									.build());
					final var dateTimeFormat = (DateTimeFormatPreferenceData) userPreferenceDateTimeFormat.getData();
					Assertions.assertEquals(tenantSettings.getDateFormat(), dateTimeFormat.getDateFormat());
					Assertions.assertEquals(tenantSettings.getTimeFormat(), dateTimeFormat.getTimeFormat());
					Assertions.assertEquals(tenantSettings.getTimeZone()
							.getZoneId(), dateTimeFormat.getTimeZone()
									.getID());

					actualUserCreatedEvent.getPermissions()
							.forEach(actualPermission -> {
								final var firstPermission = userResponse.getPermissions()
										.getFirst();
								Assertions.assertEquals(firstPermission.tenantId(), String.valueOf(actualPermission
										.getTenantId()));
								Assertions.assertEquals(firstPermission.permissionStatus()
										.name(), actualPermission.getPermissionStatus()
												.name());

							});

				});
	}

	@Test
	void testAddCustomerWithoutEmailWhenValidRequestThenReturnUserResponse() {
		// Arrange
		final var request = AddCustomerUserRequest.builder()
				.userRoleId(1L)
				.tenantId(1L)
				.firstName("First Name")
				.lastName("Last Name")
				.externalId("External ID 1100001")
				.phoneNumber("**********")
				.build();

		final var now = Instant.now();
		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.BACK_OFFICE)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();

		when(userRoleClient.getUserRole(1L, null, 1L, true)).thenReturn(UserRoleResponse.builder()
				.tenantId("1")
				.id("1")
				.build());

		when(userRoleClient.getUserRole(null, DefaultSystemRole.CUSTOMER_ROLE.getExternalId(), 1L, true)).thenReturn(
				UserRoleResponse.builder()
						.tenantId("1")
						.id("1")
						.build());

		when(tenantClient.getTenant(1L)).thenReturn(ResponseEntity.ok(TenantResponse.builder()
				.tenantId(1L)
				.realmId("realm-001")
				.build()));

		// Act & Assert
		webClient.post()
				.uri("/api/user/users")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(1L)
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isCreated()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {
					final var userResponse = response.getResponseBody();
					Assertions.assertNotNull(userResponse);

					Assertions.assertEquals(UserType.CUSTOMER, userResponse.getUserType());
					Assertions.assertEquals(request.getEmail(), userResponse.getEmail());
					Assertions.assertEquals(request.getTenantId()
							.toString(), userResponse.getPermissions()
									.getFirst()
									.tenantId());
					Assertions.assertEquals(request.getUserRoleId()
							.toString(), userResponse.getPermissions()
									.getFirst()
									.userRoleId());
					Assertions.assertEquals(request.getLastName(), userResponse.getLastName());
					Assertions.assertEquals(request.getFirstName(), userResponse.getFirstName());
					Assertions.assertEquals(UserMappingFunctionSupport.buildFullName(request.getFirstName(), request
							.getLastName()), userResponse.getFullName());
					Assertions.assertEquals(request.getExternalId(), userResponse.getExternalId());
					assertFalse(userResponse.getIsAlternativeUser());
					Assertions.assertEquals(UserStatus.ACTIVE, userResponse.getUserStatus());

					Assertions.assertThrowsExactly(UserInvitationNotFoundException.class,
							() -> userInvitationQueryService.getTenantUserInvitation(GetUserInvitationQuery.builder()
									.byUserId(MapstructCommonDomainMapper.INSTANCE.stringToUserId(userResponse.getId()))
									.byType(UserInvitationType.USER_REGISTRATION)
									.realmId("realm-001")
									.build()));

					verify(notificationCreatedEventKafkaPublisher, times(0)).publish(any(
							com.styl.pacific.kafka.notification.avro.model.NotificationCreatedAvroEvent.class));

				});
	}

	@Test
	void testAddBlockedCustomerWhenValidRequestThenReturnUserResponse() {
		// Arrange
		final var request = AddCustomerUserRequest.builder()
				.userRoleId(1L)
				.tenantId(1L)
				.firstName("First Name")
				.lastName("Last Name")
				.email("<EMAIL>")
				.externalId("External ID 1110001")
				.phoneNumber("**********")
				.userStatus(UserStatus.BLOCKED)
				.build();

		final var now = Instant.now();
		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.BACK_OFFICE)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();

		when(userRoleClient.getUserRole(1L, null, 1L, true)).thenReturn(UserRoleResponse.builder()
				.tenantId("1")
				.id("1")
				.build());

		when(userRoleClient.getUserRole(null, DefaultSystemRole.CUSTOMER_ROLE.getExternalId(), 1L, true)).thenReturn(
				UserRoleResponse.builder()
						.tenantId("1")
						.id("1")
						.build());

		when(tenantClient.getTenant(1L)).thenReturn(ResponseEntity.ok(TenantResponse.builder()
				.tenantId(1L)
				.realmId("realm-001")
				.build()));

		// Act & Assert
		webClient.post()
				.uri("/api/user/users")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(1L)
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isCreated()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {
					final var userResponse = response.getResponseBody();
					Assertions.assertNotNull(userResponse);

					Assertions.assertEquals(UserType.CUSTOMER, userResponse.getUserType());
					Assertions.assertEquals(request.getEmail(), userResponse.getEmail());
					Assertions.assertEquals(request.getTenantId()
							.toString(), userResponse.getPermissions()
									.getFirst()
									.tenantId());
					Assertions.assertEquals(request.getUserRoleId()
							.toString(), userResponse.getPermissions()
									.getFirst()
									.userRoleId());
					Assertions.assertEquals(UserPermissionStatus.CREATED, userResponse.getPermissions()
							.getFirst()
							.permissionStatus());

					Assertions.assertEquals(request.getLastName(), userResponse.getLastName());
					Assertions.assertEquals(request.getFirstName(), userResponse.getFirstName());
					Assertions.assertEquals(UserMappingFunctionSupport.buildFullName(request.getFirstName(), request
							.getLastName()), userResponse.getFullName());
					Assertions.assertEquals(request.getExternalId(), userResponse.getExternalId());
					assertFalse(userResponse.getIsAlternativeUser());
					Assertions.assertTrue(userResponse.getUserNonCompletedActions()
							.contains(UserNonCompletedAction.KEYCLOAK_REGISTRATION));
					Assertions.assertEquals(UserStatus.BLOCKED, userResponse.getUserStatus());

					Assertions.assertThrowsExactly(UserInvitationNotFoundException.class,
							() -> userInvitationQueryService.getTenantUserInvitation(GetUserInvitationQuery.builder()
									.byUserId(MapstructCommonDomainMapper.INSTANCE.stringToUserId(userResponse.getId()))
									.byType(UserInvitationType.USER_REGISTRATION)
									.realmId("realm-001")
									.build()));

					verify(notificationCreatedEventKafkaPublisher, times(0)).publish(any(
							com.styl.pacific.kafka.notification.avro.model.NotificationCreatedAvroEvent.class));

				});
	}

	@Test
	void testAddArchivedCustomerWhenValidRequestThenReturnUserResponse() {
		// Arrange
		final var request = AddCustomerUserRequest.builder()
				.userRoleId(1L)
				.tenantId(1L)
				.firstName("First Name")
				.lastName("Last Name")
				.externalId("External ID 1120001")
				.email("<EMAIL>")
				.phoneNumber("**********")
				.userStatus(UserStatus.ARCHIVED)
				.build();

		final var now = Instant.now();
		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.BACK_OFFICE)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();

		when(userRoleClient.getUserRole(1L, null, 1L, true)).thenReturn(UserRoleResponse.builder()
				.tenantId("1")
				.id("1")
				.build());

		when(userRoleClient.getUserRole(null, DefaultSystemRole.CUSTOMER_ROLE.getExternalId(), 1L, true)).thenReturn(
				UserRoleResponse.builder()
						.tenantId("1")
						.id("1")
						.build());

		when(tenantClient.getTenant(1L)).thenReturn(ResponseEntity.ok(TenantResponse.builder()
				.tenantId(1L)
				.realmId("realm-001")
				.build()));

		// Act & Assert
		webClient.post()
				.uri("/api/user/users")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(1L)
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isCreated()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {
					final var userResponse = response.getResponseBody();
					Assertions.assertNotNull(userResponse);

					Assertions.assertEquals(UserType.CUSTOMER, userResponse.getUserType());
					Assertions.assertEquals(request.getEmail(), userResponse.getEmail());
					Assertions.assertEquals(request.getTenantId()
							.toString(), userResponse.getPermissions()
									.getFirst()
									.tenantId());
					Assertions.assertEquals(request.getUserRoleId()
							.toString(), userResponse.getPermissions()
									.getFirst()
									.userRoleId());
					Assertions.assertEquals(UserPermissionStatus.CREATED, userResponse.getPermissions()
							.getFirst()
							.permissionStatus());

					Assertions.assertEquals(request.getLastName(), userResponse.getLastName());
					Assertions.assertEquals(request.getFirstName(), userResponse.getFirstName());
					Assertions.assertEquals(UserMappingFunctionSupport.buildFullName(request.getFirstName(), request
							.getLastName()), userResponse.getFullName());
					Assertions.assertEquals(request.getExternalId(), userResponse.getExternalId());
					assertFalse(userResponse.getIsAlternativeUser());
					Assertions.assertTrue(userResponse.getUserNonCompletedActions()
							.contains(UserNonCompletedAction.KEYCLOAK_REGISTRATION));
					Assertions.assertEquals(UserStatus.ARCHIVED, userResponse.getUserStatus());

					Assertions.assertThrowsExactly(UserInvitationNotFoundException.class,
							() -> userInvitationQueryService.getTenantUserInvitation(GetUserInvitationQuery.builder()
									.byUserId(MapstructCommonDomainMapper.INSTANCE.stringToUserId(userResponse.getId()))
									.byType(UserInvitationType.USER_REGISTRATION)
									.realmId("realm-001")
									.build()));

					verify(notificationCreatedEventKafkaPublisher, times(0)).publish(any(
							com.styl.pacific.kafka.notification.avro.model.NotificationCreatedAvroEvent.class));

				});
	}

	@Test
	void testAddCustomerWithSkipNotificationWhenValidRequestThenReturnUserResponse() {
		// Arrange
		final var request = AddCustomerUserRequest.builder()
				.userRoleId(1L)
				.email("<EMAIL>")
				.tenantId(1L)
				.firstName("First Name")
				.lastName("Last Name")
				.externalId("External ID 2505221614001")
				.phoneNumber("**********")
				.build();

		final var userCreatedEventCaptor = ArgumentCaptor.forClass(
				com.styl.pacific.kafka.users.avro.model.UserCreatedEvent.class);

		final var notificationEventCaptor = ArgumentCaptor.forClass(
				com.styl.pacific.kafka.notification.avro.model.NotificationCreatedAvroEvent.class);

		final var now = Instant.now();
		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.BACK_OFFICE)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();

		when(userRoleClient.getUserRole(1L, null, 1L, true)).thenReturn(UserRoleResponse.builder()
				.tenantId("1")
				.id("1")
				.build());

		when(userRoleClient.getUserRole(null, DefaultSystemRole.CUSTOMER_ROLE.getExternalId(), 1L, true)).thenReturn(
				UserRoleResponse.builder()
						.tenantId("1")
						.id("1")
						.build());

		final var tenantSettings = TenantSettingsResponse.builder()
				.timeZone(MapstructCommonMapper.INSTANCE.timezoneToTimeZoneResponse(TimeZone.getTimeZone(
						"Asia/Ho_Chi_Minh")))
				.dateFormat("YYYY-MM-dd")
				.timeFormat("HH:mm:ss")
				.defaultDomain("tenancy-cportal.com")
				.build();
		when(tenantClient.getTenant(1L)).thenReturn(ResponseEntity.ok(TenantResponse.builder()
				.tenantId(1L)
				.realmId("realm-001")
				.settings(tenantSettings)
				.build()));

		// Act & Assert
		webClient.post()
				.uri("/api/user/users?skipNotification=true")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(1L)
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isCreated()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {
					final var userResponse = response.getResponseBody();
					Assertions.assertNotNull(userResponse);

					Assertions.assertEquals(UserType.CUSTOMER, userResponse.getUserType());
					Assertions.assertEquals(request.getEmail(), userResponse.getEmail());
					Assertions.assertEquals(request.getTenantId()
							.toString(), userResponse.getPermissions()
									.getFirst()
									.tenantId());
					Assertions.assertEquals(request.getUserRoleId()
							.toString(), userResponse.getPermissions()
									.getFirst()
									.userRoleId());
					Assertions.assertEquals(request.getLastName(), userResponse.getLastName());
					Assertions.assertEquals(request.getFirstName(), userResponse.getFirstName());
					Assertions.assertEquals(UserMappingFunctionSupport.buildFullName(request.getFirstName(), request
							.getLastName()), userResponse.getFullName());
					Assertions.assertEquals(request.getExternalId(), userResponse.getExternalId());
					assertFalse(userResponse.getIsAlternativeUser());
					Assertions.assertEquals(CustomerUniqueExternalIdGenerator.buildCustomerUniqueExternalId(
							MapstructCommonDomainMapper.INSTANCE.longToTenantId(request.getTenantId()), request
									.getExternalId()), userResponse.getUniqueExternalId());

					Assertions.assertThrows(UserInvitationNotFoundException.class, () -> userInvitationQueryService
							.getTenantUserInvitation(GetUserInvitationQuery.builder()
									.byUserId(MapstructCommonDomainMapper.INSTANCE.stringToUserId(userResponse.getId()))
									.byType(UserInvitationType.USER_REGISTRATION)
									.realmId("realm-001")
									.build()));

					verify(notificationCreatedEventKafkaPublisher, times(0)).publish(notificationEventCaptor.capture());
					verify(userCreatedEventKafkaPublisher, times(1)).publish(userCreatedEventCaptor.capture());
					final var actualUserCreatedEvent = userCreatedEventCaptor.getValue();

					Assertions.assertEquals(userResponse.getId(), String.valueOf(actualUserCreatedEvent.getUserId()));
					Assertions.assertEquals(userResponse.getEmail(), actualUserCreatedEvent.getEmail());
					Assertions.assertEquals(userResponse.getFirstName(), actualUserCreatedEvent.getFirstName());
					Assertions.assertEquals(userResponse.getLastName(), actualUserCreatedEvent.getLastName());

					final var userPreferenceDateTimeFormat = userPreferenceQueryService.getUserPreference(
							FindUserPreferenceQuery.builder()
									.byTenantId(new TenantId(1L))
									.byUserId(MapstructCommonDomainMapper.INSTANCE.stringToUserId(userResponse.getId()))
									.byKey(UserPreferenceKey.ACCOUNT_DATE_TIME_FORMAT)
									.build());
					final var dateTimeFormat = (DateTimeFormatPreferenceData) userPreferenceDateTimeFormat.getData();
					Assertions.assertEquals(tenantSettings.getDateFormat(), dateTimeFormat.getDateFormat());
					Assertions.assertEquals(tenantSettings.getTimeFormat(), dateTimeFormat.getTimeFormat());
					Assertions.assertEquals(tenantSettings.getTimeZone()
							.getZoneId(), dateTimeFormat.getTimeZone()
									.getID());

					actualUserCreatedEvent.getPermissions()
							.forEach(actualPermission -> {
								final var firstPermission = userResponse.getPermissions()
										.getFirst();
								Assertions.assertEquals(firstPermission.tenantId(), String.valueOf(actualPermission
										.getTenantId()));
								Assertions.assertEquals(firstPermission.permissionStatus()
										.name(), actualPermission.getPermissionStatus()
												.name());

							});

				});
	}

	@Test
	void testThrowExceptionWhenAddCustomerWithDisallowedUserStatus() {
		// Arrange
		final var request = AddCustomerUserRequest.builder()
				.userRoleId(1L)
				.tenantId(1L)
				.firstName("First Name")
				.lastName("Last Name")
				.externalId("External ID 1130001")
				.email("<EMAIL>")
				.phoneNumber("**********")
				.userStatus(UserStatus.ACTIVE)
				.build();

		final var now = Instant.now();
		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.BACK_OFFICE)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();

		when(userRoleClient.getUserRole(1L, null, 1L, true)).thenReturn(UserRoleResponse.builder()
				.tenantId("1")
				.id("1")
				.build());

		when(userRoleClient.getUserRole(null, DefaultSystemRole.CUSTOMER_ROLE.getExternalId(), 1L, true)).thenReturn(
				UserRoleResponse.builder()
						.tenantId("1")
						.id("1")
						.build());

		when(tenantClient.getTenant(1L)).thenReturn(ResponseEntity.ok(TenantResponse.builder()
				.tenantId(1L)
				.realmId("realm-001")
				.build()));

		// Act & Assert
		webClient.post()
				.uri("/api/user/users")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(1L)
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isBadRequest()
				.expectBody(ErrorResponse.class)
				.consumeWith(response -> {
					final var error = response.getResponseBody();
					assertNotNull(error);
					assertTrue(error.getDetails()
							.contains("userStatus: Only statuses [ARCHIVED, BLOCKED] are allowed"));
				});
	}

	@Test
	void testThrowExceptionWhenAddCustomerByDuplicatedExternalId() {
		// Arrange
		final var user = userSupporter.initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(new UserRoleId(1L))
				.tenantId(new TenantId(1L))
				.userType(UserType.CUSTOMER)
				.email("<EMAIL>")
				.externalId("External-ID-1200001")
				.uniqueExternalId("1.External-ID-1200001")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.realmId("realm-001")
				.build());
		userSupporter.activateUser("sid-101", user);

		final var now = Instant.now();
		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.BACK_OFFICE)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();

		when(userRoleClient.getUserRole(1L, null, 1L, true)).thenReturn(UserRoleResponse.builder()
				.tenantId("1")
				.id("1")
				.build());

		when(userRoleClient.getUserRole(null, DefaultSystemRole.CUSTOMER_ROLE.getExternalId(), 1L, true)).thenReturn(
				UserRoleResponse.builder()
						.tenantId("1")
						.id("1")
						.build());

		when(tenantClient.getTenant(1L)).thenReturn(ResponseEntity.ok(TenantResponse.builder()
				.tenantId(1L)
				.realmId("realm-001")
				.build()));

		final var request = AddCustomerUserRequest.builder()
				.userRoleId(1L)
				.tenantId(1L)
				.email("<EMAIL>")
				.externalId(user.getExternalId())
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/user/users")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(1L)
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isBadRequest()
				.expectBody(ErrorResponse.class)
				.consumeWith(response -> {
					final var error = response.getResponseBody();
					assertNotNull(error);
					assertTrue(error.getDetails()
							.contains("ExternalId has been existed"));
				});
	}

	@Test
	void testThrowExceptionWhenUpdateCustomerByDuplicatedExternalId() {
		// Arrange
		final var user1 = userSupporter.initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(new UserRoleId(1L))
				.tenantId(new TenantId(1L))
				.userType(UserType.CUSTOMER)
				.email("<EMAIL>")
				.externalId("External-ID-3300001")
				.uniqueExternalId("1.External-ID-3300001")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.realmId("realm-001")
				.build());
		userSupporter.activateUser("sid-3300001", user1);

		final var user2 = userSupporter.initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(new UserRoleId(1L))
				.tenantId(new TenantId(1L))
				.userType(UserType.CUSTOMER)
				.email("<EMAIL>")
				.externalId("External-ID-3300002")
				.uniqueExternalId("1.External-ID-3300002")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.realmId("realm-001")
				.build());
		userSupporter.activateUser("sid-3300002", user2);

		final var now = Instant.now();
		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.BACK_OFFICE)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();

		when(userRoleClient.getUserRole(1L, null, 1L, true)).thenReturn(UserRoleResponse.builder()
				.tenantId("1")
				.id("1")
				.build());

		when(userRoleClient.getUserRole(null, DefaultSystemRole.CUSTOMER_ROLE.getExternalId(), 1L, true)).thenReturn(
				UserRoleResponse.builder()
						.tenantId("1")
						.id("1")
						.build());

		when(tenantClient.getTenant(1L)).thenReturn(ResponseEntity.ok(TenantResponse.builder()
				.tenantId(1L)
				.realmId("realm-001")
				.build()));

		final var request = AddCustomerUserRequest.builder()
				.userRoleId(1L)
				.tenantId(1L)
				.email(user2.getEmail())
				.externalId(user1.getExternalId())
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/user/users")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(1L)
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isBadRequest()
				.expectBody(ErrorResponse.class)
				.consumeWith(response -> {
					final var error = response.getResponseBody();
					assertNotNull(error);
					assertTrue(error.getDetails()
							.contains("ExternalId has been existed"));
				});
	}

	@Test
	void testAddCustomerWithoutUserGroupWhenValidRequestThenReturnUserResponse() {
		// Arrange
		final var tenantId = new TenantId(91111L);

		final var request = AddCustomerUserRequest.builder()
				.userRoleId(1L)
				.tenantId(tenantId.getValue())
				.firstName("First Name")
				.lastName("Last Name")
				.email("<EMAIL>")
				.externalId("External ID 1")
				.phoneNumber("**********")
				.build();

		final var now = Instant.now();
		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.BACK_OFFICE)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();

		when(userRoleClient.getUserRole(1L, null, tenantId.getValue(), true)).thenReturn(UserRoleResponse.builder()
				.tenantId(tenantId.getValue()
						.toString())
				.id("1")
				.build());

		when(userRoleClient.getUserRole(null, DefaultSystemRole.CUSTOMER_ROLE.getExternalId(), tenantId.getValue(),
				true)).thenReturn(UserRoleResponse.builder()
						.tenantId(tenantId.getValue()
								.toString())
						.id("1")
						.build());

		when(tenantClient.getTenant(tenantId.getValue())).thenReturn(ResponseEntity.ok(TenantResponse.builder()
				.tenantId(tenantId.getValue())
				.realmId("realm-91111L")
				.build()));

		// Act & Assert
		webClient.post()
				.uri("/api/user/users")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(1L)
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isCreated()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {
					final var userResponse = response.getResponseBody();
					Assertions.assertNotNull(userResponse);

					Assertions.assertEquals(UserType.CUSTOMER, userResponse.getUserType());
					Assertions.assertEquals(request.getEmail(), userResponse.getEmail());
					Assertions.assertEquals(request.getTenantId()
							.toString(), userResponse.getPermissions()
									.getFirst()
									.tenantId());
					Assertions.assertEquals(request.getUserRoleId()
							.toString(), userResponse.getPermissions()
									.getFirst()
									.userRoleId());
					Assertions.assertEquals(request.getLastName(), userResponse.getLastName());
					Assertions.assertEquals(request.getFirstName(), userResponse.getFirstName());
					Assertions.assertEquals(UserMappingFunctionSupport.buildFullName(request.getFirstName(), request
							.getLastName()), userResponse.getFullName());
					Assertions.assertEquals(request.getExternalId(), userResponse.getExternalId());
					assertFalse(userResponse.getIsAlternativeUser());
					Assertions.assertEquals(UserStatus.CREATED, userResponse.getUserStatus());

					final var userGroup = userResponse.getUserGroup();
					Assertions.assertEquals(UserGroupConstants.MAIN_USER_GROUP_TEMPLATE.getGroupKey(), userGroup
							.getGroupKey());
					Assertions.assertEquals(UserGroupConstants.MAIN_USER_GROUP_TEMPLATE.getGroupName(), userGroup
							.getGroupName());
					Assertions.assertEquals(UserGroupConstants.MAIN_USER_GROUP_TEMPLATE.getDescription(), userGroup
							.getDescription());
					Assertions.assertEquals(tenantId.getValue()
							.toString(), userGroup.getTenantId());
					Assertions.assertTrue(userGroup.getIsDefaultGroup());
				});
	}

	@Test
	void testDeleteUserWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var user = userSupporter.initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(new UserRoleId(1L))
				.tenantId(new TenantId(1L))
				.userType(UserType.BACK_OFFICE)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
				.build());
		userSupporter.activateUser("sid-001", user);

		// Act & Assert
		webClient.method(HttpMethod.DELETE)
				.uri("/api/user/users")
				.bodyValue(DeleteUserRequest.builder()
						.byUserId(user.getId()
								.getValue())
						.build())
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.requestId(1L)
						.tenantId(1L)
						.build()))
				.exchange()
				.expectStatus()
				.isOk()
				.expectBody(Void.class)
				.consumeWith(v -> {
					final var actualUser = userQueryService.getSingleUserById(user.getId())
							.orElse(null);
					Assertions.assertNotNull(actualUser,
							"User has been existed with status ARCHIVED and scheduling deleted at");
					Assertions.assertEquals(UserStatus.ARCHIVED, actualUser.getUserStatus());
					Assertions.assertNotNull(actualUser.getSchedulingDeletedAt());

					// Clean up user to avoid side effect to the shared db, scheduler job cleaning user
					when(keycloakServiceAccountClient.getServiceAccountAccessToken(any(
							KeycloakServiceAccountConfigProperties.class))).thenReturn(new KeycloakAccessTokenResponse(
									"KeycloakAccessToken"));
					deletingUserScheduler.startDeletingUsers();
				});
	}

	@Test
	void testDeleteUserAndThenCleaningRequiredActionWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var user = userSupporter.initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(new UserRoleId(1L))
				.tenantId(new TenantId(1L))
				.userType(UserType.BACK_OFFICE)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
				.build());
		userSupporter.activateUser("sid-5556601", user);

		final var userRealmIdCaptor = ArgumentCaptor.forClass(String.class);
		final var userSsoIdCaptor = ArgumentCaptor.forClass(String.class);
		final var keycloakAccessTokenCaptor = ArgumentCaptor.forClass(String.class);

		when(keycloakServiceAccountClient.getServiceAccountAccessToken(any(
				KeycloakServiceAccountConfigProperties.class))).thenReturn(new KeycloakAccessTokenResponse(
						"KeycloakAccessToken"));

		// Act
		userCommandService.deleteUsersInSchedule(DeleteUserCommand.builder()
				.byUserId(user.getId())
				.tenantId(new TenantId(1L))
				.build());
		deletingUserScheduler.startDeletingUsers();

		// Assert
		Assertions.assertThrowsExactly(UserNotFoundException.class, () -> {
			userQueryService.getSingleUserById(user.getId())
					.orElseThrow(() -> new UserNotFoundException("User not found"));
		});

		Arrays.stream(UserInvitationType.values())
				.forEach((type -> {
					final var exception = assertThrowsExactly(UserInvitationNotFoundException.class,
							() -> userInvitationQueryService.getTenantUserInvitation(GetUserInvitationQuery.builder()
									.byUserId(user.getId())
									.byType(type)
									.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
									.build()));
					assertEquals("Invitation was not found", exception.getMessage());
				}));
		//
		verify(keycloakServiceAccountClient).deleteUser(keycloakAccessTokenCaptor.capture(), userRealmIdCaptor
				.capture(), userSsoIdCaptor.capture());
		Assertions.assertEquals(CommonDomainConstants.BACK_OFFICE_REALM_ID, userRealmIdCaptor.getValue());
		Assertions.assertEquals("sid-5556601", userSsoIdCaptor.getValue());
		Assertions.assertEquals("Bearer KeycloakAccessToken", keycloakAccessTokenCaptor.getValue());
	}

	@Test
	void testDeleteUserAndThenCleaningUserCardWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		when(userRoleClient.getUserRole(null, DefaultSystemRole.CUSTOMER_ROLE.getExternalId(), 1L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("1").id("1").build());

		final var user = initializeNewUser(
				UpsertTenantUserCommand.builder().userRoleId(new UserRoleId(1L)).tenantId(new TenantId(1L))
						.userType(UserType.CUSTOMER).email("<EMAIL>").firstName("first name")
						.lastName("last name").phoneNumber("**********").realmId("customer-5556604").build());

		userSupporter.activateUser("sid-5556604", user);

		cardCommandService.createUserCard(CreateUserCardCommand.builder().cardId("Card 3333").cardAlias("Card Alias 1")
				.status(UserCardStatus.ACTIVE).tenantId(new TenantId(1L)).userId(user.getId()).build());

		cardCommandService.createUserCard(CreateUserCardCommand.builder().cardId("Card 4444").cardAlias("Card Alias 2")
				.status(UserCardStatus.ACTIVE).userId(user.getId()).tenantId(new TenantId(1L)).build());

		final var userRealmIdCaptor = ArgumentCaptor.forClass(String.class);
		final var userSsoIdCaptor = ArgumentCaptor.forClass(String.class);
		final var keycloakAccessTokenCaptor = ArgumentCaptor.forClass(String.class);

		when(keycloakServiceAccountClient.getServiceAccountAccessToken(
				any(KeycloakServiceAccountConfigProperties.class))).thenReturn(
				new KeycloakAccessTokenResponse("KeycloakAccessToken"));

		// Act
		userCommandService.deleteUsersInSchedule(
				DeleteUserCommand.builder().byUserId(user.getId()).tenantId(new TenantId(1L)).build());
		deletingUserScheduler.startDeletingUsers();

		// Assert
		Assertions.assertThrowsExactly(UserNotFoundException.class, () -> {
			userQueryService.getSingleUserById(user.getId())
					.orElseThrow(() -> new UserNotFoundException("User not found"));
		});

		Arrays.stream(UserInvitationType.values()).forEach((type -> {
			final var exception = assertThrowsExactly(UserInvitationNotFoundException.class,
					() -> userInvitationQueryService.getTenantUserInvitation(
							GetUserInvitationQuery.builder().byUserId(user.getId()).byType(type)
									.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID).build()));
			assertEquals("Invitation was not found", exception.getMessage());
		}));
		verify(keycloakServiceAccountClient).deleteUser(keycloakAccessTokenCaptor.capture(),
				userRealmIdCaptor.capture(), userSsoIdCaptor.capture());
		Assertions.assertEquals("customer-5556604", userRealmIdCaptor.getValue());
		Assertions.assertEquals("sid-5556604", userSsoIdCaptor.getValue());
		Assertions.assertEquals("Bearer KeycloakAccessToken", keycloakAccessTokenCaptor.getValue());

		final var result = cardQueryService.queryUserCards(UserCardPaginationQuery.builder()
				.filter(FilterUserCardPaginationQuery.builder().byUserId(user.getId()).build()).page(0).size(10)
				.sortDirection("ASC").sortFields(List.of("id")).build());
		Assertions.assertEquals(0, result.getTotalElements());
	}

	@Test
	void testDeleteUserAndThenCleaningSponsorSubAccountsWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		when(userRoleClient.getUserRole(2L, null, 2L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("2").id("2").build());

		when(userRoleClient.getUserRole(3L, null, 3L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("3").id("3").build());

		when(userRoleClient.getUserRole(null, DefaultSystemRole.CUSTOMER_ROLE.getExternalId(), 1L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("1").id("1").build());

		when(keycloakServiceAccountClient.getServiceAccountAccessToken(
				any(KeycloakServiceAccountConfigProperties.class))).thenReturn(
				new KeycloakAccessTokenResponse("KeycloakAccessToken"));

		final var userRealmIdCaptor = ArgumentCaptor.forClass(String.class);
		final var userSsoIdCaptor = ArgumentCaptor.forClass(String.class);
		final var keycloakAccessTokenCaptor = ArgumentCaptor.forClass(String.class);

		doNothing().when(keycloakServiceAccountClient).deleteUser(anyString(), anyString(), anyString());

		final var user1 = initializeNewUser(
				UpsertTenantUserCommand.builder().userRoleId(new UserRoleId(1L)).tenantId(new TenantId(1L))
						.userType(UserType.CUSTOMER).email("<EMAIL>").firstName("first name")
						.lastName("last name").phoneNumber("**********").realmId("customer-rl-001").build());
		userSupporter.activateUser("customer-9001", user1);

		final var user2 = initializeNewUser(
				UpsertTenantUserCommand.builder().userRoleId(new UserRoleId(1L)).tenantId(new TenantId(1L))
						.userType(UserType.CUSTOMER).email("<EMAIL>").firstName("first name")
						.lastName("last name").phoneNumber("**********").realmId("customer-rl-001").build());
		userSupporter.activateUser("customer-9002", user2);

		final var subAccount1 = subAccountCommandService.createSubAccount(
				CreateSubAccountNewUserCommand.builder().userTypeCreator(UserType.CUSTOMER).sponsorUserId(user1.getId())
						.firstName("first name 1").lastName("last name 1").phoneNumber("**********")
						.tenantId(new TenantId(1L)).build());

		final var subAccount2 = subAccountCommandService.createSubAccount(
				CreateSubAccountExistingUserCommand.builder().userTypeCreator(UserType.CUSTOMER)
						.sponsorUserId(user1.getId()).tenantId(new TenantId(1L)).email(user2.getEmail()).build());

		// Act
		userCommandService.deleteUsersInSchedule(
				DeleteUserCommand.builder().tenantId(new TenantId(1L)).byUserIds(Set.of(user1.getId())).build());
		deletingUserScheduler.startDeletingUsers();

		// Assert
		Assertions.assertThrowsExactly(UserNotFoundException.class, () -> {
			userQueryService.getSingleUserById(user1.getId())
					.orElseThrow(() -> new UserNotFoundException("User not found"));
		});
		verify(keycloakServiceAccountClient).deleteUser(keycloakAccessTokenCaptor.capture(),
				userRealmIdCaptor.capture(), userSsoIdCaptor.capture());
		Assertions.assertEquals("customer-rl-001", userRealmIdCaptor.getValue());
		Assertions.assertEquals("customer-9001", userSsoIdCaptor.getValue());
		Assertions.assertEquals("Bearer KeycloakAccessToken", keycloakAccessTokenCaptor.getValue());

		final var listSubAccount1 = subAccountRepository.getSubAccountsBySubAccountUserId(
				subAccount1.getSubUser().getId());
		final var listSubAccount2 = subAccountRepository.getSubAccountsBySubAccountUserId(
				(subAccount2.getSubUser().getId()));

		Assertions.assertEquals(0, listSubAccount1.size());
		Assertions.assertEquals(0, listSubAccount2.size());
	}

	@Test
	void testDeleteUserAndThenCleaningSubUserSubAccountsWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		when(userRoleClient.getUserRole(2L, null, 2L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("2").id("2").build());

		when(userRoleClient.getUserRole(3L, null, 3L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("3").id("3").build());

		when(userRoleClient.getUserRole(null, DefaultSystemRole.CUSTOMER_ROLE.getExternalId(), 1L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("1").id("1").build());

		when(keycloakServiceAccountClient.getServiceAccountAccessToken(
				any(KeycloakServiceAccountConfigProperties.class))).thenReturn(
				new KeycloakAccessTokenResponse("KeycloakAccessToken"));

		doNothing().when(keycloakServiceAccountClient).deleteUser(anyString(), anyString(), anyString());

		final var user1 = initializeNewUser(
				UpsertTenantUserCommand.builder().userRoleId(new UserRoleId(1L)).tenantId(new TenantId(1L))
						.userType(UserType.CUSTOMER).email("<EMAIL>").firstName("first name")
						.lastName("last name").phoneNumber("**********").realmId("customer-rl-001").build());
		userSupporter.activateUser("customer-10001", user1);

		final var user2 = initializeNewUser(
				UpsertTenantUserCommand.builder().userRoleId(new UserRoleId(1L)).tenantId(new TenantId(1L))
						.userType(UserType.CUSTOMER).email("<EMAIL>").firstName("first name")
						.lastName("last name").phoneNumber("**********").realmId("customer-rl-001").build());
		userSupporter.activateUser("customer-10002", user2);

		final var user3 = initializeNewUser(
				UpsertTenantUserCommand.builder().userRoleId(new UserRoleId(1L)).tenantId(new TenantId(1L))
						.userType(UserType.CUSTOMER).email("<EMAIL>").firstName("first name")
						.lastName("last name").phoneNumber("**********").realmId("customer-rl-001").build());
		userSupporter.activateUser("customer-10003", user3);

		subAccountCommandService.createSubAccount(
				CreateSubAccountExistingUserCommand.builder().userTypeCreator(UserType.CUSTOMER)
						.sponsorUserId(user1.getId()).tenantId(new TenantId(1L)).email(user2.getEmail()).build());

		subAccountCommandService.createSubAccount(
				CreateSubAccountExistingUserCommand.builder().userTypeCreator(UserType.CUSTOMER)
						.sponsorUserId(user1.getId()).tenantId(new TenantId(1L)).email(user3.getEmail()).build());

		final var userRealmIdCaptor = ArgumentCaptor.forClass(String.class);
		final var userSsoIdCaptor = ArgumentCaptor.forClass(String.class);
		final var keycloakAccessTokenCaptor = ArgumentCaptor.forClass(String.class);

		when(keycloakServiceAccountClient.getServiceAccountAccessToken(
				any(KeycloakServiceAccountConfigProperties.class))).thenReturn(
				new KeycloakAccessTokenResponse("KeycloakAccessToken"));

		// Act
		userCommandService.deleteUsersInSchedule(
				DeleteUserCommand.builder().tenantId(new TenantId(1L)).byUserIds(Set.of(user2.getId())).build());
		deletingUserScheduler.startDeletingUsers();

		// Assert
		Assertions.assertThrowsExactly(UserNotFoundException.class, () -> {
			userQueryService.getSingleUserById(user2.getId())
					.orElseThrow(() -> new UserNotFoundException("User not found"));
		});

		Arrays.stream(UserInvitationType.values()).forEach((type -> {
			final var exception = assertThrowsExactly(UserInvitationNotFoundException.class,
					() -> userInvitationQueryService.getTenantUserInvitation(
							GetUserInvitationQuery.builder().byUserId(user2.getId()).byType(type)
									.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID).build()));
			assertEquals("Invitation was not found", exception.getMessage());
		}));
		verify(keycloakServiceAccountClient).deleteUser(keycloakAccessTokenCaptor.capture(),
				userRealmIdCaptor.capture(), userSsoIdCaptor.capture());
		Assertions.assertEquals("customer-rl-001", userRealmIdCaptor.getValue());
		Assertions.assertEquals("customer-10002", userSsoIdCaptor.getValue());
		Assertions.assertEquals("Bearer KeycloakAccessToken", keycloakAccessTokenCaptor.getValue());

		final var listSubAccount = subAccountRepository.getSubAccountsBySubAccountUserId(user1.getId());

		final var pageResult = userQueryService.queryUsers(UserPaginationQuery.builder().page(0).size(10)
				.filter(FilterUserQuery.builder().byUserId(user1.getId()).build()).build());

		Assertions.assertEquals(0, listSubAccount.size());
		Assertions.assertEquals(1, pageResult.getContent().getFirst().getTotalSubAccounts());

	}

	@Test
	void testDeleteUserAndThenCleaningCardToUserWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		when(userRoleClient.getUserRole(2L, null, 2L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("2").id("2").build());

		when(userRoleClient.getUserRole(3L, null, 3L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("3").id("3").build());

		when(userRoleClient.getUserRole(null, DefaultSystemRole.CUSTOMER_ROLE.getExternalId(), 1L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("1").id("1").build());

		when(keycloakServiceAccountClient.getServiceAccountAccessToken(
				any(KeycloakServiceAccountConfigProperties.class))).thenReturn(
				new KeycloakAccessTokenResponse("KeycloakAccessToken"));

		doNothing().when(keycloakServiceAccountClient).deleteUser(anyString(), anyString(), anyString());

		final var user1 = initializeNewUser(
				UpsertTenantUserCommand.builder().userRoleId(new UserRoleId(1L)).tenantId(new TenantId(1L))
						.userType(UserType.CUSTOMER).email("<EMAIL>").firstName("first name")
						.lastName("last name").phoneNumber("**********").realmId("customer-rl-001").build());
		userSupporter.activateUser("customer-11111", user1);

		final var user2 = initializeNewUser(
				UpsertTenantUserCommand.builder().userRoleId(new UserRoleId(1L)).tenantId(new TenantId(1L))
						.userType(UserType.CUSTOMER).email("<EMAIL>").firstName("first name")
						.lastName("last name").phoneNumber("**********").realmId("customer-rl-001").build());
		userSupporter.activateUser("customer-11112", user2);

		final var subAccount1 = subAccountCommandService.createSubAccount(
				CreateSubAccountNewUserCommand.builder().userTypeCreator(UserType.CUSTOMER).sponsorUserId(user1.getId())
						.firstName("first name 1").lastName("last name 1").phoneNumber("**********")
						.tenantId(new TenantId(1L)).build());

		final var subAccount2 = subAccountCommandService.createSubAccount(
				CreateSubAccountExistingUserCommand.builder().userTypeCreator(UserType.CUSTOMER)
						.sponsorUserId(user1.getId()).tenantId(new TenantId(1L)).email(user2.getEmail()).build());

		cardCommandService.createUserCard(CreateUserCardCommand.builder().cardId("Card 111").cardAlias("Card Alias 1")
				.status(UserCardStatus.ACTIVE).tenantId(new TenantId(1L)).userId(user1.getId()).build());

		cardCommandService.createUserCard(CreateUserCardCommand.builder().cardId("Card 222").cardAlias("Card Alias 2")
				.status(UserCardStatus.ACTIVE).userId(user1.getId()).tenantId(new TenantId(1L)).build());

		final var userRealmIdCaptor = ArgumentCaptor.forClass(String.class);
		final var userSsoIdCaptor = ArgumentCaptor.forClass(String.class);
		final var keycloakAccessTokenCaptor = ArgumentCaptor.forClass(String.class);

		when(keycloakServiceAccountClient.getServiceAccountAccessToken(
				any(KeycloakServiceAccountConfigProperties.class))).thenReturn(
				new KeycloakAccessTokenResponse("KeycloakAccessToken"));

		// Act
		userCommandService.deleteUsersInSchedule(
				DeleteUserCommand.builder().tenantId(new TenantId(1L)).byUserIds(Set.of(user1.getId())).build());
		deletingUserScheduler.startDeletingUsers();

		// Assert
		Assertions.assertThrowsExactly(UserNotFoundException.class, () -> {
			userQueryService.getSingleUserById(user1.getId())
					.orElseThrow(() -> new UserNotFoundException("User not found"));
		});

		final var listSubAccount1 = subAccountRepository.getSubAccountsBySubAccountUserId(
				subAccount1.getSubUser().getId());
		final var listSubAccount2 = subAccountRepository.getSubAccountsBySubAccountUserId(
				(subAccount2.getSubUser().getId()));

		Assertions.assertEquals(0, listSubAccount1.size());
		Assertions.assertEquals(0, listSubAccount2.size());

		Arrays.stream(UserInvitationType.values()).forEach((type -> {
			final var exception = assertThrowsExactly(UserInvitationNotFoundException.class,
					() -> userInvitationQueryService.getTenantUserInvitation(
							GetUserInvitationQuery.builder().byUserId(user1.getId()).byType(type)
									.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID).build()));

			assertEquals("Invitation was not found", exception.getMessage());
		}));

		verify(keycloakServiceAccountClient).deleteUser(keycloakAccessTokenCaptor.capture(),
				userRealmIdCaptor.capture(), userSsoIdCaptor.capture());
		Assertions.assertEquals("customer-rl-001", userRealmIdCaptor.getValue());
		Assertions.assertEquals("customer-11111", userSsoIdCaptor.getValue());
		Assertions.assertEquals("Bearer KeycloakAccessToken", keycloakAccessTokenCaptor.getValue());

		final var result = cardQueryService.queryUserCards(UserCardPaginationQuery.builder()
				.filter(FilterUserCardPaginationQuery.builder().byUserId(user1.getId()).build()).page(0).size(10)
				.sortDirection("ASC").sortFields(List.of("id")).build());
		Assertions.assertEquals(0, result.getTotalElements());

		final var requiredActionResult = userRequiredActionQueryService.queryUserRequiredAction(
				UserRequiredActionPaginationQuery.builder()
						.filter(UserRequiredActionFilter.builder().byUserId(user2.getId()).build()).build());
		Assertions.assertEquals(0, requiredActionResult.getTotalElements());

	}

	@Test
	void testDeleteMainUserAndThenCleaningAlternativeAccessWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		when(userRoleClient.getUserRole(2L, null, 2L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("2").id("2").build());

		when(userRoleClient.getUserRole(3L, null, 3L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("3").id("3").build());

		when(userRoleClient.getUserRole(null, DefaultSystemRole.CUSTOMER_ROLE.getExternalId(), 1L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("1").id("1").build());

		when(keycloakServiceAccountClient.getServiceAccountAccessToken(
				any(KeycloakServiceAccountConfigProperties.class))).thenReturn(
				new KeycloakAccessTokenResponse("KeycloakAccessToken"));

		doNothing().when(keycloakServiceAccountClient).deleteUser(anyString(), anyString(), anyString());

		final var user1 = initializeNewUser(
				UpsertTenantUserCommand.builder().userRoleId(new UserRoleId(1L)).tenantId(new TenantId(1L))
						.userType(UserType.CUSTOMER).email("<EMAIL>").firstName("first name")
						.lastName("last name").phoneNumber("**********").realmId("customer-rl-001").build());
		userSupporter.activateUser("customer-************", user1);

		final var alternativeUser1 = alternativeUserAccessCommandService.createAlternativeUserAccess(user1.getId(), new TenantId(1L),
				CreateAlternativeUserAccessCommand.builder()
						.firstName("First Name")
						.lastName("Last Name")
						.email("<EMAIL>")
						.phoneNumber("**********")
						.externalId("customer-alternative-***************")
						.migrationId("customer-alternative-***************")
						.userType(UserType.CUSTOMER)
						.build(), ControlNotificationCommand.builder()
								.isSkipNotification(false)
								.build());

		userSupporter.activateUser("customer-***************", alternativeUser1.getAlternativeUser());

		final var alternativeUser2 = alternativeUserAccessCommandService.createAlternativeUserAccess(user1.getId(), new TenantId(1L),
				CreateAlternativeUserAccessCommand.builder()
						.firstName("First Name")
						.lastName("Last Name")
						.email("<EMAIL>")
						.phoneNumber("**********")
						.externalId("customer-alternative-************002")
						.migrationId("customer-alternative-************002")
						.userType(UserType.CUSTOMER)
						.build(), ControlNotificationCommand.builder()
						.isSkipNotification(false)
						.build());

		userSupporter.activateUser("customer-************002", alternativeUser2.getAlternativeUser());

		final var userRealmIdCaptor = ArgumentCaptor.forClass(String.class);
		final var userSsoIdCaptor = ArgumentCaptor.forClass(String.class);
		final var keycloakAccessTokenCaptor = ArgumentCaptor.forClass(String.class);

		when(keycloakServiceAccountClient.getServiceAccountAccessToken(
				any(KeycloakServiceAccountConfigProperties.class))).thenReturn(
				new KeycloakAccessTokenResponse("KeycloakAccessToken"));

		// Act
		userCommandService.deleteUsersInSchedule(
				DeleteUserCommand.builder().tenantId(new TenantId(1L)).byUserIds(Set.of(user1.getId())).build());
		deletingUserScheduler.startDeletingUsers();

		// Assert

		Assertions.assertThrowsExactly(UserNotFoundException.class, () -> {
			userQueryService.getSingleUserById(user1.getId())
					.orElseThrow(() -> new UserNotFoundException("User not found"));
		});

		Assertions.assertThrowsExactly(UserNotFoundException.class, () -> {
			userQueryService.getSingleUserById(alternativeUser1.getAlternativeUser().getId())
					.orElseThrow(() -> new UserNotFoundException("User not found"));
		});

		Assertions.assertThrowsExactly(UserNotFoundException.class, () -> {
			userQueryService.getSingleUserById(alternativeUser2.getAlternativeUser().getId())
					.orElseThrow(() -> new UserNotFoundException("User not found"));
		});

		Arrays.stream(UserInvitationType.values()).forEach((type -> {
			final var exception = assertThrowsExactly(UserInvitationNotFoundException.class,
					() -> userInvitationQueryService.getTenantUserInvitation(
							GetUserInvitationQuery.builder().byUserId(user1.getId()).byType(type)
									.realmId("customer-rl-001").build()));
			assertEquals("Invitation was not found", exception.getMessage());

			final var exception2 = assertThrowsExactly(UserInvitationNotFoundException.class,
					() -> userInvitationQueryService.getTenantUserInvitation(
							GetUserInvitationQuery.builder().byUserId(alternativeUser1.getAlternativeUser().getId()).byType(type)
									.realmId("customer-rl-001").build()));
			assertEquals("Invitation was not found", exception2.getMessage());

			final var exception3 = assertThrowsExactly(UserInvitationNotFoundException.class,
					() -> userInvitationQueryService.getTenantUserInvitation(
							GetUserInvitationQuery.builder().byUserId(alternativeUser2.getAlternativeUser().getId()).byType(type)
									.realmId("customer-rl-001").build()));
			assertEquals("Invitation was not found", exception3.getMessage());

		}));

		verify(keycloakServiceAccountClient, times(3)).deleteUser(keycloakAccessTokenCaptor.capture(),
				userRealmIdCaptor.capture(), userSsoIdCaptor.capture());
		userSsoIdCaptor.getAllValues().forEach(ssoId -> {
			Assertions.assertEquals("customer-rl-001", userRealmIdCaptor.getValue());
			Assertions.assertEquals("Bearer KeycloakAccessToken", keycloakAccessTokenCaptor.getValue());
			Assertions.assertTrue(List.of("customer-************", "customer-***************", "customer-************002")
					.contains(ssoId));
		});



	}

	@Test
	void testReAddSystemAdminWhenValidRequestThenReturnUserResponse() {
		// Arrange
		final var now = Instant.now();
		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.SYSTEM_ADMIN)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();

		when(userRoleClient.getUserRole(null, DefaultSystemRole.SYSTEM_ADMIN_ROLE.getExternalId(),
				CommonDomainConstants.SYSTEM_TENANT_ID.getValue(), true)).thenReturn(UserRoleResponse.builder()
						.tenantId("0")
						.id("0")
						.build());

		when(tenantClient.getTenant(CommonDomainConstants.SYSTEM_TENANT_ID.getValue())).thenReturn(ResponseEntity.ok(
				TenantResponse.builder()
						.tenantId(CommonDomainConstants.SYSTEM_TENANT_ID.getValue())
						.realmId(CommonDomainConstants.SYSTEM_TENANT_ID.toString())
						.build()));

		final var user = initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(new UserRoleId(1L))
				.tenantId(new TenantId(1L))
				.permissionStatus(UserPermissionStatus.ACTIVE)
				.userType(UserType.SYSTEM_ADMIN)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.externalId("External ID 2341")
				.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
				.build());

		userCommandService.deleteUser(user);

		final var request = AddSystemAdminUserRequest.builder()
				.userRoleId(1L)
				.email("<EMAIL>")
				.tenantId(1L)
				.firstName("First Name")
				.lastName("Last Name")
				.externalId("External ID 2341")
				.phoneNumber("**********")
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/user/users")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(1L)
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isCreated()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {
					final var userResponse = response.getResponseBody();
					Assertions.assertNotNull(userResponse);

					Assertions.assertEquals(UserType.SYSTEM_ADMIN, userResponse.getUserType());
					Assertions.assertEquals(request.getEmail(), userResponse.getEmail());
					Assertions.assertEquals(CommonDomainConstants.SYSTEM_TENANT_ID.getValue()
							.toString(), userResponse.getPermissions()
									.getFirst()
									.tenantId());
					Assertions.assertEquals(request.getUserRoleId()
							.toString(), userResponse.getPermissions()
									.getFirst()
									.userRoleId());
					Assertions.assertEquals(request.getLastName(), userResponse.getLastName());
					Assertions.assertEquals(request.getFirstName(), userResponse.getFirstName());
					Assertions.assertEquals(UserMappingFunctionSupport.buildFullName(request.getFirstName(), request
							.getLastName()), userResponse.getFullName());
					Assertions.assertEquals(request.getExternalId(), userResponse.getExternalId());
					assertFalse(userResponse.getIsAlternativeUser());

					Assertions.assertDoesNotThrow(() -> userInvitationQueryService.getTenantUserInvitation(
							GetUserInvitationQuery.builder()
									.byUserId(MapstructCommonDomainMapper.INSTANCE.stringToUserId(userResponse.getId()))
									.byType(UserInvitationType.USER_REGISTRATION)
									.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
									.build()));

					verify(notificationCreatedEventKafkaPublisher, times(2)).publish(any(
							com.styl.pacific.kafka.notification.avro.model.NotificationCreatedAvroEvent.class));

					final var userPreferenceDateTimeFormat = userPreferenceQueryService.getUserPreference(
							FindUserPreferenceQuery.builder()
									.byTenantId(CommonDomainConstants.SYSTEM_TENANT_ID)
									.byUserId(MapstructCommonDomainMapper.INSTANCE.stringToUserId(userResponse.getId()))
									.byKey(UserPreferenceKey.ACCOUNT_DATE_TIME_FORMAT)
									.build());
					final var dateTimeFormat = (DateTimeFormatPreferenceData) userPreferenceDateTimeFormat.getData();
					Assertions.assertEquals(defaultTimeFormatConfig.getDateFormat(), dateTimeFormat.getDateFormat());
					Assertions.assertEquals(defaultTimeFormatConfig.getTimeFormat(), dateTimeFormat.getTimeFormat());
					Assertions.assertEquals(defaultTimeFormatConfig.getTimeZone(), dateTimeFormat.getTimeZone()
							.getID());

				});
	}

	@Test
	void testReAddCustomerWhenValidRequestThenReturnUserResponse() {
		// Arrange
		final var now = Instant.now();
		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.SYSTEM_ADMIN)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();

		when(userRoleClient.getUserRole(null, DefaultSystemRole.CUSTOMER_ROLE.getExternalId(), 1L, true)).thenReturn(
				UserRoleResponse.builder()
						.tenantId("1")
						.id("1")
						.build());

		when(userRoleClient.getUserRole(null, DefaultSystemRole.SYSTEM_ADMIN_ROLE.getExternalId(),
				CommonDomainConstants.SYSTEM_TENANT_ID.getValue(), true)).thenReturn(UserRoleResponse.builder()
						.tenantId("0")
						.id("0")
						.build());

		when(tenantClient.getTenant(1)).thenReturn(ResponseEntity.ok(TenantResponse.builder()
				.tenantId(1L)
				.realmId("realm-customer-001")
				.build()));

		final var user = initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(new UserRoleId(1L))
				.tenantId(new TenantId(1L))
				.permissionStatus(UserPermissionStatus.ACTIVE)
				.userType(UserType.CUSTOMER)
				.email("<EMAIL>")
				.externalId("External ID 1000001")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.realmId("realm-customer-001")
				.build());

		userCommandService.deleteUser(user);

		final var request = AddCustomerUserRequest.builder()
				.userRoleId(1L)
				.email("<EMAIL>")
				.tenantId(1L)
				.firstName("First Name")
				.lastName("Last Name")
				.externalId("External ID 1000001")
				.phoneNumber("**********")
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/user/users")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(1L)
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isCreated()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {
					final var userResponse = response.getResponseBody();
					Assertions.assertNotNull(userResponse);

					Assertions.assertEquals(UserType.CUSTOMER, userResponse.getUserType());
					Assertions.assertEquals(request.getEmail(), userResponse.getEmail());
					Assertions.assertEquals(request.getUserRoleId()
							.toString(), userResponse.getPermissions()
									.getFirst()
									.userRoleId());
					Assertions.assertEquals(request.getLastName(), userResponse.getLastName());
					Assertions.assertEquals(request.getFirstName(), userResponse.getFirstName());
					Assertions.assertEquals(UserMappingFunctionSupport.buildFullName(request.getFirstName(), request
							.getLastName()), userResponse.getFullName());
					Assertions.assertEquals(request.getExternalId(), userResponse.getExternalId());
					assertFalse(userResponse.getIsAlternativeUser());

					Assertions.assertDoesNotThrow(() -> userInvitationQueryService.getTenantUserInvitation(
							GetUserInvitationQuery.builder()
									.byUserId(MapstructCommonDomainMapper.INSTANCE.stringToUserId(userResponse.getId()))
									.byType(UserInvitationType.USER_REGISTRATION)
									.realmId("realm-customer-001")
									.build()));

					verify(notificationCreatedEventKafkaPublisher, times(2)).publish(any(
							com.styl.pacific.kafka.notification.avro.model.NotificationCreatedAvroEvent.class));

				});
	}

	@Test
	void testUpdateCustomerWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var user = userSupporter.initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(new UserRoleId(1L))
				.tenantId(new TenantId(1L))
				.userType(UserType.CUSTOMER)
				.email("<EMAIL>")
				.externalId("External-ID-2500001")
				.uniqueExternalId("1.External-ID-2500001")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.realmId("realm-001")
				.build());
		userSupporter.activateUser("ssoId-2500001", user);

		when(userRoleClient.getUserRole(1L, null, 1L, true)).thenReturn(UserRoleResponse.builder()
				.tenantId("1")
				.id("1")
				.build());

		when(userRoleClient.getUserRole(null, DefaultSystemRole.CUSTOMER_ROLE.getExternalId(), 1L, true)).thenReturn(
				UserRoleResponse.builder()
						.tenantId("1")
						.id("1")
						.build());

		when(tenantClient.getTenant(1L)).thenReturn(ResponseEntity.ok(TenantResponse.builder()
				.tenantId(1L)
				.realmId("realm-001")
				.build()));

		final var request = UpdateUserRequest.builder()
				.id(user.getId()
						.getValue())
				.ssoId("ssoId-2500001")
				.userStatus(UserStatus.ACTIVE)
				.email(user.getEmail())
				.firstName(user.getFirstName())
				.lastName(user.getLastName())
				.phoneNumber(user.getPhoneNumber())
				.externalId("External-ID-2500002")
				.avatarHash("avatar-hash-2500002")
				.updatePermissions(List.of(UpdateUserPermissionRequest.builder()
						.permissionStatus(UserPermissionStatus.ACTIVE)
						.tenantId(1L)
						.userRoleId(1L)
						.build()))
				.build();

		// Act & Assert
		webClient.put()
				.uri("/api/user/users")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.requestId(1L)
						.tenantId(1L)
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {
					final var actualUserResponse = response.getResponseBody();
					assertNotNull(actualUserResponse);

					assertEquals(request.getId()
							.toString(), actualUserResponse.getId());
					assertEquals(request.getSsoId(), actualUserResponse.getSsoId());
					assertEquals(request.getAvatarHash(), actualUserResponse.getAvatarHash());
					assertEquals(UserType.CUSTOMER, actualUserResponse.getUserType());
					assertEquals(request.getEmail(), actualUserResponse.getEmail());
					assertEquals(request.getFirstName(), actualUserResponse.getFirstName());
					assertEquals(request.getLastName(), actualUserResponse.getLastName());
					Assertions.assertEquals(UserMappingFunctionSupport.buildFullName(request.getFirstName(), request
							.getLastName()), actualUserResponse.getFullName());

					assertEquals(request.getPhoneNumber(), actualUserResponse.getPhoneNumber());
					assertEquals(request.getExternalId(), actualUserResponse.getExternalId());

				});

	}

	@Test
	void testUpdateCustomerToRevertUserStatusWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var user = userSupporter.initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(new UserRoleId(1L))
				.tenantId(new TenantId(1L))
				.userType(UserType.CUSTOMER)
				.email("<EMAIL>")
				.externalId("External-ID-3500001")
				.uniqueExternalId("1.External-ID-3500001")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.realmId("realm-3500001")
				.build());
		userSupporter.activateUser("ssoId-3500001", user);
		userCommandService.deleteUsersInSchedule(DeleteUserCommand.builder()
				.byUserId(user.getId())
				.tenantId(new TenantId(1L))
				.build());

		final var expectedUser = userQueryService.getSingleUserById(user.getId())
				.orElseThrow(() -> new UserNotFoundException("User not found"));

		Assertions.assertEquals(UserStatus.ARCHIVED, expectedUser.getUserStatus(),
				"Expected user status must be ARCHIVED");
		Assertions.assertNotNull(expectedUser.getSchedulingDeletedAt(),
				"Expected user schedulingDeletedAt must be not null");

		when(userRoleClient.getUserRole(1L, null, 1L, true)).thenReturn(UserRoleResponse.builder()
				.tenantId("1")
				.id("1")
				.build());

		when(userRoleClient.getUserRole(null, DefaultSystemRole.CUSTOMER_ROLE.getExternalId(), 1L, true)).thenReturn(
				UserRoleResponse.builder()
						.tenantId("1")
						.id("1")
						.build());

		when(tenantClient.getTenant(1L)).thenReturn(ResponseEntity.ok(TenantResponse.builder()
				.tenantId(1L)
				.realmId("realm-001")
				.build()));

		final var request = UpdateUserRequest.builder()
				.id(expectedUser.getId()
						.getValue())
				.ssoId(expectedUser.getSsoId())
				.userStatus(UserStatus.ACTIVE)
				.email(expectedUser.getEmail())
				.firstName(expectedUser.getFirstName())
				.lastName(expectedUser.getLastName())
				.phoneNumber(expectedUser.getPhoneNumber())
				.externalId("External-ID-3500001")
				.updatePermissions(List.of(UpdateUserPermissionRequest.builder()
						.permissionStatus(UserPermissionStatus.ACTIVE)
						.tenantId(1L)
						.userRoleId(1L)
						.build()))
				.build();

		// Act & Assert
		webClient.put()
				.uri("/api/user/users")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.requestId(1L)
						.tenantId(1L)
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {
					final var actualUserResponse = response.getResponseBody();
					assertNotNull(actualUserResponse);

					assertEquals(request.getId()
							.toString(), actualUserResponse.getId());
					assertEquals(request.getSsoId(), actualUserResponse.getSsoId());
					assertEquals(UserType.CUSTOMER, actualUserResponse.getUserType());
					assertEquals(request.getEmail(), actualUserResponse.getEmail());
					assertEquals(request.getFirstName(), actualUserResponse.getFirstName());
					assertEquals(request.getLastName(), actualUserResponse.getLastName());
					Assertions.assertEquals(UserMappingFunctionSupport.buildFullName(request.getFirstName(), request
							.getLastName()), actualUserResponse.getFullName());
					assertEquals(request.getPhoneNumber(), actualUserResponse.getPhoneNumber());
					assertEquals(request.getExternalId(), actualUserResponse.getExternalId());
					assertEquals(request.getUserStatus(), actualUserResponse.getUserStatus());
					assertNull(actualUserResponse.getSchedulingDeletedAt());

				});

	}

	@Test
	void testUpdateUserWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var user = initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(new UserRoleId(1L))
				.tenantId(new TenantId(1L))
				.userType(UserType.BACK_OFFICE)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
				.build());

		final var request = UpdateUserRequest.builder()
				.id(user.getId()
						.getValue())
				.ssoId("sid-002")
				.userStatus(UserStatus.ARCHIVED)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.updatePermissions(List.of(UpdateUserPermissionRequest.builder()
						.permissionStatus(UserPermissionStatus.IN_ACTIVE)
						.tenantId(1L)
						.userRoleId(1L)
						.build()))
				.build();

		// Act & Assert
		webClient.put()
				.uri("/api/user/users")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.requestId(1L)
						.tenantId(1L)
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {
					assertEquals("<EMAIL>", Optional.ofNullable(response.getResponseBody())
							.map(UserResponse::getEmail)
							.orElse(null));
				});

	}

	@Test
	void testUpdateUserWithSendingInvitationWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		when(userRoleClient.getUserRole(null, DefaultSystemRole.CUSTOMER_ROLE.getExternalId(), 1L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("1").id("1").build());

		final var user = initializeNewUser(
				UpsertTenantUserCommand.builder().userRoleId(new UserRoleId(1L)).tenantId(new TenantId(1L))
						.userType(UserType.CUSTOMER).firstName("first name").lastName("last name")
						.phoneNumber("**********").realmId("customer-realm-999").build());

		final var request = UpdateUserRequest.builder().id(user.getId().getValue()).userStatus(UserStatus.ACTIVE)
				.email("<EMAIL>").firstName("first name").lastName("last name")
				.phoneNumber("**********").updatePermissions(
						List.of(UpdateUserPermissionRequest.builder().permissionStatus(UserPermissionStatus.ACTIVE)
								.tenantId(1L).userRoleId(1L).build())).build();

		// Act & Assert
		webClient.put().uri("/api/user/users").contentType(MediaType.APPLICATION_JSON).bodyValue(request).headers(
						header -> HeaderGenerator.generateHttpHeaders(
								GenerateHttpHeader.builder().requestId(1L).tenantId(1L).build())).exchange().expectHeader()
				.contentType(MediaType.APPLICATION_JSON).expectStatus().isOk().expectBody(UserResponse.class)
				.consumeWith(response -> {

					final var userResponse = response.getResponseBody();

					assertNotNull(userResponse);

					assertEquals("<EMAIL>", userResponse.getEmail());
					assertNotNull(userInvitationQueryService.getTenantUserInvitation(
							GetUserInvitationQuery.builder().byUserId(user.getId()).byTenantId(new TenantId(1L))
									.byType(UserInvitationType.USER_REGISTRATION).realmId("customer-realm-999")
									.build()));

					verify(notificationCreatedEventKafkaPublisher, times(1)).publish(
							any(com.styl.pacific.kafka.notification.avro.model.NotificationCreatedAvroEvent.class));
				});

	}

	@Test
	void testUpdateUserPermissionsWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var user = initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(new UserRoleId(1L))
				.tenantId(new TenantId(1L))
				.permissionStatus(UserPermissionStatus.ACTIVE)
				.userType(UserType.BACK_OFFICE)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
				.build());

		userCommandService.activateUser(ActivateUserTenantCommand.builder()
				.userId(user.getId())
				.ssoId("sid-100102")
				.realmId(user.getRealmId())
				.email(user.getEmail())
				.firstName(user.getFirstName())
				.lastName(user.getLastName())
				.build());

		when(userRoleClient.getUserRole(2L, null, 2L, true)).thenReturn(UserRoleResponse.builder()
				.tenantId("2")
				.id("2")
				.build());

		when(tenantClient.getTenant(2L)).thenReturn(ResponseEntity.ok(TenantResponse.builder()
				.tenantId(2L)
				.realmId("real-001")
				.build()));

		userCommandService.addSingleUser(UpsertTenantUserCommand.builder()
				.userId(user.getId())
				.userRoleId(new UserRoleId(2L))
				.tenantId(new TenantId(2L))
				.userStatus(UserStatus.ACTIVE)
				.permissionStatus(UserPermissionStatus.ACTIVE)
				.ssoId("sid-100102")
				.userType(UserType.BACK_OFFICE)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
				.build());

		final var request = UpdateUserRequest.builder()
				.id(user.getId()
						.getValue())
				.ssoId("sid-102")
				.userStatus(UserStatus.ARCHIVED)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.updatePermissions(List.of(UpdateUserPermissionRequest.builder()
						.permissionStatus(UserPermissionStatus.IN_ACTIVE)
						.tenantId(1L)
						.userRoleId(1L)
						.build()))
				.build();

		// Act & Assert
		webClient.put()
				.uri("/api/user/users")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.requestId(1L)
						.tenantId(1L)
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {
					assertEquals("<EMAIL>", Optional.ofNullable(response.getResponseBody())
							.map(UserResponse::getEmail)
							.orElse(null));
					assertEquals(1, request.getUpdatePermissions()
							.size());

					Arrays.stream(UserInvitationType.values())
							.forEach((type -> {
								final var exception = assertThrowsExactly(UserInvitationNotFoundException.class,
										() -> userInvitationQueryService.getTenantUserInvitation(GetUserInvitationQuery
												.builder()
												.byUserId(user.getId())
												.byTenantId(new TenantId(2L))
												.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
												.build()));

								assertEquals("Invitation was not found", exception.getMessage());
							}));

				});

	}

	@Test
	void testAddNewUserPermissionsWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var user = initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(new UserRoleId(1L))
				.tenantId(new TenantId(1L))
				.permissionStatus(UserPermissionStatus.ACTIVE)
				.userType(UserType.BACK_OFFICE)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
				.build());

		userCommandService.activateUser(ActivateUserTenantCommand.builder()
				.userId(user.getId())
				.ssoId("ssoId-202")
				.realmId(user.getRealmId())
				.email(user.getEmail())
				.firstName(user.getFirstName())
				.lastName(user.getLastName())
				.build());

		when(userRoleClient.getUserRole(2L, null, 2L, true)).thenReturn(UserRoleResponse.builder()
				.tenantId("2")
				.id("2")
				.build());

		when(tenantClient.getTenant(2L)).thenReturn(ResponseEntity.ok(TenantResponse.builder()
				.tenantId(2L)
				.realmId("realmId-202")
				.build()));
		final var notificationEventCaptor = ArgumentCaptor.forClass(
				com.styl.pacific.kafka.notification.avro.model.NotificationCreatedAvroEvent.class);

		final var request = UpdateUserRequest.builder()
				.id(user.getId()
						.getValue())
				.ssoId("ssoId-202")
				.userStatus(UserStatus.ACTIVE)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.updatePermissions(List.of(UpdateUserPermissionRequest.builder()
						.permissionStatus(UserPermissionStatus.ACTIVE)
						.tenantId(1L)
						.userRoleId(1L)
						.build(), UpdateUserPermissionRequest.builder()
								.permissionStatus(UserPermissionStatus.CREATED)
								.tenantId(2L)
								.userRoleId(2L)
								.build()))
				.build();

		// Act & Assert
		webClient.put()
				.uri("/api/user/users")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.requestId(1L)
						.tenantId(1L)
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {
					assertEquals(2, request.getUpdatePermissions()
							.size());
					final var pageUserRequiredActionResult = userRequiredActionQueryService.queryUserRequiredAction(
							UserRequiredActionPaginationQuery.builder()
									.filter(UserRequiredActionFilter.builder()
											.byUserId(user.getId())
											.build())
									.build());

					assertEquals(1, pageUserRequiredActionResult.getTotalElements());
					final var requiredAction = pageUserRequiredActionResult.getContent()
							.getFirst();
					assertEquals(UserRequiredActionType.ADDED_NEW_TENANT, requiredAction.getActionType());

					verify(notificationCreatedEventKafkaPublisher, times(2)).publish(notificationEventCaptor.capture());
					final var notificationEvent = notificationEventCaptor.getValue();
					Assertions.assertEquals(Action.INVITE_USER_TO_ANOTHER_TENANT, notificationEvent.getAction());
					assertThat(notificationEvent.getChannels(), Matchers.containsInAnyOrder(List.of(
							com.styl.pacific.kafka.notification.avro.model.NotificationAvroChannel.IN_APP)
							.toArray()));

					assertEquals(user.getId()
							.getValue(), notificationEvent.getUserId());
				});
	}

	@Test
	void testUpdateUserMeWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var user = initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(new UserRoleId(1L))
				.tenantId(new TenantId(1L))
				.permissionStatus(UserPermissionStatus.CREATED)
				.userType(UserType.BACK_OFFICE)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
				.build());

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId(user.getId()
						.getValue()
						.toString())
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.BACK_OFFICE)
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.build();

		final var request = UpdateUserMeRequest.builder()
				.firstName("first name 1234")
				.lastName("last name 123")
				.phoneNumber("**********")
				.externalId("externalId-123")
				.avatarHash("avatar-hash-backoffice-003")
				.build();

		// Act & Assert
		webClient.put()
				.uri("/api/user/users/profile")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(1L)
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {
					assertEquals(HttpStatus.OK, response.getStatus());
					assertEquals("first name 1234", Optional.ofNullable(response.getResponseBody())
							.map(UserResponse::getFirstName)
							.orElse(null));
					assertEquals("last name 123", Optional.ofNullable(response.getResponseBody())
							.map(UserResponse::getLastName)
							.orElse(null));
					assertEquals("externalId-123", Optional.ofNullable(response.getResponseBody())
							.map(UserResponse::getExternalId)
							.orElse(null));
					assertEquals("avatar-hash-backoffice-003", Optional.ofNullable(response.getResponseBody())
							.map(UserResponse::getAvatarHash)
							.orElse(null));

					assertEquals("**********", Optional.ofNullable(response.getResponseBody())
							.map(UserResponse::getPhoneNumber)
							.orElse(null));

				});

	}

	@Test
	void testUpdateUserMeWithUserGroupWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		when(userRoleClient.getUserRole(1L, null, 1L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("1").id("1").build());

		when(tenantClient.getTenant(1)).thenReturn(ResponseEntity.ok(
				TenantResponse.builder().tenantId(1L).realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID).build()));

		when(userRoleClient.getUserRole(null, DefaultSystemRole.CUSTOMER_ROLE.getExternalId(), 1L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("1").id("2").build());

		final var school = groupCommandService.upsertUserGroup(
				UpsertUserGroupCommand.builder().tenantId(new TenantId(1L)).groupName("School A")
						.description("School A").build());

		final var user = initializeNewUser(
				UpsertTenantUserCommand.builder().userRoleId(new UserRoleId(1L)).tenantId(new TenantId(1L))
						.permissionStatus(UserPermissionStatus.CREATED).userStatus(UserStatus.CREATED)
						.userType(UserType.CUSTOMER).email("<EMAIL>").firstName("first name")
						.lastName("last name").phoneNumber("**********").externalId("externalId3")
						.realmId("customer-x-001").build());

		final var userTokenClaim = UserTokenClaim.builder().acr("acr").aud("aud").azp("azp")
				.email("<EMAIL>").userId(user.getId().getValue().toString()).permissions(
						Set.of(UserTokenClaim.TenantRolePermission.builder().tenantId("1").userRoleId("1").build()))
				.userType(UserType.CUSTOMER).iss("iss").jti("jti").sub("sub").typ("typ").scope("scope")
				.emailVerified(true).build();

		final var request = UpdateUserMeRequest.builder().firstName("first name 1234").lastName("last name 123")
				.phoneNumber("**********").externalId("externalId-123").userGroupId(school.getId().getValue()).build();

		// Act & Assert
		webClient.put().uri("/api/user/users/profile").contentType(MediaType.APPLICATION_JSON).bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(
						GenerateHttpHeader.builder().header(header).objectMapper(objectMapper)
								.userTokenClaim(userTokenClaim).requestId(1L).tenantId(1L).build())).exchange()
				.expectHeader().contentType(MediaType.APPLICATION_JSON).expectStatus().isOk()
				.expectBody(UserResponse.class).consumeWith(response -> {
					assertEquals(HttpStatus.OK, response.getStatus());
					assertEquals("first name 1234",
							Optional.ofNullable(response.getResponseBody()).map(UserResponse::getFirstName).orElse(null));
					assertEquals("last name 123",
							Optional.ofNullable(response.getResponseBody()).map(UserResponse::getLastName).orElse(null));
					assertEquals("externalId-123",
							Optional.ofNullable(response.getResponseBody()).map(UserResponse::getExternalId).orElse(null));

					assertEquals("**********",
							Optional.ofNullable(response.getResponseBody()).map(UserResponse::getPhoneNumber).orElse(null));

					assertEquals(school.getId().getValue().toString(),
							Optional.ofNullable(response.getResponseBody()).map(it -> it.getUserGroup().getId()).orElse(null));

				});

	}

	@Test
	void testUpdateCustomerMeByAlternativeUserWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		when(userRoleClient.getUserRole(null, DefaultSystemRole.CUSTOMER_ROLE.getExternalId(), 1L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("1").id("2").build());

		final var user = initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(new UserRoleId(1L))
				.tenantId(new TenantId(1L))
				.userType(UserType.CUSTOMER)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.realmId("customer-002")
				.build());

		final var alternativeUser = alternativeUserAccessCommandService.createAlternativeUserAccess(user.getId(), new TenantId(1L),
				CreateAlternativeUserAccessCommand.builder()
						.firstName("Alternative First Name")
						.lastName("Alternative Last Name")
						.email("<EMAIL>")
						.phoneNumber("**********")
						.externalId("customer-alternative-25051317102")
						.migrationId("customer-alternative-25051317102")
						.userType(UserType.CUSTOMER)
						.build(), ControlNotificationCommand.builder()
						.isSkipNotification(false)
						.build()).getAlternativeUser();

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email(user.getEmail())
				.userId(user.getId()
						.getValue()
						.toString())
				.alternativeUserId(alternativeUser.getId()
						.getValue()
						.toString())
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.CUSTOMER)
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.build();


		final var request = UpdateUserMeRequest.builder()
				.firstName("first name 1234")
				.lastName("last name 123")
				.phoneNumber("**********")
				.build();

		// Act & Assert
		webClient.put()
				.uri("/api/user/users/profile")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(1L)
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {
					assertEquals(HttpStatus.OK, response.getStatus());
					assertEquals(alternativeUser.getId()
							.getValue()
							.toString(), Optional.ofNullable(response.getResponseBody())
							.map(UserResponse::getId)
							.orElse(null));

					assertEquals("first name 1234", Optional.ofNullable(response.getResponseBody())
							.map(UserResponse::getFirstName)
							.orElse(null));
					assertEquals("last name 123", Optional.ofNullable(response.getResponseBody())
							.map(UserResponse::getLastName)
							.orElse(null));

					assertEquals("**********", Optional.ofNullable(response.getResponseBody())
							.map(UserResponse::getPhoneNumber)
							.orElse(null));

				});

	}

	@Test
	void testGetUserProfileWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var user = initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(new UserRoleId(1L))
				.tenantId(new TenantId(1L))
				.userType(UserType.BACK_OFFICE)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
				.build());

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId(user.getId()
						.getValue()
						.toString())
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.BACK_OFFICE)
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.build();

		// Act & Assert
		webClient.get()
				.uri("/api/user/users/{userId}/profile", user.getId()
						.getValue())
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(1L)
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {

					final var userResponse = response.getResponseBody();
					assertNotNull(userResponse);
					assertEquals(user.getId()
							.getValue()
							.toString(), userResponse.getId());
					assertEquals(user.getSsoId(), userResponse.getSsoId());
					assertEquals(user.getUserType(), userResponse.getUserType());
					assertEquals(user.getEmail(), userResponse.getEmail());
					assertEquals(user.getFirstName(), userResponse.getFirstName());
					assertEquals(user.getLastName(), userResponse.getLastName());
					Assertions.assertEquals(UserMappingFunctionSupport.buildFullName(user.getFirstName(), user
							.getLastName()), userResponse.getFullName());
					assertEquals(user.getPhoneNumber(), userResponse.getPhoneNumber());
					assertEquals(user.getRealmId(), userResponse.getRealmId());
					assertEquals(user.getUserNonCompletedActions(), userResponse.getUserNonCompletedActions());
					assertEquals(user.getTotalSubAccounts(), userResponse.getTotalSubAccounts());
					assertEquals(user.getTotalSponsors(), userResponse.getTotalSponsors());
					assertFalse(userResponse.getIsAlternativeUser());
				});

	}

	@Test
	void testGetUserProfileByCustomerWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		when(userRoleClient.getUserRole(null, DefaultSystemRole.CUSTOMER_ROLE.getExternalId(), 1L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("1").id("2").build());

		final var user = initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(new UserRoleId(1L))
				.tenantId(new TenantId(1L))
				.userType(UserType.CUSTOMER)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.realmId("customer-002")
				.build());

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email(user.getEmail())
				.userId(user.getId()
						.getValue()
						.toString())
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.CUSTOMER)
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.build();

		// Act & Assert
		webClient.get()
				.uri("/api/user/users/{userId}/profile", user.getId()
						.getValue())
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(1L)
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {

					final var userResponse = response.getResponseBody();
					assertNotNull(userResponse);
					assertEquals(user.getId()
							.getValue()
							.toString(), userResponse.getId());
					assertEquals(user.getSsoId(), userResponse.getSsoId());
					assertEquals(user.getUserType(), userResponse.getUserType());
					assertEquals(user.getEmail(), userResponse.getEmail());
					assertEquals(user.getFirstName(), userResponse.getFirstName());
					assertEquals(user.getLastName(), userResponse.getLastName());
					Assertions.assertEquals(UserMappingFunctionSupport.buildFullName(user.getFirstName(), user
							.getLastName()), userResponse.getFullName());
					assertEquals(user.getPhoneNumber(), userResponse.getPhoneNumber());
					assertEquals(user.getRealmId(), userResponse.getRealmId());
					assertEquals(user.getUserNonCompletedActions(), userResponse.getUserNonCompletedActions());
					assertEquals(user.getTotalSubAccounts(), userResponse.getTotalSubAccounts());
					assertEquals(user.getTotalSponsors(), userResponse.getTotalSponsors());
					assertFalse(userResponse.getIsAlternativeUser());
				});

	}

	@Test
	void testGetCustomerProfileByAlternativeUserWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		when(userRoleClient.getUserRole(null, DefaultSystemRole.CUSTOMER_ROLE.getExternalId(), 1L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("1").id("2").build());

		final var user = initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(new UserRoleId(1L))
				.tenantId(new TenantId(1L))
				.userType(UserType.CUSTOMER)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.realmId("customer-002")
				.build());

		final var alternativeUser = alternativeUserAccessCommandService.createAlternativeUserAccess(user.getId(), new TenantId(1L),
				CreateAlternativeUserAccessCommand.builder()
						.firstName("Alternative First Name")
						.lastName("Alternative Last Name")
						.email("<EMAIL>")
						.phoneNumber("**********")
						.externalId("customer-alternative-25051317002")
						.migrationId("customer-alternative-25051317002")
						.userType(UserType.CUSTOMER)
						.build(), ControlNotificationCommand.builder()
						.isSkipNotification(false)
						.build()).getAlternativeUser();

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email(user.getEmail())
				.userId(user.getId()
						.getValue()
						.toString())
				.alternativeUserId(alternativeUser.getId()
						.getValue()
						.toString())
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.CUSTOMER)
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.build();

		// Act & Assert
		webClient.get()
				.uri("/api/user/users/{userId}/profile", user.getId()
						.getValue())
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(1L)
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {

					final var userResponse = response.getResponseBody();
					assertNotNull(userResponse);
					assertEquals(alternativeUser.getId()
							.getValue()
							.toString(), userResponse.getId());
					assertEquals(alternativeUser.getSsoId(), userResponse.getSsoId());
					assertEquals(alternativeUser.getUserType(), userResponse.getUserType());
					assertEquals(alternativeUser.getEmail(), userResponse.getEmail());
					assertEquals(alternativeUser.getFirstName(), userResponse.getFirstName());
					assertEquals(alternativeUser.getLastName(), userResponse.getLastName());
					Assertions.assertEquals(UserMappingFunctionSupport.buildFullName(alternativeUser.getFirstName(), alternativeUser
							.getLastName()), userResponse.getFullName());
					assertEquals(alternativeUser.getPhoneNumber(), userResponse.getPhoneNumber());
					assertEquals(alternativeUser.getRealmId(), userResponse.getRealmId());
					assertEquals(alternativeUser.getUserNonCompletedActions(), userResponse.getUserNonCompletedActions());
					assertEquals(alternativeUser.getTotalSubAccounts(), userResponse.getTotalSubAccounts());
					assertEquals(alternativeUser.getTotalSponsors(), userResponse.getTotalSponsors());
					assertTrue(userResponse.getIsAlternativeUser());
				});
	}

	@Test
	void testGetUserProfileWithSpecialCharacterWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var user = initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(new UserRoleId(1L))
				.tenantId(new TenantId(1L))
				.userType(UserType.BACK_OFFICE)
				.email("<EMAIL>")
				.firstName("first ¾®")
				.lastName("last œ‰Ã")
				.phoneNumber("**********")
				.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
				.build());

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email(user.getEmail())
				.userId(user.getId()
						.getValue()
						.toString())
				.name(UserMappingFunctionSupport.buildFullName(user.getFirstName(), user.getLastName()))
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.BACK_OFFICE)
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.build();

		// Act & Assert
		webClient.get()
				.uri("/api/user/users/{userId}/profile", user.getId()
						.getValue())
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(1L)
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {

					final var userResponse = response.getResponseBody();
					assertNotNull(userResponse);
					assertEquals(user.getId()
							.getValue()
							.toString(), userResponse.getId());
					assertEquals(user.getSsoId(), userResponse.getSsoId());
					assertEquals(user.getUserType(), userResponse.getUserType());
					assertEquals(user.getEmail(), userResponse.getEmail());
					assertEquals(user.getFirstName(), userResponse.getFirstName());
					assertEquals(user.getLastName(), userResponse.getLastName());
					Assertions.assertEquals(UserMappingFunctionSupport.buildFullName(user.getFirstName(), user
							.getLastName()), userResponse.getFullName());
					assertEquals(user.getPhoneNumber(), userResponse.getPhoneNumber());
					assertEquals(user.getRealmId(), userResponse.getRealmId());
					assertEquals(user.getUserNonCompletedActions(), userResponse.getUserNonCompletedActions());
					assertEquals(user.getTotalSubAccounts(), userResponse.getTotalSubAccounts());
					assertEquals(user.getTotalSponsors(), userResponse.getTotalSponsors());
					assertFalse(userResponse.getIsAlternativeUser());
				});
	}

	@Test
	void testGetUserProfileWithSubAccountCountWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		when(userRoleClient.getUserRole(2L, null, 2L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("2").id("2").build());

		when(userRoleClient.getUserRole(3L, null, 3L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("3").id("3").build());

		when(userRoleClient.getUserRole(null, DefaultSystemRole.CUSTOMER_ROLE.getExternalId(), 1L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("1").id("1").build());

		final var userTokenClaim = UserTokenClaim.builder().acr("acr").aud("aud").azp("azp")
				.email("<EMAIL>").userId("1").permissions(
						Set.of(UserTokenClaim.TenantRolePermission.builder().tenantId("1").userRoleId("1").build()))
				.userType(UserType.BACK_OFFICE).iss("iss").jti("jti").sub("sub").typ("typ").scope("scope")
				.emailVerified(true).build();

		final var user1 = userSupporter.activateUser("customer-39001", initializeNewUser(
				UpsertTenantUserCommand.builder().userRoleId(new UserRoleId(1L)).tenantId(new TenantId(1L))
						.userType(UserType.CUSTOMER).email("<EMAIL>").firstName("first name")
						.lastName("last name").phoneNumber("**********").realmId("customer-rl-001").build()));
		userSupporter.activateUser("customer-39001", user1);

		final var user2 = initializeNewUser(
				UpsertTenantUserCommand.builder().userRoleId(new UserRoleId(1L)).tenantId(new TenantId(1L))
						.userType(UserType.CUSTOMER).email("<EMAIL>").firstName("first name")
						.lastName("last name").phoneNumber("**********").realmId("customer-rl-001").build());
		userSupporter.activateUser("customer-39002", user2);

		subAccountCommandService.createSubAccount(
				CreateSubAccountNewUserCommand.builder().userTypeCreator(UserType.CUSTOMER).sponsorUserId(user1.getId())
						.firstName("first name 1").lastName("last name 1").phoneNumber("**********")
						.tenantId(new TenantId(1L)).build());

		subAccountCommandService.createSubAccount(
				CreateSubAccountExistingUserCommand.builder().userTypeCreator(UserType.CUSTOMER)
						.sponsorUserId(user1.getId()).tenantId(new TenantId(1L)).email(user2.getEmail()).build());
		// Act & Assert
		webClient.get().uri("/api/user/users/{userId}/profile", user1.getId().getValue()).headers(
						header -> HeaderGenerator.generateHttpHeaders(
								GenerateHttpHeader.builder().header(header).objectMapper(objectMapper)
										.userTokenClaim(userTokenClaim).requestId(1L).tenantId(1L).build())).exchange()
				.expectHeader().contentType(MediaType.APPLICATION_JSON).expectStatus().isOk()
				.expectBody(UserResponse.class).consumeWith(response -> {

					final var userResponse = response.getResponseBody();
					assertNotNull(userResponse);
					assertEquals(user1.getId().getValue().toString(), userResponse.getId());
					assertEquals(user1.getSsoId(), userResponse.getSsoId());
					assertEquals(user1.getUserType(), userResponse.getUserType());
					assertEquals(user1.getEmail(), userResponse.getEmail());
					assertEquals(user1.getFirstName(), userResponse.getFirstName());
					assertEquals(user1.getLastName(), userResponse.getLastName());
					Assertions.assertEquals(UserMappingFunctionSupport.buildFullName(user1.getFirstName(), user1.getLastName()),
							userResponse.getFullName());
					assertEquals(user1.getPhoneNumber(), userResponse.getPhoneNumber());
					assertEquals(user1.getRealmId(), userResponse.getRealmId());
					assertEquals(user1.getUserNonCompletedActions(), userResponse.getUserNonCompletedActions());
					assertEquals(2, userResponse.getTotalSubAccounts());
					assertNull(userResponse.getTotalSponsors());
				});
	}

	@Test
	void testGetUserProfileWithFetchRuleWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		when(userRoleClient.getUserRole(2L, null, 2L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("2").id("2").build());

		when(userRoleClient.getUserRole(3L, null, 3L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("3").id("3").build());

		when(userRoleClient.getUserRole(null, DefaultSystemRole.CUSTOMER_ROLE.getExternalId(), 1L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("1").id("1").build());

		final var userTokenClaim = UserTokenClaim.builder().acr("acr").aud("aud").azp("azp")
				.email("<EMAIL>").userId("1").permissions(
						Set.of(UserTokenClaim.TenantRolePermission.builder().tenantId("1").userRoleId("1").build()))
				.userType(UserType.BACK_OFFICE).iss("iss").jti("jti").sub("sub").typ("typ").scope("scope")
				.emailVerified(true).build();

		final var user = userSupporter.activateUser("customer-41001", initializeNewUser(
				UpsertTenantUserCommand.builder().userRoleId(new UserRoleId(1L)).tenantId(new TenantId(1L))
						.userType(UserType.CUSTOMER).email("<EMAIL>").firstName("first name")
						.lastName("last name").phoneNumber("**********").realmId("customer-rl-001").build()));

		final Map<String, String> map = new HashMap<>();
		map.put("fetchPermissions", "false");
		map.put("fetchUserGroup", "false");
		map.put("countSponsors", "false");
		map.put("countSubAccounts", "false");

		final LinkedMultiValueMap<String, String> requestParams = new LinkedMultiValueMap<>(
				map.entrySet().stream().filter(it -> StringUtils.isNotBlank(it.getValue()))
						.collect(Collectors.toMap(Map.Entry::getKey, it -> List.of(it.getValue()))));

		// Act & Assert
		webClient.get().uri(uriBuilder -> {
					uriBuilder.path("/api/user/users/{userId}/profile");
					uriBuilder.queryParams(requestParams);
					return uriBuilder.build(user.getId().getValue().toString());
				}).headers(header -> HeaderGenerator.generateHttpHeaders(
						GenerateHttpHeader.builder().header(header).objectMapper(objectMapper).userTokenClaim(userTokenClaim)
								.requestId(1L).tenantId(1L).build())).exchange().expectHeader()
				.contentType(MediaType.APPLICATION_JSON).expectStatus().isOk().expectBody(UserResponse.class)
				.consumeWith(response -> {

					final var userResponse = response.getResponseBody();
					assertNotNull(userResponse);
					assertEquals(user.getId().getValue().toString(), userResponse.getId());
					assertEquals(user.getSsoId(), userResponse.getSsoId());
					assertEquals(user.getUserType(), userResponse.getUserType());
					assertEquals(user.getEmail(), userResponse.getEmail());
					assertEquals(user.getFirstName(), userResponse.getFirstName());
					assertEquals(user.getLastName(), userResponse.getLastName());
					Assertions.assertEquals(
							UserMappingFunctionSupport.buildFullName(user.getFirstName(), user.getLastName()),
							userResponse.getFullName());

					assertEquals(user.getPhoneNumber(), userResponse.getPhoneNumber());
					assertEquals(user.getRealmId(), userResponse.getRealmId());
					assertEquals(user.getUserNonCompletedActions(), userResponse.getUserNonCompletedActions());
					assertNull(userResponse.getTotalSubAccounts());
					assertNull(userResponse.getTotalSponsors());
					assertTrue(userResponse.getPermissions().isEmpty());
					assertFalse(userResponse.getIsAlternativeUser());

					final var actualGroup = groupQueryService.getUserGroup(
							GetUserGroupQuery.builder().tenantId(new TenantId(1L))
									.userGroupId(new UserGroupId(Long.valueOf(userResponse.getUserGroup().getId())))
									.build());

					assertEquals(UserGroupConstants.MAIN_USER_GROUP_TEMPLATE.getGroupKey(), actualGroup.getGroupKey());
					assertEquals(UserGroupConstants.MAIN_USER_GROUP_TEMPLATE.getGroupName(),
							actualGroup.getGroupName());
					assertEquals(UserGroupConstants.MAIN_USER_GROUP_TEMPLATE.getDescription(),
							actualGroup.getDescription());
				});

	}

	@Test
	void testQueryUsersWhenValidRequestThenReturnSuccessfully() {
		// Arrange

		when(userRoleClient.getUserRole(1L, null, 1L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("1").id("1").build());

		when(tenantClient.getTenant(1)).thenReturn(ResponseEntity.ok(
				TenantResponse.builder().tenantId(1L).realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID).build()));

		final var user = initializeNewUser(
				UpsertTenantUserCommand.builder().userRoleId(new UserRoleId(1L)).tenantId(new TenantId(1L))
						.permissionStatus(UserPermissionStatus.CREATED).userStatus(UserStatus.CREATED)
						.userType(UserType.BACK_OFFICE).email("<EMAIL>").firstName("first name")
						.lastName("last name").phoneNumber("**********").externalId("externalId1").build());

		final var now = Instant.now();
		final var request = QueryUserPaginationRequest.builder().filter(QueryUserRequest.builder().bySsoId("sid-q001")
						.byCreatedRange(LongDateTimeRange.builder().from(now.minus(10, ChronoUnit.MINUTES).toEpochMilli())
								.to(now.toEpochMilli()).build()).byRoleIds(Set.of(1L, 2L))
						.byUserStatuses(Set.of(UserStatus.CREATED)).byUserTypes(Set.of(UserType.BACK_OFFICE))
						.byEmail("<EMAIL>").byExternalId("externalId1").byPhoneNumber("**********")
						.byName("first").byTenantId(1L).build()).size(10).page(0).sortDirection("ASC").sortFields(List.of("id"))
				.build();

		final Map<String, String> map = new HashMap<>();

		map.put("filter.bySid", request.getFilter().getBySsoId());
		map.put("filter.byCreatedRange.from",
				Optional.ofNullable(request.getFilter().getByCreatedRange()).map(LongDateTimeRange::getFrom)
						.map(Object::toString).orElse(null));
		map.put("filter.byCreatedRange.to",
				Optional.ofNullable(request.getFilter().getByCreatedRange()).map(LongDateTimeRange::getTo)
						.map(Object::toString).orElse(null));
		map.put("filter.byRoleIds",
				request.getFilter().getByRoleIds().stream().map(Object::toString).collect(Collectors.joining(",")));
		map.put("filter.byUserStatuses", Optional.ofNullable(request.getFilter().getByUserStatuses())
				.flatMap(it -> it.stream().findFirst().map(UserStatus::toString)).orElse(null));
		map.put("filter.byUserTypes", Optional.ofNullable(request.getFilter().getByUserTypes())
				.flatMap(it -> it.stream().findFirst().map(UserType::toString)).orElse(null));
		map.put("filter.byEmail",
				Optional.ofNullable(request.getFilter()).map(QueryUserRequest::getByEmail).orElse(null));
		map.put("filter.byExternalId",
				Optional.ofNullable(request.getFilter()).map(QueryUserRequest::getByExternalId).orElse(null));
		map.put("filter.byName",
				Optional.ofNullable(request.getFilter()).map(QueryUserRequest::getByName).orElse(null));
		map.put("filter.byPhoneNumber",
				Optional.ofNullable(request.getFilter()).map(QueryUserRequest::getByPhoneNumber).orElse(null));
		map.put("filter.byTenantId",
				Optional.ofNullable(request.getFilter()).map(QueryUserRequest::getByTenantId).map(Object::toString)
						.orElse(null));

		map.put("size", String.valueOf(request.getSize()));
		map.put("page", String.valueOf(request.getPage()));
		map.put("sortDirection", request.getSortDirection());
		map.put("sortFields", request.getSortFields().stream().findFirst().orElse(null));

		final LinkedMultiValueMap<String, String> requestParams = new LinkedMultiValueMap<>(
				map.entrySet().stream().filter(it -> StringUtils.isNotBlank(it.getValue()))
						.collect(Collectors.toMap(Map.Entry::getKey, it -> List.of(it.getValue()))));

		// Act & Assert
		webClient.get().uri(uriBuilder -> {
					uriBuilder.path("/api/user/users");
					uriBuilder.queryParams(requestParams);
					return uriBuilder.build();
				}).headers(header -> HeaderGenerator.generateHttpHeaders(
						GenerateHttpHeader.builder().header(header).requestId(1L).tenantId(1L).build())).exchange()
				.expectHeader().contentType(MediaType.APPLICATION_JSON).expectStatus().isOk().expectBody(Paging.class)
				.consumeWith(response -> {
					final var userResponse = Optional.ofNullable(response.getResponseBody()).map(Paging::getContent)
							.map(List::getFirst).map(it -> objectMapper.convertValue(it, UserResponse.class))
							.map(UserResponse.class::cast).orElse(null);
					assertNotNull(userResponse);
					assertEquals(user.getId().getValue().toString(), userResponse.getId());
					assertEquals(user.getSsoId(), userResponse.getSsoId());
					assertEquals(user.getUserType(), userResponse.getUserType());
					assertEquals(user.getEmail(), userResponse.getEmail());
					assertEquals(user.getFirstName(), userResponse.getFirstName());
					assertEquals(user.getLastName(), userResponse.getLastName());
					Assertions.assertEquals(
							UserMappingFunctionSupport.buildFullName(user.getFirstName(), user.getLastName()),
							userResponse.getFullName());
					assertEquals(user.getPhoneNumber(), userResponse.getPhoneNumber());
					assertEquals(user.getRealmId(), userResponse.getRealmId());
					assertEquals(user.getUserNonCompletedActions(), userResponse.getUserNonCompletedActions());
					assertEquals(user.getTotalSubAccounts(), userResponse.getTotalSubAccounts());
					assertEquals(user.getTotalSponsors(), userResponse.getTotalSponsors());
					assertFalse(userResponse.getIsAlternativeUser());

				});
	}

	@Test
	void testQueryUsersByUserGroupPathWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		when(userRoleClient.getUserRole(1L, null, 1L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("1").id("1").build());

		when(tenantClient.getTenant(1)).thenReturn(ResponseEntity.ok(
				TenantResponse.builder().tenantId(1L).realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID).build()));

		when(userRoleClient.getUserRole(null, DefaultSystemRole.CUSTOMER_ROLE.getExternalId(), 1L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("1").id("2").build());

		final var school = groupCommandService.upsertUserGroup(
				UpsertUserGroupCommand.builder().tenantId(new TenantId(1L)).groupName("School A")
						.description("School A").build());

		final var grade = groupCommandService.upsertUserGroup(
				UpsertUserGroupCommand.builder().tenantId(new TenantId(1L))
						.groupName("Grade B")
						.migrationId("grade-b-migration-id-78987")
						.description("Grade B")
						.parentId(school.getId()).build());

		final var clazz1 = groupCommandService.upsertUserGroup(
				UpsertUserGroupCommand.builder().tenantId(new TenantId(1L)).groupName("Class B11")
						.description("Class B")
						.migrationId("class-b-migration-id-78987")
						.parentId(grade.getId()).build());

		initializeNewUser(UpsertTenantUserCommand.builder().userRoleId(new UserRoleId(1L)).tenantId(new TenantId(1L))
				.permissionStatus(UserPermissionStatus.CREATED).userStatus(UserStatus.CREATED)
				.userGroupId(school.getId()).userType(UserType.CUSTOMER).email("<EMAIL>")
				.firstName("first name").lastName("last name").phoneNumber("**********").externalId("externalId1")
				.realmId("customer-x-001").build());

		final var user2 = initializeNewUser(
				UpsertTenantUserCommand.builder().userRoleId(new UserRoleId(1L)).tenantId(new TenantId(1L))
						.permissionStatus(UserPermissionStatus.CREATED).userStatus(UserStatus.CREATED)
						.userGroupId(grade.getId()).userType(UserType.CUSTOMER).email("<EMAIL>")
						.firstName("first name").lastName("last name").phoneNumber("**********")
						.externalId("externalId1").realmId("customer-x-001").build());

		final var user3 = initializeNewUser(
				UpsertTenantUserCommand.builder().userRoleId(new UserRoleId(1L)).tenantId(new TenantId(1L))
						.permissionStatus(UserPermissionStatus.CREATED).userStatus(UserStatus.CREATED)
						.userGroupId(clazz1.getId()).userType(UserType.CUSTOMER).email("<EMAIL>")
						.firstName("first name").lastName("last name").phoneNumber("**********")
						.externalId("externalId1").realmId("customer-x-001").build());

		final var users = List.of(user2, user3);
		final var groups = List.of(grade, clazz1);

		final var request = QueryUserPaginationRequest.builder()
				.filter(QueryUserRequest.builder().byUserGroupPath(grade.getPath()).byTenantId(1L).build()).size(10)
				.page(0).sortDirection("ASC").sortFields(List.of("id")).build();

		final Map<String, String> map = new HashMap<>();

		map.put("filter.byUserGroupPath", request.getFilter().getByUserGroupPath());

		map.put("filter.byTenantId",
				Optional.ofNullable(request.getFilter()).map(QueryUserRequest::getByTenantId).map(Object::toString)
						.orElse(null));

		map.put("size", String.valueOf(request.getSize()));
		map.put("page", String.valueOf(request.getPage()));
		map.put("sortDirection", request.getSortDirection());
		map.put("sortFields", request.getSortFields().stream().findFirst().orElse(null));

		final LinkedMultiValueMap<String, String> requestParams = new LinkedMultiValueMap<>(
				map.entrySet().stream().filter(it -> StringUtils.isNotBlank(it.getValue()))
						.collect(Collectors.toMap(Map.Entry::getKey, it -> List.of(it.getValue()))));

		// Act & Assert
		webClient.get().uri(uriBuilder -> {
					uriBuilder.path("/api/user/users");
					uriBuilder.queryParams(requestParams);
					return uriBuilder.build();
				}).headers(header -> HeaderGenerator.generateHttpHeaders(
						GenerateHttpHeader.builder().header(header).requestId(1L).tenantId(1L).build())).exchange()
				.expectHeader().contentType(MediaType.APPLICATION_JSON).expectStatus().isOk().expectBody(Paging.class)
				.consumeWith(response -> {
					final List<UserResponse> userResponses = Optional.ofNullable(response.getResponseBody())
							.map(Paging::getContent).orElseGet(List::of).stream()
							.map(it -> objectMapper.convertValue(it, UserResponse.class)).toList();

					Assertions.assertEquals(2, userResponses.size());

					IntStream.range(0, userResponses.size()).forEachOrdered(index -> {
						final var user = users.get(index);
						final var group = groups.get(index);
						final var actualUserResponse = userResponses.get(index);

						assertEquals(user.getId().getValue().toString(), actualUserResponse.getId());
						assertEquals(user.getSsoId(), actualUserResponse.getSsoId());
						assertEquals(user.getUserType(), actualUserResponse.getUserType());
						assertEquals(user.getEmail(), actualUserResponse.getEmail());
						assertEquals(user.getFirstName(), actualUserResponse.getFirstName());
						assertEquals(user.getLastName(), actualUserResponse.getLastName());
						Assertions.assertEquals(
								UserMappingFunctionSupport.buildFullName(user.getFirstName(), user.getLastName()),
								actualUserResponse.getFullName());

						assertEquals(user.getPhoneNumber(), actualUserResponse.getPhoneNumber());
						assertEquals(user.getRealmId(), actualUserResponse.getRealmId());
						assertEquals(user.getUserNonCompletedActions(),
								actualUserResponse.getUserNonCompletedActions());
						assertEquals(user.getTotalSubAccounts(), actualUserResponse.getTotalSubAccounts());
						assertEquals(user.getTotalSponsors(), actualUserResponse.getTotalSponsors());
						assertEquals(user.getUserGroup().getId().getValue().toString(), actualUserResponse.getUserGroup().getId());
						assertEquals(user.getUserGroup().getGroupName(), actualUserResponse.getUserGroup().getGroupName());
						assertEquals(user.getUserGroup().getMigrationId(), actualUserResponse.getUserGroup().getMigrationId());
						assertFalse(user.getIsAlternativeUser());
					});
				});
	}

	@Test
	void testQueryUsersByUserGroupPathAndChildrenLevelWhenValidRequestThenReturnSuccessfully() {
		// Arrange

		when(userRoleClient.getUserRole(null, DefaultSystemRole.CUSTOMER_ROLE.getExternalId(), 1L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("1").id("2").build());

		when(userRoleClient.getUserRole(1L, null, 1L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("1").id("1").build());

		when(tenantClient.getTenant(1)).thenReturn(ResponseEntity.ok(
				TenantResponse.builder().tenantId(1L).realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID).build()));

		final var school = groupCommandService.upsertUserGroup(
				UpsertUserGroupCommand.builder().tenantId(new TenantId(1L)).groupName("School A")
						.description("School A").build());

		final var grade = groupCommandService.upsertUserGroup(
				UpsertUserGroupCommand.builder().tenantId(new TenantId(1L)).groupName("Grade B").description("Grade B")
						.parentId(school.getId()).build());

		final var clazz2 = groupCommandService.upsertUserGroup(
				UpsertUserGroupCommand.builder().tenantId(new TenantId(1L)).groupName("Class B22")
						.description("Class B").parentId(grade.getId()).build());

		final var user1 = initializeNewUser(
				UpsertTenantUserCommand.builder().userRoleId(new UserRoleId(1L)).tenantId(new TenantId(1L))
						.permissionStatus(UserPermissionStatus.CREATED).userStatus(UserStatus.CREATED)
						.userGroupId(grade.getId()).userType(UserType.CUSTOMER).email("<EMAIL>")
						.firstName("first name").lastName("last name").phoneNumber("**********")
						.externalId("externalId1").realmId("customer-x-001").build());

		initializeNewUser(UpsertTenantUserCommand.builder().userRoleId(new UserRoleId(1L)).tenantId(new TenantId(1L))
				.permissionStatus(UserPermissionStatus.CREATED).userStatus(UserStatus.CREATED)
				.userGroupId(clazz2.getId()).userType(UserType.CUSTOMER).email("<EMAIL>")
				.firstName("first name").lastName("last name").phoneNumber("**********").externalId("externalId1")
				.realmId("customer-x-001").build());

		final var request = QueryUserPaginationRequest.builder()
				.filter(QueryUserRequest.builder().byUserGroupPath(grade.getPath()).groupLevel(0).byTenantId(1L)
						.build()).size(10).page(0).sortDirection("ASC").sortFields(List.of("id")).build();

		final Map<String, String> map = new HashMap<>();

		map.put("filter.byUserGroupPath", request.getFilter().getByUserGroupPath());
		map.put("filter.groupLevel", request.getFilter().getGroupLevel().toString());

		map.put("filter.byTenantId",
				Optional.ofNullable(request.getFilter()).map(QueryUserRequest::getByTenantId).map(Object::toString)
						.orElse(null));

		map.put("size", String.valueOf(request.getSize()));
		map.put("page", String.valueOf(request.getPage()));
		map.put("sortDirection", request.getSortDirection());
		map.put("sortFields", request.getSortFields().stream().findFirst().orElse(null));

		final LinkedMultiValueMap<String, String> requestParams = new LinkedMultiValueMap<>(
				map.entrySet().stream().filter(it -> StringUtils.isNotBlank(it.getValue()))
						.collect(Collectors.toMap(Map.Entry::getKey, it -> List.of(it.getValue()))));

		// Act & Assert
		webClient.get().uri(uriBuilder -> {
					uriBuilder.path("/api/user/users");
					uriBuilder.queryParams(requestParams);
					return uriBuilder.build();
				}).headers(header -> HeaderGenerator.generateHttpHeaders(
						GenerateHttpHeader.builder().header(header).requestId(1L).tenantId(1L).build())).exchange()
				.expectHeader().contentType(MediaType.APPLICATION_JSON).expectStatus().isOk().expectBody(Paging.class)
				.consumeWith(response -> {
					final List<UserResponse> userResponses = Optional.ofNullable(response.getResponseBody())
							.map(Paging::getContent).orElseGet(List::of).stream()
							.map(it -> objectMapper.convertValue(it, UserResponse.class)).toList();

					Assertions.assertEquals(1, userResponses.size());

					final var actualUserResponse = userResponses.getFirst();

					assertEquals(user1.getId().getValue().toString(), actualUserResponse.getId());
					assertEquals(user1.getSsoId(), actualUserResponse.getSsoId());
					assertEquals(user1.getUserType(), actualUserResponse.getUserType());
					assertEquals(user1.getEmail(), actualUserResponse.getEmail());
					assertEquals(user1.getFirstName(), actualUserResponse.getFirstName());
					assertEquals(user1.getLastName(), actualUserResponse.getLastName());
					Assertions.assertEquals(
							UserMappingFunctionSupport.buildFullName(user1.getFirstName(), user1.getLastName()),
							actualUserResponse.getFullName());
					assertEquals(user1.getPhoneNumber(), actualUserResponse.getPhoneNumber());
					assertEquals(user1.getRealmId(), actualUserResponse.getRealmId());
					assertEquals(user1.getUserNonCompletedActions(), actualUserResponse.getUserNonCompletedActions());
					assertEquals(user1.getTotalSubAccounts(), actualUserResponse.getTotalSubAccounts());
					assertEquals(user1.getTotalSponsors(), actualUserResponse.getTotalSponsors());
					;
				});
	}

	@Test
	void testQueryUsersOrderByCreatedAtDescWhenValidRequestThenReturnSuccessfully() {
		// Arrange

		when(userRoleClient.getUserRole(1003L, null, 1003L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("1003").id("1003").build());

		when(tenantClient.getTenant(1)).thenReturn(ResponseEntity.ok(
				TenantResponse.builder().tenantId(1L).realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID).build()));

		final var user0 = initializeNewUser(
				UpsertTenantUserCommand.builder().userRoleId(new UserRoleId(1003L)).tenantId(new TenantId(1003L))
						.permissionStatus(UserPermissionStatus.CREATED).userStatus(UserStatus.CREATED)
						.userType(UserType.BACK_OFFICE).email("<EMAIL>").firstName("100 first name")
						.lastName("100 last name").phoneNumber("**********").externalId("externalId1-1003").build());

		final var user1 = initializeNewUser(
				UpsertTenantUserCommand.builder().userRoleId(new UserRoleId(3L)).tenantId(new TenantId(1003L))
						.permissionStatus(UserPermissionStatus.CREATED).userStatus(UserStatus.CREATED)
						.userType(UserType.BACK_OFFICE).email("<EMAIL>").firstName("200 first name")
						.lastName("200 last name").phoneNumber("**********").externalId("externalId1-1002").build());

		final var user2 = initializeNewUser(
				UpsertTenantUserCommand.builder().userRoleId(new UserRoleId(3L)).tenantId(new TenantId(1003L))
						.permissionStatus(UserPermissionStatus.CREATED).userStatus(UserStatus.CREATED)
						.userType(UserType.BACK_OFFICE).email("<EMAIL>").firstName("300 first name")
						.lastName("300 last name").phoneNumber("**********").externalId("externalId-1003300").build());

		final var user3 = initializeNewUser(
				UpsertTenantUserCommand.builder().userRoleId(new UserRoleId(3L)).tenantId(new TenantId(1003L))
						.permissionStatus(UserPermissionStatus.CREATED).userStatus(UserStatus.CREATED)
						.userType(UserType.BACK_OFFICE).email("<EMAIL>").firstName("aaa first name")
						.lastName("aaa last name").phoneNumber("**********").externalId("externalId-1003400").build());

		final var request = QueryUserPaginationRequest.builder()
				.filter(QueryUserRequest.builder().byTenantId(1003L).byUserTypes(Set.of(UserType.BACK_OFFICE)).build())
				.size(10).page(0).sortDirection("DESC").sortFields(List.of("createdAt")).build();

		final Map<String, String> map = new HashMap<>();

		map.put("filter.byUserTypes", Optional.ofNullable(request.getFilter().getByUserTypes())
				.flatMap(it -> it.stream().findFirst().map(UserType::toString)).orElse(null));
		map.put("filter.byTenantId",
				Optional.ofNullable(request.getFilter()).map(QueryUserRequest::getByTenantId).map(Object::toString)
						.orElse(null));

		map.put("size", String.valueOf(request.getSize()));
		map.put("page", String.valueOf(request.getPage()));
		map.put("sortDirection", request.getSortDirection());
		map.put("sortFields", request.getSortFields().stream().findFirst().orElse(null));

		final LinkedMultiValueMap<String, String> requestParams = new LinkedMultiValueMap<>(
				map.entrySet().stream().filter(it -> StringUtils.isNotBlank(it.getValue()))
						.collect(Collectors.toMap(Map.Entry::getKey, it -> List.of(it.getValue()))));

		// Act & Assert
		webClient.get().uri(uriBuilder -> {
					uriBuilder.path("/api/user/users");
					uriBuilder.queryParams(requestParams);
					return uriBuilder.build();
				}).headers(header -> HeaderGenerator.generateHttpHeaders(
						GenerateHttpHeader.builder().header(header).requestId(1L).tenantId(1L).build())).exchange()
				.expectHeader().contentType(MediaType.APPLICATION_JSON).expectStatus().isOk().expectBody(Paging.class)
				.consumeWith(response -> {
					final List<UserResponse> userResponse = Optional.ofNullable(response.getResponseBody())
							.map(Paging::getContent).orElseGet(List::of).stream()
							.map(it -> objectMapper.convertValue(it, UserResponse.class)).toList();

					assertEquals(4, userResponse.size());
					assertEquals(user3.getId().getValue().toString(), userResponse.get(0).getId());
					assertEquals(user2.getId().getValue().toString(), userResponse.get(1).getId());
					assertEquals(user1.getId().getValue().toString(), userResponse.get(2).getId());
					assertEquals(user0.getId().getValue().toString(), userResponse.get(3).getId());
				});
	}

	@Test
	void testQueryUsersWithSubAccountCountDescWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		when(userRoleClient.getUserRole(null, DefaultSystemRole.CUSTOMER_ROLE.getExternalId(), 100123L, true)).thenReturn(
				UserRoleResponse.builder().tenantId("100123").id("123").build());

		final var user1 = userSupporter.activateUser("customer-***********", initializeNewUser(
				UpsertTenantUserCommand.builder().userRoleId(new UserRoleId(1L)).tenantId(new TenantId(100123L))
						.userType(UserType.CUSTOMER).email("<EMAIL>").firstName("first name")
						.lastName("last name").phoneNumber("**********").realmId("customer-realm-123").build()));

		final var user2 = initializeNewUser(
				UpsertTenantUserCommand.builder().userRoleId(new UserRoleId(1L)).tenantId(new TenantId(100123L))
						.userType(UserType.CUSTOMER).email("<EMAIL>").firstName("first name")
						.lastName("last name").phoneNumber("**********").realmId("customer-realm-123").build());
		userSupporter.activateUser("customer-29002", user2);

		final var subAccount1 = subAccountCommandService.createSubAccount(
				CreateSubAccountNewUserCommand.builder().userTypeCreator(UserType.CUSTOMER).sponsorUserId(user1.getId())
						.firstName("first name 1").lastName("last name 1").phoneNumber("**********")
						.tenantId(new TenantId(100123L)).build());

		subAccountCommandService.createSubAccount(
				CreateSubAccountExistingUserCommand.builder().userTypeCreator(UserType.CUSTOMER)
						.sponsorUserId(user1.getId()).tenantId(new TenantId(100123L)).email(user2.getEmail()).build());

		final var users = List.of(user1, user2, subAccount1.getSubUser());

		final var request = QueryUserPaginationRequest.builder()
				.filter(QueryUserRequest.builder().byTenantId(100123L).byUserTypes(Set.of(UserType.CUSTOMER)).build())
				.size(10).page(0).sortDirection("DESC").sortFields(List.of("createdAt")).build();

		final Map<String, String> map = new HashMap<>();

		map.put("filter.byUserTypes", Optional.ofNullable(request.getFilter().getByUserTypes())
				.flatMap(it -> it.stream().findFirst().map(UserType::toString)).orElse(null));
		map.put("filter.byTenantId",
				Optional.ofNullable(request.getFilter()).map(QueryUserRequest::getByTenantId).map(Object::toString)
						.orElse(null));

		map.put("size", String.valueOf(request.getSize()));
		map.put("page", String.valueOf(request.getPage()));
		map.put("sortDirection", request.getSortDirection());
		map.put("sortFields", request.getSortFields().stream().findFirst().orElse(null));

		final LinkedMultiValueMap<String, String> requestParams = new LinkedMultiValueMap<>(
				map.entrySet().stream().filter(it -> StringUtils.isNotBlank(it.getValue()))
						.collect(Collectors.toMap(Map.Entry::getKey, it -> List.of(it.getValue()))));

		// Act & Assert
		webClient.get().uri(uriBuilder -> {
					uriBuilder.path("/api/user/users");
					uriBuilder.queryParams(requestParams);
					return uriBuilder.build();
				}).headers(header -> HeaderGenerator.generateHttpHeaders(
						GenerateHttpHeader.builder().header(header).requestId(1L).tenantId(100123L).build())).exchange()
				.expectHeader().contentType(MediaType.APPLICATION_JSON).expectStatus().isOk().expectBody(Paging.class)
				.consumeWith(response -> {
					final List<UserResponse> userResponses = Optional.ofNullable(response.getResponseBody())
							.map(Paging::getContent).orElseGet(List::of).stream()
							.map(it -> objectMapper.convertValue(it, UserResponse.class)).toList();

					assertEquals(3, userResponses.size());

					IntStream.range(0, userResponses.size()).forEachOrdered(index -> {
						final var actualUser = userResponses.get(index);
						final var user = users.stream()
								.filter(it -> it.getId().getValue().toString().equals(actualUser.getId())).findFirst()
								.orElse(null);
						assertNotNull(user);
						if (user1.getId().equals(user.getId())) {
							assertEquals(2, actualUser.getTotalSubAccounts());
						} else {
							assertEquals(1, actualUser.getTotalSponsors());
						}

						assertEquals(user.getId().getValue().toString(), actualUser.getId());
						assertEquals(user.getUserType(), actualUser.getUserType());
						assertEquals(user.getEmail(), actualUser.getEmail());
						assertEquals(user.getFirstName(), actualUser.getFirstName());
						Assertions.assertEquals(
								UserMappingFunctionSupport.buildFullName(user.getFirstName(), user.getLastName()),
								actualUser.getFullName());
						assertEquals(user.getLastName(), actualUser.getLastName());
						assertEquals(user.getPhoneNumber(), actualUser.getPhoneNumber());
						assertEquals(user.getRealmId(), actualUser.getRealmId());
					});
				});
	}

	@Test
	void testChangeRoleByBackOfficeUserWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var tenantId = new TenantId(1L);
		final var currentRoleId = new UserRoleId(1L);
		final var newRoleId = new UserRoleId(2L);

		when(userRoleClient.getUserRole(currentRoleId.getValue(), null, tenantId.getValue(), true)).thenReturn(
				UserRoleResponse.builder()
						.tenantId(tenantId.getValue()
								.toString())
						.id(currentRoleId.getValue()
								.toString())
						.build());
		when(userRoleClient.getUserRole(newRoleId.getValue(), null, tenantId.getValue(), true)).thenReturn(
				UserRoleResponse.builder()
						.tenantId(tenantId.getValue()
								.toString())
						.id(newRoleId.getValue()
								.toString())
						.build());

		when(tenantClient.getTenant(tenantId.getValue())).thenReturn(ResponseEntity.ok(TenantResponse.builder()
				.tenantId(tenantId.getValue())
				.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
				.build()));

		final var user = initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(currentRoleId)
				.tenantId(tenantId)
				.permissionStatus(UserPermissionStatus.CREATED)
				.userStatus(UserStatus.CREATED)
				.userType(UserType.BACK_OFFICE)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.externalId("externalId3000")
				.build());

		final var targetUser = initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(currentRoleId)
				.tenantId(tenantId)
				.permissionStatus(UserPermissionStatus.CREATED)
				.userStatus(UserStatus.CREATED)
				.userType(UserType.BACK_OFFICE)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.externalId("externalId3001")
				.build());

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId(user.getId()
						.getValue()
						.toString())
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId(tenantId.getValue()
								.toString())
						.userRoleId(currentRoleId.getValue()
								.toString())
						.build()))
				.userType(UserType.BACK_OFFICE)
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.build();

		// Act & Assert
		webClient.put()
				.uri("/api/user/users/{userId}/roles/{userRoleId}/change", targetUser.getId()
						.getValue(), newRoleId.getValue()
								.toString())
				.contentType(MediaType.APPLICATION_JSON)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {
					final var userResponse = Optional.ofNullable(response.getResponseBody())
							.map(UserResponse.class::cast)
							.orElse(null);
					assertNotNull(userResponse);
					assertEquals(targetUser.getId()
							.getValue()
							.toString(), userResponse.getId());
					assertEquals(targetUser.getSsoId(), userResponse.getSsoId());
					assertEquals(targetUser.getUserType(), userResponse.getUserType());
					assertEquals(targetUser.getEmail(), userResponse.getEmail());
					assertEquals(targetUser.getFirstName(), userResponse.getFirstName());
					assertEquals(targetUser.getLastName(), userResponse.getLastName());
					Assertions.assertEquals(UserMappingFunctionSupport.buildFullName(targetUser.getFirstName(),
							targetUser.getLastName()), userResponse.getFullName());
					assertEquals(targetUser.getPhoneNumber(), userResponse.getPhoneNumber());
					assertEquals(targetUser.getRealmId(), userResponse.getRealmId());
					assertEquals(targetUser.getUserNonCompletedActions(), userResponse.getUserNonCompletedActions());
					assertEquals(targetUser.getTotalSubAccounts(), userResponse.getTotalSubAccounts());
					assertEquals(targetUser.getTotalSponsors(), userResponse.getTotalSponsors());

					assertEquals(1, userResponse.getPermissions()
							.stream()
							.filter(it -> tenantId.getValue()
									.toString()
									.equals(it.tenantId()) && newRoleId.getValue()
											.toString()
											.equals(it.userRoleId()))
							.toList()
							.size());

				});

	}

	@Test
	void testChangeOwnerRoleByAdminUserWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var tenantId = new TenantId(1L);
		final var currentRoleId = new UserRoleId(1L);
		final var newRoleId = new UserRoleId(2L);

		when(userRoleClient.getUserRole(currentRoleId.getValue(), DefaultSystemRole.TENANT_OWNER_ROLE.getExternalId(),
				tenantId.getValue(), true)).thenReturn(UserRoleResponse.builder()
						.tenantId(tenantId.getValue()
								.toString())
						.id(currentRoleId.getValue()
								.toString())
						.build());

		when(userRoleClient.getUserRole(newRoleId.getValue(), null, tenantId.getValue(), true)).thenReturn(
				UserRoleResponse.builder()
						.tenantId(tenantId.getValue()
								.toString())
						.id(newRoleId.getValue()
								.toString())
						.build());

		when(tenantClient.getTenant(CommonDomainConstants.SYSTEM_TENANT_ID.getValue())).thenReturn(ResponseEntity.ok(
				TenantResponse.builder()
						.tenantId(CommonDomainConstants.SYSTEM_TENANT_ID.getValue())
						.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
						.build()));

		when(tenantClient.getTenant(tenantId.getValue())).thenReturn(ResponseEntity.ok(TenantResponse.builder()
				.tenantId(tenantId.getValue())
				.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
				.build()));

		final var user = initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(currentRoleId)
				.tenantId(tenantId)
				.permissionStatus(UserPermissionStatus.CREATED)
				.userStatus(UserStatus.CREATED)
				.userType(UserType.SYSTEM_ADMIN)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.externalId("externalId1")
				.build());

		final var targetUser = initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(currentRoleId)
				.tenantId(tenantId)
				.permissionStatus(UserPermissionStatus.CREATED)
				.userStatus(UserStatus.CREATED)
				.userType(UserType.BACK_OFFICE)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.externalId("externalId1")
				.build());

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId(user.getId()
						.getValue()
						.toString())
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId(tenantId.getValue()
								.toString())
						.userRoleId(currentRoleId.getValue()
								.toString())
						.build()))
				.userType(UserType.BACK_OFFICE)
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.build();

		// Act & Assert
		webClient.put()
				.uri("/api/user/users/{userId}/roles/{userRoleId}/change", targetUser.getId()
						.getValue(), newRoleId.getValue()
								.toString())
				.contentType(MediaType.APPLICATION_JSON)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {
					final var userResponse = Optional.ofNullable(response.getResponseBody())
							.map(UserResponse.class::cast)
							.orElse(null);
					assertNotNull(userResponse);
					assertEquals(targetUser.getId()
							.getValue()
							.toString(), userResponse.getId());
					assertEquals(targetUser.getSsoId(), userResponse.getSsoId());
					assertEquals(targetUser.getUserType(), userResponse.getUserType());
					assertEquals(targetUser.getEmail(), userResponse.getEmail());
					assertEquals(targetUser.getFirstName(), userResponse.getFirstName());
					assertEquals(targetUser.getLastName(), userResponse.getLastName());
					Assertions.assertEquals(UserMappingFunctionSupport.buildFullName(targetUser.getFirstName(),
							targetUser.getLastName()), userResponse.getFullName());

					assertEquals(targetUser.getPhoneNumber(), userResponse.getPhoneNumber());
					assertEquals(targetUser.getRealmId(), userResponse.getRealmId());
					assertEquals(targetUser.getUserNonCompletedActions(), userResponse.getUserNonCompletedActions());
					assertEquals(targetUser.getTotalSubAccounts(), userResponse.getTotalSubAccounts());
					assertEquals(targetUser.getTotalSponsors(), userResponse.getTotalSponsors());

					assertEquals(1, userResponse.getPermissions()
							.stream()
							.filter(it -> tenantId.getValue()
									.toString()
									.equals(it.tenantId()) && newRoleId.getValue()
											.toString()
											.equals(it.userRoleId()))
							.toList()
							.size());

				});

	}

	@Test
	void throwExceptionWhenChangeOwnerRoleToAdminWhenHasBeenExistingOwner() {
		// Arrange
		final var tenantId = new TenantId(1L);
		final var adminRoleId = new UserRoleId(3L);
		final var ownerRoleId = new UserRoleId(54545L);
		final var currentRoleId = new UserRoleId(5L);

		when(userRoleClient.getUserRole(adminRoleId.getValue(), DefaultSystemRole.SYSTEM_ADMIN_ROLE.getExternalId(),
				tenantId.getValue(), true)).thenReturn(UserRoleResponse.builder()
						.tenantId(tenantId.getValue()
								.toString())
						.externalId(DefaultSystemRole.SYSTEM_ADMIN_ROLE.getExternalId())
						.id(adminRoleId.getValue()
								.toString())
						.build());

		when(tenantClient.getTenant(CommonDomainConstants.SYSTEM_TENANT_ID.getValue())).thenReturn(ResponseEntity.ok(
				TenantResponse.builder()
						.tenantId(CommonDomainConstants.SYSTEM_TENANT_ID.getValue())
						.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
						.build()));

		when(tenantClient.getTenant(tenantId.getValue())).thenReturn(ResponseEntity.ok(TenantResponse.builder()
				.tenantId(tenantId.getValue())
				.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
				.build()));

		final var user = initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(adminRoleId)
				.tenantId(tenantId)
				.permissionStatus(UserPermissionStatus.CREATED)
				.userStatus(UserStatus.CREATED)
				.userType(UserType.SYSTEM_ADMIN)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.externalId("externalId-admin-5000")
				.build());

		initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(ownerRoleId)
				.tenantId(tenantId)
				.permissionStatus(UserPermissionStatus.CREATED)
				.userStatus(UserStatus.CREATED)
				.userType(UserType.BACK_OFFICE)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.externalId("externalId-backoffice-4002")
				.build());

		when(userRoleClient.getUserRole(ownerRoleId.getValue(), null, tenantId.getValue(), true)).thenReturn(
				UserRoleResponse.builder()
						.tenantId(tenantId.getValue()
								.toString())
						.externalId(DefaultSystemRole.TENANT_OWNER_ROLE.getExternalId())
						.id(ownerRoleId.getValue()
								.toString())
						.build());

		final var targetUser = initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(currentRoleId)
				.tenantId(tenantId)
				.permissionStatus(UserPermissionStatus.CREATED)
				.userStatus(UserStatus.CREATED)
				.userType(UserType.BACK_OFFICE)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.externalId("externalId-backoffice-6001")
				.build());

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId(user.getId()
						.getValue()
						.toString())
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId(tenantId.getValue()
								.toString())
						.userRoleId(currentRoleId.getValue()
								.toString())
						.build()))
				.userType(UserType.SYSTEM_ADMIN)
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.build();

		// Act & Assert
		webClient.put()
				.uri("/api/user/users/{userId}/roles/{userRoleId}/change", targetUser.getId()
						.getValue(), ownerRoleId.getValue()
								.toString())
				.contentType(MediaType.APPLICATION_JSON)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isBadRequest()
				.expectBody(ErrorResponse.class)
				.consumeWith(response -> {
					final var error = response.getResponseBody();
					assertNotNull(error);
					assertTrue(error.getDetails()
							.contains("Owner has been existed. Please un-assign Tenant Owner role first."));
				});

	}

	@Test
	void testChangeOwnerRoleByAdminUserToAnotherUserSuccessfully() {
		// Arrange
		final var tenantId = new TenantId(1235L);
		final var adminRoleId = new UserRoleId(12350L);
		final var ownerRoleId = new UserRoleId(12351L);
		final var backOfficeRoleId = new UserRoleId(12352L);

		when(userRoleClient.getUserRole(adminRoleId.getValue(), DefaultSystemRole.SYSTEM_ADMIN_ROLE.getExternalId(),
				tenantId.getValue(), true)).thenReturn(UserRoleResponse.builder()
						.tenantId(tenantId.getValue()
								.toString())
						.externalId(DefaultSystemRole.SYSTEM_ADMIN_ROLE.getExternalId())
						.id(adminRoleId.getValue()
								.toString())
						.build());

		when(tenantClient.getTenant(CommonDomainConstants.SYSTEM_TENANT_ID.getValue())).thenReturn(ResponseEntity.ok(
				TenantResponse.builder()
						.tenantId(CommonDomainConstants.SYSTEM_TENANT_ID.getValue())
						.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
						.build()));

		when(tenantClient.getTenant(tenantId.getValue())).thenReturn(ResponseEntity.ok(TenantResponse.builder()
				.tenantId(tenantId.getValue())
				.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
				.build()));

		final var user = initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(adminRoleId)
				.tenantId(tenantId)
				.permissionStatus(UserPermissionStatus.CREATED)
				.userStatus(UserStatus.CREATED)
				.userType(UserType.SYSTEM_ADMIN)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.externalId("externalId-admin-1235-5000")
				.build());

		final var adminUser = initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(ownerRoleId)
				.tenantId(tenantId)
				.permissionStatus(UserPermissionStatus.CREATED)
				.userStatus(UserStatus.CREATED)
				.userType(UserType.BACK_OFFICE)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.externalId("externalId-backoffice-1235-4002")
				.build());

		final var targetUser = initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(backOfficeRoleId)
				.tenantId(tenantId)
				.permissionStatus(UserPermissionStatus.CREATED)
				.userStatus(UserStatus.CREATED)
				.userType(UserType.BACK_OFFICE)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.externalId("externalId-backoffice-1235-6001")
				.build());

		when(userRoleClient.getUserRole(ownerRoleId.getValue(), null, tenantId.getValue(), true)).thenReturn(
				UserRoleResponse.builder()
						.tenantId(tenantId.getValue()
								.toString())
						.externalId(DefaultSystemRole.TENANT_OWNER_ROLE.getExternalId())
						.id(ownerRoleId.getValue()
								.toString())
						.build());

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId(user.getId()
						.getValue()
						.toString())
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId(tenantId.getValue()
								.toString())
						.userRoleId(backOfficeRoleId.getValue()
								.toString())
						.build()))
				.userType(UserType.SYSTEM_ADMIN)
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.build();

		userCommandService.changeUserRole(tenantId, adminUser.getId(), backOfficeRoleId, userTokenClaim);

		// Act & Assert
		webClient.put()
				.uri("/api/user/users/{userId}/roles/{userRoleId}/change", targetUser.getId()
						.getValue(), ownerRoleId.getValue()
								.toString())
				.contentType(MediaType.APPLICATION_JSON)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {
					final var userResponse = Optional.ofNullable(response.getResponseBody())
							.map(UserResponse.class::cast)
							.orElse(null);
					assertNotNull(userResponse);
					assertEquals(targetUser.getId()
							.getValue()
							.toString(), userResponse.getId());
					assertEquals(targetUser.getSsoId(), userResponse.getSsoId());
					assertEquals(targetUser.getUserType(), userResponse.getUserType());
					assertEquals(targetUser.getEmail(), userResponse.getEmail());
					assertEquals(targetUser.getFirstName(), userResponse.getFirstName());
					assertEquals(targetUser.getLastName(), userResponse.getLastName());
					Assertions.assertEquals(UserMappingFunctionSupport.buildFullName(targetUser.getFirstName(),
							targetUser.getLastName()), userResponse.getFullName());
					assertEquals(targetUser.getPhoneNumber(), userResponse.getPhoneNumber());
					assertEquals(targetUser.getRealmId(), userResponse.getRealmId());
					assertEquals(targetUser.getUserNonCompletedActions(), userResponse.getUserNonCompletedActions());
					assertEquals(targetUser.getTotalSubAccounts(), userResponse.getTotalSubAccounts());
					assertEquals(targetUser.getTotalSponsors(), userResponse.getTotalSponsors());

					assertEquals(1, userResponse.getPermissions()
							.stream()
							.filter(it -> tenantId.getValue()
									.toString()
									.equals(it.tenantId()) && ownerRoleId.getValue()
											.toString()
											.equals(it.userRoleId()))
							.toList()
							.size());

				});

	}

	@Test
	void testChangeDeletedRoleToNewRoleSuccessfully() {
		// Arrange
		final var tenantId = new TenantId(2025041100L);
		final var adminRoleId = new UserRoleId(20250411001L);
		final var ownerRoleId = new UserRoleId(20250411002L);
		final var backOfficeRoleId = new UserRoleId(20250411003L);

		when(userRoleClient.getUserRole(adminRoleId.getValue(), DefaultSystemRole.SYSTEM_ADMIN_ROLE.getExternalId(),
				tenantId.getValue(), true)).thenReturn(UserRoleResponse.builder()
						.tenantId(tenantId.getValue()
								.toString())
						.externalId(DefaultSystemRole.SYSTEM_ADMIN_ROLE.getExternalId())
						.id(adminRoleId.getValue()
								.toString())
						.build());

		when(tenantClient.getTenant(CommonDomainConstants.SYSTEM_TENANT_ID.getValue())).thenReturn(ResponseEntity.ok(
				TenantResponse.builder()
						.tenantId(CommonDomainConstants.SYSTEM_TENANT_ID.getValue())
						.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
						.build()));

		when(tenantClient.getTenant(tenantId.getValue())).thenReturn(ResponseEntity.ok(TenantResponse.builder()
				.tenantId(tenantId.getValue())
				.realmId(tenantId.getValue() + "realm")
				.build()));

		when(userRoleClient.getUserRole(ownerRoleId.getValue(), null, tenantId.getValue(), true)).thenReturn(
				UserRoleResponse.builder()
						.tenantId(tenantId.getValue()
								.toString())
						.externalId(DefaultSystemRole.TENANT_OWNER_ROLE.getExternalId())
						.id(ownerRoleId.getValue()
								.toString())
						.build());

		when(userRoleClient.getUserRole(backOfficeRoleId.getValue(), null, tenantId.getValue(), true)).thenReturn(
				UserRoleResponse.builder()
						.tenantId(tenantId.getValue()
								.toString())
						.externalId(DefaultSystemRole.TENANT_OPERATOR_ROLE.getExternalId())
						.id(backOfficeRoleId.getValue()
								.toString())
						.build());

		final var adminUser = initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(adminRoleId)
				.tenantId(tenantId)
				.permissionStatus(UserPermissionStatus.CREATED)
				.userStatus(UserStatus.CREATED)
				.userType(UserType.SYSTEM_ADMIN)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.externalId("externalId-admin-2025041100-5000")
				.build());

		final var targetUser = initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(ownerRoleId)
				.tenantId(tenantId)
				.permissionStatus(UserPermissionStatus.CREATED)
				.userStatus(UserStatus.CREATED)
				.userType(UserType.BACK_OFFICE)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.externalId("backoffice-2025041100-6001")
				.build());

		when(userRoleClient.getUserRole(ownerRoleId.getValue(), null, tenantId.getValue(), true)).thenThrow(
				new FeignBadRequestException(ErrorResponse.builder()
						.details(List.of("User Role not found"))
						.code(GlobalErrorCode.AUTHORIZATION_USER_ROLE_NOT_FOUND.getValue())
						.message(GlobalErrorCode.AUTHORIZATION_USER_ROLE_NOT_FOUND.getMessage())
						.build()));

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId(adminUser.getId()
						.getValue()
						.toString())
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId(tenantId.getValue()
								.toString())
						.userRoleId(backOfficeRoleId.getValue()
								.toString())
						.build()))
				.userType(UserType.SYSTEM_ADMIN)
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.build();

		// Act & Assert
		webClient.put()
				.uri("/api/user/users/{userId}/roles/{userRoleId}/change", targetUser.getId()
						.getValue(), backOfficeRoleId.getValue()
								.toString())
				.contentType(MediaType.APPLICATION_JSON)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {
					final var userResponse = Optional.ofNullable(response.getResponseBody())
							.map(UserResponse.class::cast)
							.orElse(null);
					assertNotNull(userResponse);
					assertEquals(targetUser.getId()
							.getValue()
							.toString(), userResponse.getId());
					assertEquals(targetUser.getSsoId(), userResponse.getSsoId());
					assertEquals(targetUser.getUserType(), userResponse.getUserType());
					assertEquals(targetUser.getEmail(), userResponse.getEmail());
					assertEquals(targetUser.getFirstName(), userResponse.getFirstName());
					assertEquals(targetUser.getLastName(), userResponse.getLastName());
					Assertions.assertEquals(UserMappingFunctionSupport.buildFullName(targetUser.getFirstName(),
							targetUser.getLastName()), userResponse.getFullName());
					assertEquals(targetUser.getPhoneNumber(), userResponse.getPhoneNumber());
					assertEquals(targetUser.getRealmId(), userResponse.getRealmId());
					assertEquals(targetUser.getUserNonCompletedActions(), userResponse.getUserNonCompletedActions());
					assertEquals(targetUser.getTotalSubAccounts(), userResponse.getTotalSubAccounts());
					assertEquals(targetUser.getTotalSponsors(), userResponse.getTotalSponsors());

					assertEquals(1, userResponse.getPermissions()
							.stream()
							.filter(it -> tenantId.getValue()
									.toString()
									.equals(it.tenantId()) && backOfficeRoleId.getValue()
											.toString()
											.equals(it.userRoleId()))
							.toList()
							.size());

				});

	}

	@Test
	void throwExceptionWhenChangingNewTenantOwnerRoleByBackOffice() {
		// Arrange
		final var tenantId = new TenantId(2025041104L);
		final var backOfficeRoleId = new UserRoleId(20250411041L);
		final var ownerRoleId = new UserRoleId(20250411042L);

		final var targetUser = initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(backOfficeRoleId)
				.tenantId(tenantId)
				.permissionStatus(UserPermissionStatus.CREATED)
				.userStatus(UserStatus.CREATED)
				.userType(UserType.BACK_OFFICE)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.externalId("backoffice-2025041104-6001")
				.build());

		when(userRoleClient.getUserRole(backOfficeRoleId.getValue(), DefaultSystemRole.TENANT_OPERATOR_ROLE
				.getExternalId(), tenantId.getValue(), true)).thenReturn(UserRoleResponse.builder()
						.tenantId(tenantId.getValue()
								.toString())
						.externalId(DefaultSystemRole.TENANT_OPERATOR_ROLE.getExternalId())
						.id(backOfficeRoleId.getValue()
								.toString())
						.build());

		when(userRoleClient.getUserRole(ownerRoleId.getValue(), null, tenantId.getValue(), true)).thenReturn(
				UserRoleResponse.builder()
						.tenantId(tenantId.getValue()
								.toString())
						.externalId(DefaultSystemRole.TENANT_OWNER_ROLE.getExternalId())
						.id(backOfficeRoleId.getValue()
								.toString())
						.build());

		when(tenantClient.getTenant(CommonDomainConstants.SYSTEM_TENANT_ID.getValue())).thenReturn(ResponseEntity.ok(
				TenantResponse.builder()
						.tenantId(CommonDomainConstants.SYSTEM_TENANT_ID.getValue())
						.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
						.build()));

		when(tenantClient.getTenant(tenantId.getValue())).thenReturn(ResponseEntity.ok(TenantResponse.builder()
				.tenantId(tenantId.getValue())
				.realmId(tenantId.getValue() + "realm")
				.build()));

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId(tenantId.getValue()
								.toString())
						.userRoleId(backOfficeRoleId.getValue()
								.toString())
						.build()))
				.userType(UserType.BACK_OFFICE)
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.build();

		// Act & Assert
		webClient.put()
				.uri("/api/user/users/{userId}/roles/{userRoleId}/change", targetUser.getId()
						.getValue(), ownerRoleId.getValue()
								.toString())
				.contentType(MediaType.APPLICATION_JSON)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isBadRequest()
				.expectBody(ErrorResponse.class)
				.consumeWith(response -> {
					final var error = response.getResponseBody();
					assertNotNull(error);
					assertTrue(error.getDetails()
							.contains("Only Admin can assign Tenant Owner role."));
				});

	}

	@Test
	void throwExceptionWhenChangingCurrentTenantOwnerRoleByBackOffice() {
		// Arrange
		final var tenantId = new TenantId(2025041105L);
		final var backOfficeRoleId = new UserRoleId(20250411051L);
		final var ownerRoleId = new UserRoleId(20250411052L);

		final var backOfficeUser = initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(backOfficeRoleId)
				.tenantId(tenantId)
				.permissionStatus(UserPermissionStatus.CREATED)
				.userStatus(UserStatus.CREATED)
				.userType(UserType.BACK_OFFICE)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.externalId("backoffice-2025041105-5000")
				.build());

		final var targetUser = initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(ownerRoleId)
				.tenantId(tenantId)
				.permissionStatus(UserPermissionStatus.CREATED)
				.userStatus(UserStatus.CREATED)
				.userType(UserType.BACK_OFFICE)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.externalId("backoffice-2025041105-6001")
				.build());

		when(userRoleClient.getUserRole(backOfficeRoleId.getValue(), DefaultSystemRole.TENANT_OPERATOR_ROLE
				.getExternalId(), tenantId.getValue(), true)).thenReturn(UserRoleResponse.builder()
						.tenantId(tenantId.getValue()
								.toString())
						.externalId(DefaultSystemRole.TENANT_OPERATOR_ROLE.getExternalId())
						.id(backOfficeRoleId.getValue()
								.toString())
						.build());

		when(userRoleClient.getUserRole(ownerRoleId.getValue(), null, tenantId.getValue(), true)).thenReturn(
				UserRoleResponse.builder()
						.tenantId(tenantId.getValue()
								.toString())
						.externalId(DefaultSystemRole.TENANT_OWNER_ROLE.getExternalId())
						.id(backOfficeRoleId.getValue()
								.toString())
						.build());

		when(tenantClient.getTenant(CommonDomainConstants.SYSTEM_TENANT_ID.getValue())).thenReturn(ResponseEntity.ok(
				TenantResponse.builder()
						.tenantId(CommonDomainConstants.SYSTEM_TENANT_ID.getValue())
						.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
						.build()));

		when(tenantClient.getTenant(tenantId.getValue())).thenReturn(ResponseEntity.ok(TenantResponse.builder()
				.tenantId(tenantId.getValue())
				.realmId(tenantId.getValue() + "realm")
				.build()));

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId(backOfficeUser.getId()
						.getValue()
						.toString())
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId(tenantId.getValue()
								.toString())
						.userRoleId(backOfficeRoleId.getValue()
								.toString())
						.build()))
				.userType(UserType.BACK_OFFICE)
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.build();

		// Act & Assert
		webClient.put()
				.uri("/api/user/users/{userId}/roles/{userRoleId}/change", targetUser.getId()
						.getValue(), backOfficeRoleId.getValue()
								.toString())
				.contentType(MediaType.APPLICATION_JSON)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isBadRequest()
				.expectBody(ErrorResponse.class)
				.consumeWith(response -> {
					final var error = response.getResponseBody();
					assertNotNull(error);
					assertTrue(error.getDetails()
							.contains("Cannot assign Tenant Owner role due not to Admin"));
				});

	}

	@Test
	void testThrowExceptionWhenChangeOwnerRoleByBackOfficeUser() {
		// Arrange
		final var tenantId = new TenantId(99L);
		final var currentRoleId = new UserRoleId(1L);
		final var newRoleId = new UserRoleId(2L);

		when(userRoleClient.getUserRole(currentRoleId.getValue(), null, tenantId.getValue(), true)).thenReturn(
				UserRoleResponse.builder()
						.tenantId(tenantId.getValue()
								.toString())
						.id(currentRoleId.getValue()
								.toString())
						.externalId(DefaultSystemRole.TENANT_OWNER_ROLE.getExternalId())
						.build());

		when(userRoleClient.getUserRole(3L, null, tenantId.getValue(), true)).thenReturn(UserRoleResponse.builder()
				.tenantId(tenantId.getValue()
						.toString())
				.id("3")
				.build());

		when(userRoleClient.getUserRole(newRoleId.getValue(), null, tenantId.getValue(), true)).thenReturn(
				UserRoleResponse.builder()
						.tenantId(tenantId.getValue()
								.toString())
						.id(newRoleId.getValue()
								.toString())
						.build());

		when(tenantClient.getTenant(tenantId.getValue())).thenReturn(ResponseEntity.ok(TenantResponse.builder()
				.tenantId(tenantId.getValue())
				.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
				.build()));

		final var user = userCommandService.addSingleUser(UpsertTenantUserCommand.builder()
				.userRoleId(new UserRoleId(3L))
				.tenantId(tenantId)
				.permissionStatus(UserPermissionStatus.CREATED)
				.userStatus(UserStatus.CREATED)
				.userType(UserType.BACK_OFFICE)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.externalId("externalId1")
				.build());

		final var targetUser = userCommandService.addSingleUser(UpsertTenantUserCommand.builder()
				.userRoleId(currentRoleId)
				.tenantId(tenantId)
				.permissionStatus(UserPermissionStatus.CREATED)
				.userStatus(UserStatus.CREATED)
				.userType(UserType.BACK_OFFICE)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.externalId("externalId1")
				.build());

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId(user.getId()
						.getValue()
						.toString())
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId(tenantId.getValue()
								.toString())
						.userRoleId("3")
						.build()))
				.userType(UserType.BACK_OFFICE)
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.build();

		// Act & Assert
		webClient.put()
				.uri("/api/user/users/{userId}/roles/{userRoleId}/change", targetUser.getId()
						.getValue(), newRoleId.getValue()
								.toString())
				.contentType(MediaType.APPLICATION_JSON)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isBadRequest()
				.expectBody(ErrorResponse.class)
				.consumeWith(response -> {
					final var error = response.getResponseBody();
					assertNotNull(error);
					assertTrue(error.getDetails()
							.contains("Cannot assign Tenant Owner role due not to Admin"));
				});

	}

	private User initializeNewUser(UpsertTenantUserCommand command) {
		when(userRoleClient.getUserRole(command.userRoleId().getValue(), null, command.tenantId().getValue(),
				true)).thenReturn(UserRoleResponse.builder().tenantId(command.tenantId().getValue().toString())
				.id(command.userRoleId().getValue().toString()).build());

		when(tenantClient.getTenant(command.tenantId().getValue())).thenReturn(ResponseEntity.ok(
				TenantResponse.builder().tenantId(command.tenantId().getValue()).realmId(command.realmId()).settings(
						TenantSettingsResponse.builder().timeZone(
										MapstructCommonMapper.INSTANCE.timezoneToTimeZoneResponse(
												TimeZone.getTimeZone("Asia/Ho_Chi_Minh"))).dateFormat("YYYY-MM-dd")
								.timeFormat("HH:mm:ss").build()).build()));

		return userCommandService.addSingleUser(command);
	}
}
