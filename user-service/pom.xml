<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.styl.pacific</groupId>
        <artifactId>pacific-microservices</artifactId>
        <version>1.2.4</version>
    </parent>
    <groupId>com.styl.pacific.user.service</groupId>
    <artifactId>user-service</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>user-api</module>
        <module>user-application</module>
        <module>user-core</module>
        <module>user-data-access</module>
        <module>user-messaging</module>
        <module>user-scheduling</module>
        <module>user-shared</module>
        <module>user-test</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>net.javacrumbs.shedlock</groupId>
                <artifactId>shedlock-provider-jdbc-template</artifactId>
                <version>${shedlock-spring.version}</version>
            </dependency>
            <dependency>
                <groupId>net.javacrumbs.shedlock</groupId>
                <artifactId>shedlock-spring</artifactId>
                <version>${shedlock-spring.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
