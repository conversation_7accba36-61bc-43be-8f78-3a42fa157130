/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.messaging.users.publisher;

import com.styl.pacific.user.service.core.features.users.events.NewUserAddedEvent;
import com.styl.pacific.user.service.messaging.users.mapper.UserCreatedEventMapper;
import com.styl.pacific.user.service.messaging.users.publisher.kafka.UserCreatedEventKafkaPublisher;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class UserEventPublisher {

	private final UserCreatedEventKafkaPublisher publisher;

	@EventListener(NewUserAddedEvent.class)
	public void handleNewUserAdded(final NewUserAddedEvent event) {
		publisher.publish(UserCreatedEventMapper.INSTANCE.toEvent(UUID.randomUUID(), event.getNewUser()));
	}
}
