/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.messaging.users.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.user.service.core.features.permissions.entities.UserPermission;
import com.styl.pacific.user.service.core.features.users.entities.User;
import java.util.UUID;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface UserCreatedEventMapper {
	UserCreatedEventMapper INSTANCE = Mappers.getMapper(UserCreatedEventMapper.class);

	@Mapping(target = "id", source = "eventId")
	@Mapping(target = "userId", source = "user.id", qualifiedByName = "userIdToLong")
	@Mapping(target = "createdAt", source = "user.createdAt", qualifiedByName = "instantToLong")
	@Mapping(target = "updatedAt", source = "user.updatedAt", qualifiedByName = "instantToLong")
	com.styl.pacific.kafka.users.avro.model.UserCreatedEvent toEvent(UUID eventId, User user);

	@Mapping(target = "tenantId", source = "tenantId", qualifiedByName = "tenantIdToLong")
	@Mapping(target = "userRoleId", source = "userRoleId", qualifiedByName = "userRoleIdToLong")
	com.styl.pacific.kafka.users.avro.model.UserPermission toPermission(UserPermission userPermission);
}
