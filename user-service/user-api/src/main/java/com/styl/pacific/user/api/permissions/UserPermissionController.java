/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.api.permissions;

import com.styl.pacific.domain.valueobject.UserRoleId;
import com.styl.pacific.user.service.core.features.permissions.UserPermissionQueryService;
import com.styl.pacific.user.service.core.features.users.request.CountUserQuery;
import com.styl.pacific.user.shared.http.apis.UserPermissionApi;
import com.styl.pacific.user.shared.http.permissions.request.CountUserPermissionRequest;
import com.styl.pacific.user.shared.http.permissions.response.CountUserPermissionResponse;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class UserPermissionController implements UserPermissionApi {

	private final UserPermissionQueryService userPermissionQueryService;

	@Override
	public CountUserPermissionResponse countUserPermissions(CountUserPermissionRequest request) {
		final var result = userPermissionQueryService.countUserRoles(CountUserQuery.builder()
				.byUserRoleIds(request.getByUserRoleIds()
						.stream()
						.map(UserRoleId::new)
						.toList())
				.build());
		return CountUserPermissionResponse.builder()
				.countUserRoleResult(result.getUserRoleCountMap()
						.entrySet()
						.stream()
						.collect(Collectors.toMap(entry -> entry.getKey()
								.getValue(), entry -> CountUserPermissionResponse.UserRoleAggregationResult.builder()
										.totalUsers(entry.getValue())
										.build())))
				.build();
	}
}
