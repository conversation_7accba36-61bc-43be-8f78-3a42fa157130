/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.api.requiredactions.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.domain.valueobject.UserRequiredActionId;
import com.styl.pacific.user.service.core.features.requiredactions.request.UserRequiredActionFilter;
import com.styl.pacific.user.service.core.features.users.request.ReplyUserRequiredActionCommand;
import com.styl.pacific.user.shared.http.requiredactions.request.FilterUserRequiredActionRequest;
import com.styl.pacific.user.shared.http.requiredactions.request.ReplyUserRequiredActionRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class, })
public interface UserRequiredActionRequestMapper {
	UserRequiredActionRequestMapper INSTANCE = Mappers.getMapper(UserRequiredActionRequestMapper.class);

	@Mapping(target = "userId", source = "targetUserId")
	@Mapping(target = "userRequiredActionId", source = "userRequiredActionId")
	@Mapping(target = "repliedOption", source = "request.repliedOption")
	ReplyUserRequiredActionCommand toReplyCommand(ReplyUserRequiredActionRequest request, UserId targetUserId,
			UserRequiredActionId userRequiredActionId);

	@Mapping(target = "byTenantId", source = "byTenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "byUserId", ignore = true)
	UserRequiredActionFilter toFilter(FilterUserRequiredActionRequest source);
}