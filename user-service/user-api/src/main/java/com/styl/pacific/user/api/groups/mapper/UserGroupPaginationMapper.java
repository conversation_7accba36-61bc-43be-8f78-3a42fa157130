/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.api.groups.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.user.service.core.features.groups.entities.UserGroup;
import com.styl.pacific.user.service.core.features.groups.request.GetAllUserGroupFilter;
import com.styl.pacific.user.service.core.features.groups.request.UserGroupPaginationQuery;
import com.styl.pacific.user.shared.http.groups.request.FilterUserGroupRequest;
import com.styl.pacific.user.shared.http.groups.request.QueryUserGroupPaginationRequest;
import com.styl.pacific.user.shared.http.groups.response.UserGroupResponse;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class, UserGroupRequestMapper.class, UserGroupResponseMapper.class, })
public interface UserGroupPaginationMapper {
	UserGroupPaginationMapper INSTANCE = Mappers.getMapper(UserGroupPaginationMapper.class);

	@Mapping(target = "filter", source = "filter", qualifiedByName = "queryRequestToGetAllQuery")
	UserGroupPaginationQuery toPagingQuery(QueryUserGroupPaginationRequest source, @Context Long tenantId);

	Paging<UserGroupResponse> toPagingResponse(Paging<UserGroup> source);

	@Named("queryRequestToGetAllQuery")
	default GetAllUserGroupFilter queryRequestToGetAllQuery(FilterUserGroupRequest source, @Context Long tenantId) {
		return UserGroupRequestMapper.INSTANCE.toUserGroupQuery(source, tenantId);
	}
}
