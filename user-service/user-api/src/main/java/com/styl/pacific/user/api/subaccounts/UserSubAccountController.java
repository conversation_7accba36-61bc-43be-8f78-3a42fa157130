/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.api.subaccounts;

import com.styl.pacific.application.rest.context.RequestContext;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.domain.dto.TokenClaim;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.enums.UserType;
import com.styl.pacific.user.api.subaccounts.mapper.UserSubAccountPaginationMapper;
import com.styl.pacific.user.api.subaccounts.mapper.UserSubAccountRequestMapper;
import com.styl.pacific.user.api.subaccounts.mapper.UserSubAccountResponseMapper;
import com.styl.pacific.user.api.supporters.UserAccessResourceAuthorizer;
import com.styl.pacific.user.service.core.features.subaccounts.UserSubAccountCommandService;
import com.styl.pacific.user.service.core.features.subaccounts.UserSubAccountQueryService;
import com.styl.pacific.user.service.core.features.subaccounts.entities.UserSubAccount;
import com.styl.pacific.user.service.core.features.subaccounts.mapper.UserSubAccountMapper;
import com.styl.pacific.user.service.core.features.subaccounts.request.CancelSubAccountCommand;
import com.styl.pacific.user.service.core.features.subaccounts.request.ChangeStatusSubAccountCommand;
import com.styl.pacific.user.service.core.features.subaccounts.request.DeleteSubAccountCommand;
import com.styl.pacific.user.service.core.features.subaccounts.request.FilterSubAccountQuery;
import com.styl.pacific.user.service.core.features.subaccounts.request.SendCredentialInvitationCommand;
import com.styl.pacific.user.shared.exceptions.UserSubAccountException;
import com.styl.pacific.user.shared.http.apis.UserSubAccountApi;
import com.styl.pacific.user.shared.http.subaccounts.request.ChangeStatusSubAccountRequest;
import com.styl.pacific.user.shared.http.subaccounts.request.CreateSubAccountExistingUserRequest;
import com.styl.pacific.user.shared.http.subaccounts.request.CreateSubAccountNewUserRequest;
import com.styl.pacific.user.shared.http.subaccounts.request.CreateSubAccountRequest;
import com.styl.pacific.user.shared.http.subaccounts.request.QuerySubAccountPaginationRequest;
import com.styl.pacific.user.shared.http.subaccounts.request.SendCredentialInvitationRequest;
import com.styl.pacific.user.shared.http.subaccounts.request.UpdateSubAccountUserRequest;
import com.styl.pacific.user.shared.http.subaccounts.response.SubAccountResponse;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class UserSubAccountController implements UserSubAccountApi {

	private final UserSubAccountCommandService userSubAccountCommandService;

	private final UserSubAccountQueryService userSubAccountQueryService;

	private final RequestContext requestContext;

	@Override
	public SubAccountResponse createSubAccount(CreateSubAccountNewUserRequest request) {
		return UserSubAccountResponseMapper.INSTANCE.toResponse(establishSubAccount(request));
	}

	@Override
	public SubAccountResponse inviteExistingCustomer(CreateSubAccountExistingUserRequest request) {
		return UserSubAccountResponseMapper.INSTANCE.toResponse(establishSubAccount(request));
	}

	private UserSubAccount establishSubAccount(CreateSubAccountRequest request) {
		final var sponsorUserId = Optional.ofNullable(requestContext.getTokenClaim())
				.filter(it -> UserType.CUSTOMER.equals(it.getUserType()))
				.map(TokenClaim::getUserId)
				.map(MapstructCommonDomainMapper.INSTANCE::stringToUserId)
				// Above handling is for Customer, below handling allows for BackOffice
				.orElseGet(() -> Optional.of(request.getSponsorUserId())
						.map(MapstructCommonDomainMapper.INSTANCE::longToUserId)
						.orElseThrow(() -> new UserSubAccountException("Sponsor User ID is required")));

		final var tenantId = MapstructCommonDomainMapper.INSTANCE.longToTenantId(requestContext.getTenantId());
		final var userTypeCreator = Optional.ofNullable(requestContext.getTokenClaim())
				.map(TokenClaim::getUserType)
				.orElse(UserType.SYSTEM_ADMIN);

		return userSubAccountCommandService.createSubAccount(UserSubAccountRequestMapper.INSTANCE.toCommand(
				userTypeCreator, sponsorUserId, tenantId, request));
	}

	@Override
	public Paging<SubAccountResponse> querySubAccounts(Long userId, QuerySubAccountPaginationRequest request) {
		UserAccessResourceAuthorizer.permitCustomerResourceAccess(requestContext.getTokenClaim(), userId,
				new UserSubAccountException("Customer cannot get other user sub-accounts"));

		var paginationQuery = UserSubAccountPaginationMapper.INSTANCE.toPagingQuery(request);

		paginationQuery = paginationQuery.withFilter(Optional.ofNullable(paginationQuery.getFilter())
				.map(filter -> request.getFilter()
						.isSubUserFilter()
								? filter.withBySubUserId(MapstructCommonDomainMapper.INSTANCE.longToUserId(userId))
								: filter.withBySponsorId(MapstructCommonDomainMapper.INSTANCE.longToUserId(userId)))
				.orElseGet(() -> FilterSubAccountQuery.builder()
						.bySponsorId(MapstructCommonDomainMapper.INSTANCE.longToUserId(userId))
						.build()));

		return UserSubAccountPaginationMapper.INSTANCE.toPagingResponse(userSubAccountQueryService.querySubAccounts(
				paginationQuery));

	}

	@Override
	public void deleteSubAccount(Long subAccountId) {
		final var tenantId = MapstructCommonDomainMapper.INSTANCE.longToTenantId(requestContext.getTenantId());
		final var targetSubAccountId = MapstructCommonDomainMapper.INSTANCE.longToUserSubAccountId(subAccountId);

		if (Optional.ofNullable(requestContext.getTokenClaim())
				.map(TokenClaim::getUserType)
				.filter(UserType.CUSTOMER::equals)
				.isPresent()) {
			userSubAccountCommandService.deleteSubAccountBySponsor(DeleteSubAccountCommand.builder()
					.tenantId(tenantId)
					.sponsorId(MapstructCommonDomainMapper.INSTANCE.stringToUserId(requestContext.getTokenClaim()
							.getUserId()))
					.subAccountId(targetSubAccountId)
					.build());
			return;
		}
		userSubAccountCommandService.deleteBySubAccountId(DeleteSubAccountCommand.builder()
				.tenantId(tenantId)
				.subAccountId(targetSubAccountId)
				.build());
	}

	@Override
	public void changeStatus(ChangeStatusSubAccountRequest request, Long subAccountId) {
		final var targetSubAccountId = MapstructCommonDomainMapper.INSTANCE.longToUserSubAccountId(subAccountId);

		if (Optional.ofNullable(requestContext.getTokenClaim())
				.map(TokenClaim::getUserType)
				.filter(UserType.CUSTOMER::equals)
				.isPresent()) {
			userSubAccountCommandService.changeStatusBySponsor(ChangeStatusSubAccountCommand.builder()
					.sponsorId(MapstructCommonDomainMapper.INSTANCE.stringToUserId(requestContext.getTokenClaim()
							.getUserId()))
					.subAccountId(targetSubAccountId)
					.subAccountStatus(request.subAccountStatus())
					.build());
			return;
		}
		userSubAccountCommandService.changeStatusById(ChangeStatusSubAccountCommand.builder()
				.subAccountId(targetSubAccountId)
				.subAccountStatus(request.subAccountStatus())
				.build());
	}

	@Override
	public void cancelSubAccount(Long subAccountId) {
		final var targetSubAccountId = MapstructCommonDomainMapper.INSTANCE.longToUserSubAccountId(subAccountId);
		userSubAccountCommandService.cancelSubAccountBySubUser(CancelSubAccountCommand.builder()
				.tenantId(MapstructCommonDomainMapper.INSTANCE.longToTenantId(requestContext.getTenantId()))
				.subAccountId(targetSubAccountId)
				.subUserId(MapstructCommonDomainMapper.INSTANCE.stringToUserId(requestContext.getTokenClaim()
						.getUserId()))
				.build());
	}

	@Override
	public void sendSubAccountCredentialInvitation(SendCredentialInvitationRequest request, Long subAccountId) {
		final var targetSubAccountId = MapstructCommonDomainMapper.INSTANCE.longToUserSubAccountId(subAccountId);

		if (Optional.ofNullable(requestContext.getTokenClaim())
				.map(TokenClaim::getUserType)
				.filter(UserType.CUSTOMER::equals)
				.isPresent()) {
			userSubAccountCommandService.sendCredentialInvitationBySponsor(SendCredentialInvitationCommand.builder()
					.sponsorId(MapstructCommonDomainMapper.INSTANCE.stringToUserId(requestContext.getTokenClaim()
							.getUserId()))
					.subAccountId(targetSubAccountId)
					.email(request.email())
					.build());
			return;
		}
		userSubAccountCommandService.sendCredentialInvitationBySubAccountId(SendCredentialInvitationCommand.builder()
				.subAccountId(targetSubAccountId)
				.email(request.email())
				.build());
	}

	@Override
	public SubAccountResponse getSubAccount(Long subAccountId) {
		final var targetSubAccountId = MapstructCommonDomainMapper.INSTANCE.longToUserSubAccountId(subAccountId);

		if (Optional.ofNullable(requestContext.getTokenClaim())
				.map(TokenClaim::getUserType)
				.filter(UserType.CUSTOMER::equals)
				.isPresent()) {
			return UserSubAccountResponseMapper.INSTANCE.toResponse(userSubAccountQueryService
					.getSubAccountByUserIdAndSubAccountId(MapstructCommonDomainMapper.INSTANCE.stringToUserId(
							requestContext.getTokenClaim()
									.getUserId()), targetSubAccountId));
		}

		return UserSubAccountResponseMapper.INSTANCE.toResponse(userSubAccountQueryService.getSubAccountBySubAccountId(
				targetSubAccountId));

	}

	@Override
	public SubAccountResponse updateSubAccountUser(Long subAccountId, UpdateSubAccountUserRequest request) {
		final var tenantId = MapstructCommonDomainMapper.INSTANCE.longToTenantId(requestContext.getTenantId());
		final var sponsorUserId = MapstructCommonDomainMapper.INSTANCE.stringToUserId(requestContext.getTokenClaim()
				.getUserId());
		return UserSubAccountResponseMapper.INSTANCE.toResponse(userSubAccountCommandService.updateSubAccountUser(
				tenantId, UserSubAccountMapper.INSTANCE.toUpdateSubAccountUserCommand(sponsorUserId,
						MapstructCommonDomainMapper.INSTANCE.longToUserSubAccountId(subAccountId), request)));
	}
}
