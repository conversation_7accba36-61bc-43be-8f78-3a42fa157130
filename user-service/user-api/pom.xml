<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.styl.pacific.user.service</groupId>
        <artifactId>user-service</artifactId>
        <version>1.2.4</version>
    </parent>
    <artifactId>user-api</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>common-application</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>common-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>common-feign</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>common-mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>common-rest</artifactId>
        </dependency>

        <dependency>
            <groupId>com.styl.pacific.authz.service</groupId>
            <artifactId>authorization-shared</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.common</groupId>
            <artifactId>aws-s3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.user.service</groupId>
            <artifactId>user-core</artifactId>
        </dependency>

    </dependencies>
</project>
