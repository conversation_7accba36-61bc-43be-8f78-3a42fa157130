server:
  port: 9100

spring:
  codec:
    log-request-details: true
  application:
    name: gateway-service
  reactor:
    context-propagation: AUTO
  cloud:
    gateway:
      # Config routes for services
      routes:
        - id: tenant-service
          uri: ${pacific.clients.tenant-service.url}
          predicates:
            - Path=/api/tenant/**
          filters:
            - name: CircuitBreaker
              args:
                name: tenant-service

        - id: user-service
          uri: ${pacific.clients.user-service.url}
          predicates:
            - Path=/api/user/**
          filters:
            - name: CircuitBreaker
              args:
                name: user-service

        - id: catalog-service
          uri: ${pacific.clients.catalog-service.url}
          predicates:
            - Path=/api/catalog/**
          filters:
            - name: CircuitBreaker
              args:
                name: catalog-service

        - id: store-service
          uri: ${pacific.clients.store-service.url}
          predicates:
            - Path=/api/store/**
          filters:
            - name: CircuitBreaker
              args:
                name: store-service

        - id: order-service
          uri: ${pacific.clients.order-service.url}
          predicates:
            - Path=/api/order/**
          filters:
            - name: CircuitBreaker
              args:
                name: order-service

        - id: payment-service
          uri: ${pacific.clients.payment-service.url}
          predicates:
            - Path=/api/payment/**,/webhooks/payment/**
          filters:
            - name: CircuitBreaker
              args:
                name: payment-service

        - id: utility-service
          uri: ${pacific.clients.utility-service.url}
          predicates:
            - Path=/api/utility/**
          filters:
            - name: CircuitBreaker
              args:
                name: utility-service

        - id: authorization-service
          uri: ${pacific.clients.authorization-service.url}
          predicates:
            - Path=/api/authz/**
          filters:
            - name: CircuitBreaker
              args:
                name: authorization-service

        - id: wallet-service
          uri: ${pacific.clients.wallet-service.url}
          predicates:
            - Path=/api/wallet/**
          filters:
            - name: CircuitBreaker
              args:
                name: wallet-service

    openfeign:
      client:
        config:
          default:
            loggerLevel: FULL
            errorDecoder: com.styl.pacific.common.feign.exception.PacificFeignErrorDecoder
            queryMapEncoder: com.styl.pacific.common.feign.utils.CustomQueryMapEncoder

# For more information, visit: https://docs.spring.io/spring-cloud-gateway/reference/spring-cloud-gateway-server-mvc/filters/circuitbreaker-filter.html
resilience4j:
  timelimiter:
    configs:
      default:
        timeoutDuration: 30000

pacific:
  configs:
    tenant-service:
      tenant:
        default-tenant-id: 1
  clients:
    tenant-service:
      url: http://localhost:9201
    user-service:
      url: http://localhost:9202
    catalog-service:
      url: http://localhost:9203
    store-service:
      url: http://localhost:9204
    order-service:
      url: http://localhost:9205
    payment-service:
      url: http://localhost:9206
    utility-service:
      url: http://localhost:9208
    authorization-service:
      url: http://localhost:9209
    wallet-service:
      url: http://localhost:9210
  commons:
    feign:
      interceptor:
        mvc:
          enabled: false

  aggregators:
    device-aggregator:
      url: http://localhost:9111

  webs:
    backoffice-ui:
      url: http://localhost:9300

  security:
    api:
      authorization:
        enabled: true
      issuer-uri-pattern: https://pacific-ii-ciam-sit.styl.solutions/realms
      config:
        uri-pattern: /api/**

    webhooks:
      config:
        uri-pattern: /webhooks/**
        open-endpoints:
          - path: /webhooks/payment/stripe/**
          - path: /webhooks/payment/stripe-connect/**

    defaults:
      config:
        open-endpoints:
          - path: /actuator/**
            methods:
              - GET

  tracing:
    enabled: true
    otlp:
      endpoint: http://localhost:4317

logging:
  level:
    com.styl.pacific: DEBUG
    org.springframework: DEBUG
    org.springframework.web.reactive: DEBUG
    org.springframework.context: DEBUG

management:
  tracing:
    sampling:
      probability: 1.0