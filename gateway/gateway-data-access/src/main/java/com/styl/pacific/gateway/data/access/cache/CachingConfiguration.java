/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.gateway.data.access.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import java.time.Duration;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableCaching
public class CachingConfiguration {
	@Bean
	public Caffeine<Object, Object> caffeineConfig(
			@Value("${pacific.security.api.authorization.expired-after-access}") Duration expiredAfterAccess,
			@Value("${pacific.security.api.authorization.expired-after-write}") Duration expiredAfterWrite) {
		return Caffeine.newBuilder()
				.initialCapacity(100)
				.expireAfterAccess(expiredAfterAccess)
				.expireAfterWrite(expiredAfterWrite)
				.recordStats();
	}

	@Bean
	public CacheManager cacheManager(Caffeine<Object, Object> caffeineConfig) {
		final var manager = new CaffeineCacheManager();
		manager.setCaffeine(caffeineConfig);
		manager.setAsyncCacheMode(true);
		return manager;
	}
}
