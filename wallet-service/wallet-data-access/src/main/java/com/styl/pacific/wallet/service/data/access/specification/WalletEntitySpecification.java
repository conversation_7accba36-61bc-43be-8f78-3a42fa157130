/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.data.access.specification;

import com.styl.pacific.data.access.jpa.specification.BaseSpecification;
import com.styl.pacific.wallet.service.data.access.entity.WalletEntity;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.io.Serial;
import java.util.ArrayList;
import java.util.List;
import lombok.Builder;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */
@Builder
public class WalletEntitySpecification extends BaseSpecification<WalletEntity> {
	@Serial
	private static final long serialVersionUID = 1L;

	private List<Long> ids;

	private Long tenantId;

	private Long fundSourceId;

	private Long customerId;

	private List<Long> customerIds;

	private List<String> statuses;

	private List<String> types;

	@Override
	public Predicate toPredicate(Root<WalletEntity> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
		List<Predicate> predicates = new ArrayList<>();

		if (isNotNull(tenantId)) {
			predicates.add(equals(criteriaBuilder, root.get("tenantId"), tenantId));
		}

		if (!CollectionUtils.isEmpty(ids)) {
			predicates.add(inLongList(root.get("id"), ids));
		}

		if (isNotNull(fundSourceId)) {
			predicates.add(equals(criteriaBuilder, root.get("fundSourceId"), fundSourceId));
		}

		if (isNotNull(customerId)) {
			predicates.add(equals(criteriaBuilder, root.get("customerId"), customerId));
		}

		if (!CollectionUtils.isEmpty(customerIds)) {
			predicates.add(inLongList(root.get("customerId"), customerIds));
		}

		if (!CollectionUtils.isEmpty(statuses)) {
			predicates.add(inStringList(root.get("status"), statuses));
		}

		if (!CollectionUtils.isEmpty(types)) {
			predicates.add(inStringList(root.get("type"), types));
		}

		return and(criteriaBuilder, predicates);
	}
}
