/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.data.access.repository;

import com.styl.pacific.wallet.service.data.access.entity.WalletEntity;
import com.styl.pacific.wallet.service.enums.WalletType;
import jakarta.persistence.LockModeType;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Lock;

/**
 * <AUTHOR>
 *
 */
public interface WalletJpaRepository extends JpaRepository<WalletEntity, Long>, JpaSpecificationExecutor<WalletEntity> {
	@Lock(LockModeType.PESSIMISTIC_WRITE)
	Optional<WalletEntity> findByIdAndTenantId(Long id, Long tenantId);

	@Lock(LockModeType.PESSIMISTIC_WRITE)
	Optional<WalletEntity> findByCustomerIdAndTypeAndTenantId(Long customerId, WalletType type, Long tenantId);
}
