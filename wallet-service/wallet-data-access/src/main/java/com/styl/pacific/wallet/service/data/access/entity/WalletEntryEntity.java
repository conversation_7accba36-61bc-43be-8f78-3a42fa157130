/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.data.access.entity;

import com.styl.pacific.data.access.entity.AuditableEntity;
import com.styl.pacific.wallet.service.enums.WalletEntryStatus;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import java.math.BigInteger;
import java.time.Instant;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.SQLRestriction;

/**
 * <AUTHOR>
 */

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "tb_wallet_entry")
@SQLRestriction("deleted_at IS NULL")
public class WalletEntryEntity extends AuditableEntity {

	@Id
	private Long id;

	private Long tenantId;

	@Column(name = "wallet_id", nullable = false)
	private Long walletId;

	private Long customerId;

	private BigInteger remainingBalance;

	private String currency;

	private BigInteger balance;

	private String description;

	private Instant expiresOn;

	@Enumerated(EnumType.STRING)
	private WalletEntryStatus status;

	@Version
	private Integer version;

	@ManyToOne
	@JoinColumn(name = "wallet_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
	private WalletEntity wallet;

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (!(o instanceof WalletEntryEntity that))
			return false;
		if (!super.equals(o))
			return false;
		return Objects.equals(id, that.id) && Objects.equals(tenantId, that.tenantId) && Objects.equals(walletId,
				that.walletId) && Objects.equals(customerId, that.customerId) && Objects.equals(remainingBalance,
						that.remainingBalance) && Objects.equals(currency, that.currency) && Objects.equals(balance,
								that.balance) && Objects.equals(description, that.description) && Objects.equals(
										expiresOn, that.expiresOn) && status == that.status && Objects.equals(wallet,
												that.wallet);
	}

	@Override
	public int hashCode() {
		return Objects.hash(super.hashCode(), id, tenantId, walletId, customerId, remainingBalance, currency, balance,
				description, expiresOn, status, wallet);
	}
}
