/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.data.access.entity;

import com.styl.pacific.data.access.entity.AuditableEntity;
import com.styl.pacific.wallet.service.enums.TopupSessionStatus;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import java.math.BigInteger;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "tb_topup_session")
public class TopupSessionEntity extends AuditableEntity {

	@Id
	private Long id;

	private Long tenantId;

	@Column(name = "wallet_id", nullable = false)
	private Long walletId;

	private String paymentRef;

	private String nonce;

	private String currency;

	private BigInteger amount;

	private String description;

	@Enumerated(EnumType.STRING)
	private TopupSessionStatus status;

	@Version
	private Integer version;

	@ManyToOne
	@JoinColumn(name = "wallet_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
	private WalletEntity wallet;

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (!(o instanceof TopupSessionEntity that))
			return false;
		if (!super.equals(o))
			return false;
		return Objects.equals(id, that.id) && Objects.equals(tenantId, that.tenantId) && Objects.equals(walletId,
				that.walletId) && Objects.equals(paymentRef, that.paymentRef) && Objects.equals(nonce, that.nonce)
				&& Objects.equals(currency, that.currency) && Objects.equals(amount, that.amount) && Objects.equals(
						description, that.description) && status == that.status && Objects.equals(version, that.version)
				&& Objects.equals(wallet, that.wallet);
	}

	@Override
	public int hashCode() {
		return Objects.hash(super.hashCode(), id, tenantId, walletId, paymentRef, nonce, currency, amount, description,
				status, version, wallet);
	}
}
