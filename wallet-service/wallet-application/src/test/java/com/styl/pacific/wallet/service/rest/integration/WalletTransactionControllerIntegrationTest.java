/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.rest.integration;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.common.constant.PacificRestConstants;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.common.test.BaseWebClientWithDbTest;
import com.styl.pacific.common.test.container.KafkaContainerTest;
import com.styl.pacific.domain.dto.CurrencyResponse;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantSettingsResponse;
import com.styl.pacific.user.shared.http.users.response.UserResponse;
import com.styl.pacific.wallet.service.config.IntegrationTestConfiguration;
import com.styl.pacific.wallet.service.data.access.adapter.TenantClient;
import com.styl.pacific.wallet.service.data.access.adapter.UserClient;
import com.styl.pacific.wallet.service.domain.dto.wallet.AddBalanceCommand;
import com.styl.pacific.wallet.service.domain.dto.wallet.CreateWalletCommandDto;
import com.styl.pacific.wallet.service.domain.dto.wallet.DeductBalanceCommand;
import com.styl.pacific.wallet.service.domain.dto.wallet.NotificationControlCommand;
import com.styl.pacific.wallet.service.domain.output.repository.UserRepository;
import com.styl.pacific.wallet.service.domain.port.input.service.WalletDomainService;
import com.styl.pacific.wallet.service.domain.port.input.service.WalletTransactionDomainService;
import com.styl.pacific.wallet.service.enums.WalletType;
import com.styl.pacific.wallet.service.responses.transaction.WalletTransactionResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Random;
import java.util.TimeZone;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.MethodOrderer.OrderAnnotation;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;
import org.springframework.util.LinkedMultiValueMap;

/**
 * <AUTHOR>
 */

@TestMethodOrder(OrderAnnotation.class)
@ContextConfiguration(classes = { IntegrationTestConfiguration.class })
class WalletTransactionControllerIntegrationTest extends BaseWebClientWithDbTest implements KafkaContainerTest {

	@Autowired
	private WalletDomainService walletDomainService;

	@Autowired
	private WalletTransactionDomainService walletTransactionDomainService;

	@Autowired
	private ObjectMapper objectMapper;

	@MockitoBean
	private UserClient userClient;

	@MockitoBean
	private TenantClient tenantClient;

	@MockitoSpyBean
	private UserRepository userRepository;

	@Order(1)
	@Test
	void testQueryTransactionsWhenDeletedWalletThenReturnWalletResponse() {
		// Arrange
		final var tenantId = 1L;
		final var customerId = 2505231609001L;

		when(userClient.getUserProfile(eq(customerId), any())).thenReturn(UserResponse.builder()
				.id(String.valueOf(customerId))
				.firstName("first-%s".formatted(customerId))
				.lastName("last-%s".formatted(customerId))
				.fullName("%s-%s".formatted("first-%s".formatted(customerId), "last-%s".formatted(customerId)))
				.email("<EMAIL>".formatted(customerId))
				.build());
		when(tenantClient.getTenant(anyLong())).thenReturn(ResponseEntity.ok(TenantResponse.builder()
				.tenantId(tenantId)
				.realmId("realm-001")
				.settings(TenantSettingsResponse.builder()
						.timeZone(MapstructCommonMapper.INSTANCE.timezoneToTimeZoneResponse(TimeZone.getTimeZone(
								"Asia/Ho_Chi_Minh")))
						.dateFormat("YYYY-MM-dd")
						.timeFormat("HH:mm:ss")
						.currency(CurrencyResponse.builder()
								.currencyCode("SGD")
								.build())
						.defaultDomain("http://tenancy-cportal.com")
						.build())
				.build()));

		final var wallet = walletDomainService.createWallet(CreateWalletCommandDto.builder()
				.tenantId(tenantId)
				.customerId(customerId)
				.type(WalletType.DEPOSIT)
				.build());

		final var transaction1 = walletDomainService.addBalance(AddBalanceCommand.builder()
				.tenantId(tenantId)
				.walletId(wallet.getId()
						.getValue())
				.amount(new Money(1000))
				.build(), NotificationControlCommand.builder()
						.isSkipNotification(true)
						.build());

		final var transaction2 = walletDomainService.deductBalance(DeductBalanceCommand.builder()
				.tenantId(tenantId)
				.walletId(wallet.getId()
						.getValue())
				.amount(new Money(1000))
				.build());

		when(userRepository.existsById(eq(customerId))).thenReturn(false);
		walletDomainService.delete(wallet.getId()
				.getValue(), tenantId);

		final Map<String, String> requestParamMap = new HashMap<>();

		requestParamMap.put("filter.customerIds", String.valueOf(customerId));
		requestParamMap.put("size", "10");
		requestParamMap.put("page", "0");
		requestParamMap.put("sortDirection", "ASC");
		requestParamMap.put("sortFields", "id");

		// Act & Assert
		webClient.get()

				.uri(uriBuilder -> {
					final LinkedMultiValueMap<String, String> requestParams = new LinkedMultiValueMap<>(requestParamMap
							.entrySet()
							.stream()
							.filter(it -> StringUtils.isNotBlank(it.getValue()))
							.collect(Collectors.toMap(Map.Entry::getKey, it -> List.of(it.getValue()))));

					uriBuilder.path("/api/wallet/transactions");
					uriBuilder.queryParams(requestParams);
					return uriBuilder.build();
				})
				.headers(httpHeaders -> httpHeaders.setAll(getHttpHeaders(tenantId)))
				.exchange()
				.expectStatus()
				.isOk()
				.expectBody(Paging.class)
				.consumeWith(response -> {
					Assertions.assertNotNull(response.getResponseBody());

					final var responses = Optional.ofNullable(response.getResponseBody())
							.map(Paging::getContent)
							.map(ls -> (List<WalletTransactionResponse>) ls.stream()
									.map(it -> objectMapper.convertValue(it, WalletTransactionResponse.class))
									.toList())
							.orElseGet(List::of);

					Assertions.assertEquals(2, responses.size());

					Assertions.assertEquals(transaction1.getId()
							.getValue()
							.toString(), responses.get(0)
									.getTransactionId());
					Assertions.assertEquals(transaction2.getId()
							.getValue()
							.toString(), responses.get(1)
									.getTransactionId());

				});

	}

	@Order(1)
	@Test
	void testQueryTransactionsByMigrationIdWhenValidThenReturnWalletResponse() {
		// Arrange
		final var tenantId = 1L;
		final var customerId = 2505231107002L;

		when(userClient.getUserProfile(eq(customerId), any())).thenReturn(UserResponse.builder()
				.id(String.valueOf(customerId))
				.firstName("first-%s".formatted(customerId))
				.lastName("last-%s".formatted(customerId))
				.fullName("%s-%s".formatted("first-%s".formatted(customerId), "last-%s".formatted(customerId)))
				.email("<EMAIL>".formatted(customerId))
				.build());
		when(tenantClient.getTenant(anyLong())).thenReturn(ResponseEntity.ok(TenantResponse.builder()
				.tenantId(tenantId)
				.realmId("realm-001")
				.settings(TenantSettingsResponse.builder()
						.timeZone(MapstructCommonMapper.INSTANCE.timezoneToTimeZoneResponse(TimeZone.getTimeZone(
								"Asia/Ho_Chi_Minh")))
						.dateFormat("YYYY-MM-dd")
						.timeFormat("HH:mm:ss")
						.currency(CurrencyResponse.builder()
								.currencyCode("SGD")
								.build())
						.defaultDomain("http://tenancy-cportal.com")
						.build())
				.build()));

		final var wallet = walletDomainService.createWallet(CreateWalletCommandDto.builder()
				.tenantId(tenantId)
				.customerId(customerId)
				.type(WalletType.DEPOSIT)
				.build());

		final var transaction1 = walletDomainService.addBalance(AddBalanceCommand.builder()
				.tenantId(tenantId)
				.walletId(wallet.getId()
						.getValue())
				.amount(new Money(1000))
				.migrationId("migration-id-2505231107002-1")
				.build(), NotificationControlCommand.builder()
						.isSkipNotification(true)
						.build());

		walletDomainService.addBalance(AddBalanceCommand.builder()
				.tenantId(tenantId)
				.walletId(wallet.getId()
						.getValue())
				.amount(new Money(1000))
				.migrationId("migration-id-2505231107002-2")
				.build(), NotificationControlCommand.builder()
						.isSkipNotification(true)
						.build());

		final Map<String, String> requestParamMap = new HashMap<>();

		requestParamMap.put("filter.migrationId", transaction1.getMigrationId());
		requestParamMap.put("size", "10");
		requestParamMap.put("page", "0");
		requestParamMap.put("sortDirection", "ASC");
		requestParamMap.put("sortFields", "id");

		// Act & Assert
		webClient.get()

				.uri(uriBuilder -> {
					final LinkedMultiValueMap<String, String> requestParams = new LinkedMultiValueMap<>(requestParamMap
							.entrySet()
							.stream()
							.filter(it -> StringUtils.isNotBlank(it.getValue()))
							.collect(Collectors.toMap(Map.Entry::getKey, it -> List.of(it.getValue()))));

					uriBuilder.path("/api/wallet/transactions");
					uriBuilder.queryParams(requestParams);
					return uriBuilder.build();
				})
				.headers(httpHeaders -> httpHeaders.setAll(getHttpHeaders(tenantId)))
				.exchange()
				.expectStatus()
				.isOk()
				.expectBody(Paging.class)
				.consumeWith(response -> {
					Assertions.assertNotNull(response.getResponseBody());

					final var responses = Optional.ofNullable(response.getResponseBody())
							.map(Paging::getContent)
							.map(ls -> (List<WalletTransactionResponse>) ls.stream()
									.map(it -> objectMapper.convertValue(it, WalletTransactionResponse.class))
									.toList())
							.orElseGet(List::of);

					Assertions.assertEquals(1, responses.size());
					Assertions.assertEquals(transaction1.getId()
							.getValue()
							.toString(), responses.getFirst()
									.getTransactionId());
					Assertions.assertEquals(transaction1.getMigrationId(), responses.getFirst()
							.getMigrationId());
				});

	}

	private Map<String, String> getHttpHeaders(long tenantId) {
		Map<String, String> map = new HashMap<>();
		map.put(PacificRestConstants.PlatformHeader.HEADER_X_REQUEST_ID, Long.toString(new Random().nextLong()));
		map.put(PacificRestConstants.PlatformHeader.HEADER_X_TENANT_ID, Long.toString(tenantId));
		return map;
	}
}
