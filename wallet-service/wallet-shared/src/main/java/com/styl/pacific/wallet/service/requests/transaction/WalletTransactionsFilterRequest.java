/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.requests.transaction;

import com.styl.pacific.wallet.service.enums.TransactionCategory;
import com.styl.pacific.wallet.service.enums.TransactionType;
import java.math.BigInteger;
import java.util.List;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Builder
@Data
public class WalletTransactionsFilterRequest {

	private final List<String> ids;

	private final List<String> customerIds;

	private final List<String> paymentTransactionIds;

	private final String walletId;

	private final BigInteger fromAmount;

	private final BigInteger toAmount;

	private final Long fromTime;

	private final Long toTime;

	private final List<TransactionType> transactionTypes;

	private final List<TransactionCategory> transactionCategories;

	private final String migrationId;
}
