/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.requests.wallet;

import com.styl.pacific.common.validator.currency.Currency;
import com.styl.pacific.common.validator.dateformat.Date;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.math.BigInteger;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreditMultiWalletRequest {
	@NotBlank
	private String paymentSessionId;

	@NotNull
	private BigInteger amount;

	@NotBlank
	@Currency
	private String currency;

	private String deviceId;

	private String description;

	@Builder.Default
	private Boolean acceptDelegatedWallet = Boolean.TRUE;

	@Date(message = "requestedAt must be a valid format: yyyy-MM-dd")
	String requestedAt;

	private String cardId;

	@NotEmpty
	private List<String> walletIds;
}
