/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.entity;

import com.styl.pacific.domain.entity.AggregateRoot;
import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.WalletSettingId;
import java.time.Instant;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public class WalletSetting extends AggregateRoot<WalletSettingId> {

	private TenantId tenantId;

	private List<Money> presetAmounts;

	private String currency;

	private Money minTopupAmount;

	private Money maxTopupAmount;

	private Money maxDailyOfflineSpendPerDeviceAmount;

	private Instant createdAt;

	private Instant updatedAt;

	private Instant deletedAt;

	private WalletSetting(Builder builder) {
		setId(builder.id);
		setTenantId(builder.tenantId);
		presetAmounts = builder.presetAmounts;
		currency = builder.currency;
		minTopupAmount = builder.minTopupAmount;
		maxTopupAmount = builder.maxTopupAmount;
		maxDailyOfflineSpendPerDeviceAmount = builder.maxDailyOfflineSpendPerDeviceAmount;
		createdAt = builder.createdAt;
		updatedAt = builder.updatedAt;
		deletedAt = builder.deletedAt;
	}

	public static final class Builder {
		private WalletSettingId id;
		private TenantId tenantId;
		private List<Money> presetAmounts;
		private String currency;
		private Money minTopupAmount;
		private Money maxTopupAmount;
		private Money maxDailyOfflineSpendPerDeviceAmount;
		private Instant createdAt;
		private Instant updatedAt;
		private Instant deletedAt;

		private Builder() {
		}

		public static Builder builder() {
			return new Builder();
		}

		public Builder id(WalletSettingId id) {
			this.id = id;
			return this;
		}

		public Builder tenantId(TenantId tenantId) {
			this.tenantId = tenantId;
			return this;
		}

		public Builder presetAmounts(List<Money> tags) {
			this.presetAmounts = tags;
			return this;
		}

		public Builder currency(String currency) {
			this.currency = currency;
			return this;
		}

		public Builder minTopupAmount(Money minTopupAmount) {
			this.minTopupAmount = minTopupAmount;
			return this;
		}

		public Builder maxTopupAmount(Money maxTopupAmount) {
			this.maxTopupAmount = maxTopupAmount;
			return this;
		}

		public Builder maxDailyOfflineSpendPerDeviceAmount(Money maxDailyOfflineSpendPerDeviceAmount) {
			this.maxDailyOfflineSpendPerDeviceAmount = maxDailyOfflineSpendPerDeviceAmount;
			return this;
		}

		public Builder createdAt(Instant createdAt) {
			this.createdAt = createdAt;
			return this;
		}

		public Builder updatedAt(Instant updatedAt) {
			this.updatedAt = updatedAt;
			return this;
		}

		public Builder deletedAt(Instant deletedAt) {
			this.deletedAt = deletedAt;
			return this;
		}

		public WalletSetting build() {
			return new WalletSetting(this);
		}
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (!(o instanceof WalletSetting that))
			return false;
		if (!super.equals(o))
			return false;
		return Objects.equals(tenantId, that.tenantId) && Objects.equals(presetAmounts, that.presetAmounts) && Objects
				.equals(currency, that.currency) && Objects.equals(minTopupAmount, that.minTopupAmount) && Objects
						.equals(maxTopupAmount, that.maxTopupAmount) && Objects.equals(
								maxDailyOfflineSpendPerDeviceAmount, that.maxDailyOfflineSpendPerDeviceAmount)
				&& Objects.equals(createdAt, that.createdAt) && Objects.equals(updatedAt, that.updatedAt) && Objects
						.equals(deletedAt, that.deletedAt);
	}

	@Override
	public int hashCode() {
		return Objects.hash(super.hashCode(), tenantId, presetAmounts, currency, minTopupAmount, maxTopupAmount,
				maxDailyOfflineSpendPerDeviceAmount, createdAt, updatedAt, deletedAt);
	}
}
