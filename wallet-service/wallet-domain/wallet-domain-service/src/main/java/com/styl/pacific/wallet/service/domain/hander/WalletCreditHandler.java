/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.domain.hander;

import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.wallet.service.domain.dto.walletentry.FindWalletEntriesQuery;
import com.styl.pacific.wallet.service.domain.helper.WalletDelegateSettingHelper;
import com.styl.pacific.wallet.service.domain.helper.WalletHelper;
import com.styl.pacific.wallet.service.domain.helper.WalletSpendingLimitSettingHelper;
import com.styl.pacific.wallet.service.domain.helper.WalletTransactionHelper;
import com.styl.pacific.wallet.service.domain.mapper.WalletTransactionDataMapper;
import com.styl.pacific.wallet.service.entity.CreateCreditOfflineTransactionCommand;
import com.styl.pacific.wallet.service.entity.CreditDto;
import com.styl.pacific.wallet.service.entity.Wallet;
import com.styl.pacific.wallet.service.entity.WalletDelegateSetting;
import com.styl.pacific.wallet.service.entity.WalletEntry;
import com.styl.pacific.wallet.service.entity.WalletTransaction;
import com.styl.pacific.wallet.service.enums.TransactionCategory;
import com.styl.pacific.wallet.service.enums.TransactionType;
import com.styl.pacific.wallet.service.exception.InsufficientBalanceException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */

@Component
@AllArgsConstructor
public class WalletCreditHandler {

	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	private final WalletHelper walletHelper;

	private final WalletTransactionHelper walletTransactionHelper;
	private final WalletDelegateSettingHelper walletDelegateSettingHelper;
	private final WalletSpendingLimitSettingHelper walletSpendingLimitSettingHelper;

	@Transactional
	public WalletTransaction handle(CreditDto creditDto) {

		LocalDate requestedDate = Objects.nonNull(creditDto.getRequestedAt())
				? LocalDate.parse(creditDto.getRequestedAt())
				: Instant.now()
						.atZone(ZoneId.of("UTC"))
						.toLocalDate();

		// get and locking wallet to consistent
		Wallet walletExisting = walletHelper.getWalletAndLockingOrThrowNotFound(creditDto.getTargetWalletId(), creditDto
				.getTenantId());

		// choose subWallet or parentWallet to pay
		Wallet targetWallet = validateAndChooseWalletToPay(walletExisting, creditDto);

		// validate spending limit of Individual Wallet
		walletTransactionHelper.validateIndividualWalletSpendingLimit(targetWallet, creditDto.getAmount(),
				requestedDate);

		List<WalletEntry> walletEntriesExisting = walletHelper.findAvailableWalletEntry(FindWalletEntriesQuery.builder()
				.tenantId(targetWallet.getTenantId()
						.getValue())
				.walletId(targetWallet.getId()
						.getValue())
				.customerId(targetWallet.getCustomerId()
						.getValue())
				.build());

		// validate entry
		walletHelper.validateWalletEntries(walletEntriesExisting, creditDto.getAmount());

		// keep old balance
		Money oldBalance = new Money(targetWallet.getBalance());

		// deduct amount Wallet Entry, just return items changed
		List<WalletEntry> changedWalletEntries = targetWallet.deductAmountWalletEntry(walletEntriesExisting, creditDto
				.getAmount());

		// deduct amount Wallet Entry
		targetWallet.deductBalance(creditDto.getAmount());

		// just save items changed
		walletHelper.saveWalletEntry(changedWalletEntries);
		walletHelper.saveWallet(targetWallet);

		// update Spending Limit
		walletSpendingLimitSettingHelper.addSpending(walletExisting.getTenantId()
				.getValue(), walletExisting.getId()
						.getValue(), new Money(creditDto.getAmount()), requestedDate);

		// create wallet transaction
		return walletTransactionHelper.createWalletTransaction(WalletTransactionDataMapper.INSTANCE
				.toCreateWalletTransactionCommand(targetWallet, creditDto, oldBalance, TransactionType.CREDIT,
						TransactionCategory.PURCHASE));

	}

	private Wallet validateAndChooseWalletToPay(Wallet wallet, CreditDto creditDto) {

		// this wallet can be funded wallet or deposit wallet
		// if it is funded wallet, wallet status must be active
		// if it is deposit wallet, wallet status may be active or inactive
		// + if deposit wallet is active--> allow pay by this wallet --> delegate
		// + if deposit wallet is inactive--> dont allow pay by this wallet--> just delegate

		// may be deposit wallet--> check status active
		if (wallet.isSufficient(creditDto.getAmount()) && wallet.isActive()) {
			return wallet;
		}

		if (!creditDto.isAcceptDelegatedWallet()) {
			logger.info("Do not allow wallet delegated with customerId {}", wallet.getCustomerId()
					.getValue());
			throw new InsufficientBalanceException("Balance is insufficient");
		}

		// find delegate
		WalletDelegateSetting setting = walletDelegateSettingHelper.findDelegateSettingExisting(wallet.getTenantId()
				.getValue(), wallet.getCustomerId()
						.getValue(), wallet.getId()
								.getValue());

		if (Objects.isNull(setting)) {
			logger.info("There is no delegate setting for subCustomerId: {}", wallet.getCustomerId()
					.getValue());
			throw new InsufficientBalanceException("Balance is insufficient");
		}

		Wallet parentWallet = walletHelper.getWalletAndLockingOrThrowNotFound(setting.getParentWalletId()
				.getValue(), setting.getTenantId()
						.getValue());

		if (!parentWallet.isSufficient(creditDto.getAmount())) {
			logger.info("Balance is insufficient with parentWalletId: {}", wallet.getId()
					.getValue());
			throw new InsufficientBalanceException("Balance is insufficient");
		}
		return parentWallet;
	}

	@Transactional
	public WalletTransaction handleOffline(CreateCreditOfflineTransactionCommand command) {
		// skip spending limit + dont delegate
		// get and locking wallet deposit, dont care status
		Wallet wallet = walletHelper.getDepositWalletAndLockingOrThrowNotFound(command.getUserId(), command
				.getTenantId());

		// keep old balance
		Money oldBalance = new Money(wallet.getBalance());

		List<WalletEntry> walletEntriesExisting = walletHelper.findAvailableWalletEntry(FindWalletEntriesQuery.builder()
				.tenantId(wallet.getTenantId()
						.getValue())
				.walletId(wallet.getId()
						.getValue())
				.customerId(wallet.getCustomerId()
						.getValue())
				.build());

		// deduct amount Wallet Entry, just return items changed
		List<WalletEntry> changedWalletEntries = wallet.deductAmountWalletEntryWithOffline(walletEntriesExisting,
				command.getAmount());

		// deduct amount Wallet Entry
		wallet.deductBalance(command.getAmount());

		// just save items changed
		walletHelper.saveWalletEntry(changedWalletEntries);
		walletHelper.saveWallet(wallet);

		// create wallet transaction
		return walletTransactionHelper.createWalletTransaction(WalletTransactionDataMapper.INSTANCE
				.toCreateOfflineWalletTransactionCommand(wallet, command, oldBalance, TransactionType.CREDIT,
						TransactionCategory.PURCHASE));

	}
}
