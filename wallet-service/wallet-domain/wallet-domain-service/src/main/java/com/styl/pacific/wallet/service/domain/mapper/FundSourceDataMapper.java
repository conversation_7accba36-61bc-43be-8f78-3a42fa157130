/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.domain.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.domain.valueobject.FundSourceId;
import com.styl.pacific.domain.valueobject.FundSourceTopupSchedulerId;
import com.styl.pacific.wallet.service.domain.dto.fundsource.CreateFundSourceCommand;
import com.styl.pacific.wallet.service.domain.dto.fundsource.CreateFundSourceTopupSchedulerCommand;
import com.styl.pacific.wallet.service.domain.dto.fundsource.UpdateFundSourceCommand;
import com.styl.pacific.wallet.service.domain.dto.fundsource.UpdateFundSourceTopupSchedulerCommand;
import com.styl.pacific.wallet.service.entity.FundSource;
import com.styl.pacific.wallet.service.entity.FundSourceTopupHistory;
import com.styl.pacific.wallet.service.entity.FundSourceTopupScheduler;
import com.styl.pacific.wallet.service.enums.FundSourceStatus;
import com.styl.pacific.wallet.service.enums.FundSourceTopupStatus;
import java.time.Instant;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, uses = { WalletDataCommonMapper.class, MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface FundSourceDataMapper {

	FundSourceDataMapper INSTANCE = Mappers.getMapper(FundSourceDataMapper.class);

	@Mapping(target = "updatedAt", ignore = true)
	@Mapping(target = "deletedAt", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "tenantId", qualifiedByName = "longToTenantId")
	FundSource createFundSourceCommandToFundSource(CreateFundSourceCommand command, FundSourceId id);

	@Mapping(target = "updatedAt", ignore = true)
	@Mapping(target = "deletedAt", ignore = true)
	@Mapping(target = "createdAt", source = "fundSourceExisting.createdAt")
	@Mapping(target = "tenantId", source = "fundSourceExisting.tenantId")
	@Mapping(target = "name", source = "command.name")
	@Mapping(target = "description", source = "command.description")
	@Mapping(target = "status", source = "command.status")
	@Mapping(target = "fundExpiresOn", source = "command.fundExpiresOn")
	@Mapping(target = "fundExpiresInMs", source = "command.fundExpiresInMs")
	FundSource updateFundSourceCommandToFundSource(UpdateFundSourceCommand command, FundSource fundSourceExisting);

	@Mapping(target = "status", source = "status")
	FundSource withStatusToFundSource(FundSource existing, FundSourceStatus status);

	@Mapping(target = "nextTopupDate", source = "nextTopupDate")
	FundSourceTopupScheduler updateTopupScheduler(FundSourceTopupScheduler scheduler, Instant nextTopupDate);

	@Mapping(target = "status", source = "status")
	FundSourceTopupHistory updateStatusFundSourceTopupHistory(FundSourceTopupHistory history,
			FundSourceTopupStatus status);

	@Mapping(target = "lastTopupDate", source = "lastTopupDate")
	FundSourceTopupScheduler updateLastTopupScheduler(FundSourceTopupScheduler scheduler, Instant lastTopupDate);

	@Mapping(target = "updatedAt", ignore = true)
	@Mapping(target = "startTopupDate", source = "command.startTopupDate")
	@Mapping(target = "endTopupDate", source = "command.endTopupDate")
	@Mapping(target = "nextTopupDate", ignore = true)
	@Mapping(target = "lastTopupDate", ignore = true)
	@Mapping(target = "deletedAt", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "amount", source = "command.topupAmount", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "fundSourceId", qualifiedByName = "longToFundSourceId")
	@Mapping(target = "lastSchedulerHistoryId", ignore = true)
	FundSourceTopupScheduler createFundSourceTopupSchedulerCommandToFundSourceTopupScheduler(
			CreateFundSourceTopupSchedulerCommand command, FundSourceTopupSchedulerId id);

	@Mapping(target = "startTopupDate", source = "command.startTopupDate")
	@Mapping(target = "endTopupDate", source = "command.endTopupDate")
	@Mapping(target = "amount", source = "command.topupAmount", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "topupFrequency", source = "command.topupFrequency")
	@Mapping(target = "currency", source = "fundSourceTopupScheduler.currency")
	@Mapping(target = "tenantId", source = "fundSourceTopupScheduler.tenantId")
	@Mapping(target = "id", source = "fundSourceTopupScheduler.id")
	@Mapping(target = "fundSourceId", source = "fundSourceTopupScheduler.fundSourceId")
	@Mapping(target = "nextTopupDate", source = "fundSourceTopupScheduler.nextTopupDate")
	FundSourceTopupScheduler updateFundSourceTopupSchedulerCommandToFundSourceTopupScheduler(
			UpdateFundSourceTopupSchedulerCommand command, FundSourceTopupScheduler fundSourceTopupScheduler);
}
