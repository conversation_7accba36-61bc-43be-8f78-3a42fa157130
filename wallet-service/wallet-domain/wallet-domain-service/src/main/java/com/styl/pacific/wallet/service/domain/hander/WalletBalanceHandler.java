/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.domain.hander;

import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.wallet.service.domain.dto.wallet.AddBalanceCommand;
import com.styl.pacific.wallet.service.domain.dto.wallet.DeductBalanceCommand;
import com.styl.pacific.wallet.service.domain.dto.walletentry.FindWalletEntriesQuery;
import com.styl.pacific.wallet.service.domain.helper.WalletHelper;
import com.styl.pacific.wallet.service.domain.helper.WalletTransactionHelper;
import com.styl.pacific.wallet.service.domain.mapper.WalletDataMapper;
import com.styl.pacific.wallet.service.domain.mapper.WalletTransactionDataMapper;
import com.styl.pacific.wallet.service.entity.Wallet;
import com.styl.pacific.wallet.service.entity.WalletEntry;
import com.styl.pacific.wallet.service.entity.WalletTransaction;
import com.styl.pacific.wallet.service.enums.TransactionCategory;
import com.styl.pacific.wallet.service.enums.TransactionType;
import com.styl.pacific.wallet.service.enums.WalletEntryStatus;
import java.util.List;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */

@Component
@AllArgsConstructor
public class WalletBalanceHandler {

	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	private final WalletHelper walletHelper;

	private final WalletTransactionHelper walletTransactionHelper;

	@Transactional
	public WalletTransaction addBalance(AddBalanceCommand command) {

		Wallet wallet = walletHelper.getWalletAndLockingOrThrowNotFound(command.getWalletId(), command.getTenantId());

		// keep old balance
		Money oldBalance = new Money(wallet.getBalance());

		final var hasDebt = wallet.getBalance()
				.isLessThanZero();
		final var debtAmount = hasDebt
				? new Money(Math.abs(wallet.getBalance()
						.getAmount()
						.longValue()))
				: Money.ZERO;
		final var addingAmount = command.getAmount()
				.subtract(debtAmount);

		if (addingAmount.isGreaterThanZero()) {
			// create wallet entries
			walletHelper.createWalletEntry(WalletDataMapper.INSTANCE.toCreateWalletEntryCommand(wallet, addingAmount,
					WalletEntryStatus.AVAILABLE, command.getExpiresOn()));
		}

		// add balance and save wallet
		wallet.addAmount(command.getAmount());
		walletHelper.saveWallet(wallet);

		// create transaction
		return walletTransactionHelper.createWalletTransaction(WalletTransactionDataMapper.INSTANCE
				.toCreateWalletTransactionCommand(wallet, command.getAmount(), command.getDescription(), oldBalance,
						TransactionType.DEBIT, TransactionCategory.DEPOSIT, command.getMigrationId()));
	}

	@Transactional
	public WalletTransaction deductBalance(DeductBalanceCommand command) {

		Wallet wallet = walletHelper.getWalletAndLockingOrThrowNotFound(command.getWalletId(), command.getTenantId());

		// get wallet entries
		List<WalletEntry> walletEntries = walletHelper.findAvailableWalletEntry(FindWalletEntriesQuery.builder()
				.tenantId(wallet.getTenantId()
						.getValue())
				.walletId(wallet.getId()
						.getValue())
				.customerId(wallet.getCustomerId()
						.getValue())
				.build());

		// keep old balance
		Money oldBalance = new Money(wallet.getBalance());

		// deduct wallet
		wallet.deductBalance(command.getAmount());

		// deduct entries, just return these entries changed
		List<WalletEntry> changedWalletEntries = wallet.deductAmountWalletEntry(walletEntries, command.getAmount());

		// save wallet + entries
		walletHelper.saveWallet(wallet);
		walletHelper.saveWalletEntry(changedWalletEntries);

		// create transaction
		return walletTransactionHelper.createWalletTransaction(WalletTransactionDataMapper.INSTANCE
				.toCreateWalletTransactionCommand(wallet, command.getAmount(), command.getDescription(), oldBalance,
						TransactionType.CREDIT, TransactionCategory.WITHDRAW, null));
	}
}
