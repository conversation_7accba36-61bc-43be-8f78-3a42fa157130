/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.domain.dto.wallet;

import com.styl.pacific.wallet.service.enums.WalletStatus;
import com.styl.pacific.wallet.service.enums.WalletType;
import java.util.List;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Builder
@Data
public class WalletsFilterQuery {

	private final List<Long> ids;

	private final Long tenantId;

	private final String name;

	private final Long customerId;

	private final List<Long> customerIds;

	private final Long sourceFundId;

	private final List<WalletStatus> statuses;

	private final List<WalletType> types;

}
