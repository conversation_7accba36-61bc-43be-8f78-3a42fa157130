/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.domain.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.wallet.service.domain.dto.walletsetting.SaveWalletSettingCommand;
import com.styl.pacific.wallet.service.entity.WalletSetting;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, uses = { WalletDataCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface WalletSettingMapper {
	WalletSettingMapper INSTANCE = Mappers.getMapper(WalletSettingMapper.class);

	@Mapping(target = "tenantId", source = "setting.tenantId")
	@Mapping(target = "currency", source = "command.currency")
	@Mapping(target = "maxTopupAmount", source = "command.maxTopupAmount", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "minTopupAmount", source = "command.minTopupAmount", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "presetAmounts", source = "command.presetAmounts", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "maxDailyOfflineSpendPerDeviceAmount", source = "command.maxDailyOfflineSpendPerDeviceAmount", qualifiedByName = "bigIntegerToMoney")
	WalletSetting saveWalletCommandToWalletSetting(SaveWalletSettingCommand command, WalletSetting setting);

	@Mapping(target = "updatedAt", ignore = true)
	@Mapping(target = "presetAmounts", ignore = true)
	@Mapping(target = "id", ignore = true)
	@Mapping(target = "deletedAt", ignore = true)
	@Mapping(target = "currency", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "maxTopupAmount", expression = "java(Money.ZERO)")
	@Mapping(target = "minTopupAmount", expression = "java(Money.ZERO)")
	@Mapping(target = "maxDailyOfflineSpendPerDeviceAmount", expression = "java(Money.ZERO)")
	@Mapping(target = "tenantId", qualifiedByName = "longToTenantId")
	WalletSetting toWalletSettingDefault(Long tenantId);
}
