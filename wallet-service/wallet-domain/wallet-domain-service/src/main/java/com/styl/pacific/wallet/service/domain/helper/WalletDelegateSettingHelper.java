/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.domain.helper;

import com.styl.pacific.wallet.service.domain.dto.walletsetting.GetWalletDelegateSettingQuery;
import com.styl.pacific.wallet.service.domain.mapper.WalletDelegateSettingMapper;
import com.styl.pacific.wallet.service.domain.output.repository.WalletDelegateSettingRepository;
import com.styl.pacific.wallet.service.entity.WalletDelegateSetting;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class WalletDelegateSettingHelper {
	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	private final WalletDelegateSettingRepository walletDelegateSettingRepository;

	public WalletDelegateSetting getWalletDelegateSettingOrGetDefaultIfNotFound(GetWalletDelegateSettingQuery query) {
		return walletDelegateSettingRepository.getWalletDelegateSetting(query)
				.orElse(WalletDelegateSettingMapper.INSTANCE.toWalletDelegateSettingDefault(query.getTenantId()));
	}

	public WalletDelegateSetting findDelegateSettingExisting(Long tenantId, Long subCustomerId, Long subWalletId) {
		return walletDelegateSettingRepository.getWalletDelegateSetting(GetWalletDelegateSettingQuery.builder()
				.tenantId(tenantId)
				.subCustomerId(subCustomerId)
				.subWalletId(subWalletId)
				.build())
				.orElse(null);
	}
}
