/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.loadtest.order.apis.cash;

import com.styl.pacific.order.service.shared.http.order.v1.request.place.OrderLineItemRequest;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CoreGatewayOrderCashPaymentConfigProperties {
	private String tenantId;
	private String orderNumber;
	private String storeId;

	private String preOrderId;
	private String mealTimeId;
	private String staffCode;
	private String staffName;
	private String customerId;
	private String customerEmail;
	private String customerName;
	private List<OrderLineItemRequest> lineItems;

	private String note;

	private String taxName;
	private BigDecimal taxRate;
	private Boolean taxInclude;
	private BigInteger taxAmount;

	private BigInteger subtotalAmount;
	private BigInteger discountAmount;
	private BigInteger totalAmount;

	private String cashPaymentMethodId;
}
