/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.loadtest.order.apis.cash.mapper;

import static com.styl.pacific.loadtest.order.apis.cash.DeviceOrderCashPaymentFlowApi.LOAD_TEST_SEQUENCER;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.loadtest.order.apis.cash.DeviceOrderCashPaymentConfigProperties;
import com.styl.pacific.order.service.shared.http.order.v1.request.place.CreateOrderRequest;
import com.styl.pacific.order.service.shared.http.order.v1.request.place.OrderDetailRequest;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.Instant;
import java.time.LocalTime;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface CreateDeviceOrderCashManifestRequestMapper {
	CreateDeviceOrderCashManifestRequestMapper INSTANCE = Mappers.getMapper(
			CreateDeviceOrderCashManifestRequestMapper.class);

	@Mapping(target = "payment.paymentMethodId", source = "deviceOrderCashPaymentConfigProperties.cashPaymentMethodId")
	@Mapping(target = "order", source = "deviceOrderCashPaymentConfigProperties")
	CreateOrderRequest toPlaceOrderRequestByCashPayment(
			DeviceOrderCashPaymentConfigProperties deviceOrderCashPaymentConfigProperties);

	@AfterMapping
	default void afterMappingPlaceOrderRequest(
			DeviceOrderCashPaymentConfigProperties deviceOrderCashPaymentConfigProperties,
			@MappingTarget OrderDetailRequest.OrderDetailRequestBuilder builder) {
		final var localTime = LocalTime.now();
		builder.orderNumber("ORDLOAD-%s%s%s".formatted(localTime.getHour(), localTime.getMinute(), LOAD_TEST_SEQUENCER
				.getAndIncrement()));
		builder.subtotalAmount(calculateSubtotalAmount(deviceOrderCashPaymentConfigProperties));
		builder.taxAmount(calculateTaxAmount(deviceOrderCashPaymentConfigProperties));
		builder.totalAmount(calculateTotalAmount(deviceOrderCashPaymentConfigProperties));
		builder.note(generateNote(deviceOrderCashPaymentConfigProperties));
	}

	default BigInteger calculateSubtotalAmount(
			DeviceOrderCashPaymentConfigProperties deviceOrderCashPaymentConfigProperties) {

		final var subTotalAmount = deviceOrderCashPaymentConfigProperties.getLineItems()
				.stream()
				.map(it -> it.totalAmount()
						.multiply(BigInteger.valueOf(it.quantity())))
				.reduce(BigInteger.ZERO, BigInteger::add);

		return deviceOrderCashPaymentConfigProperties.getTaxInclude()
				? (new Money(subTotalAmount).divide(BigDecimal.ONE.add(deviceOrderCashPaymentConfigProperties
						.getTaxRate()))).getAmount()
				: subTotalAmount;
	}

	default BigInteger calculateTaxAmount(
			DeviceOrderCashPaymentConfigProperties deviceOrderCashPaymentConfigProperties) {
		final var subtotalAmount = calculateSubtotalAmount(deviceOrderCashPaymentConfigProperties);
		return new Money(subtotalAmount.subtract(deviceOrderCashPaymentConfigProperties.getDiscountAmount())).multiply(
				deviceOrderCashPaymentConfigProperties.getTaxRate())
				.getAmount();
	}

	default BigInteger calculateTotalAmount(
			DeviceOrderCashPaymentConfigProperties deviceOrderCashPaymentConfigProperties) {
		return new Money(calculateTaxAmount(deviceOrderCashPaymentConfigProperties)).add(new Money(
				calculateSubtotalAmount(deviceOrderCashPaymentConfigProperties)))
				.subtract(new Money(deviceOrderCashPaymentConfigProperties.getDiscountAmount()))
				.getAmount();
	}

	default String generateNote(DeviceOrderCashPaymentConfigProperties deviceOrderCashPaymentConfigProperties) {
		return "Load Test: %s: %s. ".formatted(Instant.now()
				.toString(), deviceOrderCashPaymentConfigProperties.getNote());
	}

}