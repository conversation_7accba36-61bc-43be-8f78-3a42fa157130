/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.loadtest.commons.objectmapper;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.io.IOException;
import java.time.Instant;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;

@NoArgsConstructor
public class LoadTestMapper {
	public static final ObjectMapper YAML_MAPPER;
	public static final ObjectMapper JSON_MAPPER;

	static {
		YAML_MAPPER = new ObjectMapper(new YAMLFactory());

		final var simpleModule = new SimpleModule();
		simpleModule.addDeserializer(Instant.class, new JsonDeserializer<>() {
			@Override
			public Instant deserialize(JsonParser p, DeserializationContext context) throws IOException {

				if (p.currentToken() == null || !StringUtils.isNumeric(p.getValueAsString())) {
					return null;
				}

				return Instant.ofEpochMilli(p.getValueAsLong());
			}
		});
		simpleModule.addSerializer(Instant.class, new JsonSerializer<>() {
			@SneakyThrows
			@Override
			public void serialize(Instant instant, JsonGenerator generator, SerializerProvider provider) {
				if (instant != null) {
					generator.writeNumber(instant.toEpochMilli());
				} else {
					generator.writeNull();
				}
			}
		});
		JSON_MAPPER = new ObjectMapper();
		JSON_MAPPER.registerModules(simpleModule, new JavaTimeModule());
	}
}
