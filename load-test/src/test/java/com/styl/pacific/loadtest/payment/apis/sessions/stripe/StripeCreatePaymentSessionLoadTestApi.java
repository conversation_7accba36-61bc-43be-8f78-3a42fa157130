/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.loadtest.payment.apis.sessions.stripe;

import static io.gatling.javaapi.core.CoreDsl.StringBody;
import static io.gatling.javaapi.core.CoreDsl.exec;
import static io.gatling.javaapi.http.HttpDsl.http;

import com.styl.pacific.common.constant.PacificRestConstants;
import com.styl.pacific.loadtest.commons.ApiConnectionsConfigProperties;
import com.styl.pacific.loadtest.commons.LoadTestApi;
import com.styl.pacific.loadtest.commons.objectmapper.LoadTestMapper;
import com.styl.pacific.loadtest.commons.utils.RandomUtils;
import com.styl.pacific.payment.shared.enums.PaymentTransactionType;
import com.styl.pacific.payment.shared.http.sessions.request.CreatePaymentSessionRequest;
import com.styl.pacific.payment.shared.http.sessions.request.stripe.StripeWebPaymentSessionParamsRequest;
import io.gatling.javaapi.core.ChainBuilder;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;

@RequiredArgsConstructor
public class StripeCreatePaymentSessionLoadTestApi implements LoadTestApi {

	private final ApiConnectionsConfigProperties apiConnectionsConfigProperties;
	private final StripePaymentSessionApiConfigProperties apiConfigProperties;

	@SneakyThrows
	@Override
	public ChainBuilder buildChain() {
		return exec(http("StripeCreatePaymentSessionApi").post("/api/payment/sessions")
				.header("Authorization", apiConnectionsConfigProperties.getUserToken())
				.header(PacificRestConstants.PlatformHeader.HEADER_X_TENANT_ID, apiConfigProperties.getTenantId()
						.toString())
				.body(StringBody(LoadTestMapper.JSON_MAPPER.writeValueAsString(CreatePaymentSessionRequest.builder()
						.paymentMethodId(apiConfigProperties.getStripePaymentMethodId())
						.description("Payment Order #Payment Method %s".formatted(apiConfigProperties
								.getStripePaymentMethodId()))
						.paymentReference("ONSITE_ORDER.100%s.loadtest%s".formatted(RandomUtils.getRandomTimestamp(),
								RandomUtils.getRandomTimestamp()))
						.transactionType(PaymentTransactionType.PURCHASE)
						.amount(apiConfigProperties.getAmount())
						.netAmount(apiConfigProperties.getNetAmount())
						.fee(apiConfigProperties.getFee())
						.customerId(apiConfigProperties.getCustomerId())
						.currencyCode(apiConfigProperties.getCurrencyCode())
						.customerEmail(apiConfigProperties.getCustomerEmail())
						.customerName(apiConfigProperties.getCustomerName())
						.systemSource(apiConfigProperties.getSystemSource())
						.processorParams(StripeWebPaymentSessionParamsRequest.builder()
								.cancelRedirectURL(apiConfigProperties.getCancelRedirectUrl())
								.successRedirectURL(apiConfigProperties.getSuccessRedirectUrl())
								.build())
						.build())))).exitHereIfFailed();

	}
}
