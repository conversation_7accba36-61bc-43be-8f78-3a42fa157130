/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.loadtest.payment.apis.settlements.nets;

import static io.gatling.javaapi.core.CoreDsl.StringBody;
import static io.gatling.javaapi.core.CoreDsl.bodyString;
import static io.gatling.javaapi.core.CoreDsl.exec;
import static io.gatling.javaapi.core.CoreDsl.jmesPath;
import static io.gatling.javaapi.http.HttpDsl.http;
import static io.gatling.javaapi.http.HttpDsl.status;

import com.styl.pacific.common.constant.PacificRestConstants;
import com.styl.pacific.domain.enums.PaymentTransactionStatus;
import com.styl.pacific.loadtest.commons.ApiConnectionsConfigProperties;
import com.styl.pacific.loadtest.commons.LoadTestApi;
import com.styl.pacific.loadtest.commons.objectmapper.LoadTestMapper;
import com.styl.pacific.loadtest.commons.utils.RandomUtils;
import com.styl.pacific.loadtest.payment.apis.sessions.nets.NetsPaymentSessionApiConfigProperties;
import com.styl.pacific.payment.shared.enums.PaymentTransactionType;
import com.styl.pacific.payment.shared.http.sessions.request.CreatePaymentSessionRequest;
import com.styl.pacific.payment.shared.http.sessions.request.nets.NetsPaymentSessionParamsRequest;
import com.styl.pacific.payment.shared.http.settlement.request.SettlePaymentSessionRequest;
import com.styl.pacific.payment.shared.http.settlement.request.nets.NetsPaymentSettlementSessionRequest;
import io.gatling.javaapi.core.ChainBuilder;
import java.time.Instant;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;

@RequiredArgsConstructor
public class NetsPaymentSettlementLoadTestApi implements LoadTestApi {

	private final ApiConnectionsConfigProperties apiConnection;
	private final NetsPaymentSessionApiConfigProperties apiConfigProperties;

	@SneakyThrows
	@Override
	public ChainBuilder buildChain() {
		return exec(http("NetsCreatePaymentSessionApi").post("/api/payment/sessions")
				.header("Authorization", apiConnection.getUserToken())
				.header(PacificRestConstants.PlatformHeader.HEADER_X_TENANT_ID, apiConfigProperties.getTenantId()
						.toString())
				.body(StringBody(LoadTestMapper.JSON_MAPPER.writeValueAsString(CreatePaymentSessionRequest.builder()
						.paymentMethodId(apiConfigProperties.getNetsPaymentMethodId())
						.description("Payment Order #Payment Method %s".formatted(apiConfigProperties
								.getNetsPaymentMethodId()))
						.paymentReference("ONSITE_ORDER.100%s.loadtest%s".formatted(RandomUtils.getRandomTimestamp(),
								RandomUtils.getRandomTimestamp()))
						.transactionType(PaymentTransactionType.PURCHASE)
						.amount(apiConfigProperties.getAmount())
						.netAmount(apiConfigProperties.getNetAmount())
						.fee(apiConfigProperties.getFee())
						.customerId(apiConfigProperties.getCustomerId())
						.currencyCode(apiConfigProperties.getCurrencyCode())
						.customerEmail(apiConfigProperties.getCustomerEmail())
						.customerName(apiConfigProperties.getCustomerName())
						.systemSource(apiConfigProperties.getSystemSource())
						.processorParams(NetsPaymentSessionParamsRequest.builder()
								.build())
						.build())))
				.check(status().is(201))
				.check(bodyString().saveAs("PaymentSessionDataResponse"))
				.check(jmesPath("paymentSessionId").saveAs("PaymentSessionDataResponse_paymentSessionId")))
				.exitHereIfFailed()
				.exec(session -> {
					System.out.println("PaymentSessionDataResponse: " + session.get("PaymentSessionDataResponse"));
					System.out.println("PaymentSessionDataResponse: " + session.get(
							"PaymentSessionDataResponse_paymentSessionId"));
					return session;
				})
				.exec(http("NetsSettlementPaymentSessionApi").post(
						"/api/payment/sessions/#{PaymentSessionDataResponse_paymentSessionId}/settle")
						.header("Authorization", apiConnection.getUserToken())
						.header(PacificRestConstants.PlatformHeader.HEADER_X_TENANT_ID, apiConfigProperties
								.getTenantId()
								.toString())
						.body(StringBody(LoadTestMapper.JSON_MAPPER.writeValueAsString(SettlePaymentSessionRequest
								.builder()
								.transactionStatus(PaymentTransactionStatus.SUCCEEDED)
								.paidAt(Instant.now())
								.settlementData(NetsPaymentSettlementSessionRequest.builder()
										.build())
								.build()))));

	}
}
