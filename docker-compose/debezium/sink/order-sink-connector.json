{"name": "order-sink-connector", "config": {"connector.class": "io.debezium.connector.jdbc.JdbcSinkConnector", "tasks.max": "1", "connection.url": "**************************************************************", "connection.username": "postgres", "connection.password": "postgres", "insert.mode": "upsert", "auto.create": "true", "delete.enabled": "true", "primary.key.mode": "record_key", "schema.evolution": "basic", "database.time_zone": "UTC", "transforms": "Reroute", "transforms.Reroute.type": "io.debezium.transforms.ByLogicalTableRouter", "transforms.Reroute.topic.regex": "([^.]+)\\.([^.]+)\\.([^.]+)\\.([^.]+)", "transforms.Reroute.topic.replacement": "$4_view", "hibernate.c3p0.idle_test_period": "300", "topics": "cdc.catalog.catalog-service.tb_category,cdc.catalog.catalog-service.tb_healthier_choice,cdc.catalog.catalog-service.tb_product,cdc.catalog.catalog-service.tb_product_image,cdc.catalog.catalog-service.tb_product_allergen,cdc.catalog.catalog-service.tb_product_nutrition,cdc.catalog.catalog-service.tb_product_option,cdc.catalog.catalog-service.tb_product_option_item"}}