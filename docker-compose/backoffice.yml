version: '3.8'
services:
  backoffice-ui:
    image: com.styl.pacific.backoffice-ui:${APPLICATION_VERSION}
    hostname: backoffice-ui
    ports:
      - "9300:80"
    volumes:
      - ./volumes/backoffice/env.js:/app/env.js
    depends_on:
      keycloak:
        condition: service_healthy
    healthcheck:
      test: "curl --fail --silent localhost:80/index.html|| exit 1"
      start_period: 10s
      interval: 5s
      retries: 15
      timeout: 5s
    networks:
      - ${GLOBAL_NETWORK:-backoffice}
