version: "3.8"
services:
  debezium:
    image: debezium/connect:${DEBEZIUM_VERSION:-latest}
    restart: always
    hostname: debezium
    depends_on:
      zookeeper:
        condition: service_healthy
      kafka-broker-1:
        condition: service_healthy
      kafka-broker-2:
        condition: service_healthy
      kafka-broker-3:
        condition: service_healthy
      postgres:
        condition: service_healthy
    ports:
      - "${DEBEZIUM_PORT}:8083"
    environment:
      BOOTSTRAP_SERVERS: kafka-broker-1:9092,kafka-broker-2:9092,kafka-broker-3:9092
      GROUP_ID: 1
      CONFIG_STORAGE_TOPIC: connect_configs
      STATUS_STORAGE_TOPIC: connect_statuses
      OFFSET_STORAGE_TOPIC: connect_offsets
      KEY_CONVERTER: org.apache.kafka.connect.json.JsonConverter
      VALUE_CONVERTER: org.apache.kafka.connect.json.JsonConverter
      ENABLE_DEBEZIUM_SCRIPTING: "true"
    healthcheck:
      test:
        [
          "CMD",
          "curl",
          "--silent",
          "--fail",
          "-X",
          "GET",
          "http://localhost:8083/connectors",
        ]
      interval: 5s
      timeout: 5s
      retries: 15
    networks:
      - ${GLOBAL_NETWORK:-debezium}
  debezium-ui:
    image: debezium/debezium-ui:${DEBEZIUM_UI_VERSION:-latest}
    restart: always
    hostname: debezium-ui
    depends_on:
      debezium:
        condition: service_healthy
    ports:
      - "${DEBEZIUM_UI_PORT}:8080"
    environment:
      KAFKA_CONNECT_URIS: http://debezium:8083
    healthcheck:
      test:
        [
          "CMD",
          "curl",
          "--silent",
          "--fail",
          "-X",
          "GET",
          "http://localhost:8080",
        ]
      interval: 5s
      timeout: 5s
      retries: 15
    networks:
      - ${GLOBAL_NETWORK:-debezium}
