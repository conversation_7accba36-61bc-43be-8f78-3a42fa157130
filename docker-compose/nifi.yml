version: '3.8'
services:
  nifi:
    image: apache/nifi:2.3.0
    hostname: ec2-47-129-235-168.ap-southeast-1.compute.amazonaws.com
    container_name: ${NIFI_HOST}
    user: "0:0"
    ports:
      - "8443:8443"
    environment:
#      NIFI_WEB_HTTP_PORT: 8443
      NIFI_WEB_HTTPS_HOST: ec2-47-129-235-168.ap-southeast-1.compute.amazonaws.com
      NIFI_WEB_HTTPS_PORT: 8443
      SINGLE_USER_CREDENTIALS_USERNAME: admin
      SINGLE_USER_CREDENTIALS_PASSWORD: Admin@123

      # Security configuration with custom certificates
      NIFI_SECURITY_KEYSTORE: /opt/nifi/nifi-current/conf/keystore.p12
      NIFI_SECURITY_KEYSTORE_TYPE: PKCS12
      NIFI_SECURITY_KEYSTORE_PASSWD: nopass

      NIFI_SECURITY_TRUSTSTORE: /opt/nifi/nifi-current/conf/truststore.p12
      NIFI_SECURITY_TRUSTSTORE_TYPE: PKCS12
      NIFI_SECURITY_TRUSTSTORE_PASSWD: nopass

    volumes:
      - "./certs/keystore.p12:/opt/nifi/nifi-current/conf/keystore.p12:ro"
      - "./certs/truststore.p12:/opt/nifi/nifi-current/conf/truststore.p12:ro"
      - pacific_nifi_conf:/opt/nifi/nifi-current/conf
      - pacific_nifi_database:/opt/nifi/nifi-current/database_repository
      - pacific_nifi_flowfile:/opt/nifi/nifi-current/flowfile_repository
      - pacific_nifi_content:/opt/nifi/nifi-current/content_repository
      - pacific_nifi_provenance:/opt/nifi/nifi-current/provenance_repository
      - pacific_nifi_state:/opt/nifi/nifi-current/state
      - pacific_nifi_logs:/opt/nifi/nifi-current/logs
      - "./volumes/nifi/libs/postgresql-42.7.5.jar:/opt/nifi/nifi-current/lib/postgresql-42.7.5.jar"
      - "./volumes/nifi/libs/mysql-connector-j-8.4.0.jar:/opt/nifi/nifi-current/lib/mysql-connector-j-8.4.0.jar"
      - "./volumes/nifi/output:/opt/nifi/nifi-current/output"
      - "../pacific-nar/target/pacific-nar-1.0.0.nar:/opt/nifi/nifi-current/lib/pacific-nar-1.0.0.nar"
    healthcheck:
      test: ["CMD", "curl", "-f", "https://nifi:8443/nifi", "-k"]
      interval: 5s
      timeout: 5s
      retries: 15
    networks:
      - nifi-network

volumes:
  pacific_nifi_conf:
    external: true
  pacific_nifi_database:
    external: true
  pacific_nifi_flowfile:
    external: true
  pacific_nifi_content:
    external: true
  pacific_nifi_provenance:
    external: true
  pacific_nifi_state:
    external: true
  pacific_nifi_logs:
    external: true
