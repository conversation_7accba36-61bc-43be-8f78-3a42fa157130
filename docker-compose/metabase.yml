version: "3.9"
services:
  metabase:
    image: metabase/metabase:latest
    container_name: metabase
    hostname: metabase
    volumes:
      - /dev/urandom:/dev/random:ro
    ports:
      - 8080:3000
    environment:
      MB_DB_TYPE: postgres
      MB_DB_PORT: 5432
      MB_DB_DBNAME: metabase
      MB_DB_USER: metabase
      MB_DB_PASS: metabase
      MB_DB_HOST: postgres-metabase
    healthcheck:
      test: curl --fail -I http://localhost:3000/api/health || exit 1
      interval: 15s
      timeout: 5s
      retries: 5
    networks:
      - ${GLOBAL_NETWORK:-metabase}

  postgres-metabase:
    image: postgres:16.3
    hostname: postgres-metabase
    environment:
      POSTGRES_USER: metabase
      POSTGRES_DB: metabase
      POSTGRES_PASSWORD: metabase
    networks:
      - ${GLOBAL_NETWORK:-metabase}

