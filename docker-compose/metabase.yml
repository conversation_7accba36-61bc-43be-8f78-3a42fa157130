version: "3.9"
services:
  containerbase-metabase:
    image: metabase/metabase:latest
    container_name: containerbase-metabase
    hostname: containerbase-metabase
    volumes:
      - /dev/urandom:/dev/random:ro
    ports:
      - '${METABASE_PORT}:3000'
    environment:
      MB_DB_TYPE: postgres
      MB_DB_PORT: 5432
      MB_DB_DBNAME: metabase
      MB_DB_USER:  postgres
      MB_DB_PASS: postgres
      MB_DB_HOST: containerbase-db
    healthcheck:
      test: curl --fail -I http://localhost:3000/api/health || exit 1
      interval: 15s
      timeout: 5s
      retries: 5
    networks:
      - ${GLOBAL_NETWORK:-containerbase}
