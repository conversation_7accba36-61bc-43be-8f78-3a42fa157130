/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.device.aggregator.features.workingshift;

import com.styl.pacific.device.aggregator.features.workingshift.clients.CashFloatClient;
import com.styl.pacific.store.shared.http.requests.cashfloat.CreateCashFloatRequest;
import com.styl.pacific.store.shared.http.requests.cashfloat.UpdateCashFloatRequest;
import com.styl.pacific.store.shared.http.responses.cashfloat.CashFloatResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class CashFloatServiceImpl implements CashFloatService {
	private final CashFloatClient cashFloatClient;

	@Override
	public CashFloatResponse createCashFloat(CreateCashFloatRequest request) {
		return cashFloatClient.createCashFloat(request);
	}

	@Override
	public CashFloatResponse updateCashFloat(long id, UpdateCashFloatRequest request) {
		return cashFloatClient.updateCashFloat(id, request);
	}
}
