/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.device.aggregator.features.store;

import com.styl.pacific.device.aggregator.features.store.dtos.DeviceSessionDto;
import com.styl.pacific.device.aggregator.features.tenant.dtos.DeviceCombineSettingResponse;
import com.styl.pacific.store.shared.http.requests.device.InitDeviceRequest;
import com.styl.pacific.store.shared.http.requests.device.PingDeviceRequest;
import com.styl.pacific.store.shared.http.responses.device.DeviceSessionResponse;

/**
 * <AUTHOR>
 */
public interface DeviceService {
	DeviceSessionDto getDeviceSessionById(String sessionId);

	DeviceCombineSettingResponse getDeviceSetting(long storeId, String deviceId);

	DeviceSessionResponse initDeviceSession(InitDeviceRequest request, String deviceId);

	void ping(PingDeviceRequest request, String deviceId);
}
