/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.device.aggregator.features.order.controller;

import com.styl.pacific.device.aggregator.features.order.apis.ServiceChargeApi;
import com.styl.pacific.device.aggregator.features.order.services.ServiceChargeService;
import com.styl.pacific.domain.dto.Content;
import com.styl.pacific.order.service.shared.http.servicecharge.request.ServiceChargeQueryRequest;
import com.styl.pacific.order.service.shared.http.servicecharge.response.ServiceChargeDetailDTOResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
public class ServiceChargeController implements ServiceChargeApi {
	private final ServiceChargeService serviceChargeService;

	@Override
	public Content<ServiceChargeDetailDTOResponse> findDetailAll(ServiceChargeQueryRequest request) {
		return serviceChargeService.findDetailAll(request);
	}

	@Override
	public ServiceChargeDetailDTOResponse findById(String id) {
		return serviceChargeService.findById(id);
	}
}
