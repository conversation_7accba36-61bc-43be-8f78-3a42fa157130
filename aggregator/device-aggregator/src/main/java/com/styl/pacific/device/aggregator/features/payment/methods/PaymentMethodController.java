/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.device.aggregator.features.payment.methods;

import com.styl.pacific.device.aggregator.features.payment.apis.PaymentMethodApi;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.payment.shared.http.methods.request.QueryPaymentMethodPaginationRequest;
import com.styl.pacific.payment.shared.http.methods.response.PaymentMethodResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class PaymentMethodController implements PaymentMethodApi {
	private final PaymentMethodService paymentMethodService;

	@Override
	public PaymentMethodResponse getPaymentMethod(Long methodId) {
		return paymentMethodService.getPaymentMethod(methodId);

	}

	@Override
	public Paging<PaymentMethodResponse> queryPaymentMethods(QueryPaymentMethodPaginationRequest request) {
		return paymentMethodService.queryPaymentMethods(request);

	}
}
