/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.device.aggregator.features.payment.transactions;

import com.styl.pacific.device.aggregator.features.payment.apis.PaymentTransactionApi;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.payment.shared.http.transactions.request.CreateOfflineTransactionRequest;
import com.styl.pacific.payment.shared.http.transactions.request.QueryPaymentTransactionPaginationRequest;
import com.styl.pacific.payment.shared.http.transactions.response.PaymentTransactionResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class PaymentTransactionController implements PaymentTransactionApi {

	private final PaymentTransactionService paymentTransactionService;

	@Override
	public PaymentTransactionResponse getPaymentTransaction(Long transactionId) {
		return paymentTransactionService.getPaymentTransaction(transactionId);
	}

	@Override
	public Paging<PaymentTransactionResponse> queryPaymentTransactions(
			QueryPaymentTransactionPaginationRequest request) {
		return paymentTransactionService.queryPaymentTransactions(request);
	}

	@Override
	public PaymentTransactionResponse createOfflineTransaction(CreateOfflineTransactionRequest request) {
		return paymentTransactionService.createOfflineTransaction(request);
	}
}
