/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.device.aggregator.features.users.customers;

import com.styl.pacific.device.aggregator.features.users.dtos.UserCardDto;
import com.styl.pacific.device.aggregator.features.users.dtos.UserDto;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.user.shared.http.cards.request.QueryUserCardPaginationRequest;
import com.styl.pacific.user.shared.http.users.request.QueryUserPaginationRequest;

public interface CustomerService {
	UserDto getUserProfile(Long userId);

	Paging<UserDto> queryCustomers(QueryUserPaginationRequest request);

	Paging<UserCardDto> queryCustomerCardsByUserId(Long userId, QueryUserCardPaginationRequest request);

	Paging<UserCardDto> queryAllCustomerCards(QueryUserCardPaginationRequest request);

}
