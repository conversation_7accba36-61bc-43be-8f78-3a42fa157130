/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.device.aggregator.features.tenant.dtos;

import com.styl.pacific.domain.dto.CurrencyResponse;
import com.styl.pacific.store.shared.http.enums.DeviceType;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.BusinessFeatureResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TaxResponse;
import java.util.List;
import lombok.Builder;

/**
 * <AUTHOR>
 */
@Builder
public record DeviceCombineSettingResponse(String deviceId,
		DeviceType type,
		String deviceName,
		String storeId,
		String storeName,
		String tenantId,
		String tenantName,
		CurrencyResponse currency,
		String receiptHeader,
		String receiptFooter,
		List<TaxResponse> taxes,
		List<BusinessFeatureResponse> featureFlags) {
}
