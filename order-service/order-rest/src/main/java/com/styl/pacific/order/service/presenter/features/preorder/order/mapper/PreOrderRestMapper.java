/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.presenter.features.preorder.order.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.order.service.domain.features.common.mapper.CommonDataMapper;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.PreOrderLightDto;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.PreOrderSummaryDto;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.command.CancelPreOrderCommand;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.command.CreatePreOrderCommand;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.command.CreatePreOrderLineItemCommand;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.command.PreOrderDetailCommand;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.command.PreOrderLineItemOptionCommand;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.command.PreOrderPaymentCommand;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.query.PreOrderFilter;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.query.PreOrderHistorySummaryFilter;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.query.PreOrderPagingQuery;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.query.PreOrderSummaryQuery;
import com.styl.pacific.order.service.domain.features.preorder.order.entity.PreOrder;
import com.styl.pacific.order.service.presenter.features.order.mapper.OrderRestMapper;
import com.styl.pacific.order.service.presenter.features.product.mapper.ProductRestMapper;
import com.styl.pacific.order.service.shared.http.preorder.order.request.CancelPreOrderRequest;
import com.styl.pacific.order.service.shared.http.preorder.order.request.CreatePreOrderRequest;
import com.styl.pacific.order.service.shared.http.preorder.order.request.PreOrderDetailRequest;
import com.styl.pacific.order.service.shared.http.preorder.order.request.PreOrderLineItemOptionRequest;
import com.styl.pacific.order.service.shared.http.preorder.order.request.PreOrderLineItemRequest;
import com.styl.pacific.order.service.shared.http.preorder.order.request.PreOrderPaymentRequest;
import com.styl.pacific.order.service.shared.http.preorder.order.request.query.PreOrderFilterRequest;
import com.styl.pacific.order.service.shared.http.preorder.order.request.query.PreOrderPagingQueryRequest;
import com.styl.pacific.order.service.shared.http.preorder.order.request.query.PreOrderSummaryFilterRequest;
import com.styl.pacific.order.service.shared.http.preorder.order.request.query.PreOrderSummaryQueryRequest;
import com.styl.pacific.order.service.shared.http.preorder.order.response.PreOrderLightResponse;
import com.styl.pacific.order.service.shared.http.preorder.order.response.PreOrderResponse;
import com.styl.pacific.order.service.shared.http.preorder.order.response.PreOrderSummaryResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, uses = { OrderRestMapper.class, CommonDataMapper.class,
		ProductRestMapper.class, MapstructCommonMapper.class, MapstructCommonDomainMapper.class })
public interface PreOrderRestMapper {
	PreOrderRestMapper INSTANCE = Mappers.getMapper(PreOrderRestMapper.class);

	CreatePreOrderCommand toCommand(CreatePreOrderRequest request);

	PreOrderDetailCommand toCommand(PreOrderDetailRequest request);

	PreOrderPaymentCommand toCommand(PreOrderPaymentRequest request);

	@Mapping(target = "note", source = "note", qualifiedByName = { "stringToClearance" })
	CreatePreOrderLineItemCommand toCommand(PreOrderLineItemRequest request);

	PreOrderLineItemOptionCommand toCommand(PreOrderLineItemOptionRequest request);

	@Mapping(target = "refundable", expression = "java(Boolean.TRUE)")
	CancelPreOrderCommand toCommand(CancelPreOrderRequest request);

	@Mapping(target = "id", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "tenantId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "issuerId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "customerId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "subtotalAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "serviceChargeAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "taxAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "discountAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "totalAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "paymentMethodId", source = "paymentMethodId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "paymentTransactionId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "cancellationDueAt", qualifiedByName = "instantToLong")
	@Mapping(target = "canceledAt", qualifiedByName = "instantToLong")
	@Mapping(target = "expiredAt", qualifiedByName = "instantToLong")
	@Mapping(target = "createdAt", qualifiedByName = "instantToLong")
	@Mapping(target = "updatedAt", qualifiedByName = "instantToLong")
	@Mapping(target = "currency", source = "currencyCode", qualifiedByName = "currencyCodeToResponse")
	PreOrderResponse toResponse(PreOrder model);

	@Mapping(target = "id", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "tenantId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "issuerId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "customerId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "subtotalAmount", source = "subtotalAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "serviceChargeAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "taxAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "discountAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "totalAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "paymentMethodId", source = "paymentMethodId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "paymentTransactionId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "cancellationDueAt", qualifiedByName = "instantToLong")
	@Mapping(target = "canceledAt", qualifiedByName = "instantToLong")
	@Mapping(target = "expiredAt", qualifiedByName = "instantToLong")
	@Mapping(target = "createdAt", qualifiedByName = "instantToLong")
	@Mapping(target = "updatedAt", qualifiedByName = "instantToLong")
	@Mapping(target = "currency", source = "currencyCode", qualifiedByName = "currencyCodeToResponse")
	PreOrderLightResponse toLightResponse(PreOrder model);

	@Mapping(target = "id", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "tenantId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "issuerId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "customerId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "subtotalAmount", source = "subtotalAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "serviceChargeAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "taxAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "discountAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "totalAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "paymentMethodId", source = "paymentMethodId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "paymentTransactionId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "cancellationDueAt", qualifiedByName = "instantToLong")
	@Mapping(target = "canceledAt", qualifiedByName = "instantToLong")
	@Mapping(target = "expiredAt", qualifiedByName = "instantToLong")
	@Mapping(target = "createdAt", qualifiedByName = "instantToLong")
	@Mapping(target = "updatedAt", qualifiedByName = "instantToLong")
	@Mapping(target = "currency", source = "currencyCode", qualifiedByName = "currencyCodeToResponse")
	PreOrderLightResponse toLightResponse(PreOrderLightDto model);

	@Mapping(target = "preOrderId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "subOrderId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "mealTimeId", qualifiedByName = "baseLongIdToLong")
	PreOrderSummaryResponse toResponse(PreOrderSummaryDto dto);

	@Mapping(target = "id", qualifiedByName = "baseLongIdToLong")
	PreOrderSummaryResponse.PreOrderMenuItemPlacementResponse toResponse(
			PreOrderSummaryDto.PreOrderMenuItemPlacementDto dto);

	@Mapping(target = "isExpired", ignore = true)
	PreOrderFilter toFilter(PreOrderFilterRequest filter);

	PreOrderPagingQuery toQuery(PreOrderPagingQueryRequest query);

	PreOrderHistorySummaryFilter toFilter(PreOrderSummaryFilterRequest filter);

	PreOrderSummaryQuery toQuery(PreOrderSummaryQueryRequest query);

}
