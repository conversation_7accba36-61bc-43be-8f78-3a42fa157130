<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.styl.pacific.order.service</groupId>
        <artifactId>order-service</artifactId>
        <version>1.2.4</version>
    </parent>
    <artifactId>order-data-access</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-core</artifactId>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>common-data-access</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>common-feign</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>common-mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.catalog.service</groupId>
            <artifactId>catalog-shared</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.order.service</groupId>
            <artifactId>order-data-access-dynamodb</artifactId>
        </dependency>

        <dependency>
            <groupId>com.styl.pacific.order.service</groupId>
            <artifactId>order-data-access-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.order.service</groupId>
            <artifactId>order-domain-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.payment.service</groupId>
            <artifactId>payment-shared</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.store.service</groupId>
            <artifactId>store-shared</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.tenant.service</groupId>
            <artifactId>tenant-shared</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.user.service</groupId>
            <artifactId>user-shared</artifactId>
        </dependency>
    </dependencies>
</project>
