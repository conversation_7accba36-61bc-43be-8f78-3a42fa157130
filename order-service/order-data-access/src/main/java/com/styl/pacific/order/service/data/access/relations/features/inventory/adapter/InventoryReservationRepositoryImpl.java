/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.relations.features.inventory.adapter;

import com.styl.pacific.domain.valueobject.OrderId;
import com.styl.pacific.order.service.data.access.jpa.features.inventory.entity.InventoryReservationEntity;
import com.styl.pacific.order.service.data.access.jpa.features.inventory.entity.InventoryReservationId;
import com.styl.pacific.order.service.data.access.jpa.features.inventory.repository.InventoryJpaRepository;
import com.styl.pacific.order.service.data.access.jpa.features.inventory.repository.InventoryReservationJpaRepository;
import com.styl.pacific.order.service.data.access.jpa.features.inventory.specification.InventoryReservationSpecifications;
import com.styl.pacific.order.service.data.access.relations.features.inventory.mapper.InventoryDataAccessMapper;
import com.styl.pacific.order.service.domain.features.inventory.entity.InventoryReservation;
import com.styl.pacific.order.service.domain.features.inventory.entity.ReservationQuantity;
import com.styl.pacific.order.service.domain.features.inventory.enums.ReservationStatus;
import com.styl.pacific.order.service.domain.features.inventory.exceptions.InsufficientInventoryQuantityException;
import com.styl.pacific.order.service.domain.features.inventory.ports.output.repository.InventoryReservationRepository;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.repository.query.FluentQuery;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class InventoryReservationRepositoryImpl implements InventoryReservationRepository {
	private final InventoryReservationJpaRepository reservationJpaRepository;
	private final InventoryJpaRepository inventoryJpaRepository;

	@Override
	public InventoryReservation reserveInventory(InventoryReservation reservation) {
		List<ReservationQuantity> reservations = reservation.getReservations()
				.stream()
				.sorted(Comparator.comparing(o -> o.getProductId()
						.getValue()))
				.toList();
		for (ReservationQuantity reservationQuantity : reservations) {
			int result = inventoryJpaRepository.addQuantity(reservation.getTenantId()
					.getValue(), reservationQuantity.getProductId()
							.getValue(), -reservationQuantity.getQuantity());
			if (result == 0) {
				throw new InsufficientInventoryQuantityException(String.format(
						"Insufficient inventory quantity for product %s with quantity %d", reservationQuantity
								.getProductId(), reservationQuantity.getQuantity()));
			}
		}
		InventoryReservationEntity entity = InventoryDataAccessMapper.INSTANCE
				.inventoryReservationToInventoryReservationEntity(reservation);
		entity.setStatus(ReservationStatus.SUCCESS);
		return InventoryDataAccessMapper.INSTANCE.entityToModel(reservationJpaRepository.save(entity));
	}

	@Override
	public InventoryReservation save(InventoryReservation reservation) {
		InventoryReservationEntity entity = InventoryDataAccessMapper.INSTANCE
				.inventoryReservationToInventoryReservationEntity(reservation);
		return InventoryDataAccessMapper.INSTANCE.entityToModel(reservationJpaRepository.save(entity));
	}

	@Override
	public InventoryReservation update(InventoryReservation reservation) {
		InventoryReservationEntity entity = InventoryDataAccessMapper.INSTANCE
				.inventoryReservationToInventoryReservationEntity(reservation);
		return InventoryDataAccessMapper.INSTANCE.entityToModel(reservationJpaRepository.saveAndFlush(entity));
	}

	@Override
	public Optional<InventoryReservation> findByOrderIdAndStoreId(Long tenantId, Long orderId, Long storeId) {
		Specification<InventoryReservationEntity> specification = InventoryReservationSpecifications.byTenantIdAndId(
				tenantId, new InventoryReservationId(storeId, orderId));
		return reservationJpaRepository.findOne(specification)
				.map(InventoryDataAccessMapper.INSTANCE::entityToModel);
	}

	@Override
	public List<InventoryReservation> findAllByOrderIds(List<OrderId> orderIds) {
		List<Long> orderIdsValue = Optional.ofNullable(orderIds)
				.map(ids -> ids.stream()
						.map(OrderId::getValue)
						.toList())
				.orElse(Collections.emptyList());
		if (orderIdsValue.isEmpty()) {
			return Collections.emptyList();
		}
		return reservationJpaRepository.findBy(InventoryReservationSpecifications.withOrderIds(orderIdsValue),
				FluentQuery.FetchableFluentQuery::all)
				.stream()
				.map(InventoryDataAccessMapper.INSTANCE::entityToModel)
				.toList();
	}
}
