/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.relations.features.product.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.order.service.data.access.jpa.features.product.entity.ProductImageEntity;
import com.styl.pacific.order.service.data.access.relations.features.common.mapper.CommonDataAccessMapper;
import com.styl.pacific.order.service.domain.features.common.mapper.CommonDataMapper;
import com.styl.pacific.order.service.domain.features.product.dto.ProductImageDto;
import com.styl.pacific.order.service.domain.features.product.entity.ProductImage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class, CommonDataMapper.class,
		MapstructCommonDomainMapper.class, CommonDataAccessMapper.class })
public interface ProductImageDataAccessMapper {
	ProductImageDataAccessMapper INSTANCE = Mappers.getMapper(ProductImageDataAccessMapper.class);

	@Mapping(target = "id", source = "entity.id", qualifiedByName = "longToProductImageId")
	@Mapping(target = "productId", source = "entity.productId", qualifiedByName = "longToProductId")
	ProductImage productImageEntityToProductImage(ProductImageEntity entity);

	@Mapping(target = "id", source = "image.id", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "productId", source = "image.productId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "position", source = "image.position")
	@Mapping(target = "product", ignore = true)
	@Mapping(target = "deletedAt", ignore = true)
	ProductImageEntity productImageToProductImageEntity(ProductImage image);

	ProductImageDto productImageToProductImageResponse(ProductImageEntity image);
}
