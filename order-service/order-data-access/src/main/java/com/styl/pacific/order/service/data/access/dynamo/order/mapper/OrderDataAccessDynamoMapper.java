/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.dynamo.order.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.order.service.data.access.relations.features.common.mapper.CommonDataAccessMapper;
import com.styl.pacific.order.service.domain.features.common.mapper.CommonDataMapper;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { CommonDataAccessMapper.class, CommonDataMapper.class,
		MapstructCommonMapper.class, MapstructCommonDomainMapper.class })
public interface OrderDataAccessDynamoMapper {
	OrderDataAccessDynamoMapper INSTANCE = Mappers.getMapper(OrderDataAccessDynamoMapper.class);

	//	@Mapping(target = "version", source = "eventVersion")
	//	@Mapping(target = "sortKey", ignore = true)
	//	@Mapping(target = "partitionKey", ignore = true)
	//	@Mapping(target = "id", source = "id", qualifiedByName = "baseLongIdToLong")
	//	@Mapping(target = "tenantId", source = "tenantId", qualifiedByName = "baseLongIdToLong")
	//	@Mapping(target = "storeId", source = "storeId", qualifiedByName = "baseLongIdToLong")
	//	@Mapping(target = "preOrderId", source = "preOrderId", qualifiedByName = "baseLongIdToLong")
	//	@Mapping(target = "mealTimeId", source = "mealTimeId", qualifiedByName = "baseLongIdToLong")
	//	@Mapping(target = "customerId", source = "customerId", qualifiedByName = "baseLongIdToLong")
	//	@Mapping(target = "issuerId", source = "issuerId", qualifiedByName = "baseLongIdToLong")
	//	@Mapping(target = "taxAmount", source = "taxAmount", qualifiedByName = "moneyToBigInteger")
	//	@Mapping(target = "subtotalAmount", source = "subtotalAmount", qualifiedByName = "moneyToBigInteger")
	//	@Mapping(target = "discountAmount", source = "discountAmount", qualifiedByName = "moneyToBigInteger")
	//	@Mapping(target = "totalAmount", source = "totalAmount", qualifiedByName = "moneyToBigInteger")
	//	@Mapping(target = "paymentMethodId", source = "paymentMethodId", qualifiedByName = "baseLongIdToLong")
	//	@Mapping(target = "paymentTransactionId", source = "paymentTransactionId", qualifiedByName = "baseLongIdToLong")
	//	OrderDocument orderToOrderSchemaEntity(Order order);
	//
	//	@Mapping(target = "id", source = "id", qualifiedByName = "baseLongIdToLong")
	//	@Mapping(target = "tenantId", qualifiedByName = "baseLongIdToLong")
	//	@Mapping(target = "orderId", source = "orderId", qualifiedByName = "baseLongIdToLong")
	//	@Mapping(target = "productId", source = "productId", qualifiedByName = "baseLongIdToLong")
	//	@Mapping(target = "menuId", qualifiedByName = "baseLongIdToLong")
	//	@Mapping(target = "menuItemId", qualifiedByName = "baseLongIdToLong")
	//	@Mapping(target = "preOrderMenuId", qualifiedByName = "baseLongIdToLong")
	//	@Mapping(target = "preOrderMenuItemId", qualifiedByName = "baseLongIdToLong")
	//	@Mapping(target = "mealTimeId", qualifiedByName = "baseLongIdToLong")
	//	@Mapping(target = "unitPrice", source = "unitPrice", qualifiedByName = "moneyToBigInteger")
	//	@Mapping(target = "optionPrice", source = "optionPrice", qualifiedByName = "moneyToBigInteger")
	//	@Mapping(target = "totalDiscount", source = "totalDiscount", qualifiedByName = "moneyToBigInteger")
	//	@Mapping(target = "totalAmount", source = "totalAmount", qualifiedByName = "moneyToBigInteger")
	//	OrderLineItemDocument orderLineItemToOrderLineItemSchemaEntity(OrderLineItem orderLineItem);
	//
	//	@Mapping(target = "additionalPrice", source = "additionalPrice", qualifiedByName = "moneyToBigInteger")
	//	ProductOptionItemDocument productOptionItemToProductOptionItemSchemaEntity(
	//			ProductOptionItemMetadata productOptionItem);
	//
	//	ProductOptionDocument productOptionToProductOptionSchemaEntity(ProductOptionMetadata productOption);
	//
	//	@Mapping(target = "unitPrice", source = "unitPrice", qualifiedByName = "moneyToBigInteger")
	//	@Mapping(target = "listingPrice", source = "listingPrice", qualifiedByName = "moneyToBigInteger")
	//	ProductDocument productToProductSchemeEntity(ProductMetadata product);
	//
	//	HealthierChoiceDocument healthierChoiceToHealthierChoiceSchemaEntity(HealthierChoiceMetadata healthierChoice);
	//
	//	CategoryDocument categoryToCategorySchemaEntity(CategoryMetadata category);

	//	HealthierChoiceMetadata healthierChoiceSchemaEntityToHealthierChoice(
	//			HealthierChoiceDocument healthierChoiceDocument);
	//
	//	CategoryMetadata categorySchemaEntityToCategory(CategoryDocument categoryDocument);
	//
	//	@Mapping(target = "unitPrice", source = "unitPrice", qualifiedByName = "bigIntegerToMoney")
	//	@Mapping(target = "listingPrice", source = "listingPrice", qualifiedByName = "bigIntegerToMoney")
	//	ProductMetadata productSchemaEntityToProduct(ProductDocument productSchemaEntity);
	//
	//	ProductOptionMetadata productOptionSchemaEntityToProductOption(ProductOptionDocument productOptionDocument);
	//
	//	@Mapping(target = "additionalPrice", source = "additionalPrice", qualifiedByName = "bigIntegerToMoney")
	//	ProductOptionItemMetadata productOptionItemSchemaEntityToProductOptionItem(
	//			ProductOptionItemDocument productOptionItemDocument);
	//
	//	ProductImageMetadata productImageSchemaEntityToProductImage(ProductImageDocument productImageDocument);
	//
	//	@Mapping(target = "id", source = "id", qualifiedByName = "longToOrderLineItemId")
	//	@Mapping(target = "tenantId", qualifiedByName = "longToTenantId")
	//	@Mapping(target = "orderId", source = "orderId", qualifiedByName = "longToOrderId")
	//	@Mapping(target = "productId", source = "productId", qualifiedByName = "longToProductId")
	//	@Mapping(target = "menuId", qualifiedByName = "longToMenuId")
	//	@Mapping(target = "menuItemId", qualifiedByName = "longToMenuItemId")
	//	@Mapping(target = "preOrderMenuId", qualifiedByName = "longToPreOrderMenuId")
	//	@Mapping(target = "preOrderMenuItemId", qualifiedByName = "longToPreOrderMenuItemId")
	//	@Mapping(target = "mealTimeId", qualifiedByName = "longToMealTimeId")
	//	@Mapping(target = "unitPrice", source = "unitPrice", qualifiedByName = "bigIntegerToMoney")
	//	@Mapping(target = "optionPrice", source = "optionPrice", qualifiedByName = "bigIntegerToMoney")
	//	@Mapping(target = "totalDiscount", source = "totalDiscount", qualifiedByName = "bigIntegerToMoney")
	//	@Mapping(target = "totalAmount", source = "totalAmount", qualifiedByName = "bigIntegerToMoney")
	//	OrderLineItem orderLineItemSchemaEntityToOrderLineItem(OrderLineItemDocument orderLineItemDocument);

	//	@Mapping(target = "collectionDate", ignore = true)
	//	@Mapping(target = "version", ignore = true)
	//	@Mapping(target = "eventVersion", source = "version")
	//	@Mapping(target = "id", source = "id", qualifiedByName = "longToOrderId")
	//	@Mapping(target = "tenantId", source = "tenantId", qualifiedByName = "longToTenantId")
	//	@Mapping(target = "storeId", source = "storeId", qualifiedByName = "longToStoreId")
	//	@Mapping(target = "preOrderId", source = "preOrderId", qualifiedByName = "longToPreOrderId")
	//	@Mapping(target = "mealTimeId", source = "mealTimeId", qualifiedByName = "longToMealTimeId")
	//	@Mapping(target = "customerId", source = "customerId", qualifiedByName = "longToUserId")
	//	@Mapping(target = "issuerId", source = "issuerId", qualifiedByName = "longToUserId")
	//	@Mapping(target = "taxAmount", source = "taxAmount", qualifiedByName = "bigIntegerToMoney")
	//	@Mapping(target = "subtotalAmount", source = "subtotalAmount", qualifiedByName = "bigIntegerToMoney")
	//	@Mapping(target = "discountAmount", source = "discountAmount", qualifiedByName = "bigIntegerToMoney")
	//	@Mapping(target = "totalAmount", source = "totalAmount", qualifiedByName = "bigIntegerToMoney")
	//	@Mapping(target = "paymentMethodId", source = "paymentMethodId", qualifiedByName = "longToPaymentMethodId")
	//	@Mapping(target = "paymentTransactionId", source = "paymentTransactionId", qualifiedByName = "longToPaymentTransactionId")
	//	Order orderSchemaEntityToOrder(OrderDocument orderDocument);
	//
	//	@Mapping(target = "id", source = "id", qualifiedByName = "longToOrderLineItemId")
	//	@Mapping(target = "tenantId", qualifiedByName = "longToTenantId")
	//	@Mapping(target = "orderId", source = "orderId", qualifiedByName = "longToOrderId")
	//	@Mapping(target = "productId", source = "productId", qualifiedByName = "longToProductId")
	//	@Mapping(target = "menuId", qualifiedByName = "longToMenuId")
	//	@Mapping(target = "menuItemId", qualifiedByName = "longToMenuItemId")
	//	@Mapping(target = "preOrderMenuId", qualifiedByName = "longToPreOrderMenuId")
	//	@Mapping(target = "preOrderMenuItemId", qualifiedByName = "longToPreOrderMenuItemId")
	//	@Mapping(target = "mealTimeId", qualifiedByName = "longToMealTimeId")
	//	@Mapping(target = "unitPrice", source = "unitPrice", qualifiedByName = "bigIntegerToMoney")
	//	@Mapping(target = "optionPrice", source = "optionPrice", qualifiedByName = "bigIntegerToMoney")
	//	@Mapping(target = "totalDiscount", source = "totalDiscount", qualifiedByName = "bigIntegerToMoney")
	//	@Mapping(target = "totalAmount", source = "totalAmount", qualifiedByName = "bigIntegerToMoney")
	//	OrderLineItemDto orderLineItemSchemaEntityToOrderLineItemDto(OrderLineItemDocument orderLineItemDocument);

	//	@Mapping(target = "mealTime", ignore = true)
	//	@Mapping(target = "collectionDate", ignore = true)
	//	@Mapping(target = "version", ignore = true)
	//	@Mapping(target = "eventVersion", source = "version")
	//	@Mapping(target = "id", source = "id", qualifiedByName = "longToOrderId")
	//	@Mapping(target = "tenantId", source = "tenantId", qualifiedByName = "longToTenantId")
	//	@Mapping(target = "storeId", source = "storeId", qualifiedByName = "longToStoreId")
	//	@Mapping(target = "preOrderId", source = "preOrderId", qualifiedByName = "longToPreOrderId")
	//	@Mapping(target = "mealTimeId", source = "mealTimeId", qualifiedByName = "longToMealTimeId")
	//	@Mapping(target = "customerId", source = "customerId", qualifiedByName = "longToUserId")
	//	@Mapping(target = "issuerId", source = "issuerId", qualifiedByName = "longToUserId")
	//	@Mapping(target = "taxAmount", source = "taxAmount", qualifiedByName = "bigIntegerToMoney")
	//	@Mapping(target = "subtotalAmount", source = "subtotalAmount", qualifiedByName = "bigIntegerToMoney")
	//	@Mapping(target = "discountAmount", source = "discountAmount", qualifiedByName = "bigIntegerToMoney")
	//	@Mapping(target = "totalAmount", source = "totalAmount", qualifiedByName = "bigIntegerToMoney")
	//	@Mapping(target = "paymentMethodId", source = "paymentMethodId", qualifiedByName = "longToPaymentMethodId")
	//	@Mapping(target = "paymentTransactionId", source = "paymentTransactionId", qualifiedByName = "longToPaymentTransactionId")
	//	OrderDto orderSchemaEntityToOrderDto(OrderDocument orderDocument);
}
