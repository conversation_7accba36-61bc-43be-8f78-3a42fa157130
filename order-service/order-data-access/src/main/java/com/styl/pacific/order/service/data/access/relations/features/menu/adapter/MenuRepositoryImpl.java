/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.relations.features.menu.adapter;

import com.styl.pacific.data.access.utils.JpaPageableUtils;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.MenuId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.data.access.jpa.features.menu.entity.MenuEntity;
import com.styl.pacific.order.service.data.access.jpa.features.menu.entity.MenuItemEntity;
import com.styl.pacific.order.service.data.access.jpa.features.menu.repository.ArrangementJpaRepository;
import com.styl.pacific.order.service.data.access.jpa.features.menu.repository.MenuItemJpaRepository;
import com.styl.pacific.order.service.data.access.jpa.features.menu.repository.MenuJpaRepository;
import com.styl.pacific.order.service.data.access.jpa.features.menu.specification.MenuItemSpecifications;
import com.styl.pacific.order.service.data.access.jpa.features.menu.specification.MenuSpecifications;
import com.styl.pacific.order.service.data.access.relations.features.menu.mapper.MenuDataAccessMapper;
import com.styl.pacific.order.service.domain.features.common.exception.OrderRetryableException;
import com.styl.pacific.order.service.domain.features.menu.dto.query.MenuFilter;
import com.styl.pacific.order.service.domain.features.menu.dto.query.PaginationMenuQuery;
import com.styl.pacific.order.service.domain.features.menu.entity.Menu;
import com.styl.pacific.order.service.domain.features.menu.enums.MenuStatus;
import com.styl.pacific.order.service.domain.features.menu.ports.output.repository.MenuRepository;
import jakarta.persistence.OptimisticLockException;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class MenuRepositoryImpl implements MenuRepository {
	private static final Logger log = LoggerFactory.getLogger(MenuRepositoryImpl.class);
	private final MenuJpaRepository menuJpaRepository;
	private final MenuItemJpaRepository menuItemJpaRepository;
	private final ArrangementJpaRepository arrangementJpaRepository;

	@Override
	public Optional<Menu> findById(TenantId tenantId, MenuId menuId) {
		return menuJpaRepository.findOne(MenuSpecifications.withTenantIdAndId(tenantId.getValue(), menuId.getValue()))
				.map(MenuDataAccessMapper.INSTANCE::toModel);
	}

	@Override
	public void deleteById(TenantId tenantId, MenuId menuId) {
		List<MenuItemEntity> items = menuItemJpaRepository.findAll(MenuItemSpecifications.withMenuId(menuId
				.getValue()));
		arrangementJpaRepository.deleteAll(items.stream()
				.map(MenuItemEntity::getArrangement)
				.toList());
		menuItemJpaRepository.deleteAll(items);
		menuJpaRepository.findOne(MenuSpecifications.withTenantIdAndId(tenantId.getValue(), menuId.getValue()))
				.ifPresent(menuJpaRepository::delete);
	}

	@Override
	public boolean existsById(TenantId tenantId, MenuId menuId) {
		return menuJpaRepository.exists(MenuSpecifications.withTenantIdAndId(tenantId.getValue(), menuId.getValue()));
	}

	@Override
	public Menu create(Menu menu) {
		MenuEntity entity = MenuDataAccessMapper.INSTANCE.toEntity(menu);
		return MenuDataAccessMapper.INSTANCE.toModel(menuJpaRepository.save(entity));
	}

	@Override
	public Menu update(Menu menu) {
		try {
			MenuEntity entity = MenuDataAccessMapper.INSTANCE.toEntity(menu);
			return MenuDataAccessMapper.INSTANCE.toModel(menuJpaRepository.saveAndFlush(entity));
		} catch (OptimisticLockException e) {
			log.info("Optimistic lock exception occurred while updating menu with id: {}", menu.getId());
			throw new OrderRetryableException("Please try again later");
		}

	}

	@Override
	public void changeStatus(TenantId tenantId, MenuId menuId, MenuStatus status) {
		try {
			Specification<MenuEntity> spec = MenuSpecifications.withTenantIdAndId(tenantId.getValue(), menuId
					.getValue());
			Optional<MenuEntity> optionalMenu = menuJpaRepository.findOne(spec);
			if (optionalMenu.isPresent()) {
				MenuEntity menu = optionalMenu.get();
				menu.setStatus(status);
				menuJpaRepository.saveAndFlush(menu);
			}
		} catch (OptimisticLockException e) {
			log.info("Optimistic lock exception occurred while changing statuses of menu with id: {}", menuId);
			throw new OrderRetryableException("Please try again later");
		}
	}

	@Override
	public Paging<Menu> findAllPaging(TenantId tenantId, PaginationMenuQuery query) {
		Pageable pageable = JpaPageableUtils.createPageable(query.getPage(), query.getSize(), query.getSortFields(),
				query.getSortDirection(), MenuEntity.SORTABLE_FIELDS, MenuEntity.FIELD_ID);
		MenuFilter filter = Optional.ofNullable(query.getFilter())
				.orElse(MenuFilter.builder()
						.build());
		Specification<MenuEntity> specification = MenuSpecifications.withMultipleCriteria(Optional.ofNullable(tenantId)
				.map(TenantId::getValue)
				.orElse(null), filter.name(), filter.storeIds(), filter.type(), filter.statuses());
		var res = menuJpaRepository.findAll(specification, pageable)
				.map(MenuDataAccessMapper.INSTANCE::toModel);
		return Paging.<Menu>builder()
				.page(res.getPageable()
						.getPageNumber())
				.sort(res.getPageable()
						.getSort()
						.toList()
						.stream()
						.map(Sort.Order::toString)
						.toList())
				.content(res.getContent())
				.totalElements(res.getTotalElements())
				.totalPages(res.getTotalPages())
				.build();
	}

}
