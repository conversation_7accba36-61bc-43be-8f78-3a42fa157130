/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.relations.features.menuboard.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.order.service.data.access.jpa.features.menuboard.entity.MenuBoardSessionEntity;
import com.styl.pacific.order.service.data.access.relations.features.common.mapper.CommonDataAccessMapper;
import com.styl.pacific.order.service.domain.features.common.mapper.CommonDataMapper;
import com.styl.pacific.order.service.domain.features.menuboard.dto.MenuBoardSessionDetailDto;
import com.styl.pacific.order.service.domain.features.menuboard.entity.MenuBoardSession;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, uses = { MenuBoardDataAccessMapper.class,
		CommonDataAccessMapper.class, CommonDataMapper.class, MapstructCommonDomainMapper.class,
		MapstructCommonMapper.class })
public interface MenuBoardSessionDataAccessMapper {
	MenuBoardSessionDataAccessMapper INSTANCE = Mappers.getMapper(MenuBoardSessionDataAccessMapper.class);

	@Mapping(target = "id", qualifiedByName = "longToMenuBoardSessionId")
	@Mapping(target = "tenantId", qualifiedByName = "longToTenantId")
	MenuBoardSessionDetailDto toDto(MenuBoardSessionEntity entity);

	@Mapping(target = "id", qualifiedByName = "longToMenuBoardSessionId")
	@Mapping(target = "tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "menuBoardId", qualifiedByName = "longToMenuBoardId")
	MenuBoardSession toModel(MenuBoardSessionEntity entity);

	@Mapping(target = "id", qualifiedByName = "baseUUIDIdToString")
	@Mapping(target = "tenantId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "menuBoardId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "menuBoard.id", source = "menuBoardId", qualifiedByName = "baseLongIdToLong")
	MenuBoardSessionEntity toEntity(MenuBoardSession model);
}
