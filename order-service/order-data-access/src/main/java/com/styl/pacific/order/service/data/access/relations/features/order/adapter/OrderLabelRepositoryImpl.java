/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.relations.features.order.adapter;

import static com.styl.pacific.domain.enums.order.OrderType.INSTANT_ORDER;
import static com.styl.pacific.domain.enums.order.OrderType.ONSITE_ORDER;
import static com.styl.pacific.domain.enums.order.OrderType.PRE_ORDER;

import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.Border;
import com.itextpdf.layout.borders.DashedBorder;
import com.itextpdf.layout.borders.DottedBorder;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Div;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.BorderRadius;
import com.itextpdf.layout.properties.HorizontalAlignment;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.UnitValue;
import com.itextpdf.layout.properties.VerticalAlignment;
import com.styl.pacific.order.service.domain.features.label.entity.CellFormatProperties;
import com.styl.pacific.order.service.domain.features.label.entity.LabelConfig;
import com.styl.pacific.order.service.domain.features.label.entity.LabelLayoutProperties;
import com.styl.pacific.order.service.domain.features.label.entity.PageSetupProperties;
import com.styl.pacific.order.service.domain.features.label.enums.BorderStyle;
import com.styl.pacific.order.service.domain.features.mealtime.entity.MealTime;
import com.styl.pacific.order.service.domain.features.order.dto.OrderDto;
import com.styl.pacific.order.service.domain.features.order.ports.output.repository.OrderLabelRepository;
import com.styl.pacific.order.service.domain.features.tenant.dto.TenantDto;
import com.styl.pacific.order.service.domain.utils.TenantHelper;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class OrderLabelRepositoryImpl implements OrderLabelRepository {
	@Override
	public ByteArrayOutputStream generateOrderLabels(TenantDto tenant, LabelConfig labelConfig, List<OrderDto> orders) {
		ByteArrayOutputStream resultBytes = new ByteArrayOutputStream();
		PageSetupProperties pageSetup = Optional.ofNullable(labelConfig.getPageSetup())
				.orElse(PageSetupProperties.builder()
						.build());
		CellFormatProperties cellFormat = Optional.ofNullable(labelConfig.getCellFormat())
				.orElse(CellFormatProperties.builder()
						.build());
		LabelLayoutProperties labelLayout = Optional.ofNullable(labelConfig.getLabelLayout())
				.orElse(LabelLayoutProperties.builder()
						.build());

		try (Document document = new Document(new PdfDocument(new PdfWriter(resultBytes)), new PageSize(toPx(pageSetup
				.getPageWidth(), RoundingMode.UP), toPx(pageSetup.getPageHeight(), RoundingMode.UP)))) {

			document.setMargins(toPx(pageSetup.getPageMarginTop(), RoundingMode.DOWN), toPx(pageSetup
					.getPageMarginRight(), RoundingMode.DOWN), toPx(pageSetup.getPageMarginBottom(), RoundingMode.DOWN),
					toPx(pageSetup.getPageMarginLeft(), RoundingMode.DOWN));
			Table table = new Table(UnitValue.createPercentArray(labelLayout.getDimensionNumberOfColumns()), true);
			table.setWidth(UnitValue.createPercentValue(100));
			table.setFixedLayout();
			table.setHorizontalAlignment(HorizontalAlignment.LEFT);
			table.setVerticalAlignment(VerticalAlignment.TOP);

			for (OrderDto order : orders) {
				Cell cell = new Cell();
				cell.setKeepTogether(true);
				cell.setBorder(Border.NO_BORDER);
				cell.setPaddings(0, 0, 0, 0);
				cell.setHeight(toPx(labelLayout.getDimensionHeight(), RoundingMode.DOWN));
				cell.setMaxHeight(cell.getHeight());
				Div div = new Div();
				div.setFillAvailableArea(true);

				if (BorderStyle.NONE.equals(cellFormat.getBorderType())) {
					div.setBorder(Border.NO_BORDER);
				} else {
					float borderSize = Math.min((float) calculateBorderSize(toPx(pageSetup.getPageWidth(),
							RoundingMode.DOWN), toPx(pageSetup.getPageHeight(), RoundingMode.DOWN), 0.005), 3);
					div.setBorderRadius(new BorderRadius(borderSize));
					switch (cellFormat.getBorderType()) {
					case DOTTED:
						div.setBorder(new DottedBorder(borderSize));
						break;
					case DASHED:
						div.setBorder(new DashedBorder(borderSize));
						break;
					case SOLID:
					default:
						div.setBorder(new SolidBorder(borderSize));
						break;
					}
				}

				div.setPaddings(toPx(cellFormat.getAreaPaddingTop(), RoundingMode.DOWN), toPx(cellFormat
						.getAreaPaddingRight(), RoundingMode.DOWN), toPx(cellFormat.getAreaPaddingBottom(),
								RoundingMode.DOWN), toPx(cellFormat.getAreaPaddingLeft(), RoundingMode.DOWN));
				div.setMargins(toPx(labelLayout.getCellMarginTop(), RoundingMode.DOWN), toPx(labelLayout
						.getCellMarginRight(), RoundingMode.DOWN), toPx(labelLayout.getCellMarginBottom(),
								RoundingMode.DOWN), toPx(labelLayout.getCellMarginLeft(), RoundingMode.DOWN));
				Paragraph paragraphChild = new Paragraph(processLabel(tenant, order));
				paragraphChild.setFontSize(cellFormat.getFontSize()
						.floatValue());
				switch (cellFormat.getVerticalAlignment()) {
				case ALIGN_TOP:
					div.setVerticalAlignment(VerticalAlignment.TOP);
					break;
				case ALIGN_BOTTOM:
					div.setVerticalAlignment(VerticalAlignment.BOTTOM);
					break;
				default:
					div.setVerticalAlignment(VerticalAlignment.MIDDLE);
					break;
				}

				switch (cellFormat.getHorizontalAlignment()) {
				case ALIGN_LEFT:
					paragraphChild.setTextAlignment(TextAlignment.LEFT);
					div.setHorizontalAlignment(HorizontalAlignment.LEFT);
					break;
				case ALIGN_RIGHT:
					paragraphChild.setTextAlignment(TextAlignment.RIGHT);
					div.setHorizontalAlignment(HorizontalAlignment.RIGHT);
					break;
				case ALIGN_CENTER:
				default:
					paragraphChild.setTextAlignment(TextAlignment.CENTER);
					div.setHorizontalAlignment(HorizontalAlignment.CENTER);
					break;
				}
				div.add(paragraphChild);
				cell.add(div);
				table.addCell(cell);
			}
			document.add(table);
			table.complete();
		}
		return resultBytes;
	}

	private String processLabel(TenantDto tenant, OrderDto orderDto) {
		return switch (orderDto.type()) {
		case INSTANT_ORDER, ONSITE_ORDER -> processOnsiteOrder(tenant, orderDto);
		case PRE_ORDER -> processPreOrder(tenant, orderDto);
		};
	}

	private String processOnsiteOrder(TenantDto tenant, OrderDto orderDto) {
		ZoneId zoneId = TenantHelper.getZoneId(tenant);
		List<String> labels = new ArrayList<>();
		if (StringUtils.isNotBlank(orderDto.orderNumber())) {
			labels.add(String.format("Order: #%s", orderDto.orderNumber()));
		}
		if (StringUtils.isNotBlank(orderDto.customerName())) {
			labels.add(String.format("Customer: %s", orderDto.customerName()));
		}
		String datePattern = "dd-MMM-yyyy";
		DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(datePattern);
		labels.add(String.format("Date: %s", dateFormatter.format(orderDto.createdAt()
				.atZone(zoneId))));
		List<String> newList = insertBetween(labels, System.lineSeparator());
		StringBuilder label = new StringBuilder();
		for (String s : newList) {
			label.append(s);
		}
		return label.toString();
	}

	private String processPreOrder(TenantDto tenant, OrderDto orderDto) {
		ZoneId zoneId = TenantHelper.getZoneId(tenant);
		List<String> labels = new ArrayList<>();

		StringBuilder orderNumber = new StringBuilder();
		if (StringUtils.isNotBlank(orderDto.orderNumber())) {
			orderNumber.append(String.format("Order: #%s", orderDto.orderNumber()));
		}
		if (Objects.nonNull(orderDto.preOrderMenuType())) {
			orderNumber.append(switch (orderDto.preOrderMenuType()) {
			case COLLECTION -> " - Pickup";
			case DELIVERY -> " - Delivery";
			});
		}
		labels.add(orderNumber.toString());

		if (StringUtils.isNotBlank(orderDto.customerName())) {
			labels.add(String.format("Customer: %s", orderDto.customerName()));
		}
		String datePattern = "dd-MMM-yyyy";
		DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(datePattern);
		labels.add(String.format("Date: %s", dateFormatter.format(orderDto.createdAt()
				.atZone(zoneId))));
		if (Objects.nonNull(orderDto.mealTime())) {
			MealTime mealTime = Optional.of(orderDto.mealTime())
					.orElse(MealTime.builder()
							.build());
			String timePattern = "h:mm a";
			labels.add(String.format("Meal Time: %s %s to %s", mealTime.getName(), DateTimeFormatter.ofPattern(
					timePattern)
					.format(mealTime.getStartTime()), DateTimeFormatter.ofPattern(timePattern)
							.format(mealTime.getEndTime())));
		}
		List<String> newList = insertBetween(labels, System.lineSeparator());
		StringBuilder label = new StringBuilder();
		for (String s : newList) {
			label.append(s);
		}
		return label.toString();
	}

	public static float toPx(float mm, RoundingMode roundingMode) {
		BigDecimal px = BigDecimal.valueOf(mm)
				.multiply(BigDecimal.valueOf(3.7795275591F))
				.setScale(1, roundingMode);
		return px.floatValue();
	}

	private <T> List<T> insertBetween(List<T> list, T separator) {
		List<T> result = new ArrayList<>();
		for (int i = 0; i < list.size(); i++) {
			result.add(list.get(i));
			if (i < list.size() - 1) {
				result.add(separator);
			}
		}
		return result;
	}

	private double calculateBorderSize(double pageWidth, double pageHeight, double borderRatio) {
		if (pageWidth <= 0 || pageHeight <= 0) {
			throw new IllegalArgumentException("Page dimensions must be positive numbers.");
		}
		if (borderRatio <= 0 || borderRatio > 1) {
			throw new IllegalArgumentException("Border ratio must be between 0 and 1.");
		}

		double smallerDimension = Math.min(pageWidth, pageHeight);
		return smallerDimension * borderRatio;
	}

}
