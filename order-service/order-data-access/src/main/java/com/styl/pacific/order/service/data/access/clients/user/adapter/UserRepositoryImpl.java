/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.clients.user.adapter;

import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.order.service.data.access.clients.user.UserProfileClient;
import com.styl.pacific.order.service.data.access.clients.user.mapper.UserFeignClientMapper;
import com.styl.pacific.order.service.domain.features.user.dto.UserDto;
import com.styl.pacific.order.service.domain.features.user.port.output.repository.UserRepository;
import com.styl.pacific.user.shared.http.users.request.FetchRuleRequest;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class UserRepositoryImpl implements UserRepository {
	private final UserProfileClient userProfileClient;

	@Override
	public Optional<UserDto> findById(UserId id) {
		try {
			UserDto userDto = UserFeignClientMapper.INSTANCE.userResponseToUserDto(userProfileClient.getUserProfile(id
					.getValue(), FetchRuleRequest.builder()
							.fetchPermissions(false)
							.countSponsors(false)
							.countSubAccounts(false)
							.build()));
			return Optional.of(userDto);
		} catch (Exception e) {
			return Optional.empty();
		}
	}
}
