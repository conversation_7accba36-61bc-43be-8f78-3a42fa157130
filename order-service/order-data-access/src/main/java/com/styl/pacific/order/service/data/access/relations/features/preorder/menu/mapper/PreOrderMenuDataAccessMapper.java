/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.relations.features.preorder.menu.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.order.service.data.access.jpa.features.preorder.menu.entity.PreOrderMenuEntity;
import com.styl.pacific.order.service.data.access.jpa.features.preorder.menu.entity.PreOrderMenuItemEntity;
import com.styl.pacific.order.service.data.access.relations.features.mealtime.mapper.MealTimeDataAccessMapper;
import com.styl.pacific.order.service.data.access.relations.features.product.mapper.ProductDataAccessMapper;
import com.styl.pacific.order.service.domain.features.common.mapper.CommonDataMapper;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.model.PreOrderMenuDto;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.model.PreOrderMenuItemDto;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.model.PreOrderMenuItemLightDto;
import com.styl.pacific.order.service.domain.features.preorder.menu.entity.PreOrderMenu;
import com.styl.pacific.order.service.domain.features.preorder.menu.entity.PreOrderMenuItem;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, uses = { MealTimeDataAccessMapper.class,
		ProductDataAccessMapper.class, CommonDataMapper.class, MapstructCommonDomainMapper.class,
		MapstructCommonMapper.class })
public interface PreOrderMenuDataAccessMapper {
	PreOrderMenuDataAccessMapper INSTANCE = Mappers.getMapper(PreOrderMenuDataAccessMapper.class);

	@Mapping(target = "id", source = "model.id", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "tenantId", source = "model.tenantId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "storeId", source = "model.storeId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "deletedAt", ignore = true)
	PreOrderMenuEntity toEntity(PreOrderMenu model);

	@Mapping(target = "id", source = "entity.id", qualifiedByName = "longToPreOrderMenuId")
	@Mapping(target = "tenantId", source = "entity.tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "storeId", source = "entity.storeId", qualifiedByName = "longToStoreId")
	PreOrderMenu toModel(PreOrderMenuEntity entity);

	@Mapping(target = "id", qualifiedByName = "longToPreOrderMenuId")
	@Mapping(target = "tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "storeId", qualifiedByName = "longToStoreId")
	PreOrderMenuDto toDto(PreOrderMenuEntity entity);

	@Mapping(target = "menu", ignore = true)
	@Mapping(target = "id", source = "model.id", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "chainId", source = "model.chainId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "productId", source = "model.productId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "product.id", source = "model.productId", qualifiedByName = "baseLongIdToLong", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
	@Mapping(target = "preOrderMenuId", source = "model.preOrderMenuId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "mealTimeId", source = "model.mealTimeId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "mealTime.id", source = "model.mealTimeId", qualifiedByName = "baseLongIdToLong", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
	@Mapping(target = "deletedAt", ignore = true)
	PreOrderMenuItemEntity toEntity(PreOrderMenuItem model);

	@Mapping(target = "id", source = "entity.id", qualifiedByName = "longToPreOrderMenuItemId")
	@Mapping(target = "chainId", source = "entity.chainId", qualifiedByName = "longToPreOrderMenuItemChainId")
	@Mapping(target = "productId", source = "entity.productId", qualifiedByName = "longToProductId")
	@Mapping(target = "preOrderMenuId", source = "entity.preOrderMenuId", qualifiedByName = "longToPreOrderMenuId")
	@Mapping(target = "mealTimeId", source = "entity.mealTimeId", qualifiedByName = "longToMealTimeId")
	PreOrderMenuItem toModel(PreOrderMenuItemEntity entity);

	@Mapping(target = "id", qualifiedByName = "longToPreOrderMenuItemId")
	@Mapping(target = "preOrderMenuId", qualifiedByName = "longToPreOrderMenuId")
	@Mapping(target = "chainId", qualifiedByName = "longToPreOrderMenuItemChainId")
	@Mapping(target = "mealTimeId", qualifiedByName = "longToMealTimeId")
	@Mapping(target = "mealTime", source = "mealTime")
	PreOrderMenuItemDto toDto(PreOrderMenuItemEntity entity);

	@Mapping(target = "id", qualifiedByName = "longToPreOrderMenuItemId")
	@Mapping(target = "preOrderMenuId", qualifiedByName = "longToPreOrderMenuId")
	@Mapping(target = "chainId", qualifiedByName = "longToPreOrderMenuItemChainId")
	@Mapping(target = "mealTimeId", qualifiedByName = "longToMealTimeId")
	PreOrderMenuItemLightDto toLightDto(PreOrderMenuItemEntity entity);
}
