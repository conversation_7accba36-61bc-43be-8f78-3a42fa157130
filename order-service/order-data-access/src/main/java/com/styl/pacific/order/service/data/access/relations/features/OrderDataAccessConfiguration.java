/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.relations.features;

import com.styl.pacific.application.rest.context.RequestContext;
import com.styl.pacific.application.rest.context.RequestContextSupporter;
import com.styl.pacific.domain.dto.TokenClaim;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * <AUTHOR>
 */

@Configuration
@EnableJpaAuditing
@EnableJpaRepositories(basePackages = { "com.styl.pacific.order.service.data.access" })
@EntityScan(basePackages = { "com.styl.pacific.order.service.data.access" })
@RequiredArgsConstructor
public class OrderDataAccessConfiguration {
	private final RequestContextSupporter requestContextSupporter;

	@Bean
	public AuditorAware<Long> auditorProvider() {
		return () -> requestContextSupporter.tryGetRequestContext()
				.map(RequestContext::getTokenClaim)
				.map(TokenClaim::getUserId)
				.map(Long::valueOf);
	}
}
