/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.relations.features.menuboard.adapter;

import com.styl.pacific.data.access.utils.JpaPageableUtils;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.MenuBoardId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.data.access.jpa.features.menuboard.entity.MenuBoardSessionEntity;
import com.styl.pacific.order.service.data.access.jpa.features.menuboard.repository.MenuBoardSessionJpaRepository;
import com.styl.pacific.order.service.data.access.jpa.features.menuboard.specification.MenuBoardSessionSpecifications;
import com.styl.pacific.order.service.data.access.relations.features.menuboard.mapper.MenuBoardSessionDataAccessMapper;
import com.styl.pacific.order.service.domain.features.menuboard.dto.MenuBoardSessionDetailDto;
import com.styl.pacific.order.service.domain.features.menuboard.dto.query.MenuBoardSessionFilter;
import com.styl.pacific.order.service.domain.features.menuboard.dto.query.PaginationMenuBoardSessionQuery;
import com.styl.pacific.order.service.domain.features.menuboard.entity.MenuBoardSession;
import com.styl.pacific.order.service.domain.features.menuboard.ports.output.repository.MenuBoardSessionRepository;
import com.styl.pacific.order.service.domain.features.menuboard.valueobjects.MenuBoardSessionId;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class MenuBoardSessionRepositoryImpl implements MenuBoardSessionRepository {
	private final MenuBoardSessionJpaRepository menuBoardSessionJpaRepository;

	@Override
	public Optional<MenuBoardSessionDetailDto> findDetailById(TenantId tenantId, MenuBoardSessionId id) {
		Specification<MenuBoardSessionEntity> specification = MenuBoardSessionSpecifications.withTenantIdAndId(Optional
				.ofNullable(tenantId)
				.map(TenantId::getValue)
				.orElse(null), id.getValue());
		return menuBoardSessionJpaRepository.findOne(specification)
				.map(MenuBoardSessionDataAccessMapper.INSTANCE::toDto);
	}

	@Override
	public Optional<MenuBoardSession> findById(TenantId tenantId, MenuBoardSessionId id) {
		Specification<MenuBoardSessionEntity> specification = MenuBoardSessionSpecifications.withTenantIdAndId(Optional
				.ofNullable(tenantId)
				.map(TenantId::getValue)
				.orElse(null), id.getValue());
		return menuBoardSessionJpaRepository.findOne(specification)
				.map(MenuBoardSessionDataAccessMapper.INSTANCE::toModel);
	}

	@Override
	public Paging<MenuBoardSession> findAllPaging(TenantId tenantId, PaginationMenuBoardSessionQuery query) {
		Pageable pageable = JpaPageableUtils.createPageable(query.getPage(), query.getSize(), query.getSortFields(),
				query.getSortDirection(), MenuBoardSessionEntity.SORTABLE_FIELDS, MenuBoardSessionEntity.FIELD_ID);
		MenuBoardSessionFilter filter = Optional.ofNullable(query.getFilter())
				.orElse(MenuBoardSessionFilter.builder()
						.build());
		Specification<MenuBoardSessionEntity> specification = MenuBoardSessionSpecifications.withMultipleCriteria(
				Optional.ofNullable(tenantId)
						.map(TenantId::getValue)
						.orElse(null), filter.menuBoardIds(), filter.name());
		Page<MenuBoardSession> page = menuBoardSessionJpaRepository.findAll(specification, pageable)
				.map(MenuBoardSessionDataAccessMapper.INSTANCE::toModel);
		return Paging.<MenuBoardSession>builder()
				.page(page.getPageable()
						.getPageNumber())
				.sort(page.getPageable()
						.getSort()
						.toList()
						.stream()
						.map(Sort.Order::toString)
						.toList())
				.content(page.getContent())
				.totalElements(page.getTotalElements())
				.totalPages(page.getTotalPages())
				.build();
	}

	@Override
	public MenuBoardSession save(MenuBoardSession menuBoardSession) {
		MenuBoardSessionEntity entity = MenuBoardSessionDataAccessMapper.INSTANCE.toEntity(menuBoardSession);
		return MenuBoardSessionDataAccessMapper.INSTANCE.toModel(menuBoardSessionJpaRepository.save(entity));
	}

	@Override
	public MenuBoardSessionDetailDto saveDetail(MenuBoardSession menuBoardSession) {
		MenuBoardSessionEntity entity = MenuBoardSessionDataAccessMapper.INSTANCE.toEntity(menuBoardSession);
		return MenuBoardSessionDataAccessMapper.INSTANCE.toDto(menuBoardSessionJpaRepository.save(entity));
	}

	@Override
	public boolean existsById(TenantId tenantId, MenuBoardSessionId id) {
		Specification<MenuBoardSessionEntity> specification = MenuBoardSessionSpecifications.withTenantIdAndId(Optional
				.ofNullable(tenantId)
				.map(TenantId::getValue)
				.orElse(null), id.getValue());
		return menuBoardSessionJpaRepository.exists(specification);
	}

	@Override
	public boolean existsByMenuBoardId(TenantId tenantId, MenuBoardId menuBoardId) {
		Specification<MenuBoardSessionEntity> specification = MenuBoardSessionSpecifications.withTenantIdAndMenuBoardId(
				Optional.ofNullable(tenantId)
						.map(TenantId::getValue)
						.orElse(null), menuBoardId.getValue());
		return menuBoardSessionJpaRepository.exists(specification);
	}

	@Override
	public void deleteById(TenantId tenantId, MenuBoardSessionId id) {
		Specification<MenuBoardSessionEntity> specification = MenuBoardSessionSpecifications.withTenantIdAndId(Optional
				.ofNullable(tenantId)
				.map(TenantId::getValue)
				.orElse(null), id.getValue());
		menuBoardSessionJpaRepository.delete(specification);
	}

	@Override
	public void deleteByMenuBoardId(TenantId tenantId, MenuBoardId menuBoardId) {
		Specification<MenuBoardSessionEntity> specification = MenuBoardSessionSpecifications.withTenantIdAndMenuBoardId(
				Optional.ofNullable(tenantId)
						.map(TenantId::getValue)
						.orElse(null), menuBoardId.getValue());
		menuBoardSessionJpaRepository.delete(specification);
	}
}
