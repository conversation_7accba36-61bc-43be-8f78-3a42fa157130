/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.clients.store.adapter;

import com.styl.pacific.common.feign.exception.FeignBadRequestException;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.StoreId;
import com.styl.pacific.order.service.data.access.clients.store.StoreClient;
import com.styl.pacific.order.service.data.access.clients.store.mapper.StoreClientMapper;
import com.styl.pacific.order.service.domain.features.store.dto.StoreDto;
import com.styl.pacific.order.service.domain.features.store.dto.query.PaginationStoreQuery;
import com.styl.pacific.order.service.domain.features.store.ports.output.repository.StoreRepository;
import com.styl.pacific.store.shared.http.requests.store.FindStoresRequest;
import com.styl.pacific.store.shared.http.responses.store.ListStoresResponse;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class StoreRepositoryImpl implements StoreRepository {
	private static final Logger log = LoggerFactory.getLogger(StoreRepositoryImpl.class);
	private final StoreClient storeClient;

	@Override
	public Optional<StoreDto> findById(StoreId id) {
		try {
			return Optional.ofNullable(storeClient.getStore(id.getValue()))
					.filter(responseEntity -> responseEntity.getStatusCode()
							.is2xxSuccessful())
					.map(ResponseEntity::getBody)
					.map(StoreClientMapper.INSTANCE::toDto);
		} catch (FeignBadRequestException exception) {
			log.error("Error when calling store service", exception);
			return Optional.empty();
		}
	}

	@Override
	public Paging<StoreDto> findAllPaging(PaginationStoreQuery query) {
		FindStoresRequest request = StoreClientMapper.INSTANCE.toQuery(query);
		try {
			final var response = storeClient.findStores(request);
			if (!Boolean.TRUE.equals(response.getStatusCode()
					.is2xxSuccessful())) {
				throw new FeignBadRequestException("Error when calling store service");
			}
			ListStoresResponse listStoresResponse = Objects.requireNonNull(response.getBody());

			return Paging.<StoreDto>builder()
					.content(listStoresResponse.getContent()
							.stream()
							.map(StoreClientMapper.INSTANCE::toDto)
							.toList())
					.page(listStoresResponse.getPage())
					.totalElements(listStoresResponse.getTotalElements())
					.totalPages(listStoresResponse.getTotalPages())
					.sort(listStoresResponse.getSort())
					.build();
		} catch (FeignBadRequestException exception) {
			log.error("Error when calling store service", exception);
			throw exception;
		}

	}
}
