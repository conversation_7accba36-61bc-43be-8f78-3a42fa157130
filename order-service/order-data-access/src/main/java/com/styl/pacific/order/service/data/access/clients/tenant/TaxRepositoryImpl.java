/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.clients.tenant;

import com.styl.pacific.application.rest.exception.PacificFeignException;
import com.styl.pacific.order.service.data.access.clients.tenant.mapper.TaxClientMapper;
import com.styl.pacific.order.service.domain.features.tenant.dto.TaxDto;
import com.styl.pacific.order.service.domain.features.tenant.output.repository.TaxRepository;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TaxResponse;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class TaxRepositoryImpl implements TaxRepository {
	private static final Logger log = LoggerFactory.getLogger(TaxRepositoryImpl.class);
	private final TaxClient taxClient;

	@Override
	public Optional<TaxDto> getCurrentTax() {
		try {
			TaxResponse taxResponse = taxClient.getCurrentTax()
					.getBody();
			return Optional.ofNullable(TaxClientMapper.INSTANCE.toDto(taxResponse));
		} catch (PacificFeignException e) {
			log.info("Get current tax failed: {}", e.getMessage(), e);
			return Optional.empty();
		}
	}
}
