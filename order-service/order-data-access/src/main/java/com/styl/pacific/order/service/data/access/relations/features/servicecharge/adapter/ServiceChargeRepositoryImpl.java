/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.relations.features.servicecharge.adapter;

import com.styl.pacific.domain.valueobject.BaseId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.data.access.jpa.features.servicecharge.entity.ServiceChargeByPlatformEntity;
import com.styl.pacific.order.service.data.access.jpa.features.servicecharge.entity.ServiceChargeByPlatformEntityId;
import com.styl.pacific.order.service.data.access.jpa.features.servicecharge.entity.ServiceChargeEntity;
import com.styl.pacific.order.service.data.access.jpa.features.servicecharge.repository.ServiceChargeByPlatformJpaRepository;
import com.styl.pacific.order.service.data.access.jpa.features.servicecharge.repository.ServiceChargeJpaRepository;
import com.styl.pacific.order.service.data.access.jpa.features.servicecharge.specification.ServiceChargeByPlatformSpecifications;
import com.styl.pacific.order.service.data.access.jpa.features.servicecharge.specification.ServiceChargeSpecifications;
import com.styl.pacific.order.service.data.access.relations.features.servicecharge.mapper.ServiceChargeDataAccessMapper;
import com.styl.pacific.order.service.domain.features.servicecharge.dto.model.ServiceChargeDetailDTO;
import com.styl.pacific.order.service.domain.features.servicecharge.dto.query.ServiceChargeFilter;
import com.styl.pacific.order.service.domain.features.servicecharge.dto.query.ServiceChargeQuery;
import com.styl.pacific.order.service.domain.features.servicecharge.entity.ServiceCharge;
import com.styl.pacific.order.service.domain.features.servicecharge.entity.ServiceChargeByPlatform;
import com.styl.pacific.order.service.domain.features.servicecharge.enums.PlatformOrderType;
import com.styl.pacific.order.service.domain.features.servicecharge.mapper.ServiceChargeDataMapper;
import com.styl.pacific.order.service.domain.features.servicecharge.ports.output.repository.ServiceChargeRepository;
import com.styl.pacific.order.service.domain.features.servicecharge.valueobjects.ServiceChargeId;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class ServiceChargeRepositoryImpl implements ServiceChargeRepository {
	private final ServiceChargeJpaRepository serviceChargeJpaRepository;
	private final ServiceChargeByPlatformJpaRepository serviceChargeByPlatformJpaRepository;

	@Override
	public List<ServiceChargeDetailDTO> findDetailsAll(TenantId tenantId, ServiceChargeQuery query) {
		ServiceChargeFilter filter = Optional.ofNullable(query.getFilter())
				.orElse(ServiceChargeFilter.builder()
						.build());
		Specification<ServiceChargeEntity> specification = ServiceChargeSpecifications.withMultipleCriteria(Optional.of(
				tenantId)
				.map(TenantId::getValue)
				.orElse(null), filter.getIsActive(), filter.getPlatformOrderTypes(), filter.getServiceChargeIds());

		List<ServiceChargeEntity> serviceCharges = serviceChargeJpaRepository.findAll(specification);

		List<Long> ids = serviceCharges.stream()
				.map(ServiceChargeEntity::getId)
				.toList();
		Specification<ServiceChargeByPlatformEntity> chargeByPlatformEntitySpecification = ServiceChargeByPlatformSpecifications
				.withInServiceChargeIdsWithoutEmpty(ids);
		Map<Long, List<ServiceChargeByPlatformEntity>> entityMap = serviceChargeByPlatformJpaRepository.findAll(
				chargeByPlatformEntitySpecification)
				.stream()
				.collect(Collectors.groupingBy(serviceChargeByPlatformEntity -> serviceChargeByPlatformEntity.getId()
						.getServiceChargeId()));
		return serviceCharges.stream()
				.map(serviceChargeEntity -> {
					List<ServiceChargeByPlatform> serviceChargeByPlatformEntities = entityMap.get(serviceChargeEntity
							.getId())
							.stream()
							.map(ServiceChargeDataAccessMapper.INSTANCE::toModel)
							.toList();
					ServiceCharge serviceCharge = ServiceChargeDataAccessMapper.INSTANCE.toModel(serviceChargeEntity);

					return ServiceChargeDataMapper.INSTANCE.toDetailDTO(serviceCharge, serviceChargeByPlatformEntities
							.stream()
							.map(ServiceChargeByPlatform::getPlatformOrderType)
							.toList());
				})
				.toList();
	}

	@Override
	public List<ServiceCharge> findAll(TenantId tenantId, ServiceChargeQuery query) {
		ServiceChargeFilter filter = Optional.ofNullable(query.getFilter())
				.orElse(ServiceChargeFilter.builder()
						.build());
		Specification<ServiceChargeEntity> specification = ServiceChargeSpecifications.withMultipleCriteria(Optional.of(
				tenantId)
				.map(TenantId::getValue)
				.orElse(null), filter.getIsActive(), filter.getPlatformOrderTypes(), filter.getServiceChargeIds());
		return serviceChargeJpaRepository.findAll(specification)
				.stream()
				.map(ServiceChargeDataAccessMapper.INSTANCE::toModel)
				.toList();
	}

	@Override
	public Optional<ServiceCharge> findById(TenantId tenantId, ServiceChargeId id) {
		return serviceChargeJpaRepository.findOne(ServiceChargeSpecifications.withTenantIdAndId(Optional.of(tenantId)
				.map(TenantId::getValue)
				.orElse(null), Optional.of(id)
						.map(ServiceChargeId::getValue)
						.orElse(null)))
				.map(ServiceChargeDataAccessMapper.INSTANCE::toModel);
	}

	@Override
	public Optional<ServiceChargeDetailDTO> findDetailById(TenantId tenantId, ServiceChargeId id) {
		Specification<ServiceChargeEntity> specification = ServiceChargeSpecifications.withTenantIdAndId(Optional.of(
				tenantId)
				.map(TenantId::getValue)
				.orElse(null), Optional.of(id)
						.map(ServiceChargeId::getValue)
						.orElse(null));

		return serviceChargeJpaRepository.findBy(specification, q -> q.project(
				ServiceChargeEntity.FIELD_SERVICE_CHARGE_BY_PLATFORMS)
				.limit(1)
				.one())
				.map(serviceChargeEntity -> {
					List<ServiceChargeByPlatform> serviceChargeByPlatformEntities = serviceChargeEntity
							.getServiceChargeByPlatforms()
							.stream()
							.map(ServiceChargeDataAccessMapper.INSTANCE::toModel)
							.toList();
					ServiceCharge serviceCharge = ServiceChargeDataAccessMapper.INSTANCE.toModel(serviceChargeEntity);

					return ServiceChargeDataMapper.INSTANCE.toDetailDTO(serviceCharge, serviceChargeByPlatformEntities
							.stream()
							.map(ServiceChargeByPlatform::getPlatformOrderType)
							.toList());
				});
	}

	@Override
	public ServiceCharge save(ServiceCharge serviceCharge) {
		ServiceChargeEntity entity = ServiceChargeDataAccessMapper.INSTANCE.toEntity(serviceCharge);
		return ServiceChargeDataAccessMapper.INSTANCE.toModel(serviceChargeJpaRepository.save(entity));
	}

	@Override
	public ServiceCharge update(ServiceCharge serviceCharge) {
		Specification<ServiceChargeEntity> specification = ServiceChargeSpecifications.withTenantIdAndId(Optional.of(
				serviceCharge)
				.map(ServiceCharge::getTenantId)
				.map(BaseId::getValue)
				.orElse(null), Optional.of(serviceCharge)
						.map(ServiceCharge::getId)
						.map(BaseId::getValue)
						.orElse(null));
		Optional<ServiceChargeEntity> optional = serviceChargeJpaRepository.findOne(specification);
		if (optional.isPresent()) {
			ServiceChargeEntity serviceChargeEntity = optional.get();
			ServiceChargeDataAccessMapper.INSTANCE.updateEntity(serviceChargeEntity, serviceCharge);
			return ServiceChargeDataAccessMapper.INSTANCE.toModel(serviceChargeJpaRepository.saveAndFlush(
					serviceChargeEntity));
		} else {
			ServiceChargeEntity entity = ServiceChargeDataAccessMapper.INSTANCE.toEntity(serviceCharge);
			return ServiceChargeDataAccessMapper.INSTANCE.toModel(serviceChargeJpaRepository.saveAndFlush(entity));
		}
	}

	@Override
	public void delete(TenantId tenantId, ServiceChargeId id) {
		Specification<ServiceChargeByPlatformEntity> chargeByPlatformEntitySpecification = ServiceChargeByPlatformSpecifications
				.withServiceChargeId(Optional.of(id)
						.map(ServiceChargeId::getValue)
						.orElse(null));
		serviceChargeByPlatformJpaRepository.delete(chargeByPlatformEntitySpecification);
		Specification<ServiceChargeEntity> specification = ServiceChargeSpecifications.withTenantIdAndId(Optional.of(
				tenantId)
				.map(TenantId::getValue)
				.orElse(null), Optional.of(id)
						.map(ServiceChargeId::getValue)
						.orElse(null));
		serviceChargeJpaRepository.delete(specification);
	}

	@Override
	public boolean existsById(TenantId tenantId, ServiceChargeId id) {
		Specification<ServiceChargeEntity> specification = ServiceChargeSpecifications.withTenantIdAndId(Optional.of(
				tenantId)
				.map(TenantId::getValue)
				.orElse(null), Optional.of(id)
						.map(ServiceChargeId::getValue)
						.orElse(null));
		return serviceChargeJpaRepository.exists(specification);
	}

	@Override
	public boolean existsByName(TenantId tenantId, String name) {
		Specification<ServiceChargeEntity> specification = ServiceChargeSpecifications.withTenantIdAndNameIgnoreCase(
				Optional.of(tenantId)
						.map(TenantId::getValue)
						.orElse(null), name);
		return serviceChargeJpaRepository.exists(specification);
	}

	@Override
	public boolean existsByNameAndNotId(TenantId tenantId, ServiceChargeId id, String name) {
		Specification<ServiceChargeEntity> specification = ServiceChargeSpecifications
				.withTenantIdAndNotIdAndNameIgnoreCase(Optional.of(tenantId)
						.map(TenantId::getValue)
						.orElse(null), Optional.of(id)
								.map(ServiceChargeId::getValue)
								.orElse(null), name);
		return serviceChargeJpaRepository.exists(specification);
	}

	@Override
	public List<ServiceCharge> findAllByPlatform(TenantId tenantId, PlatformOrderType platformOrderType,
			Boolean isActive) {
		Specification<ServiceChargeByPlatformEntity> specification = ServiceChargeByPlatformSpecifications
				.withTenantIdAndPlatformOrderType(Optional.of(tenantId)
						.map(TenantId::getValue)
						.orElse(null), platformOrderType);
		List<Long> serviceChargeIds = serviceChargeByPlatformJpaRepository.findAll(specification)
				.stream()
				.map(ServiceChargeByPlatformEntity::getId)
				.map(ServiceChargeByPlatformEntityId::getServiceChargeId)
				.toList();
		Specification<ServiceChargeEntity> serviceChargeEntitySpecification = ServiceChargeSpecifications
				.withTenantIdAndIdsAndStatus(Optional.of(tenantId)
						.map(TenantId::getValue)
						.orElse(null), serviceChargeIds, isActive);
		return serviceChargeJpaRepository.findAll(serviceChargeEntitySpecification)
				.stream()
				.map(ServiceChargeDataAccessMapper.INSTANCE::toModel)
				.toList();
	}
}
