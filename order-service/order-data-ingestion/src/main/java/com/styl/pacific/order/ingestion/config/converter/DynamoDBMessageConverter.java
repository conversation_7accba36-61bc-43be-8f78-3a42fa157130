/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.ingestion.config.converter;

import com.amazonaws.services.lambda.runtime.events.DynamodbEvent;
import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import jakarta.annotation.Nullable;
import java.io.IOException;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.NonNull;
import org.springframework.messaging.Message;
import org.springframework.messaging.converter.AbstractMessageConverter;
import org.springframework.util.MimeType;

/**
 * <AUTHOR>
 */
public class DynamoDBMessageConverter extends AbstractMessageConverter {
	private static final Logger iLogger = LoggerFactory.getLogger(DynamoDBMessageConverter.class);
	private final ObjectMapper objectMapper;

	public DynamoDBMessageConverter() {
		super(new MimeType("application", "ddb"));
		this.objectMapper = new RecordObjectMapper();
		SimpleModule module = new SimpleModule();
		module.addDeserializer(Date.class, new JsonDeserializer<Date>() {
			@Override
			public Date deserialize(JsonParser jsonParser, DeserializationContext deserializationContext)
					throws IOException, JacksonException {
				double approximateCreationDateTime = jsonParser.getValueAsDouble();
				return new Date((long) approximateCreationDateTime * 1000);
			}
		});
		objectMapper.registerModule(module);
	}

	@Override
	protected boolean supports(Class<?> cls) {
		return cls.equals(DynamodbEvent.DynamodbStreamRecord.class);
	}

	@Override
	protected Object convertFromInternal(@NonNull Message<?> message, @NonNull Class<?> targetClass,
			@Nullable Object conversionHint) {
		try {
			iLogger.info(new String((byte[]) message.getPayload()));
			return objectMapper.readValue((byte[]) message.getPayload(), DynamodbEvent.DynamodbStreamRecord.class);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}

}
