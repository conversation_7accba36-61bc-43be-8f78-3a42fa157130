/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.acces.dynamodb.features.order.repository;

import com.styl.pacific.aws.dynamodb.repository.DynamoDbRepository;
import com.styl.pacific.order.service.data.acces.dynamodb.features.order.entity.OrderDocument;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;

@Repository
public class OrderDynamoDbRepository extends DynamoDbRepository<OrderDocument, Long> {
	protected OrderDynamoDbRepository(DynamoDbClient dynamoDbClient, DynamoDbEnhancedClient dynamoDbEnhancedClient) {
		super(dynamoDbClient, dynamoDbEnhancedClient);
	}

	@Override
	protected @NonNull Class<OrderDocument> getClassType() {
		return OrderDocument.class;
	}

	@Override
	protected @NonNull String getTableName() {
		return OrderDocument.TABLE_NAME;
	}
}
