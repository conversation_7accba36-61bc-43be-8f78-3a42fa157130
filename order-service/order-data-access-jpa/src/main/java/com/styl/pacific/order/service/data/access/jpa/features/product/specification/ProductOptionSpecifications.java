/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.jpa.features.product.specification;

import com.styl.pacific.data.access.jpa.specification.BaseSpecification;
import com.styl.pacific.order.service.data.access.jpa.features.product.entity.ProductOptionEntity;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import org.springframework.data.jpa.domain.Specification;

/**
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ProductOptionSpecifications {

	public static Specification<ProductOptionEntity> withInProductIds(List<Long> ids) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<ProductOptionEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return in(root.get(ProductOptionEntity.FIELD_PRODUCT_ID), new ArrayList<>(ids));
			}
		};
	}

	public static Specification<ProductOptionEntity> withProductId(Long id) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<ProductOptionEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(ProductOptionEntity.FIELD_PRODUCT_ID), id);
			}
		};
	}
}
