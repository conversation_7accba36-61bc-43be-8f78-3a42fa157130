/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.jpa.features.inventory.repository;

import com.styl.pacific.order.service.data.access.jpa.features.inventory.entity.InventoryEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

/**
 * <AUTHOR>
 */
public interface InventoryJpaRepository extends JpaRepository<InventoryEntity, Long>,
		JpaSpecificationExecutor<InventoryEntity> {

	@Modifying
	@Query("UPDATE InventoryEntity i SET i.quantity = i.quantity + :quantity, i.version = i.version + 1 WHERE i.tenantId = :tenantId AND i.productId = :productId AND i.quantity >= abs(:quantity)")
	int addQuantity(Long tenantId, Long productId, Long quantity);

}
