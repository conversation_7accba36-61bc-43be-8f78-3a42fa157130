/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.jpa.features.servicecharge.specification;

import com.styl.pacific.data.access.jpa.specification.BaseSpecification;
import com.styl.pacific.order.service.data.access.jpa.features.servicecharge.entity.ServiceChargeByPlatformEntity;
import com.styl.pacific.order.service.data.access.jpa.features.servicecharge.entity.ServiceChargeByPlatformEntityId;
import com.styl.pacific.order.service.domain.features.servicecharge.enums.PlatformOrderType;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.lang.NonNull;

/**
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ServiceChargeByPlatformSpecifications {

	public static Specification<ServiceChargeByPlatformEntity> withTenantIdAndPlatformOrderType(Long tenantId,
			PlatformOrderType platformOrderType) {
		List<Specification<ServiceChargeByPlatformEntity>> specifications = new ArrayList<>();
		if (Objects.nonNull(tenantId)) {
			specifications.add(withTenantId(tenantId));
		}
		if (Objects.nonNull(platformOrderType)) {
			specifications.add(withPlatformOrderType(platformOrderType));
		}
		return specifications.stream()
				.reduce(Specification::and)
				.orElse((root, query, criteriaBuilder) -> criteriaBuilder.conjunction());
	}

	public static Specification<ServiceChargeByPlatformEntity> withTenantIdAndServiceChargeId(Long tenantId,
			Long serviceChargeId) {
		List<Specification<ServiceChargeByPlatformEntity>> specifications = new ArrayList<>();
		if (Objects.nonNull(tenantId)) {
			specifications.add(withTenantId(tenantId));
		}
		if (Objects.nonNull(serviceChargeId)) {
			specifications.add(withServiceChargeId(serviceChargeId));
		}
		return specifications.stream()
				.reduce(Specification::and)
				.orElse((root, query, criteriaBuilder) -> criteriaBuilder.conjunction());
	}

	public static Specification<ServiceChargeByPlatformEntity> withServiceChargeIdAndNotInPlatformOrderType(
			Long serviceChargeId, List<PlatformOrderType> platformOrderTypes) {
		List<Specification<ServiceChargeByPlatformEntity>> specifications = new ArrayList<>();
		if (Objects.nonNull(serviceChargeId)) {
			specifications.add(withServiceChargeId(serviceChargeId));
		}
		if (Objects.nonNull(platformOrderTypes) && !platformOrderTypes.isEmpty()) {
			specifications.add(withNotInPlatformOrderType(platformOrderTypes));
		}
		return specifications.stream()
				.reduce(Specification::and)
				.orElse((root, query, criteriaBuilder) -> criteriaBuilder.conjunction());
	}

	public static Specification<ServiceChargeByPlatformEntity> withInServiceChargeIdsWithoutEmpty(
			List<Long> serviceChargeIds) {
		List<Specification<ServiceChargeByPlatformEntity>> specifications = new ArrayList<>();
		if (Objects.nonNull(serviceChargeIds) && !serviceChargeIds.isEmpty()) {
			specifications.add(withInServiceChargeIds(serviceChargeIds));
		} else {
			specifications.add((root, query, criteriaBuilder) -> criteriaBuilder.disjunction());
		}
		return specifications.stream()
				.reduce(Specification::and)
				.orElse((root, query, criteriaBuilder) -> criteriaBuilder.conjunction());
	}

	public static Specification<ServiceChargeByPlatformEntity> withServiceChargeId(Long serviceChargeId) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<ServiceChargeByPlatformEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(ServiceChargeByPlatformEntity.FIELD_ID)
						.get(ServiceChargeByPlatformEntityId.SERVICE_CHARGE_ID), serviceChargeId);
			}
		};
	}

	public static Specification<ServiceChargeByPlatformEntity> withInServiceChargeIds(List<Long> serviceChargeIds) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<ServiceChargeByPlatformEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return root.get(ServiceChargeByPlatformEntity.FIELD_ID)
						.get(ServiceChargeByPlatformEntityId.SERVICE_CHARGE_ID)
						.in(serviceChargeIds);
			}
		};
	}

	public static Specification<ServiceChargeByPlatformEntity> withPlatformOrderType(PlatformOrderType type) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<ServiceChargeByPlatformEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(ServiceChargeByPlatformEntity.FIELD_ID)
						.get(ServiceChargeByPlatformEntityId.PLATFORM_ORDER_TYPE), type);
			}
		};
	}

	public static Specification<ServiceChargeByPlatformEntity> withNotInPlatformOrderType(
			List<PlatformOrderType> platformOrderTypes) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<ServiceChargeByPlatformEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return root.get(ServiceChargeByPlatformEntity.FIELD_ID)
						.get(ServiceChargeByPlatformEntityId.PLATFORM_ORDER_TYPE)
						.in(platformOrderTypes)
						.not();
			}
		};
	}

	public static Specification<ServiceChargeByPlatformEntity> withTenantId(Long tenantId) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<ServiceChargeByPlatformEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(ServiceChargeByPlatformEntity.FIELD_TENANT_ID), tenantId);
			}
		};
	}
}
