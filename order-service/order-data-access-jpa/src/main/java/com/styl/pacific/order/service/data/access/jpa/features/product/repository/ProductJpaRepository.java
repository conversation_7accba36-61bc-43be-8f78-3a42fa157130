/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.jpa.features.product.repository;

import com.styl.pacific.order.service.data.access.jpa.features.product.alias.ProductBriefAlias;
import com.styl.pacific.order.service.data.access.jpa.features.product.entity.ProductEntity;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * <AUTHOR>
 */
public interface ProductJpaRepository extends JpaRepository<ProductEntity, Long>,
		JpaSpecificationExecutor<ProductEntity> {
	boolean existsByTenantIdAndId(Long tenantId, Long id);

	Optional<ProductEntity> findByTenantIdAndId(Long tenantId, Long id);

	@Query(value = "SELECT p.id as id, p.tenantId as tenantId, p.storeId as storeId, p.name as name, p.sku as sku, p.barcode as barcode, "
			+ "p.unitPrice as unitPrice, p.listingPrice as listingPrice, p.currencyCode as currencyCode, p.preparationTime as preparationTime, "
			+ "p.status as status, p.createdAt as createdAt, p.updatedAt as updatedAt "
			+ "FROM ProductEntity p "
			+ "WHERE p.tenantId = :tenantId AND p.storeId = :storeId AND p.id IN (:ids) and p.deletedAt is null")
	List<ProductBriefAlias> findAllBriefByTenantIdAndStoreIdAndListIds(@Param("tenantId") Long tenantId,
			@Param("storeId") Long storeId, @Param("ids") List<Long> ids);

	List<ProductEntity> findAllByTenantIdAndStoreIdAndIdIn(Long tenantId, Long storeId, List<Long> ids);

	@Query(value = "SELECT p.id as id, p.tenantId as tenantId, p.storeId as storeId, p.name as name, p.sku as sku, p.barcode as barcode, "
			+ "p.unitPrice as unitPrice, p.listingPrice as listingPrice, p.currencyCode as currencyCode, p.preparationTime as preparationTime, "
			+ "p.status as status, p.createdAt as createdAt, p.updatedAt as updatedAt "
			+ "FROM ProductEntity p "
			+ "WHERE p.tenantId = :tenantId AND p.id = :id and p.deletedAt is null")
	Optional<ProductBriefAlias> findBriefById(Long tenantId, Long id);
}
