/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.jpa.features.menu.entity;

import com.styl.pacific.data.access.entity.AuditableEntity;
import com.styl.pacific.order.service.data.access.jpa.features.menu.entity.arrangement.ArrangementEntity;
import com.styl.pacific.order.service.data.access.jpa.features.product.entity.ProductEntity;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import java.time.Instant;
import java.util.List;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Table(name = "tb_menu_item")
@AllArgsConstructor
@NoArgsConstructor
public class MenuItemEntity extends AuditableEntity {
	public static final String FIELD_ID = "id";
	public static final String FIELD_MENU_ID = "menuId";
	public static final String FIELD_PRODUCT = "product";
	public static final String FIELD_PRODUCT_ID = "productId";
	public static final String FIELD_POSITION = "position";
	public static final String FIELD_ARRANGEMENT = "arrangement";
	public static final String FIELD_DELETED_AT = "deletedAt";

	public static final Set<String> SORTABLE_FIELDS = Set.of(String.join("_", List.of(FIELD_PRODUCT,
			ProductEntity.FIELD_ID)), FIELD_POSITION);

	@Id
	private Long id;

	@Column(name = "product_id", nullable = false)
	private Long productId;

	@ManyToOne
	@JoinColumn(name = "product_id", insertable = false, updatable = false)
	private ProductEntity product;

	@Column(nullable = false)
	private Integer position;

	@Column(name = "menu_id", nullable = false)
	private Long menuId;

	@OneToOne(cascade = CascadeType.ALL)
	@JoinColumn(name = "id", referencedColumnName = "menu_item_id")
	private ArrangementEntity arrangement;

	private MenuItemEntity(Builder builder) {
		setDeletedAt(builder.deletedAt);
		setCreatedAt(builder.createdAt);
		setUpdatedAt(builder.updatedAt);
		setId(builder.id);
		setProductId(builder.productId);
		setProduct(builder.product);
		setPosition(builder.position);
		setMenuId(builder.menuId);
		setArrangement(builder.arrangement);
	}

	public static Builder builder() {
		return new Builder();
	}

	public static final class Builder {
		private Instant deletedAt;
		private Instant createdAt;
		private Instant updatedAt;
		private Long id;
		private Long productId;
		private ProductEntity product;
		private Integer position;
		private Long menuId;
		private ArrangementEntity arrangement;

		private Builder() {
		}

		public Builder deletedAt(Instant deletedAt) {
			this.deletedAt = deletedAt;
			return this;
		}

		public Builder createdAt(Instant createdAt) {
			this.createdAt = createdAt;
			return this;
		}

		public Builder updatedAt(Instant updatedAt) {
			this.updatedAt = updatedAt;
			return this;
		}

		public Builder id(Long id) {
			this.id = id;
			return this;
		}

		public Builder productId(Long productId) {
			this.productId = productId;
			return this;
		}

		public Builder product(ProductEntity product) {
			this.product = product;
			return this;
		}

		public Builder position(Integer position) {
			this.position = position;
			return this;
		}

		public Builder menuId(Long menuId) {
			this.menuId = menuId;
			return this;
		}

		public Builder arrangement(ArrangementEntity arrangement) {
			this.arrangement = arrangement;
			return this;
		}

		public MenuItemEntity build() {
			return new MenuItemEntity(this);
		}
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;

		if (!(o instanceof MenuItemEntity that))
			return false;

		return new EqualsBuilder().appendSuper(super.equals(o))
				.append(id, that.id)
				.append(productId, that.productId)
				.append(product, that.product)
				.append(position, that.position)
				.append(menuId, that.menuId)
				.append(arrangement, that.arrangement)
				.isEquals();
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder(17, 37).appendSuper(super.hashCode())
				.append(id)
				.append(productId)
				.append(product)
				.append(position)
				.append(menuId)
				.append(arrangement)
				.toHashCode();
	}
}
