/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.jpa.features.servicecharge.entity;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.Instant;
import java.util.List;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

/**
 * <AUTHOR>
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Entity
@Table(name = "service_charges")
@EntityListeners(AuditingEntityListener.class)
public class ServiceChargeEntity {

	public static final String FIELD_ID = "id";
	public static final String FIELD_TENANT_ID = "tenantId";
	public static final String FIELD_IS_ACTIVE = "isActive";
	public static final String FIELD_NAME = "name";
	public static final String FIELD_SERVICE_CHARGE_BY_PLATFORMS = "serviceChargeByPlatforms";
	public static final String FIELD_CREATED_AT = "createdAt";

	public static final Set<String> SORTABLE_FIELDS = Set.of(FIELD_ID, FIELD_TENANT_ID, FIELD_IS_ACTIVE, FIELD_NAME,
			FIELD_CREATED_AT);

	@Id
	private Long id;
	@Column(nullable = false)
	private Long tenantId;
	@Column(nullable = false, columnDefinition = "boolean default true")
	private Boolean isActive;
	@Column(nullable = false)
	private String name;
	private String description;
	@Column(nullable = false)
	private BigInteger chargeFixedAmount;
	@Column(nullable = false)
	private BigDecimal chargeRate;
	@Column(nullable = false)
	private String currencyCode;
	@OneToMany(mappedBy = "serviceCharge", cascade = CascadeType.DETACH)
	private List<ServiceChargeByPlatformEntity> serviceChargeByPlatforms;
	@CreatedDate
	@Temporal(TemporalType.TIMESTAMP)
	@Column(nullable = false, updatable = false)
	private Instant createdAt;
	@LastModifiedDate
	@Temporal(TemporalType.TIMESTAMP)
	private Instant updatedAt;
	@CreatedBy
	private Long createdBy;
	@LastModifiedBy
	private Long updatedBy;
}
