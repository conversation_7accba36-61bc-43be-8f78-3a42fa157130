/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.jpa.features.mealtime.entity;

import com.styl.pacific.data.access.entity.AuditableEntity;
import com.styl.pacific.data.access.jpa.converter.JpaLocalTimeUTCConverter;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.time.Instant;
import java.time.LocalTime;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.hibernate.annotations.SQLDelete;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "tb_meal_time")
@SQLDelete(sql = "UPDATE tb_meal_time SET deleted_at = current_timestamp WHERE id = ?")
public class MealTimeEntity extends AuditableEntity {
	public static final String FIELD_ID = "id";
	public static final String FIELD_TENANT_ID = "tenantId";
	public static final String FIELD_START_TIME = "startTime";
	public static final String FIELD_END_TIME = "endTime";
	public static final String FIELD_NAME = "name";

	public static final Set<String> SORTABLE_FIELDS = Set.of(FIELD_START_TIME, FIELD_END_TIME, FIELD_NAME);

	@Id
	private Long id;
	@Column(nullable = false)
	private Long tenantId;
	@Column(nullable = false)
	private String name;
	@Convert(converter = JpaLocalTimeUTCConverter.class)
	@Column(nullable = false)
	private LocalTime startTime;
	@Convert(converter = JpaLocalTimeUTCConverter.class)
	@Column(nullable = false)
	private LocalTime endTime;
	@Column(nullable = false)
	private String color;

	private MealTimeEntity(Builder builder) {
		setDeletedAt(builder.deletedAt);
		setCreatedAt(builder.createdAt);
		setUpdatedAt(builder.updatedAt);
		setId(builder.id);
		setTenantId(builder.tenantId);
		setName(builder.name);
		setStartTime(builder.startTime);
		setEndTime(builder.endTime);
		setColor(builder.color);
	}

	public static Builder builder() {
		return new Builder();
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;

		if (!(o instanceof MealTimeEntity that))
			return false;

		return new EqualsBuilder().appendSuper(super.equals(o))
				.append(id, that.id)
				.append(tenantId, that.tenantId)
				.append(name, that.name)
				.append(startTime, that.startTime)
				.append(endTime, that.endTime)
				.append(color, that.color)
				.isEquals();
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder(17, 37).appendSuper(super.hashCode())
				.append(id)
				.append(tenantId)
				.append(name)
				.append(startTime)
				.append(endTime)
				.append(color)
				.toHashCode();
	}

	public static final class Builder {
		private Instant deletedAt;
		private Instant createdAt;
		private Instant updatedAt;
		private Long id;
		private Long tenantId;
		private String name;
		private LocalTime startTime;
		private LocalTime endTime;
		private String color;

		private Builder() {
		}

		public Builder deletedAt(Instant deletedAt) {
			this.deletedAt = deletedAt;
			return this;
		}

		public Builder createdAt(Instant createdAt) {
			this.createdAt = createdAt;
			return this;
		}

		public Builder updatedAt(Instant updatedAt) {
			this.updatedAt = updatedAt;
			return this;
		}

		public Builder id(Long id) {
			this.id = id;
			return this;
		}

		public Builder tenantId(Long tenantId) {
			this.tenantId = tenantId;
			return this;
		}

		public Builder name(String name) {
			this.name = name;
			return this;
		}

		public Builder startTime(LocalTime startTime) {
			this.startTime = startTime;
			return this;
		}

		public Builder endTime(LocalTime endTime) {
			this.endTime = endTime;
			return this;
		}

		public Builder color(String color) {
			this.color = color;
			return this;
		}

		public MealTimeEntity build() {
			return new MealTimeEntity(this);
		}
	}

}
