/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.jpa.features.menu.specification;

import com.styl.pacific.data.access.jpa.specification.BaseSpecification;
import com.styl.pacific.order.service.data.access.jpa.features.menu.entity.MenuItemEntity;
import com.styl.pacific.order.service.data.access.jpa.features.menu.entity.arrangement.ArrangementEntity;
import com.styl.pacific.order.service.data.access.jpa.features.product.entity.ProductEntity;
import com.styl.pacific.order.service.domain.features.product.enums.ProductStatus;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.lang.NonNull;

/**
 * <AUTHOR>
 */
public class MenuItemSpecifications {

	public static Specification<MenuItemEntity> withMenuIdAndId(Long menuId, Long id) {
		List<Specification<MenuItemEntity>> specifications = new ArrayList<>();
		if (Objects.nonNull(menuId)) {
			specifications.add(withMenuId(menuId));
		}
		if (Objects.nonNull(id)) {
			specifications.add(withId(id));
		}
		specifications.add(withProductIsNotDeleted());
		return specifications.stream()
				.reduce(Specification::and)
				.orElse((root, query, criteriaBuilder) -> criteriaBuilder.conjunction());
	}

	public static Specification<MenuItemEntity> withMenuIdAndProductId(Long menuId, Long productId) {
		List<Specification<MenuItemEntity>> specifications = new ArrayList<>();
		if (Objects.nonNull(menuId)) {
			specifications.add(withMenuId(menuId));
		}
		if (Objects.nonNull(productId)) {
			specifications.add(withProductId(productId));
		}
		specifications.add(withProductIsNotDeleted());
		return specifications.stream()
				.reduce(Specification::and)
				.orElse((root, query, criteriaBuilder) -> criteriaBuilder.conjunction());
	}

	public static Specification<MenuItemEntity> withIdAndProductId(Long id, Long productId) {
		List<Specification<MenuItemEntity>> specifications = new ArrayList<>();
		if (Objects.nonNull(productId)) {
			specifications.add(withProductId(productId));
		}
		if (Objects.nonNull(id)) {
			specifications.add(withId(id));
		}
		specifications.add(withProductIsNotDeleted());
		return specifications.stream()
				.reduce(Specification::and)
				.orElse((root, query, criteriaBuilder) -> criteriaBuilder.conjunction());
	}

	public static Specification<MenuItemEntity> withMultipleCriteria(List<Long> ids, List<Long> productIds, Long menuId,
			String name, String sku, Long categoryId, Set<ProductStatus> statuses, LocalDate fromDate,
			LocalTime fromTime, LocalDate toDate, LocalTime toTime) {
		List<Specification<MenuItemEntity>> specifications = new ArrayList<>();
		if (Objects.nonNull(ids) && !ids.isEmpty()) {
			specifications.add(withInIds(ids));
		}
		if (Objects.nonNull(productIds) && !productIds.isEmpty()) {
			specifications.add(withInProductIds(productIds));
		}
		if (Objects.nonNull(menuId)) {
			specifications.add(withMenuId(menuId));
		}
		if (StringUtils.isNotBlank(name)) {
			specifications.add(withName(name));
		}
		if (StringUtils.isNotBlank(sku)) {
			specifications.add(withSku(sku));
		}
		if (Objects.nonNull(categoryId)) {
			specifications.add(withCategoryId(categoryId));
		}
		if (Objects.nonNull(statuses) && !statuses.isEmpty()) {
			specifications.add(withInProductStatuses(statuses));
		}
		if (Objects.nonNull(fromDate)) {
			specifications.add(withFromDate(fromDate));
		}
		if (Objects.nonNull(fromTime)) {
			specifications.add(withFromTime(fromTime));
		}
		if (Objects.nonNull(toDate)) {
			specifications.add(withToDate(toDate));
		}
		if (Objects.nonNull(toTime)) {
			specifications.add(withToTime(toTime));
		}
		specifications.add(withProductIsNotDeleted());
		return specifications.stream()
				.reduce(Specification::and)
				.orElse((root, query, criteriaBuilder) -> criteriaBuilder.conjunction());
	}

	public static Specification<MenuItemEntity> withId(Long id) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<MenuItemEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(MenuItemEntity.FIELD_ID), id);
			}
		};
	}

	public static Specification<MenuItemEntity> withIds(List<Long> ids) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<MenuItemEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return root.get(MenuItemEntity.FIELD_ID)
						.in(ids);
			}
		};
	}

	public static Specification<MenuItemEntity> withMenuId(Long menuId) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<MenuItemEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(MenuItemEntity.FIELD_MENU_ID), menuId);
			}
		};
	}

	public static Specification<MenuItemEntity> withName(String name) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<MenuItemEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return like(criteriaBuilder, root.get(MenuItemEntity.FIELD_PRODUCT)
						.get(ProductEntity.FIELD_NAME), name);
			}
		};
	}

	public static Specification<MenuItemEntity> withSku(String sku) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<MenuItemEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return like(criteriaBuilder, root.get(MenuItemEntity.FIELD_PRODUCT)
						.get(ProductEntity.FIELD_SKU), sku);
			}
		};
	}

	public static Specification<MenuItemEntity> withCategoryId(Long categoryId) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<MenuItemEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(MenuItemEntity.FIELD_PRODUCT)
						.get(ProductEntity.FIELD_CATEGORY_ID), categoryId);
			}
		};
	}

	public static Specification<MenuItemEntity> withFromDate(LocalDate fromDate) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<MenuItemEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return criteriaBuilder.or(root.get(MenuItemEntity.FIELD_ARRANGEMENT)
						.get(ArrangementEntity.FIELD_END_DATE)
						.isNull(), criteriaBuilder.greaterThanOrEqualTo(root.get(MenuItemEntity.FIELD_ARRANGEMENT)
								.get(ArrangementEntity.FIELD_END_DATE), fromDate));
			}
		};
	}

	public static Specification<MenuItemEntity> withFromTime(LocalTime fromTime) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<MenuItemEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return criteriaBuilder.or(root.get(MenuItemEntity.FIELD_ARRANGEMENT)
						.get(ArrangementEntity.FIELD_END_TIME)
						.isNull(), criteriaBuilder.greaterThanOrEqualTo(root.get(MenuItemEntity.FIELD_ARRANGEMENT)
								.get(ArrangementEntity.FIELD_END_TIME), fromTime));
			}
		};
	}

	public static Specification<MenuItemEntity> withToDate(LocalDate toDate) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<MenuItemEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return criteriaBuilder.or(root.get(MenuItemEntity.FIELD_ARRANGEMENT)
						.get(ArrangementEntity.FIELD_START_DATE)
						.isNull(), criteriaBuilder.lessThanOrEqualTo(root.get(MenuItemEntity.FIELD_ARRANGEMENT)
								.get(ArrangementEntity.FIELD_START_DATE), toDate));
			}
		};
	}

	public static Specification<MenuItemEntity> withToTime(LocalTime toTime) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<MenuItemEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return criteriaBuilder.or(root.get(MenuItemEntity.FIELD_ARRANGEMENT)
						.get(ArrangementEntity.FIELD_START_TIME)
						.isNull(), criteriaBuilder.lessThanOrEqualTo(root.get(MenuItemEntity.FIELD_ARRANGEMENT)
								.get(ArrangementEntity.FIELD_START_TIME), toTime));
			}
		};
	}

	public static Specification<MenuItemEntity> withProductId(Long productId) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<MenuItemEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(MenuItemEntity.FIELD_PRODUCT)
						.get(ProductEntity.FIELD_ID), productId);
			}
		};
	}

	public static Specification<MenuItemEntity> withProductStatus(ProductStatus status) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<MenuItemEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(MenuItemEntity.FIELD_PRODUCT)
						.get(ProductEntity.FIELD_STATUS), status);
			}
		};
	}

	public static Specification<MenuItemEntity> withInProductStatuses(Set<ProductStatus> statuses) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<MenuItemEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return root.get(MenuItemEntity.FIELD_PRODUCT)
						.get(ProductEntity.FIELD_STATUS)
						.in(statuses);
			}
		};
	}

	public static Specification<MenuItemEntity> withProductIsNotDeleted() {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<MenuItemEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return root.get(MenuItemEntity.FIELD_PRODUCT)
						.get(ProductEntity.FIELD_DELETED_AT)
						.isNull();
			}
		};
	}

	public static Specification<MenuItemEntity> withInIds(List<Long> ids) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<MenuItemEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return root.get(MenuItemEntity.FIELD_ID)
						.in(ids);
			}
		};
	}

	public static Specification<MenuItemEntity> withInProductIds(List<Long> productIds) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<MenuItemEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return root.get(MenuItemEntity.FIELD_PRODUCT)
						.get(ProductEntity.FIELD_ID)
						.in(productIds);
			}
		};
	}
}
