/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.jpa.features.order.specification;

import com.styl.pacific.data.access.jpa.specification.BaseSpecification;
import com.styl.pacific.order.service.data.access.jpa.features.order.entity.OrderLineItemEntity;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.lang.NonNull;

/**
 * <AUTHOR>
 */
public class OrderLineItemSpecifications {

	public static Specification<OrderLineItemEntity> withMultipleCriteria(Long tenantId, List<Long> orderIds,
			List<Long> ids) {
		List<Specification<OrderLineItemEntity>> specifications = new ArrayList<>();
		if (Objects.nonNull(tenantId)) {
			specifications.add(withTenantId(tenantId));
		}
		if (Objects.nonNull(orderIds) && !orderIds.isEmpty()) {
			specifications.add(withOrderIds(orderIds));
		}
		if (Objects.nonNull(ids) && !ids.isEmpty()) {
			specifications.add(withIds(ids));
		}
		return specifications.stream()
				.reduce(Specification::and)
				.orElse((root, query, criteriaBuilder) -> criteriaBuilder.conjunction());
	}

	public static Specification<OrderLineItemEntity> withTenantIdAndId(Long tenantId, Long id) {
		List<Specification<OrderLineItemEntity>> specifications = new ArrayList<>();
		if (Objects.nonNull(tenantId)) {
			specifications.add(withTenantId(tenantId));
		}
		if (Objects.nonNull(id)) {
			specifications.add(withId(id));
		}
		return specifications.stream()
				.reduce(Specification::and)
				.orElse((root, query, criteriaBuilder) -> criteriaBuilder.conjunction());
	}

	public static Specification<OrderLineItemEntity> withTenantIdAndOrderId(Long tenantId, Long orderId) {
		List<Specification<OrderLineItemEntity>> specifications = new ArrayList<>();
		if (Objects.nonNull(tenantId)) {
			specifications.add(withTenantId(tenantId));
		}
		if (Objects.nonNull(orderId)) {
			specifications.add(withOrderId(orderId));
		}
		return specifications.stream()
				.reduce(Specification::and)
				.orElse((root, query, criteriaBuilder) -> criteriaBuilder.conjunction());
	}

	public static Specification<OrderLineItemEntity> withTenantId(Long tenantId) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderLineItemEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(OrderLineItemEntity.FIELD_TENANT_ID), tenantId);
			}
		};
	}

	public static Specification<OrderLineItemEntity> withId(Long id) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderLineItemEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(OrderLineItemEntity.FIELD_ID), id);
			}
		};
	}

	public static Specification<OrderLineItemEntity> withIds(List<Long> ids) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderLineItemEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return root.get(OrderLineItemEntity.FIELD_ID)
						.in(ids);
			}
		};
	}

	public static Specification<OrderLineItemEntity> withOrderId(Long orderId) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderLineItemEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(OrderLineItemEntity.FIELD_ORDER_ID), orderId);
			}
		};
	}

	public static Specification<OrderLineItemEntity> withOrderIds(List<Long> orderIds) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<OrderLineItemEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return root.get(OrderLineItemEntity.FIELD_ORDER_ID)
						.in(orderIds);
			}
		};
	}
}
