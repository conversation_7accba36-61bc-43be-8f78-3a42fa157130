/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.preorder.order.handler;

import com.styl.pacific.domain.enums.order.OrderCancellationType;
import com.styl.pacific.domain.valueobject.BaseId;
import com.styl.pacific.domain.valueobject.PreOrderId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.domain.features.order.entity.Order;
import com.styl.pacific.order.service.domain.features.order.entity.OrderLineItem;
import com.styl.pacific.order.service.domain.features.order.entity.OrderNumberAuditSequence;
import com.styl.pacific.order.service.domain.features.order.helpers.OrderNumberHelper;
import com.styl.pacific.order.service.domain.features.preorder.menu.ports.output.repository.PreOrderMenuItemRepository;
import com.styl.pacific.order.service.domain.features.preorder.menu.valueobjects.PreOrderMenuItemId;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.PreOrderLightDto;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.command.CancelPreOrderCommand;
import com.styl.pacific.order.service.domain.features.preorder.order.entity.PreOrder;
import com.styl.pacific.order.service.domain.features.preorder.order.mapper.PreOrderDataMapper;
import com.styl.pacific.order.service.domain.features.preorder.order.ports.output.repository.PreOrderRepository;
import com.styl.pacific.order.service.domain.features.preorder.order.processor.PreOrderNumberProcessor;
import com.styl.pacific.order.service.domain.features.tenant.dto.TenantDto;
import com.styl.pacific.order.service.domain.features.tenant.helper.TenantCheckHelper;
import com.styl.pacific.order.service.domain.utils.TenantHelper;
import java.time.ZoneId;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class PreOrderCommandHandler {
	private final PreOrderRepository preOrderRepository;
	private final PreOrderMenuItemRepository preOrderMenuItemRepository;
	private final TenantCheckHelper tenantCheckHelper;
	private final PreOrderNumberProcessor preOrderNumberProcessor;
	private final OrderNumberHelper orderNumberHelper;

	@Transactional
	public PreOrderLightDto place(TenantId tenantId, PreOrder preOrder) {
		TenantDto tenant = tenantCheckHelper.tenantCheck(tenantId);
		reservationInventory(preOrder);
		confirmInventory(tenant, preOrder);
		return PreOrderDataMapper.INSTANCE.toDto(preOrderRepository.save(preOrder));
	}

	private void reservationInventory(PreOrder preOrder) {

		SortedMap<PreOrderMenuItemId, Long> reserveStock = new TreeMap<>(Comparator.comparing(BaseId::getValue));
		reserveStock.putAll(preOrder.getSubOrders()
				.stream()
				.map(Order::getLineItems)
				.flatMap(List::stream)
				.collect(Collectors.groupingBy(OrderLineItem::getPreOrderMenuItemId, Collectors.summingLong(
						OrderLineItem::getQuantity))));
		reserveStock.sequencedEntrySet()
				.forEach(entry -> {
					preOrderMenuItemRepository.addOrderedQuantity(entry.getKey(), entry.getValue()
							.intValue());
				});
	}

	private void confirmInventory(TenantDto tenant, PreOrder preOrder) {
		for (Order order : preOrder.getSubOrders()) {
			ZoneId zoneId = TenantHelper.getZoneId(tenant);
			OrderNumberAuditSequence auditSequence = orderNumberHelper.currentNumberAudit(order.getTenantId(), order
					.getStoreId(), zoneId);
			order.setOrderNumber(preOrderNumberProcessor.generateOrderNumber(auditSequence));
		}
	}

	@Transactional
	public void cancel(PreOrder preOrder, OrderCancellationType cancellationType, CancelPreOrderCommand command,
			boolean ignoreExpired) {
		preOrder.cancel(command.reason(), cancellationType, command.refundable(), ignoreExpired);
		preOrderRepository.save(preOrder);
	}

	@Transactional
	public void markPreOrderAsExpired(PreOrder preOrder) {
		preOrder.markAsExpiration();
		preOrderRepository.save(preOrder);
	}

	@Transactional
	public void markBulkPreOrderAsExpired(List<PreOrder> preOrders) {
		preOrders.forEach(PreOrder::markAsExpiration);
		preOrderRepository.updateAll(preOrders);
	}

	@Transactional
	public Optional<PreOrder> findById(TenantId tenantId, PreOrderId id) {
		return preOrderRepository.findById(tenantId, id);
	}

	@Transactional
	public List<PreOrder> findAllByIds(TenantId tenantId, List<PreOrderId> ids) {
		return preOrderRepository.findAllDtoByIds(tenantId, ids);
	}
}
