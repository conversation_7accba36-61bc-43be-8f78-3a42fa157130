/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.preorder.menu.ports.input.service;

import com.styl.pacific.domain.dto.Content;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.PreOrderMenuId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.command.CreatePreOrderMenuItemCommand;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.command.UpdatePreOrderMenuItemCommand;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.model.PreOrderMenuItemDto;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.model.PreOrderMenuItemLightDto;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.query.DeletePreOrderMenuItemQuery;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.query.PreOrderMenuItemPagingQuery;
import com.styl.pacific.order.service.domain.features.preorder.menu.valueobjects.PreOrderMenuItemId;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
public interface PreOrderMenuItemService {
	PreOrderMenuItemDto findById(@NotNull(message = "tenantId must not be null") TenantId tenantId,
			@NotNull(message = "preOrderMenuId must not be null") PreOrderMenuId preOrderMenuId,
			@NotNull(message = "id must not be null") PreOrderMenuItemId id);

	Paging<PreOrderMenuItemDto> findAllPaging(TenantId tenantId,
			@NotNull(message = "preOrderMenuId must not be null") PreOrderMenuId preOrderMenuId,
			@Valid PreOrderMenuItemPagingQuery query);

	Content<PreOrderMenuItemLightDto> create(@NotNull(message = "tenantId must not be null") TenantId tenantId,
			@NotNull(message = "preOrderMenuId must not be null") PreOrderMenuId preOrderMenuId,
			@NotNull(message = "command must not be null") @Valid CreatePreOrderMenuItemCommand command);

	Content<PreOrderMenuItemLightDto> update(@NotNull(message = "tenantId must not be null") TenantId tenantId,
			@NotNull(message = "preOrderMenuId must not be null") PreOrderMenuId preOrderMenuId,
			@NotNull(message = "preOrderMenuItemId must not be null") PreOrderMenuItemId preOrderMenuItemId,
			@NotNull(message = "command must not be null") @Valid UpdatePreOrderMenuItemCommand command);

	void deleteById(@NotNull(message = "tenantId must not be null") TenantId tenantId,
			@NotNull(message = "preOrderMenuId must not be null") PreOrderMenuId preOrderMenuId,
			@NotNull(message = "id must not be null") PreOrderMenuItemId id, @Valid DeletePreOrderMenuItemQuery query);
}
