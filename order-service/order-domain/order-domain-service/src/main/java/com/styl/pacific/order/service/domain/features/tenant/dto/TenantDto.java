/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.tenant.dto;

import com.styl.pacific.domain.dto.CountryResponse;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Builder
@Getter
public class TenantDto {
	private final Long tenantId;

	private final String name;

	private final String businessRegNo;

	private final String businessType;

	private final String status;

	private final String email;

	private final String phoneNumber;

	private final String contactRemarks;

	private final String email2;

	private final String phoneNumber2;

	private final String contactRemarks2;

	private final String email3;

	private final String phoneNumber3;

	private final String contactRemarks3;

	private final String website;

	private final LogoDto logo;

	private final String realmId;

	private final String addressLine1;

	private final String addressLine2;

	private final String city;

	private final CountryResponse country;

	private final String postalCode;

	private final Long createdAt;

	private final Long updatedAt;

	private final Long activatedAt;

	private final TenantSettingDto settings;

	@Getter
	@Builder
	public static class LogoDto {
		private final String path;
		private String url;
	}
}
