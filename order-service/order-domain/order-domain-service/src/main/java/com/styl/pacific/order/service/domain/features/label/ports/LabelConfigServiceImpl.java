/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.label.ports;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.LabelConfigId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.domain.features.common.exception.OrderCommonValidateException;
import com.styl.pacific.order.service.domain.features.label.dto.command.CellFormatPropertiesCommand;
import com.styl.pacific.order.service.domain.features.label.dto.command.CreateLabelConfigCommand;
import com.styl.pacific.order.service.domain.features.label.dto.command.LabelLayoutPropertiesCommand;
import com.styl.pacific.order.service.domain.features.label.dto.command.PageSetupPropertiesCommand;
import com.styl.pacific.order.service.domain.features.label.dto.command.UpdateLabelConfigCommand;
import com.styl.pacific.order.service.domain.features.label.dto.query.LabelConfigPaginationQuery;
import com.styl.pacific.order.service.domain.features.label.entity.LabelConfig;
import com.styl.pacific.order.service.domain.features.label.exceptions.LabelConfigNotFoundException;
import com.styl.pacific.order.service.domain.features.label.id.generator.LabelConfigIdGenerator;
import com.styl.pacific.order.service.domain.features.label.mapper.LabelConfigDataMapper;
import com.styl.pacific.order.service.domain.features.label.ports.input.service.LabelConfigService;
import com.styl.pacific.order.service.domain.features.label.ports.output.LabelConfigRepository;
import com.styl.pacific.order.service.domain.features.tenant.helper.TenantCheckHelper;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 */
@Service
@Validated
@RequiredArgsConstructor
public class LabelConfigServiceImpl implements LabelConfigService {
	private static final Logger log = LoggerFactory.getLogger(LabelConfigServiceImpl.class);
	private final LabelConfigRepository labelConfigRepository;
	private final TenantCheckHelper tenantCheckHelper;
	private final LabelConfigIdGenerator labelConfigIdGenerator;

	@Override
	@Transactional(readOnly = true)
	public LabelConfig findById(TenantId tenantId, LabelConfigId id) {
		return labelConfigCheck(tenantId, id);
	}

	@Override
	@Transactional(readOnly = true)
	public Paging<LabelConfig> findAllPaging(TenantId tenantId, LabelConfigPaginationQuery query) {
		return labelConfigRepository.findAllPaging(tenantId, query);
	}

	@Override
	@Transactional
	public LabelConfig create(TenantId tenantId, CreateLabelConfigCommand command) {
		tenantCheckHelper.tenantCheck(tenantId);
		validate(command.pageSetup(), command.labelLayout(), command.cellFormat());
		LabelConfig labelConfig = LabelConfigDataMapper.INSTANCE.toModel(tenantId, command);
		labelConfig.setId(labelConfigIdGenerator.nextId());
		return labelConfigRepository.save(labelConfig);
	}

	@Override
	@Transactional
	public LabelConfig update(TenantId tenantId, LabelConfigId id, UpdateLabelConfigCommand command) {
		LabelConfig labelConfig = labelConfigCheck(tenantId, id);
		validate(command.pageSetup(), command.labelLayout(), command.cellFormat());
		LabelConfigDataMapper.INSTANCE.updateModel(labelConfig, command);
		return labelConfigRepository.update(labelConfig);
	}

	@Override
	@Transactional
	public void delete(TenantId tenantId, LabelConfigId id) {
		labelConfigCheck(tenantId, id);
		labelConfigRepository.delete(tenantId, id);
	}

	private LabelConfig labelConfigCheck(TenantId tenantId, LabelConfigId id) {
		return labelConfigRepository.findById(tenantId, id)
				.orElseThrow(() -> {
					log.info("Label config {} not found", id);
					return new LabelConfigNotFoundException(String.format("Label config %s not found", id.getValue()));
				});
	}

	private void validate(PageSetupPropertiesCommand pageSetup, LabelLayoutPropertiesCommand labelLayout,
			CellFormatPropertiesCommand cellFormat) {
		var sizeHeightInside = labelLayout.dimensionHeight() + labelLayout.cellMarginTop() + labelLayout
				.cellMarginBottom();
		var pageHeight = pageSetup.pageHeight();
		if (sizeHeightInside > pageHeight) {
			throw new OrderCommonValidateException("Label layout height is greater than page height");
		}
		var sizeWidthInside = (labelLayout.cellMarginLeft() + labelLayout.cellMarginRight() + cellFormat
				.areaPaddingLeft() + cellFormat.areaPaddingRight()) * labelLayout.dimensionNumberOfColumns();
		var pageWidth = pageSetup.pageWidth();

		if (sizeWidthInside > pageWidth) {
			throw new OrderCommonValidateException("Label layout width is greater than page width");
		}

		var cellHeight = labelLayout.dimensionHeight();
		var cellPaddingVertical = cellFormat.areaPaddingTop() + cellFormat.areaPaddingBottom();
		if (cellHeight < cellPaddingVertical) {
			throw new OrderCommonValidateException("Cell height is less than cell padding vertical");
		}
	}
}
