/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.preorder.order.ports.input.service;

import com.styl.pacific.domain.dto.TokenClaim;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.PreOrderId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.PreOrderLightDto;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.PreOrderSummaryDto;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.command.CancelPreOrderCommand;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.command.CreatePreOrderCommand;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.query.PreOrderPagingQuery;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.query.PreOrderSummaryQuery;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
public interface PreOrderService {
	PreOrderLightDto create(@NotNull(message = "tenantId must not be null") TenantId tenantId,
			@NotNull(message = "tokenClaim must not be null") TokenClaim tokenClaim,
			@NotNull(message = "command must not be null") @Valid CreatePreOrderCommand command);

	Paging<PreOrderSummaryDto> findSummary(@NotNull(message = "tenantId must not be null") TenantId tenantId,
			TokenClaim tokenClaim, @Valid PreOrderSummaryQuery query);

	Paging<PreOrderLightDto> findAllPaging(TenantId tenantId, TokenClaim tokenClaim, @Valid PreOrderPagingQuery query);

	PreOrderLightDto findById(@NotNull(message = "tenantId must not be null") TenantId tenantId, TokenClaim tokenClaim,
			@NotNull(message = "id must not be null") PreOrderId id);

	void cancelById(TenantId tenantId, TokenClaim tokenClaim,
			@NotNull(message = "preOrderId must not be null") PreOrderId preOrderId,
			@NotNull(message = "command must not be null") @Valid CancelPreOrderCommand command);
}
