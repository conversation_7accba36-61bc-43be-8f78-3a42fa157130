/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.preorder.order.dto;

import com.styl.pacific.domain.enums.order.OrderCancellationType;
import com.styl.pacific.domain.enums.order.OrderPaymentStatus;
import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.domain.valueobject.PaymentMethodId;
import com.styl.pacific.domain.valueobject.PaymentTransactionId;
import com.styl.pacific.domain.valueobject.PreOrderId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.order.service.domain.features.order.dto.OrderLightDto;
import com.styl.pacific.order.service.domain.features.preorder.order.enums.PreOrderStatus;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.With;

/**
 * <AUTHOR>
 */
@Builder
@Data
@With
@AllArgsConstructor
public class PreOrderDto {
	private PreOrderId id;
	private TenantId tenantId;
	private String systemSource;
	private Long version;

	private UserId issuerId;
	private UserId customerId;
	private String customerName;
	private String customerEmail;

	private List<OrderLightDto> subOrders;

	private PreOrderStatus status;

	private Money subtotalAmount;
	private Money serviceChargeAmount;
	private String taxName;
	private BigDecimal taxRate;
	private Boolean taxInclude;
	private Money taxAmount;
	private Money discountAmount;
	private Money totalAmount;
	private String currencyCode;

	private PaymentMethodId paymentMethodId;
	private String paymentMethodName;
	private String paymentRef;
	private PaymentTransactionId paymentTransactionId;
	private OrderPaymentStatus paymentStatus;
	private Boolean refundable;
	private UUID nonce;

	private Instant cancellationDueAt;
	private OrderCancellationType cancellationType;
	private String cancelReason;
	private Instant canceledAt;
	private Instant expiredAt;

	private Instant createdAt;
	private Instant updatedAt;
}
