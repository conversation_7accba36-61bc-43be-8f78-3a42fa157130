/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.order.dto.command.place;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigInteger;
import lombok.Builder;

@Builder
public record CreateOrderLineItemCommand(@NotNull(message = "menuItemId must not be null") Long menuItemId,
		@NotNull(message = "productId must not be null") Long productId,
		@Valid @NotNull(message = "metadata must not be null") ProductMetadataCommand metadata,
		@NotBlank(message = "productName must not be empty or null") String productName,
		String option,
		@NotNull(message = "quantity must not be null") @Min(value = 0, message = "quantity must be greater or equal than 0") Long quantity,
		@Size(max = 255, message = "note must not exceed 255 characters") String note,
		@NotNull(message = "unitPrice must not be null") @Min(value = 0, message = "unitPrice must be greater or equal than 0") BigInteger unitPrice,
		BigInteger optionPrice,
		@Min(value = 0, message = "totalDiscount must be greater or equal than 0") BigInteger totalDiscount,
		@NotNull(message = "totalAmount must not be null") @Min(value = 0, message = "totalAmount must be greater or equal than 0") BigInteger totalAmount) {
}
