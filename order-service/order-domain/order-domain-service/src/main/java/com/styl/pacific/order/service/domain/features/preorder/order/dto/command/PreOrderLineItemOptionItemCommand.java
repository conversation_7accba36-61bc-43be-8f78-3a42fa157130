/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.preorder.order.dto.command;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigInteger;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Builder
@Data
@AllArgsConstructor
public class PreOrderLineItemOptionItemCommand {
	@NotNull(message = "optionItemId must not be null")
	private Long optionItemId;
	@Size(max = 255, message = "name must be less than or equal to 255 characters")
	@NotBlank(message = "name must not be empty")
	private String name;
	@NotNull(message = "additionalPrice must not be null")
	@Min(value = 0, message = "additionalPrice must be greater or equal than 0")
	private BigInteger additionalPrice;
}
