/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.preorder.menu.dto.query;

import com.styl.pacific.common.validator.dateformat.Date;
import com.styl.pacific.order.service.domain.features.preorder.menu.enums.PreOrderMenuItemStatus;
import com.styl.pacific.order.service.domain.features.product.enums.ProductStatus;
import java.util.List;
import java.util.Set;
import lombok.Builder;
import lombok.With;

/**
 * <AUTHOR>
 */
@Builder
@With
public record PreOrderMenuItemFilter(List<Long> ids,
		List<Long> productIds,
		String name,
		Long mealTimeId,
		Long categoryId,
		@Date(message = "startDate must be valid date format") String startDate,
		@Date(message = "endDate must be valid date format") String endDate,
		List<PreOrderMenuItemStatus> statuses,
		Set<ProductStatus> productStatuses) {
}
