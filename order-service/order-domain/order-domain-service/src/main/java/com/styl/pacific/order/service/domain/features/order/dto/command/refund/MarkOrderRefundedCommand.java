/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.order.dto.command.refund;

import java.math.BigInteger;
import lombok.Builder;

/**
 * <AUTHOR>
 */
@Builder
public record MarkOrderRefundedCommand(String id,
		Long tenantId,
		Long paymentReverseId,
		String paymentSessionId,
		Long paymentTransactionId,
		String paymentProcessorId,
		String idempotencyKey,
		Long paymentMethodId,
		String transactionType,
		Long customerId,
		String customerEmail,
		String customerName,
		BigInteger refundingAmount,
		BigInteger totalRefundedAmount,
		Long createdAt,
		Long updatedAt) {
}
