/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.utils;

import com.styl.pacific.domain.dto.TokenClaim;
import com.styl.pacific.domain.enums.UserType;
import com.styl.pacific.domain.valueobject.UserId;
import java.util.Objects;
import java.util.Optional;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.math.NumberUtils;

/**
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class TokenClaimHelper {
	public static boolean isCustomerToken(TokenClaim tokenClaim) {
		return Objects.nonNull(tokenClaim) && UserType.CUSTOMER.equals(tokenClaim.getUserType());
	}

	public static UserId getUserId(TokenClaim tokenClaim) {
		return Optional.ofNullable(tokenClaim.getUserId())
				.map(t -> NumberUtils.isCreatable(t) ? Long.parseLong(t) : null)
				.map(UserId::new)
				.orElse(null);
	}
}
