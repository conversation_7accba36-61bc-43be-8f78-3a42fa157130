/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.preorder.assignment.dto.query;

import com.styl.pacific.common.validator.dateformat.Date;
import com.styl.pacific.order.service.domain.features.preorder.menu.enums.PreOrderMenuItemStatus;
import com.styl.pacific.order.service.domain.features.preorder.menu.enums.PreOrderMenuStatus;
import com.styl.pacific.order.service.domain.features.product.enums.ProductStatus;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Builder;
import lombok.With;

/**
 * <AUTHOR>
 */
@Builder
@With
public record PreOrderMenuItemSummaryFilter(Long userId,
		@Date(message = "fromDate must be valid date format") @NotNull(message = "fromDate must not be null") String fromDate,
		@Date(message = "toDate must be valid date format") String toDate,
		Long mealTimeId,
		String name,
		Long categoryId,
		List<ProductStatus> productStatuses,
		List<PreOrderMenuItemStatus> menuItemStatuses,
		List<PreOrderMenuStatus> menuStatuses) {
}
