/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.preorder.order.dto.command;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigInteger;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreatePreOrderLineItemCommand {
	@NotNull(message = "preOrderMenuItemId must not be null")
	private Long preOrderMenuItemId;
	@NotNull(message = "productId must not be null")
	private Long productId;
	@Size(max = 255, message = "productName must be less than or equal to 255 characters")
	@NotBlank(message = "productName must not be empty or null")
	private String productName;
	@NotNull(message = "quantity must not be null")
	@Min(value = 1, message = "quantity must be greater or equal than 1")
	private Long quantity;
	@Size(max = 255, message = "note must be less than or equal to 255 characters")
	private String note;
	@Valid
	private List<PreOrderLineItemOptionCommand> options;
	@NotNull(message = "unitPrice must not be null")
	@Min(value = 0, message = "unitPrice must be greater or equal than 0")
	private BigInteger unitPrice;
	@NotNull(message = "totalDiscount must not be null")
	@Min(value = 0, message = "totalDiscount must be greater or equal than 0")
	private BigInteger totalDiscount;
	@NotNull(message = "totalAmount must not be null")
	@Min(value = 0, message = "totalAmount must be greater or equal than 0")
	private BigInteger totalAmount;
}
