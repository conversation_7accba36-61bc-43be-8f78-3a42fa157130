/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.menu.ports.input.service;

import com.styl.pacific.domain.dto.Content;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.MenuId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.domain.features.menu.dto.command.CreateMenuCommand;
import com.styl.pacific.order.service.domain.features.menu.dto.command.UpdateMenuCommand;
import com.styl.pacific.order.service.domain.features.menu.dto.query.MenuQuery;
import com.styl.pacific.order.service.domain.features.menu.dto.query.PaginationMenuQuery;
import com.styl.pacific.order.service.domain.features.menu.entity.Menu;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
public interface MenuService {
	Menu findById(@NotNull(message = "tenantId must not be null") TenantId tenantId,
			@NotNull(message = "id must not be null") MenuId id);

	Content<Menu> findAll(TenantId tenantId, @Valid MenuQuery query);

	Menu create(@NotNull(message = "tenantId must not be null") TenantId tenantId,
			@Valid @NotNull(message = "command must not be null") CreateMenuCommand command);

	Menu update(@NotNull(message = "tenantId must not be null") TenantId tenantId,
			@NotNull(message = "id must not be null") MenuId id,
			@Valid @NotNull(message = "command must not be null") UpdateMenuCommand command);

	void deleteById(@NotNull(message = "tenantId must not be null") TenantId tenantId,
			@NotNull(message = "id must not be null") MenuId id);

	void activeById(@NotNull(message = "tenantId must not be null") TenantId tenantId,
			@NotNull(message = "id must not be null") MenuId id);

	void deActiveById(@NotNull(message = "tenantId must not be null") TenantId tenantId,
			@NotNull(message = "id must not be null") MenuId id);

	Paging<Menu> findAllPaging(TenantId tenantId, @Valid PaginationMenuQuery query);
}
