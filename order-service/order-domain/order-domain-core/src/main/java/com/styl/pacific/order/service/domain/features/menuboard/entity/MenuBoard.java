/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.menuboard.entity;

import com.styl.pacific.domain.entity.AggregateRoot;
import com.styl.pacific.domain.valueobject.MenuBoardId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.domain.features.menuboard.enums.MenuBoardDisplayMode;
import java.time.Instant;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class MenuBoard extends AggregateRoot<MenuBoardId> {
	private String name;
	private List<MenuBoardImage> images;
	private MenuBoardDisplayMode displayMode;
	private Integer delayBetweenImages;
	private Instant createdAt;
	private Instant updatedAt;

	private MenuBoard(Builder builder) {
		setId(builder.id);
		setTenantId(builder.tenantId);
		setName(builder.name);
		setImages(builder.images);
		setDisplayMode(builder.displayMode);
		setDelayBetweenImages(builder.delayBetweenImages);
		setCreatedAt(builder.createdAt);
		setUpdatedAt(builder.updatedAt);
	}

	public static Builder builder() {
		return new Builder();
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public List<MenuBoardImage> getImages() {
		return images;
	}

	public void setImages(List<MenuBoardImage> images) {
		this.images = images;
	}

	public MenuBoardDisplayMode getDisplayMode() {
		return displayMode;
	}

	public void setDisplayMode(MenuBoardDisplayMode displayMode) {
		this.displayMode = displayMode;
	}

	public Integer getDelayBetweenImages() {
		return delayBetweenImages;
	}

	public void setDelayBetweenImages(Integer delayBetweenImages) {
		this.delayBetweenImages = delayBetweenImages;
	}

	public Instant getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Instant createdAt) {
		this.createdAt = createdAt;
	}

	public Instant getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Instant updatedAt) {
		this.updatedAt = updatedAt;
	}

	public static final class Builder {
		private MenuBoardId id;
		private TenantId tenantId;
		private String name;
		private List<MenuBoardImage> images;
		private MenuBoardDisplayMode displayMode;
		private Integer delayBetweenImages;
		private Instant createdAt;
		private Instant updatedAt;

		private Builder() {
		}

		public Builder id(MenuBoardId id) {
			this.id = id;
			return this;
		}

		public Builder tenantId(TenantId tenantId) {
			this.tenantId = tenantId;
			return this;
		}

		public Builder name(String name) {
			this.name = name;
			return this;
		}

		public Builder images(List<MenuBoardImage> images) {
			this.images = images;
			return this;
		}

		public Builder displayMode(MenuBoardDisplayMode displayMode) {
			this.displayMode = displayMode;
			return this;
		}

		public Builder delayBetweenImages(Integer delayBetweenImages) {
			this.delayBetweenImages = delayBetweenImages;
			return this;
		}

		public Builder createdAt(Instant createdAt) {
			this.createdAt = createdAt;
			return this;
		}

		public Builder updatedAt(Instant updatedAt) {
			this.updatedAt = updatedAt;
			return this;
		}

		public MenuBoard build() {
			return new MenuBoard(this);
		}
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (!(o instanceof MenuBoard menuBoard))
			return false;
		if (!super.equals(o))
			return false;
		return Objects.equals(name, menuBoard.name) && Objects.equals(images, menuBoard.images) && displayMode
				== menuBoard.displayMode && Objects.equals(delayBetweenImages, menuBoard.delayBetweenImages) && Objects
						.equals(createdAt, menuBoard.createdAt) && Objects.equals(updatedAt, menuBoard.updatedAt);
	}

	@Override
	public int hashCode() {
		return Objects.hash(super.hashCode(), name, images, displayMode, delayBetweenImages, createdAt, updatedAt);
	}
}
