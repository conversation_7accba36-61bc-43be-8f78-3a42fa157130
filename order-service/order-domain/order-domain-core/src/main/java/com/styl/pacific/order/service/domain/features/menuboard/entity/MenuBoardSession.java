/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.menuboard.entity;

import com.styl.pacific.domain.entity.AggregateRoot;
import com.styl.pacific.domain.valueobject.MenuBoardId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.domain.features.menuboard.valueobjects.MenuBoardSessionId;
import java.time.Instant;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class MenuBoardSession extends AggregateRoot<MenuBoardSessionId> {
	private String name;
	private MenuBoardId menuBoardId;
	private Instant createdAt;
	private Instant updatedAt;

	private MenuBoardSession(Builder builder) {
		setId(builder.id);
		setTenantId(builder.tenantId);
		setName(builder.name);
		setMenuBoardId(builder.menuBoardId);
		setCreatedAt(builder.createdAt);
		setUpdatedAt(builder.updatedAt);
	}

	public static Builder builder() {
		return new Builder();
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public MenuBoardId getMenuBoardId() {
		return menuBoardId;
	}

	public void setMenuBoardId(MenuBoardId menuBoardId) {
		this.menuBoardId = menuBoardId;
	}

	public Instant getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Instant createdAt) {
		this.createdAt = createdAt;
	}

	public Instant getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Instant updatedAt) {
		this.updatedAt = updatedAt;
	}

	public static final class Builder {
		private MenuBoardSessionId id;
		private TenantId tenantId;
		private String name;
		private MenuBoardId menuBoardId;
		private Instant createdAt;
		private Instant updatedAt;

		private Builder() {
		}

		public Builder id(MenuBoardSessionId id) {
			this.id = id;
			return this;
		}

		public Builder tenantId(TenantId tenantId) {
			this.tenantId = tenantId;
			return this;
		}

		public Builder name(String name) {
			this.name = name;
			return this;
		}

		public Builder menuBoardId(MenuBoardId menuBoardId) {
			this.menuBoardId = menuBoardId;
			return this;
		}

		public Builder createdAt(Instant createdAt) {
			this.createdAt = createdAt;
			return this;
		}

		public Builder updatedAt(Instant updatedAt) {
			this.updatedAt = updatedAt;
			return this;
		}

		public MenuBoardSession build() {
			return new MenuBoardSession(this);
		}
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (!(o instanceof MenuBoardSession session))
			return false;
		if (!super.equals(o))
			return false;
		return Objects.equals(name, session.name) && Objects.equals(menuBoardId, session.menuBoardId) && Objects.equals(
				createdAt, session.createdAt) && Objects.equals(updatedAt, session.updatedAt);
	}

	@Override
	public int hashCode() {
		return Objects.hash(super.hashCode(), name, menuBoardId, createdAt, updatedAt);
	}
}
