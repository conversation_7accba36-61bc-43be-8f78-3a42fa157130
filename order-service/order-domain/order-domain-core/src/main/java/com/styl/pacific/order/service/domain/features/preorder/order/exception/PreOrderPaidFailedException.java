/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.preorder.order.exception;

import com.styl.pacific.domain.exception.GlobalErrorCode;
import com.styl.pacific.domain.processor.ErrorCodeValue;
import com.styl.pacific.order.service.domain.features.common.exception.OrderDomainException;
import com.styl.pacific.order.service.domain.features.preorder.order.entity.PreOrder;

/**
 * <AUTHOR>
 */
@ErrorCodeValue(GlobalErrorCode.ORDER_PAYMENT_FAILED)
public class PreOrderPaidFailedException extends OrderDomainException {
	private final PreOrder order;
	private final boolean refundable;

	public PreOrderPaidFailedException(String message, Throwable cause, PreOrder order, boolean refundable) {
		super(message, cause);
		this.order = order;
		this.refundable = refundable;
	}

	public PreOrderPaidFailedException(String message, PreOrder order, boolean refundable) {
		super(message);
		this.order = order;
		this.refundable = refundable;
	}

	public PreOrder getOrder() {
		return order;
	}

	public boolean isRefundable() {
		return refundable;
	}
}
