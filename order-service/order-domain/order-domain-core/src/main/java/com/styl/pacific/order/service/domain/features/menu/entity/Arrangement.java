/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.menu.entity;

import com.styl.pacific.domain.entity.BaseEntity;
import com.styl.pacific.domain.valueobject.MenuItemId;
import com.styl.pacific.order.service.domain.features.common.exception.enums.DateOfWeek;
import java.time.Instant;
import java.time.LocalTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class Arrangement extends BaseEntity<MenuItemId> {
	private Instant startDate;
	private Instant endDate;
	private LocalTime startTime;
	private LocalTime endTime;
	private List<DateOfWeek> availableOn;
	private Instant createdAt;
	private Instant updatedAt;

	private Arrangement(Builder builder) {
		setId(builder.id);
		setStartDate(builder.startDate);
		setEndDate(builder.endDate);
		setStartTime(builder.startTime);
		setEndTime(builder.endTime);
		setAvailableOn(builder.availableOn);
		setCreatedAt(builder.createdAt);
		setUpdatedAt(builder.updatedAt);
	}

	public static Builder builder() {
		return new Builder();
	}

	public Instant getStartDate() {
		return startDate;
	}

	public void setStartDate(Instant startDate) {
		this.startDate = startDate;
	}

	public Instant getEndDate() {
		return endDate;
	}

	public void setEndDate(Instant endDate) {
		this.endDate = endDate;
	}

	public LocalTime getStartTime() {
		return startTime;
	}

	public void setStartTime(LocalTime startTime) {
		this.startTime = startTime;
	}

	public LocalTime getEndTime() {
		return endTime;
	}

	public void setEndTime(LocalTime endTime) {
		this.endTime = endTime;
	}

	public List<DateOfWeek> getAvailableOn() {
		return availableOn;
	}

	public void setAvailableOn(List<DateOfWeek> availableOn) {
		this.availableOn = availableOn;
	}

	public Instant getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Instant createdAt) {
		this.createdAt = createdAt;
	}

	public Instant getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Instant updatedAt) {
		this.updatedAt = updatedAt;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (!(o instanceof Arrangement that))
			return false;
		if (!super.equals(o))
			return false;
		return Objects.equals(startDate, that.startDate) && Objects.equals(endDate, that.endDate) && Objects.equals(
				startTime, that.startTime) && Objects.equals(endTime, that.endTime) && Objects.equals(availableOn,
						that.availableOn) && Objects.equals(createdAt, that.createdAt) && Objects.equals(updatedAt,
								that.updatedAt);
	}

	@Override
	public int hashCode() {
		return Objects.hash(super.hashCode(), startDate, endDate, startTime, endTime, availableOn, createdAt,
				updatedAt);
	}

	public static final class Builder {
		private MenuItemId id;
		private Instant startDate;
		private Instant endDate;
		private LocalTime startTime;
		private LocalTime endTime;
		private List<DateOfWeek> availableOn;
		private Instant createdAt;
		private Instant updatedAt;

		private Builder() {
		}

		public Builder id(MenuItemId id) {
			this.id = id;
			return this;
		}

		public Builder startDate(Instant startDate) {
			this.startDate = startDate;
			return this;
		}

		public Builder endDate(Instant endDate) {
			this.endDate = endDate;
			return this;
		}

		public Builder startTime(LocalTime startTime) {
			this.startTime = startTime;
			return this;
		}

		public Builder endTime(LocalTime endTime) {
			this.endTime = endTime;
			return this;
		}

		public Builder availableOn(List<DateOfWeek> availableOn) {
			this.availableOn = availableOn;
			return this;
		}

		public Builder createdAt(Instant createdAt) {
			this.createdAt = createdAt;
			return this;
		}

		public Builder updatedAt(Instant updatedAt) {
			this.updatedAt = updatedAt;
			return this;
		}

		public Arrangement build() {
			return new Arrangement(this);
		}
	}
}
