/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.product.entity;

import com.styl.pacific.domain.valueobject.AllergenId;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

/**
 * <AUTHOR>
 */
public class ProductAllergen {
	private AllergenId allergenId;

	public ProductAllergen(AllergenId allergenId) {
		this.allergenId = allergenId;
	}

	public AllergenId getAllergenId() {
		return allergenId;
	}

	public void setAllergenId(AllergenId allergenId) {
		this.allergenId = allergenId;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;

		if (!(o instanceof ProductAllergen that))
			return false;

		return new EqualsBuilder().append(allergenId, that.allergenId)
				.isEquals();
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder(17, 37).append(allergenId)
				.toHashCode();
	}
}
