/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.scheduling.features.orders;

import com.styl.pacific.order.service.domain.features.order.ports.input.service.OrderRefundService;
import com.styl.pacific.order.service.scheduling.features.orders.config.OrderRefundJobConfig;
import jakarta.annotation.PostConstruct;
import java.time.Duration;
import java.time.Instant;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.core.LockAssert;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.DependsOn;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@ConditionalOnProperty(name = "pacific.schedule.order.order-refund-job.enabled", havingValue = "true")
@DependsOn("schedulingConfiguration")
@RequiredArgsConstructor
@Component
public class OrderRefundJobScheduler {
	private final OrderRefundService orderRefundService;
	private final OrderRefundJobConfig orderRefundJobConfig;

	@PostConstruct
	public void initial() {
		log.info("OrderRefundJobScheduler started");
	}

	@Scheduled(cron = "${pacific.schedule.order.order-refund-job.cron-expression}")
	@SchedulerLock(name = "Order.OrderRefundJob", lockAtLeastFor = "${pacific.schedule.order.order-refund-job.lock-at-least-for:PT30S}", lockAtMostFor = "${pacific.schedule.order.order-refund-job.lock-at-most-for:PT4M30s}")
	public void scheduleOrderRefundCheckJob() {
		LockAssert.assertLocked();
		final var now = Instant.now();
		log.info("OrderRefundCheckJob is running at {}", now);
		int orderBatchSize = Optional.ofNullable(orderRefundJobConfig.getOrderBatchSize())
				.orElse(100);
		Instant timeout = now.plus(orderRefundJobConfig.getLockAtMostFor());
		try {
			orderRefundService.processRefundingOrder(orderBatchSize, timeout);
		} finally {
			log.info("OrderRefundCheckJob is finished by {}", Duration.between(now, Instant.now()));
		}

	}
}
