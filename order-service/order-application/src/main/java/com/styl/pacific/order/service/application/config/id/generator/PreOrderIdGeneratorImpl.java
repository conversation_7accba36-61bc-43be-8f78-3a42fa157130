/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.application.config.id.generator;

import com.styl.pacific.domain.valueobject.PreOrderId;
import com.styl.pacific.order.service.domain.features.preorder.order.id.generator.PreOrderIdGenerator;
import com.styl.pacific.utils.snowflake.id.Snowflake;

/**
 * <AUTHOR>
 */
public class PreOrderIdGeneratorImpl implements PreOrderIdGenerator {

	private final Snowflake snowflake;

	public PreOrderIdGeneratorImpl() {
		this.snowflake = new Snowflake();
	}

	@Override
	public PreOrderId nextId() {
		return new PreOrderId(snowflake.nextId());
	}
}
