/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.shared.http.preorder.order.response;

import com.styl.pacific.domain.dto.CurrencyResponse;
import com.styl.pacific.domain.enums.order.OrderCancellationType;
import com.styl.pacific.domain.enums.order.OrderPaymentStatus;
import com.styl.pacific.order.service.shared.http.order.v1.response.detail.OrderResponse;
import com.styl.pacific.order.service.shared.http.preorder.order.enums.PreOrderStatus;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Builder
@Data
public class PreOrderResponse {
	private String id;
	private String tenantId;
	private String systemSource;
	private Long version;

	private String issuerId;
	private String customerId;
	private String customerName;
	private String customerEmail;

	private List<OrderResponse> subOrders;

	private PreOrderStatus status;

	private BigInteger subtotalAmount;
	private BigInteger serviceChargeAmount;
	private BigInteger discountAmount;

	private String taxName;
	private BigDecimal taxRate;
	private Boolean taxInclude;
	private BigInteger taxAmount;

	private BigInteger totalAmount;
	private CurrencyResponse currency;

	private String paymentMethodId;
	private String paymentMethodName;
	private String paymentRef;
	private String paymentTransactionId;
	private OrderPaymentStatus paymentStatus;
	private Boolean refundable;

	private Long cancellationDueAt;
	private OrderCancellationType cancellationType;
	private String cancelReason;
	private Long canceledAt;
	private Long expiredAt;

	private Long createdAt;
	private Long updatedAt;
}
