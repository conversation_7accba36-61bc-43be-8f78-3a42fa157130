/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.shared.http.order.v2.request.place;

import com.styl.pacific.common.validator.currency.Currency;
import com.styl.pacific.common.validator.rate.ValidRate;
import com.styl.pacific.order.service.shared.http.servicecharge.enums.PlatformOrderType;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;
import lombok.Builder;

@Builder
public record OrderDetailV2Request(
		@Size(max = 255, message = "idempotencyKey must not exceed 255 characters") String idempotencyKey,
		@Size(max = 255, message = "systemSource must not exceed 255 characters") String systemSource,
		@Size(max = 80, message = "orderNumber must not exceed 80 characters") String orderNumber,
		@NotNull(message = "storeId must not be null") String storeId,

		@Digits(integer = 19, fraction = 0, message = "preOrderId must be a number") String preOrderId,
		@Digits(integer = 19, fraction = 0, message = "mealTimeId must be a number") String mealTimeId,

		@Size(max = 255, message = "staffCode must not exceed 255 characters") String staffCode,
		@Size(max = 80, message = "staffName must not exceed 80 characters") String staffName,

		@Digits(integer = 19, fraction = 0, message = "customerId must be a number") String customerId,
		@Email String customerEmail,
		@Size(max = 80, message = "customerName must not exceed 80 characters") String customerName,

		@Valid @NotNull(message = "lineItems must not be null") @Size(min = 1, message = "lineItems must have at least 1 item") List<OrderLineItemV2Request> lineItems,
		@Size(max = 255, message = "note must not exceed 255 characters") String note,
		@Valid List<OrderServiceChargeV2Request> serviceCharges,
		String taxName,
		@ValidRate @Min(value = 0, message = "taxRate must be greater than or equal to 0") BigDecimal taxRate,
		Boolean taxInclude,
		@Min(value = 0, message = "taxAmount must be greater than or equal to 0") BigInteger taxAmount,

		@Min(value = 0, message = "subtotalAmount must be greater than or equal to 0") @NotNull(message = "subtotalAmount must not be null") BigInteger subtotalAmount,
		@Min(value = 0, message = "serviceChargeAmount must be greater than or equal to 0") @NotNull(message = "serviceChargeAmount must not be null") BigInteger serviceChargeAmount,
		@Min(value = 0, message = "discountAmount must be greater than or equal to 0") @NotNull(message = "discountAmount must not be null") BigInteger discountAmount,
		@Min(value = 0, message = "totalAmount must be greater than or equal to 0") @NotNull(message = "totalAmount must not be null") BigInteger totalAmount,
		@Currency(message = "currencyCode must be a valid currency code") @NotNull(message = "currencyCode must not be null") String currencyCode,
		@NotNull(message = "platformOrderType must not be null") PlatformOrderType platformOrderType) {

}
