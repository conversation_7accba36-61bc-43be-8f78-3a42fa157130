/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.shared.http.order.v2.request.offline.place;

import com.styl.pacific.common.validator.rate.ValidRate;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.math.BigInteger;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Builder
@Data
public class OrderOfflineServiceChargeRequest {
	@NotNull(message = "serviceChargeId must not be null")
	@Digits(integer = 21, fraction = 0, message = "serviceChargeId must be a valid number")
	private String serviceChargeId;
	@NotBlank(message = "name must not be empty or null")
	@Size(max = 80, message = "name must not exceed 80 characters")
	private String name;
	@Size(max = 255, message = "description must not exceed 255 characters")
	private String description;
	@NotNull(message = "chargeFixedAmount must not be null")
	@Min(value = 0, message = "chargeFixedAmount must not be less than 0")
	private BigInteger chargeFixedAmount;
	@ValidRate
	@NotNull(message = "chargeRate must not be null")
	@Min(value = 0, message = "chargeRate must not be less than 0")
	private BigDecimal chargeRate;
	@NotNull(message = "amount must not be null")
	@Min(value = 0, message = "amount must not be less than 0")
	private BigInteger amount;
}
