/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.shared.http.servicecharge.response;

import com.styl.pacific.order.service.shared.http.servicecharge.enums.PlatformOrderType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Builder
@Data
@AllArgsConstructor
public class ServiceChargeByPlatformDTOResponse {
	private String tenantId;
	private String serviceChargeId;
	private PlatformOrderType platformOrderType;
	private Long createdAt;
	private Long updatedAt;
	private String createdBy;
	private String updatedBy;
}
