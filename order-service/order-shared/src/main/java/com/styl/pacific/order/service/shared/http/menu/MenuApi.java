/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.shared.http.menu;

import com.styl.pacific.domain.dto.Content;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.permissions.PacificApiAuthorized;
import com.styl.pacific.domain.permissions.PacificApiPermissionKey;
import com.styl.pacific.domain.permissions.PlatformApiSecurityLevel;
import com.styl.pacific.order.service.shared.http.menu.request.CreateMenuRequest;
import com.styl.pacific.order.service.shared.http.menu.request.MenuQueryRequest;
import com.styl.pacific.order.service.shared.http.menu.request.PaginationMenuQueryRequest;
import com.styl.pacific.order.service.shared.http.menu.request.UpdateMenuRequest;
import com.styl.pacific.order.service.shared.http.menu.response.MenuStubResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Digits;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * <AUTHOR>
 */
public interface MenuApi {
	@GetMapping("/api/order/menus/list")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	Content<MenuStubResponse> findAll(@Valid @SpringQueryMap @ModelAttribute MenuQueryRequest query);

	@GetMapping("/api/order/menus")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	Paging<MenuStubResponse> findAllPaging(@Valid @SpringQueryMap @ModelAttribute PaginationMenuQueryRequest query);

	@GetMapping("/api/order/menus/{id}")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	MenuStubResponse findById(
			@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be a valid number") String id);

	@PostMapping("/api/order/menus")
	@ResponseStatus(HttpStatus.CREATED)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.MENU_MGMT_ADD)
	MenuStubResponse create(@Valid @RequestBody CreateMenuRequest request);

	@PutMapping("/api/order/menus/{id}")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.MENU_MGMT_UPDATE)
	MenuStubResponse update(
			@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be a valid number") String id,
			@Valid @RequestBody UpdateMenuRequest request);

	@DeleteMapping("/api/order/menus/{id}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.MENU_MGMT_DELETE)
	void deleteById(@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be a valid number") String id);

	@PatchMapping("/api/order/menus/{id}/activate")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.MENU_MGMT_UPDATE)
	void activateById(
			@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be a valid number") String id);

	@PatchMapping("/api/order/menus/{id}/deactivate")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.MENU_MGMT_UPDATE)
	void deActivateById(
			@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be a valid number") String id);
}
