/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.preorder.dto;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.styl.pacific.domain.enums.PreOrderMenuType;
import com.styl.pacific.domain.valueobject.PreOrderMenuId;
import com.styl.pacific.domain.valueobject.StoreId;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.model.PreOrderMenuDto;
import java.time.Instant;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
class PreOrderMenuDtoTest {
	private static final Long PRE_ORDER_MENU_ID = 1L;
	private static final Long TENANT_ID = 2L;
	private static final Long STORE_ID = 2L;
	private static final String NAME = "name";
	private static final String DESCRIPTION = "description";
	private static final PreOrderMenuType TYPE = PreOrderMenuType.COLLECTION;
	private static final Instant CREATED_AT = Instant.now();
	private static final Instant UPDATED_AT = Instant.now();

	@Test
	void shouldCreated_whenBuilder() {
		// Act
		PreOrderMenuDto dto = PreOrderMenuDto.builder()
				.id(new PreOrderMenuId(PRE_ORDER_MENU_ID))
				.storeId(new StoreId(STORE_ID))
				.name(NAME)
				.description(DESCRIPTION)
				.type(TYPE)
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.build();
		// Assert
		assertEquals(PRE_ORDER_MENU_ID, dto.id()
				.getValue());
		assertEquals(STORE_ID, dto.storeId()
				.getValue());
		assertEquals(NAME, dto.name());
		assertEquals(DESCRIPTION, dto.description());
		assertEquals(TYPE, dto.type());
		assertEquals(CREATED_AT, dto.createdAt());
		assertEquals(UPDATED_AT, dto.updatedAt());
	}
}
