/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.preorder.dto.command;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.styl.pacific.order.service.domain.features.common.exception.enums.DateOfWeek;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.command.UpdatePreOrderMenuItemCommand;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import java.util.List;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
class UpdatePreOrderMenuItemCommandTest {
	private static final Long MEAL_TIME_ID = 1L;
	private static final Long PRODUCT_ID = 2L;
	private static final String START_DATE = "2021-09-01";
	private static final String END_DATE = "2021-09-30";
	private static final Integer CAPACITY = 100;
	private static final List<DateOfWeek> AVAILABLE_ON = List.of(DateOfWeek.MONDAY, DateOfWeek.TUESDAY,
			DateOfWeek.WEDNESDAY, DateOfWeek.THURSDAY, DateOfWeek.FRIDAY, DateOfWeek.SATURDAY, DateOfWeek.SUNDAY);
	private static final Boolean REPEATED = false;

	private Validator validator;

	@BeforeEach
	public void setUp() {
		try (ValidatorFactory factory = Validation.buildDefaultValidatorFactory()) {
			validator = factory.getValidator();
		}
	}

	//	@Test
	//	void shouldCreated_whenConstructor() {
	//		// Act
	//		UpdatePreOrderMenuItemCommand command = UpdatePreOrderMenuItemCommand.builder()
	//				.mealTimeId(MEAL_TIME_ID)
	//				.startDate(START_DATE)
	//				.endDate(END_DATE)
	//				.capacity(CAPACITY)
	//				.availableOn(AVAILABLE_ON)
	//				.repeated(REPEATED)
	//				.build();
	//		// Assert
	//		assertEquals(MEAL_TIME_ID, command.mealTimeId());
	//		assertEquals(START_DATE, command.startDate());
	//		assertEquals(END_DATE, command.endDate());
	//		assertEquals(CAPACITY, command.capacity());
	//		assertEquals(AVAILABLE_ON, command.availableOn());
	//		assertEquals(REPEATED, command.repeated());
	//	}

	//	@Test
	//	void whenCommand_shouldValid() {
	//		// Arrange
	//		UpdatePreOrderMenuItemCommand command = UpdatePreOrderMenuItemCommand.builder()
	//				.mealTimeId(MEAL_TIME_ID)
	//				.startDate(LocalDate.now()
	//						.toString())
	//				.endDate(LocalDate.now()
	//						.plusDays(1)
	//						.toString())
	//				.capacity(CAPACITY)
	//				.availableOn(AVAILABLE_ON)
	//				.repeated(REPEATED)
	//				.build();
	//
	//		// Act
	//		Set<ConstraintViolation<UpdatePreOrderMenuItemCommand>> violations = validator.validate(command);
	//
	//		// Assert
	//		assertTrue(violations.isEmpty());
	//	}

	@Test
	void whenCommandWithMissingProperties_shouldNotValid() {
		// Arrange
		int violationsExpect = 1;
		UpdatePreOrderMenuItemCommand command = UpdatePreOrderMenuItemCommand.builder()
				.build();
		// Act
		Set<ConstraintViolation<UpdatePreOrderMenuItemCommand>> violations = validator.validate(command);
		// Assert
		assertEquals(violationsExpect, violations.size());
	}
}
