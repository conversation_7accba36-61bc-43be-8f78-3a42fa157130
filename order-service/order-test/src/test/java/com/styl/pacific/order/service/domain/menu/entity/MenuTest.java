/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.menu.entity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.styl.pacific.domain.valueobject.MenuId;
import com.styl.pacific.domain.valueobject.StoreId;
import com.styl.pacific.order.service.domain.features.common.exception.enums.DateOfWeek;
import com.styl.pacific.order.service.domain.features.menu.entity.Menu;
import com.styl.pacific.order.service.domain.features.menu.enums.MenuStatus;
import com.styl.pacific.order.service.domain.features.menu.enums.MenuType;
import java.time.Instant;
import java.time.LocalTime;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
class MenuTest {
	private static final Long MENU_ID = 1L;
	private static final Long STORE_ID = 2L;
	private static final String NAME = "name";
	private static final String DESCRIPTION = "description";
	private static final MenuType MENU_TYPE = MenuType.POS;
	private static final MenuStatus MENU_STATUS = MenuStatus.ACTIVE;

	private static final Long MENU_ITEM_ID = 3L;
	private static final Long PRODUCT_ID = 5L;
	private static final Integer POSITION = 5;

	private static final Instant START_DATE = Instant.now();
	private static final Instant END_DATE = Instant.now();
	private static final LocalTime START_TIME = LocalTime.now();
	private static final LocalTime END_TIME = LocalTime.now();
	private static final DateOfWeek DATE_OF_WEEK = DateOfWeek.MONDAY;

	private static final Instant CREATED_AT = Instant.now();
	private static final Instant UPDATED_AT = Instant.now();

	@Test
	void shouldCreated_whenBuilder() {
		// Act
		Menu menu = Menu.builder()
				.id(new MenuId(MENU_ID))
				.storeId(new StoreId(STORE_ID))
				.name(NAME)
				.description(DESCRIPTION)
				.type(MENU_TYPE)
				.status(MENU_STATUS)
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.build();
		// Assert
		assertEquals(MENU_ID, menu.getId()
				.getValue());
		assertEquals(STORE_ID, menu.getStoreId()
				.getValue());
		assertEquals(NAME, menu.getName());
		assertEquals(DESCRIPTION, menu.getDescription());
		assertEquals(MENU_TYPE, menu.getType());
		assertEquals(MENU_STATUS, menu.getStatus());
		assertEquals(CREATED_AT, menu.getCreatedAt());
		assertEquals(UPDATED_AT, menu.getUpdatedAt());
	}

	@Test
	void shouldAssign_whenSetter() {
		// Arrange
		Menu menuMock = Menu.builder()
				.build();
		// Act
		menuMock.setId(new MenuId(MENU_ID));
		menuMock.setStoreId(new StoreId(STORE_ID));
		menuMock.setName(NAME);
		menuMock.setDescription(DESCRIPTION);
		menuMock.setType(MENU_TYPE);
		menuMock.setStatus(MENU_STATUS);
		menuMock.setCreatedAt(CREATED_AT);
		menuMock.setUpdatedAt(UPDATED_AT);
		// Assert
		assertEquals(MENU_ID, menuMock.getId()
				.getValue());
		assertEquals(STORE_ID, menuMock.getStoreId()
				.getValue());
		assertEquals(NAME, menuMock.getName());
		assertEquals(DESCRIPTION, menuMock.getDescription());
		assertEquals(MENU_TYPE, menuMock.getType());
		assertEquals(MENU_STATUS, menuMock.getStatus());
		assertEquals(CREATED_AT, menuMock.getCreatedAt());
		assertEquals(UPDATED_AT, menuMock.getUpdatedAt());
	}

	@Test
	void shouldEqual_whenEquals() {
		// Arrange
		Menu menu1 = getMenu();
		Menu menu2 = getMenu();
		// Act
		boolean isEqual = menu1.equals(menu2);
		// Assert
		assertTrue(isEqual);
	}

	@Test
	void shouldNotEqual_whenEquals() {
		// Arrange
		Menu menu1 = getMenu();
		Menu menu2 = getMenu();
		menu2.setType(MenuType.APP);
		// Act
		boolean isEqual = menu1.equals(menu2);
		// Assert
		assertFalse(isEqual);
	}

	@Test
	void shouldEqual_whenHashCode() {
		// Arrange
		Menu menu1 = getMenu();
		Menu menu2 = getMenu();
		// Act
		boolean isEqual = menu1.hashCode() == menu2.hashCode();
		// Assert
		assertTrue(isEqual);
	}

	@Test
	void shouldNotEqual_whenHashCode() {
		// Arrange
		Menu menu1 = getMenu();
		Menu menu2 = getMenu();
		menu2.setType(MenuType.APP);
		// Act
		boolean isEqual = menu1.hashCode() == menu2.hashCode();
		// Assert
		assertFalse(isEqual);
	}

	private Menu getMenu() {
		return Menu.builder()
				.id(new MenuId(MENU_ID))
				.storeId(new StoreId(STORE_ID))
				.name(NAME)
				.description(DESCRIPTION)
				.type(MENU_TYPE)
				.status(MENU_STATUS)
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.build();
	}

}
