/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.menuboard;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.styl.pacific.domain.valueobject.MenuBoardId;
import com.styl.pacific.domain.valueobject.MenuBoardImageId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.domain.features.menuboard.dto.command.CreateMenuBoardCommand;
import com.styl.pacific.order.service.domain.features.menuboard.dto.command.CreateMenuBoardImageCommand;
import com.styl.pacific.order.service.domain.features.menuboard.dto.command.UpdateMenuBoardCommand;
import com.styl.pacific.order.service.domain.features.menuboard.dto.command.UpdateMenuBoardImageCommand;
import com.styl.pacific.order.service.domain.features.menuboard.entity.MenuBoard;
import com.styl.pacific.order.service.domain.features.menuboard.entity.MenuBoardImage;
import com.styl.pacific.order.service.domain.features.menuboard.enums.MenuBoardDisplayMode;
import com.styl.pacific.order.service.domain.features.menuboard.id.generator.MenuBoardIdGenerator;
import com.styl.pacific.order.service.domain.features.menuboard.id.generator.MenuBoardImageIdGenerator;
import com.styl.pacific.order.service.domain.features.menuboard.ports.MenuBoardServiceImpl;
import com.styl.pacific.order.service.domain.features.menuboard.ports.output.repository.MenuBoardActivationCodeSessionRepository;
import com.styl.pacific.order.service.domain.features.menuboard.ports.output.repository.MenuBoardRepository;
import com.styl.pacific.order.service.domain.features.menuboard.ports.output.repository.MenuBoardSessionRepository;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class MenuBoardServiceImplTest {

	private static final Long ID = 1L;
	private static final Long TENANT_ID = 2L;
	private static final String NAME = "Menu Board Name";
	private static final Integer DELAY_BETWEEN_IMAGES = 5;
	private static final MenuBoardDisplayMode DISPLAY_MODE = MenuBoardDisplayMode.CAROUSEL;

	private static final Long IMAGE_ID = 3L;
	private static final String IMAGE_PATH = "bucket:image/path.png";

	@Mock
	private MenuBoardRepository menuBoardRepository;
	@Mock
	private MenuBoardActivationCodeSessionRepository menuBoardActivationCodeSessionRepository;
	@Mock
	private MenuBoardSessionRepository menuBoardSessionRepository;
	@Mock
	private MenuBoardIdGenerator menuBoardIdGenerator;
	@Mock
	private MenuBoardImageIdGenerator menuBoardImageIdGenerator;

	@InjectMocks
	private MenuBoardServiceImpl menuBoardService;

	@Test
	public void shouldReturnMenuBoard_whenSave() {
		// Arrange
		CreateMenuBoardCommand commandMock = CreateMenuBoardCommand.builder()
				.name(NAME)
				.displayMode(DISPLAY_MODE)
				.images(List.of(CreateMenuBoardImageCommand.builder()
						.imagePath(IMAGE_PATH)
						.build()))
				.delayBetweenImages(DELAY_BETWEEN_IMAGES)
				.build();
		MenuBoard menuBoardMock = getMockMenuBoard();
		when(menuBoardIdGenerator.nextId()).thenReturn(new MenuBoardId(ID));
		when(menuBoardImageIdGenerator.nextId()).thenReturn(new MenuBoardImageId(IMAGE_ID));
		when(menuBoardRepository.save(any())).thenReturn(menuBoardMock);
		// Act
		MenuBoard menuBoard = menuBoardService.save(new TenantId(TENANT_ID), commandMock);
		// Assert
		ArgumentCaptor<MenuBoard> menuBoardArgumentCaptor = ArgumentCaptor.forClass(MenuBoard.class);
		verify(menuBoardRepository).save(menuBoardArgumentCaptor.capture());
		MenuBoard menuBoardArgument = menuBoardArgumentCaptor.getValue();
		assertEquals(ID, menuBoardArgument.getId()
				.getValue());
		assertEquals(TENANT_ID, menuBoardArgument.getTenantId()
				.getValue());
		assertEquals(NAME, menuBoardArgument.getName());
		assertEquals(DISPLAY_MODE, menuBoardArgument.getDisplayMode());
		assertEquals(DELAY_BETWEEN_IMAGES, menuBoardArgument.getDelayBetweenImages());
		assertEquals(1, menuBoardArgument.getImages()
				.size());
		assertEquals(IMAGE_ID, menuBoardArgument.getImages()
				.getFirst()
				.getId()
				.getValue());
		assertEquals(ID, menuBoardArgument.getImages()
				.getFirst()
				.getMenuBoardId()
				.getValue());
		assertEquals(IMAGE_PATH, menuBoardArgument.getImages()
				.getFirst()
				.getImagePath());

		verify(menuBoardIdGenerator, times(1)).nextId();
		verify(menuBoardImageIdGenerator, times(1)).nextId();
		verify(menuBoardRepository, times(1)).save(menuBoardMock);
		assertEquals(menuBoardMock, menuBoard);

	}

	@Test
	public void shouldReturnMenuBoard_whenUpdate() {
		// Arrange
		Long newImageId = 4L;
		String newName = "New Menu Board Name";
		MenuBoardDisplayMode newDisplayMode = MenuBoardDisplayMode.CAROUSEL;
		Integer newDelayBetweenImages = 10;
		String newImagePath = "bucket:image/new/path.png";
		UpdateMenuBoardCommand commandMock = UpdateMenuBoardCommand.builder()
				.name(newName)
				.displayMode(newDisplayMode)
				.images(List.of(UpdateMenuBoardImageCommand.builder()
						.imagePath(newImagePath)
						.build()))
				.delayBetweenImages(newDelayBetweenImages)
				.build();
		MenuBoard menuBoardMock = getMockMenuBoard();
		when(menuBoardImageIdGenerator.nextId()).thenReturn(new MenuBoardImageId(newImageId));
		when(menuBoardRepository.findById(any(), any())).thenReturn(Optional.of(menuBoardMock));
		when(menuBoardRepository.update(any())).thenReturn(menuBoardMock);
		// Act
		MenuBoard menuBoard = menuBoardService.update(new TenantId(TENANT_ID), new MenuBoardId(ID), commandMock);
		// Assert
		verify(menuBoardRepository, times(1)).findById(eq(new TenantId(TENANT_ID)), eq(new MenuBoardId(ID)));
		ArgumentCaptor<MenuBoard> menuBoardArgumentCaptor = ArgumentCaptor.forClass(MenuBoard.class);
		verify(menuBoardRepository, times(1)).update(menuBoardArgumentCaptor.capture());
		MenuBoard menuBoardArgument = menuBoardArgumentCaptor.getValue();
		assertEquals(ID, menuBoardArgument.getId()
				.getValue());
		assertEquals(TENANT_ID, menuBoardArgument.getTenantId()
				.getValue());
		assertEquals(newName, menuBoardArgument.getName());
		assertEquals(newDisplayMode, menuBoardArgument.getDisplayMode());
		assertEquals(newDelayBetweenImages, menuBoardArgument.getDelayBetweenImages());
		assertEquals(1, menuBoardArgument.getImages()
				.size());
		assertEquals(newImageId, menuBoardArgument.getImages()
				.getFirst()
				.getId()
				.getValue());
		assertEquals(ID, menuBoardArgument.getImages()
				.getFirst()
				.getMenuBoardId()
				.getValue());
		assertEquals(newImagePath, menuBoardArgument.getImages()
				.getFirst()
				.getImagePath());

		assertEquals(menuBoardMock, menuBoard);
	}

	@Test
	public void shouldSuccess_whenDelete() {
		// Arrange
		MenuBoard menuBoardMock = getMockMenuBoard();
		when(menuBoardRepository.existsById(any(), any())).thenReturn(true);
		// Act
		menuBoardService.deleteById(new TenantId(TENANT_ID), new MenuBoardId(ID));
		// Assert
		verify(menuBoardRepository, times(1)).existsById(eq(new TenantId(TENANT_ID)), eq(new MenuBoardId(ID)));
		verify(menuBoardRepository, times(1)).deleteById(eq(new TenantId(TENANT_ID)), eq(new MenuBoardId(ID)));
	}

	private MenuBoard getMockMenuBoard() {
		return MenuBoard.builder()
				.id(new MenuBoardId(ID))
				.tenantId(new TenantId(TENANT_ID))
				.name(NAME)
				.displayMode(DISPLAY_MODE)
				.images(new ArrayList<>(List.of(MenuBoardImage.builder()
						.id(new MenuBoardImageId(IMAGE_ID))
						.menuBoardId(new MenuBoardId(ID))
						.imagePath(IMAGE_PATH)
						.build())))
				.delayBetweenImages(DELAY_BETWEEN_IMAGES)
				.build();
	}
}
