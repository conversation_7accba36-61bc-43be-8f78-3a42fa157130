/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.relations.servicecharge.adapter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.styl.pacific.common.test.BaseDataJpaTest;
import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.config.IntegrationTestConfiguration;
import com.styl.pacific.order.service.domain.features.servicecharge.entity.ServiceCharge;
import com.styl.pacific.order.service.domain.features.servicecharge.entity.ServiceChargeByPlatform;
import com.styl.pacific.order.service.domain.features.servicecharge.enums.PlatformOrderType;
import com.styl.pacific.order.service.domain.features.servicecharge.ports.output.repository.ServiceChargeByPlatformRepository;
import com.styl.pacific.order.service.domain.features.servicecharge.ports.output.repository.ServiceChargeRepository;
import com.styl.pacific.order.service.domain.features.servicecharge.valueobjects.ServiceChargeId;
import io.micrometer.core.instrument.MeterRegistry;
import java.math.BigDecimal;
import java.util.List;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;

/**
 * <AUTHOR>
 */
@ExtendWith(SpringExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ContextConfiguration(classes = { IntegrationTestConfiguration.class })
public class ServiceChargeByPlatformRepositoryIntegrationTest extends BaseDataJpaTest {
	@MockitoBean
	private MeterRegistry meterRegistry;

	@Autowired
	private ServiceChargeRepository serviceChargeRepository;

	@Autowired
	private ServiceChargeByPlatformRepository serviceChargeByPlatformRepository;

	@Test
	@Order(0)
	public void shouldReturn_whenSaveAll() {
		// Arrange
		ServiceCharge serviceCharge = ServiceCharge.builder()
				.id(new ServiceChargeId(100L))
				.tenantId(new TenantId(2L))
				.name("Test")
				.description("Description")
				.isActive(true)
				.chargeFixedAmount(Money.ZERO)
				.chargeRate(BigDecimal.ZERO)
				.currencyCode("USD")
				.build();
		serviceChargeRepository.save(serviceCharge);
		List<ServiceChargeByPlatform> serviceChargeByPlatforms = List.of(ServiceChargeByPlatform.builder()
				.serviceChargeId(serviceCharge.getId())
				.tenantId(serviceCharge.getTenantId())
				.platformOrderType(PlatformOrderType.GUEST_ORDER)
				.build(), ServiceChargeByPlatform.builder()
						.serviceChargeId(serviceCharge.getId())
						.tenantId(serviceCharge.getTenantId())
						.platformOrderType(PlatformOrderType.ONLINE_ORDER)
						.build());
		// Act
		List<ServiceChargeByPlatform> result = serviceChargeByPlatformRepository.saveAll(serviceChargeByPlatforms);
		// Assert
		assertNotNull(result);
		List<ServiceChargeByPlatform> resultCheck = serviceChargeByPlatformRepository.findAllByServiceChargeId(
				serviceCharge.getTenantId(), serviceCharge.getId());
		assertEquals(serviceChargeByPlatforms.size(), resultCheck.size());
	}

	@Test
	@Order(1)
	public void shouldReturn_whenUpdateAll() {
		// Arrange
		ServiceCharge serviceCharge = ServiceCharge.builder()
				.id(new ServiceChargeId(101L))
				.tenantId(new TenantId(2L))
				.name("Test")
				.description("Description")
				.isActive(true)
				.chargeFixedAmount(Money.ZERO)
				.chargeRate(BigDecimal.ZERO)
				.currencyCode("USD")
				.build();
		serviceChargeRepository.save(serviceCharge);
		List<ServiceChargeByPlatform> serviceChargeByPlatforms = List.of(ServiceChargeByPlatform.builder()
				.serviceChargeId(serviceCharge.getId())
				.tenantId(serviceCharge.getTenantId())
				.platformOrderType(PlatformOrderType.GUEST_ORDER)
				.build(), ServiceChargeByPlatform.builder()
						.serviceChargeId(serviceCharge.getId())
						.tenantId(serviceCharge.getTenantId())
						.platformOrderType(PlatformOrderType.ONLINE_ORDER)
						.build());
		serviceChargeByPlatformRepository.saveAll(serviceChargeByPlatforms);
		List<ServiceChargeByPlatform> serviceChargeByPlatformsUpdate = List.of(ServiceChargeByPlatform.builder()
				.serviceChargeId(serviceCharge.getId())
				.tenantId(serviceCharge.getTenantId())
				.platformOrderType(PlatformOrderType.PRE_ORDER)
				.build(), ServiceChargeByPlatform.builder()
						.serviceChargeId(serviceCharge.getId())
						.tenantId(serviceCharge.getTenantId())
						.platformOrderType(PlatformOrderType.ONLINE_ORDER)
						.build());

		// Act
		List<ServiceChargeByPlatform> result = serviceChargeByPlatformRepository.updateAll(
				serviceChargeByPlatformsUpdate);
		// Assert
		assertNotNull(result);
		List<ServiceChargeByPlatform> resultCheck = serviceChargeByPlatformRepository.findAllByServiceChargeId(
				serviceCharge.getTenantId(), serviceCharge.getId());
		assertEquals(serviceChargeByPlatformsUpdate.size(), resultCheck.size());
		assertTrue(resultCheck.stream()
				.anyMatch(serviceChargeByPlatform -> serviceChargeByPlatform.getPlatformOrderType()
						.equals(PlatformOrderType.PRE_ORDER)));
		assertTrue(resultCheck.stream()
				.anyMatch(serviceChargeByPlatform -> serviceChargeByPlatform.getPlatformOrderType()
						.equals(PlatformOrderType.ONLINE_ORDER)));
	}

	@Test
	@Order(2)
	public void shouldReturn_whenFindByServiceChargeId() {
		// Arrange
		ServiceCharge serviceCharge = ServiceCharge.builder()
				.id(new ServiceChargeId(101L))
				.tenantId(new TenantId(2L))
				.name("Test")
				.description("Description")
				.isActive(true)
				.chargeFixedAmount(Money.ZERO)
				.chargeRate(BigDecimal.ZERO)
				.currencyCode("USD")
				.build();
		serviceChargeRepository.save(serviceCharge);
		List<ServiceChargeByPlatform> serviceChargeByPlatforms = List.of(ServiceChargeByPlatform.builder()
				.serviceChargeId(serviceCharge.getId())
				.tenantId(serviceCharge.getTenantId())
				.platformOrderType(PlatformOrderType.GUEST_ORDER)
				.build(), ServiceChargeByPlatform.builder()
						.serviceChargeId(serviceCharge.getId())
						.tenantId(serviceCharge.getTenantId())
						.platformOrderType(PlatformOrderType.ONLINE_ORDER)
						.build());
		serviceChargeByPlatformRepository.saveAll(serviceChargeByPlatforms);
		// Act
		List<ServiceChargeByPlatform> result = serviceChargeByPlatformRepository.findAllByServiceChargeId(serviceCharge
				.getTenantId(), serviceCharge.getId());
		// Assert
		assertNotNull(result);
		assertEquals(serviceChargeByPlatforms.size(), result.size());
	}

}
