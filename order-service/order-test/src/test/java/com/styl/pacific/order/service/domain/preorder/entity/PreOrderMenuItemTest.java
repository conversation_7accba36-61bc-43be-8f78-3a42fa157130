/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.preorder.entity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.styl.pacific.domain.valueobject.PreOrderMenuId;
import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.order.service.domain.features.mealtime.valueobjects.MealTimeId;
import com.styl.pacific.order.service.domain.features.preorder.menu.entity.PreOrderMenuItem;
import com.styl.pacific.order.service.domain.features.preorder.menu.valueobjects.PreOrderMenuItemChainId;
import com.styl.pacific.order.service.domain.features.preorder.menu.valueobjects.PreOrderMenuItemId;
import java.time.Instant;
import java.time.LocalDate;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
class PreOrderMenuItemTest {
	private static final Long ID = 1L;
	private static final Long CHAIN_ID = 2L;
	private static final Long PRE_ORDER_MENU_ID = 3L;
	private static final Long MEAL_TIME_ID = 4L;
	private static final Long PRODUCT_ID = 5L;
	private static final LocalDate DATE = LocalDate.now();
	private static final Integer CAPACITY = 10;
	private static final Integer ORDERED = 5;
	private static final Instant CREATED_AT = Instant.now();
	private static final Instant UPDATED_AT = Instant.now();

	@Test
	void shouldCreated_whenBuilder() {
		// Act
		PreOrderMenuItem preOrderMenuItem = PreOrderMenuItem.builder()
				.id(new PreOrderMenuItemId(ID))
				.chainId(new PreOrderMenuItemChainId(CHAIN_ID))
				.preOrderMenuId(new PreOrderMenuId(PRE_ORDER_MENU_ID))
				.productId(new ProductId(PRODUCT_ID))
				.mealTimeId(new MealTimeId(MEAL_TIME_ID))
				.capacity(CAPACITY)
				.ordered(ORDERED)
				.date(DATE)
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.build();
		// Assert
		assertEquals(ID, preOrderMenuItem.getId()
				.getValue());
		assertEquals(CHAIN_ID, preOrderMenuItem.getChainId()
				.getValue());
		assertEquals(PRE_ORDER_MENU_ID, preOrderMenuItem.getPreOrderMenuId()
				.getValue());
		assertEquals(PRODUCT_ID, preOrderMenuItem.getProductId()
				.getValue());
		assertEquals(MEAL_TIME_ID, preOrderMenuItem.getMealTimeId()
				.getValue());
		assertEquals(DATE, preOrderMenuItem.getDate());
		assertEquals(CAPACITY, preOrderMenuItem.getCapacity());
		assertEquals(ORDERED, preOrderMenuItem.getOrdered());
		assertEquals(CREATED_AT, preOrderMenuItem.getCreatedAt());
		assertEquals(UPDATED_AT, preOrderMenuItem.getUpdatedAt());
	}

	@Test
	void shouldInvoke_whenSetter() {
		// Arrange
		PreOrderMenuItem preOrderMenuItem = PreOrderMenuItem.builder()
				.build();
		// Act
		preOrderMenuItem.setId(new PreOrderMenuItemId(ID));
		preOrderMenuItem.setChainId(new PreOrderMenuItemChainId(CHAIN_ID));
		preOrderMenuItem.setPreOrderMenuId(new PreOrderMenuId(PRE_ORDER_MENU_ID));
		preOrderMenuItem.setProductId(new ProductId(PRODUCT_ID));
		preOrderMenuItem.setMealTimeId(new MealTimeId(MEAL_TIME_ID));
		preOrderMenuItem.setDate(DATE);
		preOrderMenuItem.setOrdered(ORDERED);
		preOrderMenuItem.setCapacity(CAPACITY);
		preOrderMenuItem.setCreatedAt(CREATED_AT);
		preOrderMenuItem.setUpdatedAt(UPDATED_AT);
		// Assert
		assertEquals(ID, preOrderMenuItem.getId()
				.getValue());
		assertEquals(CHAIN_ID, preOrderMenuItem.getChainId()
				.getValue());
		assertEquals(PRE_ORDER_MENU_ID, preOrderMenuItem.getPreOrderMenuId()
				.getValue());
		assertEquals(PRODUCT_ID, preOrderMenuItem.getProductId()
				.getValue());
		assertEquals(MEAL_TIME_ID, preOrderMenuItem.getMealTimeId()
				.getValue());
		assertEquals(CAPACITY, preOrderMenuItem.getCapacity());
		assertEquals(ORDERED, preOrderMenuItem.getOrdered());
		assertEquals(DATE, preOrderMenuItem.getDate());
		assertEquals(CREATED_AT, preOrderMenuItem.getCreatedAt());
		assertEquals(UPDATED_AT, preOrderMenuItem.getUpdatedAt());
	}

	@Test
	void shouldEqual_whenEquals() {
		// Arrange
		PreOrderMenuItem preOrderMenuItem1 = getPreOrderMenuItem();
		PreOrderMenuItem preOrderMenuItem2 = getPreOrderMenuItem();
		// Act
		boolean isEqual = preOrderMenuItem1.equals(preOrderMenuItem2);
		// Assert
		assertTrue(isEqual);
	}

	@Test
	void shouldNotEqual_whenEquals() {
		// Arrange
		PreOrderMenuItem preOrderMenuItem1 = getPreOrderMenuItem();
		PreOrderMenuItem preOrderMenuItem2 = getPreOrderMenuItem();
		preOrderMenuItem2.setProductId(new ProductId(4L));
		// Act
		boolean isEqual = preOrderMenuItem1.equals(preOrderMenuItem2);
		// Assert
		assertFalse(isEqual);
	}

	@Test
	void shouldEqual_whenHashCode() {
		// Arrange
		PreOrderMenuItem preOrderMenuItem1 = getPreOrderMenuItem();
		PreOrderMenuItem preOrderMenuItem2 = getPreOrderMenuItem();
		// Act
		boolean isEqual = preOrderMenuItem1.hashCode() == preOrderMenuItem2.hashCode();
		// Assert
		assertTrue(isEqual);
	}

	@Test
	void shouldNotEqual_whenHashCode() {
		// Arrange
		PreOrderMenuItem preOrderMenuItem1 = getPreOrderMenuItem();
		PreOrderMenuItem preOrderMenuItem2 = getPreOrderMenuItem();
		preOrderMenuItem2.setProductId(new ProductId(4L));
		// Act
		boolean isEqual = preOrderMenuItem1.hashCode() == preOrderMenuItem2.hashCode();
		// Assert
		assertFalse(isEqual);
	}

	private PreOrderMenuItem getPreOrderMenuItem() {
		return PreOrderMenuItem.builder()
				.id(new PreOrderMenuItemId(ID))
				.chainId(new PreOrderMenuItemChainId(CHAIN_ID))
				.preOrderMenuId(new PreOrderMenuId(PRE_ORDER_MENU_ID))
				.productId(new ProductId(PRODUCT_ID))
				.mealTimeId(new MealTimeId(MEAL_TIME_ID))
				.capacity(CAPACITY)
				.ordered(ORDERED)
				.date(DATE)
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.build();
	}
}
