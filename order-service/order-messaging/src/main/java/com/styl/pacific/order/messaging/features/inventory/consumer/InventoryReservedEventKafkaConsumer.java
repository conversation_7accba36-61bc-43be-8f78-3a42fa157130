/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.messaging.features.inventory.consumer;

import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.kafka.consumer.KafkaConsumer;
import com.styl.pacific.kafka.inventory.avro.model.InventoryReservedAvroEvent;
import com.styl.pacific.order.messaging.features.inventory.mapper.InventoryMessagingMapper;
import com.styl.pacific.order.service.domain.features.inventory.dto.command.ReserveInventoryCommand;
import com.styl.pacific.order.service.domain.features.inventory.ports.input.service.InventoryReservationService;
import jakarta.annotation.PostConstruct;
import java.util.UUID;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.annotation.RetryableTopic;
import org.springframework.retry.annotation.Backoff;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
@ConditionalOnProperty(name = "pacific.kafka.order-service.consumers.inventory-reserved-event.enabled", havingValue = "true")
public class InventoryReservedEventKafkaConsumer implements KafkaConsumer<UUID, InventoryReservedAvroEvent> {
	private static final Logger log = LoggerFactory.getLogger(InventoryReservedEventKafkaConsumer.class);
	private final InventoryReservationService inventoryService;

	@PostConstruct
	public void init() {
		log.info("Constructed InventoryReservedEventKafkaConsumer");
	}

	@Override
	@RetryableTopic(backoff = @Backoff(delayExpression = "#{${pacific.kafka.order-service.consumers.inventory-reserved-event.retry-interval-ms}}"), attempts = "${pacific.kafka.order-service.consumers.inventory-reserved-event.retry-attempts}", autoCreateTopics = "false", dltTopicSuffix = "-${pacific.kafka.order-service.consumers.inventory-reserved-event.group-id}-dlt", retryTopicSuffix = "-${pacific.kafka.order-service.consumers.inventory-reserved-event.group-id}-retry")
	@KafkaListener(id = "${pacific.kafka.order-service.consumers.inventory-reserved-event.group-id}", topics = "${pacific.kafka.order-service.consumers.inventory-reserved-event.topic-name}")
	public void receive(InventoryReservedAvroEvent message, UUID key, Integer partion, Long offset) {
		log.info("Received reservation inventory event with messageId: {}, tenantId: {}, partion {}, offset {}", message
				.getId(), message.getTenantId(), partion, offset);
		ReserveInventoryCommand command = InventoryMessagingMapper.INSTANCE.avroToCommand(message);
		inventoryService.reserveInventory(new TenantId(message.getTenantId()), command);
	}
}
