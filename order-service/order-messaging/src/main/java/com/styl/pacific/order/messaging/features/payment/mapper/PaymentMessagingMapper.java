/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.messaging.features.payment.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.kafka.payments.avro.model.PaymentOfflineTransactionCreatedEventAvroModel;
import com.styl.pacific.kafka.payments.avro.model.PaymentReversalCommandAvroModel;
import com.styl.pacific.kafka.payments.avro.model.PaymentReversedEventAvroModel;
import com.styl.pacific.kafka.payments.avro.model.PaymentSettlementCreatedEventAvroModel;
import com.styl.pacific.order.service.domain.features.common.mapper.CommonDataMapper;
import com.styl.pacific.order.service.domain.features.order.dto.command.payment.OrderOfflinePaymentTransactionSettlementCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.payment.OrderPaymentSettlementCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.refund.MarkOrderRefundedCommand;
import com.styl.pacific.order.service.domain.features.payment.event.PaymentReverseEvent;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, uses = { CommonDataMapper.class, MapstructCommonDomainMapper.class,
		MapstructCommonMapper.class })
public interface PaymentMessagingMapper {

	PaymentMessagingMapper INSTANCE = Mappers.getMapper(PaymentMessagingMapper.class);

	@Mapping(target = "paymentTransactionId", qualifiedByName = "longToPaymentTransactionId")
	@Mapping(target = "tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "paymentMethodId", qualifiedByName = "longToPaymentMethodId")
	@Mapping(target = "customerId", qualifiedByName = "longToUserId")
	@Mapping(target = "paidAt", qualifiedByName = "longToInstant")
	@Mapping(target = "initiatedAt", qualifiedByName = "longToInstant")
	OrderPaymentSettlementCommand toOrderPaymentSettlementCommand(PaymentSettlementCreatedEventAvroModel model);

	@Mapping(target = "paymentTransactionId", qualifiedByName = "longToPaymentTransactionId")
	@Mapping(target = "tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "paymentMethodId", qualifiedByName = "longToPaymentMethodId")
	@Mapping(target = "customerId", qualifiedByName = "longToUserId")
	@Mapping(target = "paidAt", qualifiedByName = "longToInstant")
	@Mapping(target = "initiatedAt", qualifiedByName = "longToInstant")
	OrderOfflinePaymentTransactionSettlementCommand toCommand(PaymentOfflineTransactionCreatedEventAvroModel model);

	@Mapping(target = "id", source = "id")
	@Mapping(target = "tenantId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "paymentTransactionId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "refundingAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "zonedDateTimeToLong")
	PaymentReversalCommandAvroModel toAvroModel(PaymentReverseEvent event);

	MarkOrderRefundedCommand toCommand(PaymentReversedEventAvroModel event);
}
