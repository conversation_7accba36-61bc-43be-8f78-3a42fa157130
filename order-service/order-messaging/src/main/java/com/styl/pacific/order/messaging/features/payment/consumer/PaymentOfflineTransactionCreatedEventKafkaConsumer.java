/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.messaging.features.payment.consumer;

import com.styl.pacific.kafka.consumer.KafkaConsumer;
import com.styl.pacific.kafka.payments.avro.model.PaymentOfflineTransactionCreatedEventAvroModel;
import com.styl.pacific.kafka.payments.avro.model.PaymentTransactionType;
import com.styl.pacific.order.messaging.features.payment.mapper.PaymentMessagingMapper;
import com.styl.pacific.order.service.domain.features.order.dto.command.payment.OrderOfflinePaymentTransactionSettlementCommand;
import com.styl.pacific.order.service.domain.features.order.exception.OrderPaidFailedException;
import com.styl.pacific.order.service.domain.features.payment.port.input.service.OrderOfflinePaymentService;
import jakarta.annotation.PostConstruct;
import java.util.UUID;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.annotation.RetryableTopic;
import org.springframework.retry.annotation.Backoff;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
@ConditionalOnProperty(name = "pacific.kafka.order-service.consumers.payment-offline-transaction-created-event.enabled", havingValue = "true")
public class PaymentOfflineTransactionCreatedEventKafkaConsumer implements
		KafkaConsumer<UUID, PaymentOfflineTransactionCreatedEventAvroModel> {

	private static final Logger log = LoggerFactory.getLogger(PaymentOfflineTransactionCreatedEventKafkaConsumer.class);
	private final OrderOfflinePaymentService orderPaymentService;

	@PostConstruct
	public void init() {
		log.info("Constructed PaymentOfflineTransactionCreatedEventKafkaConsumer");
	}

	@Override
	@RetryableTopic(backoff = @Backoff(delayExpression = "#{${pacific.kafka.order-service.consumers.payment-offline-transaction-created-event.retry-interval-ms}}"), attempts = "${pacific.kafka.order-service.consumers.payment-offline-transaction-created-event.retry-attempts}", autoCreateTopics = "false", exclude = {
			OrderPaidFailedException.class }, dltTopicSuffix = "-${pacific.kafka.order-service.consumers.payment-offline-transaction-created-event.group-id}-dlt", retryTopicSuffix = "-${pacific.kafka.order-service.consumers.payment-offline-transaction-created-event.group-id}-retry")
	@KafkaListener(id = "${pacific.kafka.order-service.consumers.payment-offline-transaction-created-event.group-id}", topics = "${pacific.kafka.order-service.consumers.payment-offline-transaction-created-event.topic-name}")
	public void receive(PaymentOfflineTransactionCreatedEventAvroModel message, UUID key, Integer partion,
			Long offset) {
		log.info("Received payment settlement created event with messageId: {}, tenantId: {}, partion {}, offset {}",
				message.getId(), message.getTenantId(), partion, offset);
		if (PaymentTransactionType.TOP_UP.equals(message.getTransactionType())) {
			return;
		}
		OrderOfflinePaymentTransactionSettlementCommand command = PaymentMessagingMapper.INSTANCE.toCommand(message);
		orderPaymentService.settleOrderPayment(command);
	}
}
