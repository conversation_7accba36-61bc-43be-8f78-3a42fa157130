FROM public.ecr.aws/amazoncorretto/amazoncorretto:21


ADD target/*.jar ./
ADD target/dependency/*.jar ./

# Set runtime interface client as default command for the container runtime
ENTRYPOINT [ "/usr/bin/java", "-cp", "./*", "com.amazonaws.services.lambda.runtime.api.client.AWSLambda" ]
# Pass the name of the function handler as an argument to the runtime
CMD [ "com.styl.pacific.lambda.datasync.S3LambdaDataSyncHandler::handleRequest" ]