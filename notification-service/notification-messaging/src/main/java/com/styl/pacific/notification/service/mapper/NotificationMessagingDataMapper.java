/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.kafka.notification.avro.model.EmailNotificationCreatedAvroEvent;
import com.styl.pacific.kafka.notification.avro.model.InAppNotificationCreatedAvroEvent;
import com.styl.pacific.kafka.notification.avro.model.NotificationCreatedAvroEvent;
import com.styl.pacific.kafka.notification.avro.model.QueueNotificationCreatedAvroEvent;
import com.styl.pacific.notification.service.domain.dto.CreateEmailNotificationCommand;
import com.styl.pacific.notification.service.domain.dto.CreateInAppNotificationCommand;
import com.styl.pacific.notification.service.domain.dto.CreateNotificationCommand;
import com.styl.pacific.notification.service.domain.dto.HandleNotificationCommand;
import com.styl.pacific.notification.service.domain.event.EmailNotificationCreatedEvent;
import com.styl.pacific.notification.service.domain.event.InAppNotificationCreatedEvent;
import com.styl.pacific.notification.service.domain.event.QueueNotificationCreatedEvent;
import com.styl.pacific.notification.service.enums.NotificationChannel;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class })
public interface NotificationMessagingDataMapper {

	NotificationMessagingDataMapper INSTANCE = Mappers.getMapper(NotificationMessagingDataMapper.class);

	HandleNotificationCommand notificationCreatedEventToHandleNotificationCommand(NotificationCreatedAvroEvent event);

	QueueNotificationCreatedAvroEvent queueNotificationCreatedEventToAvroQueueNotificationCreatedEvent(
			QueueNotificationCreatedEvent event);

	@Mapping(target = "source", source = "event.notification.source")
	@Mapping(target = "expiresAt", source = "event.notification.expiresAt", qualifiedByName = "instantToLong")
	@Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "zonedDateTimeToLong")
	EmailNotificationCreatedAvroEvent emailNotificationCreatedEventToAvroEmailNotificationCreatedEvent(
			EmailNotificationCreatedEvent event);

	CreateNotificationCommand notificationCreatedAvroEventToCreateNotificationCommand(
			QueueNotificationCreatedAvroEvent event);

	@Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "zonedDateTimeToLong")
	InAppNotificationCreatedAvroEvent inAppNotificationCreatedEventToAvroInAppNotificationCreatedEvent(
			InAppNotificationCreatedEvent event);

	CreateInAppNotificationCommand inAppNotificationCreatedAvroEventToCreateInAppNotificationCommand(
			InAppNotificationCreatedAvroEvent event, NotificationChannel channel);

	CreateEmailNotificationCommand emailNotificationCreatedAvroEventToCreateEmailNotificationCommand(
			EmailNotificationCreatedAvroEvent event, NotificationChannel channel);
}
