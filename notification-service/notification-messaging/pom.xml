<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.styl.pacific.notification.service</groupId>
        <artifactId>notification-service</artifactId>
        <version>1.2.4</version>
    </parent>

    <artifactId>notification-messaging</artifactId>
    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>kafka-consumer</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>kafka-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>kafka-producer</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.notification.service</groupId>
            <artifactId>notification-domain-service</artifactId>
        </dependency>
    </dependencies>

</project>
