/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.rest.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.notification.service.domain.dto.DeleteInAppNotificationCommand;
import com.styl.pacific.notification.service.domain.dto.FindInAppNotificationsQuery;
import com.styl.pacific.notification.service.domain.dto.InAppNotificationsFilterQuery;
import com.styl.pacific.notification.service.domain.dto.ReadInAppNotificationCommand;
import com.styl.pacific.notification.service.domain.entity.InAppNotificationTracking;
import com.styl.pacific.notification.service.requests.DeleteInAppNotificationRequest;
import com.styl.pacific.notification.service.requests.FindInAppNotificationRequest;
import com.styl.pacific.notification.service.requests.InAppNotificationsFilterRequest;
import com.styl.pacific.notification.service.requests.ReadInAppNotificationRequest;
import com.styl.pacific.notification.service.response.InAppNotificationTrackingResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface NotificationControllerMapper {

	NotificationControllerMapper INSTANCE = Mappers.getMapper(NotificationControllerMapper.class);

	@Mapping(target = "sortFields", ignore = true)
	@Mapping(target = "sortDirection", ignore = true)
	@Mapping(target = "page", ignore = true)
	@Mapping(target = "filter", source = "filter")
	FindInAppNotificationsQuery findsInAppNotificationRequestToFindsInAppNotificationQuery(
			FindInAppNotificationRequest request, InAppNotificationsFilterQuery filter);

	@Mapping(target = "tenantId", source = "tenantId")
	InAppNotificationsFilterQuery inAppNotificationsFilterRequestToInAppNotificationFilterQuery(
			InAppNotificationsFilterRequest filter, Long tenantId, String userId);

	@Mapping(target = "tenantId", source = "tenantId")
	DeleteInAppNotificationCommand deleteInAppNotificationRequestToDeleteInAppNotificationCommand(
			DeleteInAppNotificationRequest request, Long tenantId, String userId);

	@Mapping(target = "tenantId", source = "tenantId")
	ReadInAppNotificationCommand readInAppNotificationRequestToReadInAppNotificationCommand(
			ReadInAppNotificationRequest request, Long tenantId, String userId);

	@Mapping(target = "userId", qualifiedByName = "userIdToString")
	@Mapping(target = "tenantId", qualifiedByName = "tenantIdToString")
	InAppNotificationTrackingResponse inAppNotificationTrackingToResponse(
			InAppNotificationTracking inAppNotificationTracking);
}
