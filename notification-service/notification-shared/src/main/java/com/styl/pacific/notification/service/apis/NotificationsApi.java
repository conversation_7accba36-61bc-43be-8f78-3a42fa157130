/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.apis;

import com.styl.pacific.domain.permissions.PacificApiAuthorized;
import com.styl.pacific.notification.service.requests.DeleteInAppNotificationRequest;
import com.styl.pacific.notification.service.requests.FindInAppNotificationRequest;
import com.styl.pacific.notification.service.requests.ReadInAppNotificationRequest;
import com.styl.pacific.notification.service.response.InAppNotificationTrackingResponse;
import com.styl.pacific.notification.service.response.ListInAppNotificationResponse;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;

public interface NotificationsApi {

	@GetMapping(path = "/api/notification/notifications")
	@PacificApiAuthorized
	ListInAppNotificationResponse findInAppNotifications(
			@SpringQueryMap @ModelAttribute @Valid FindInAppNotificationRequest request);

	@DeleteMapping(path = "/api/notification/notifications")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	void deleteInAppNotifications(@RequestBody @Valid DeleteInAppNotificationRequest request);

	@PutMapping(path = "/api/notification/notifications/read")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	void readInAppNotifications(@RequestBody @Valid ReadInAppNotificationRequest request);

	@PutMapping(path = "/api/notification/notifications/tracking")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	void saveInAppNotificationTracking();

	@GetMapping(path = "/api/notification/notifications/tracking")
	@PacificApiAuthorized
	InAppNotificationTrackingResponse getInAppNotificationTracking();
}
