/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@Builder
@AllArgsConstructor
public class InAppNotificationResponse {

	@JsonSerialize(using = ToStringSerializer.class)
	private final Long id;

	@JsonSerialize(using = ToStringSerializer.class)
	private final Long tenantId;

	@JsonSerialize(using = ToStringSerializer.class)
	private final Long userId;

	private final Boolean read;

	private final String action;

	private final String content;

	private final Map<String, String> data;

	private final String source;

	private final String title;

	private final Long createdAt;
}
