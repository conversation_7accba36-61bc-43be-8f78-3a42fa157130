/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.domain.hander;

import com.styl.pacific.notification.service.domain.dto.CreateNotificationCommand;
import com.styl.pacific.notification.service.domain.entity.Notification;
import com.styl.pacific.notification.service.domain.event.InAppNotificationCreatedEvent;
import com.styl.pacific.notification.service.domain.hander.helper.CreateInAppNotificationHelper;
import com.styl.pacific.notification.service.domain.output.publisher.NotificationQueueInAppPublisher;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
@AllArgsConstructor
public class InAppHandler implements ChannelHandler {
	private final CreateInAppNotificationHelper createInAppNotificationHelper;
	private final NotificationQueueInAppPublisher notificationQueueInAppPublisher;

	@Override
	public Notification createNotification(CreateNotificationCommand command) {
		Notification notification = createInAppNotificationHelper.createInAppNotification(command);
		InAppNotificationCreatedEvent event = new InAppNotificationCreatedEvent(notification);
		notificationQueueInAppPublisher.publish(event);
		return event.getNotification();
	}
}
