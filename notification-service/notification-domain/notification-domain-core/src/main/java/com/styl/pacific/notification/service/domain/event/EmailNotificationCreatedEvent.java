/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.domain.event;

import com.styl.pacific.notification.service.constant.EmailNotificationKey;
import com.styl.pacific.notification.service.domain.entity.Notification;
import com.styl.pacific.utils.json.JsonFormatUtil;
import java.util.List;
import lombok.Getter;

@Getter
public class EmailNotificationCreatedEvent extends NotificationEvent {
	private final String subject;
	private final String body;
	private final String to;
	private final List<String> bcc;
	private final List<String> cc;

	public EmailNotificationCreatedEvent(Notification notification) {
		super(notification);
		subject = notification.getData()
				.get(EmailNotificationKey.SUBJECT);
		body = notification.getData()
				.get(EmailNotificationKey.BODY);
		to = notification.getData()
				.get(EmailNotificationKey.TO);
		bcc = JsonFormatUtil.convertToList(notification.getData()
				.get(EmailNotificationKey.BCC), String.class);
		cc = JsonFormatUtil.convertToList(notification.getData()
				.get(EmailNotificationKey.CC), String.class);
	}

	private EmailNotificationCreatedEvent(Builder builder) {
		super(builder.notification);
		subject = builder.subject;
		body = builder.body;
		to = builder.to;
		bcc = builder.bcc;
		cc = builder.cc;
	}

	public static final class Builder {
		private Notification notification;
		private String subject;
		private String body;
		private String to;
		private List<String> bcc;
		private List<String> cc;

		private Builder() {
		}

		public static Builder newBuilder() {
			return new Builder();
		}

		public Builder subject(String subject) {
			this.subject = subject;
			return this;
		}

		public Builder body(String body) {
			this.body = body;
			return this;
		}

		public Builder to(String to) {
			this.to = to;
			return this;
		}

		public Builder bcc(List<String> bcc) {
			this.bcc = bcc;
			return this;
		}

		public Builder cc(List<String> cc) {
			this.cc = cc;
			return this;
		}

		public Builder notification(Notification notification) {
			this.notification = notification;
			return this;
		}

		public EmailNotificationCreatedEvent build() {
			return new EmailNotificationCreatedEvent(this);
		}
	}
}
