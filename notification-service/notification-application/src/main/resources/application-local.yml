---
server:
  port: 9208
logging:
  level:
    com.styl.pacific: DEBUG
  pattern:
    level: "%5p [name:${spring.application.name:},traceId:%X{traceId:-},spanId:%X{spanId:-}]"
management:
  tracing:
    sampling:
      probability: 1.0

kafka-config:
  bootstrap-servers: "localhost:11091"
  schema-registry-url-key: schema.registry.url
  schema-registry-url: http://localhost:11081

pacific:
  clients:
    user-service:
      url: "localhost:9202"
