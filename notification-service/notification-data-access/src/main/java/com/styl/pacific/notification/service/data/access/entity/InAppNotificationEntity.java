/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.data.access.entity;

import com.styl.pacific.aws.dynamodb.utils.converter.EpochMillisFormatConverter;
import com.styl.pacific.common.validator.dynamodb.DynamoDBTable;
import java.time.Instant;
import java.util.Map;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import software.amazon.awssdk.enhanced.dynamodb.extensions.annotations.DynamoDbAutoGeneratedTimestampAttribute;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbConvertedBy;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey;

/**
 * <AUTHOR>
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@DynamoDbBean
@DynamoDBTable(name = InAppNotificationEntity.TABLE_NAME)
public class InAppNotificationEntity {
	public static final String TABLE_NAME = "inapp_notifications";

	@Getter(onMethod_ = @__({ @DynamoDbSortKey, @DynamoDbAttribute("id") }))
	private Long id;
	@Getter(onMethod_ = @__({ @DynamoDbAttribute("user_id") }))
	private Long userId;
	@Getter(onMethod_ = @__({ @DynamoDbAttribute("tenant_id") }))
	private Long tenantId;
	@Getter(onMethod_ = @__({ @DynamoDbPartitionKey, @DynamoDbAttribute("partition_key") }))
	private String partitionKey;
	@Getter(onMethod_ = @__({ @DynamoDbAttribute("read") }))
	private Boolean read;
	@Getter(onMethod_ = @__({ @DynamoDbAttribute("title") }))
	private String title;
	@Getter(onMethod_ = @__({ @DynamoDbAttribute("action") }))
	private String action;
	@Getter(onMethod_ = @__({ @DynamoDbAttribute("content") }))
	private String content;
	@Getter(onMethod_ = @__({ @DynamoDbAttribute("data") }))
	private Map<String, String> data;
	@Getter(onMethod_ = @__({ @DynamoDbAttribute("source") }))
	private String source;
	@Getter(onMethod_ = @__({ @DynamoDbAutoGeneratedTimestampAttribute,
			@DynamoDbConvertedBy(value = EpochMillisFormatConverter.class), @DynamoDbAttribute("created_at") }))
	private Instant createdAt;
	@Getter(onMethod_ = @__({ @DynamoDbAutoGeneratedTimestampAttribute,
			@DynamoDbConvertedBy(value = EpochMillisFormatConverter.class), @DynamoDbAttribute("updated_at") }))
	private Instant updatedAt;
	@Getter(onMethod_ = @__({ @DynamoDbConvertedBy(value = EpochMillisFormatConverter.class),
			@DynamoDbAttribute("time_to_live") }))
	private Instant timeToLive;

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (!(o instanceof InAppNotificationEntity entity))
			return false;
		return Objects.equals(id, entity.id) && Objects.equals(userId, entity.userId) && Objects.equals(tenantId,
				entity.tenantId) && Objects.equals(partitionKey, entity.partitionKey) && Objects.equals(read,
						entity.read) && Objects.equals(title, entity.title) && Objects.equals(action, entity.action)
				&& Objects.equals(content, entity.content) && Objects.equals(source, entity.source) && Objects.equals(
						createdAt, entity.createdAt) && Objects.equals(updatedAt, entity.updatedAt) && Objects.equals(
								timeToLive, entity.timeToLive) && Objects.equals(data, entity.data);
	}

	@Override
	public int hashCode() {
		return Objects.hash(id, userId, tenantId, partitionKey, read, title, action, content, data, source, createdAt,
				updatedAt, timeToLive);
	}
}
