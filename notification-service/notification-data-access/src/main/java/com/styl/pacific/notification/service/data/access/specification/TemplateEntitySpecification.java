/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.data.access.specification;

import com.styl.pacific.data.access.jpa.specification.BaseSpecification;
import com.styl.pacific.notification.service.data.access.entity.TemplateEntity;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.io.Serial;
import java.util.ArrayList;
import java.util.List;
import lombok.Builder;

/**
 * <AUTHOR>
 */
@Builder
public class TemplateEntitySpecification extends BaseSpecification<TemplateEntity> {
	@Serial
	private static final long serialVersionUID = 1L;

	private Long tenantId;

	private String action;

	private String channel;

	@Override
	public Predicate toPredicate(Root<TemplateEntity> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
		List<Predicate> predicates = new ArrayList<>();

		if (isNotNull(tenantId)) {
			predicates.add(equals(criteriaBuilder, root.get("tenantId"), tenantId));
		}

		if (isNotBlank(action)) {
			predicates.add(like(criteriaBuilder, root.get("action"), action));
		}

		if (isNotBlank(channel)) {
			predicates.add(like(criteriaBuilder, root.get("channel"), channel));
		}

		return and(criteriaBuilder, predicates);
	}
}
