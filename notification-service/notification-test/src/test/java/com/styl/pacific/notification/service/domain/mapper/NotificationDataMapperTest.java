/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.domain.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.styl.pacific.domain.valueobject.InAppNotificationId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.notification.service.domain.dto.CreateEmailNotificationCommand;
import com.styl.pacific.notification.service.domain.dto.CreateInAppNotificationCommand;
import com.styl.pacific.notification.service.domain.dto.CreateNotificationCommand;
import com.styl.pacific.notification.service.domain.dto.HandleNotificationCommand;
import com.styl.pacific.notification.service.domain.entity.EmailNotification;
import com.styl.pacific.notification.service.domain.entity.InAppNotification;
import com.styl.pacific.notification.service.domain.entity.Notification;
import com.styl.pacific.notification.service.domain.event.QueueNotificationCreatedEvent;
import com.styl.pacific.notification.service.enums.NotificationChannel;
import com.styl.pacific.notification.service.response.InAppNotificationResponse;
import java.time.Instant;
import java.util.Map;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
class NotificationDataMapperTest {

	private NotificationDataMapper mapper;

	@BeforeEach
	public void setUp() {
		mapper = NotificationDataMapper.INSTANCE;
	}

	@Test
	void whenMapperFromHandleNotificationCommand_shouldReturnQueueNotificationCreatedEvent() {
		// Arrange
		HandleNotificationCommand handleNotificationCommand = HandleNotificationCommand.builder()
				.id(UUID.randomUUID())
				.source("store-service")
				.action("ACTION_1")
				.userId(1L)
				.tenantId(1L)
				.data(Map.of("k1", "v1"))
				.build();
		long createdAt = Instant.now()
				.toEpochMilli();
		// Act
		QueueNotificationCreatedEvent event = mapper.notificationHandleToNotificationCreatedEvent(
				handleNotificationCommand, NotificationChannel.IN_APP, UUID.randomUUID(), createdAt);
		// Assert
		assertEquals(handleNotificationCommand.getAction(), event.getAction());
		assertEquals(handleNotificationCommand.getSource(), event.getSource());
		assertEquals(handleNotificationCommand.getTenantId(), event.getTenantId());
		assertEquals(handleNotificationCommand.getUserId(), event.getUserId());
		assertEquals(handleNotificationCommand.getData(), event.getData());
		assertEquals(NotificationChannel.IN_APP, event.getChannel());
		assertEquals(createdAt, event.getCreatedAt());
	}

	@Test
	void whenMapperFromCreateNotificationCommand_shouldReturnNotification() {
		// Arrange
		CreateNotificationCommand createNotificationCommand = CreateNotificationCommand.builder()
				.id(UUID.randomUUID())
				.source("store-service")
				.action("ACTION_1")
				.userId(1L)
				.tenantId(1L)
				.data(Map.of("k1", "v1"))
				.channel(NotificationChannel.IN_APP)
				.build();
		// Act
		Notification notification = mapper.createNotificationCommandToNotification(createNotificationCommand);
		// Assert
		assertEquals(createNotificationCommand.getAction(), notification.getAction());
		assertEquals(createNotificationCommand.getSource(), notification.getSource());
		assertEquals(createNotificationCommand.getTenantId(), notification.getTenantId());
		assertEquals(createNotificationCommand.getUserId(), notification.getUserId());
		assertEquals(createNotificationCommand.getData(), notification.getData());
		assertEquals(createNotificationCommand.getChannel(), notification.getChannel());
	}

	@Test
	void whenMapperFromCreateInAppNotificationCommand_shouldReturnInAppNotification() {
		// Arrange
		CreateInAppNotificationCommand createNotificationCommand = CreateInAppNotificationCommand.Builder.newBuilder()
				.source("store-service")
				.action("ACTION_1")
				.content("content")
				.userId(1L)
				.tenantId(1L)
				.title("title12")
				.build();
		// Act
		InAppNotification notification = mapper.inAppNotificationToNewInAppNotification(createNotificationCommand,
				new InAppNotificationId(123L), true);
		// Assert
		assertEquals(createNotificationCommand.getAction(), notification.getAction());
		assertEquals(createNotificationCommand.getSource(), notification.getSource());
		assertEquals(createNotificationCommand.getTenantId(), notification.getTenantId()
				.getValue());
		assertEquals(createNotificationCommand.getUserId(), notification.getUserId()
				.getValue());
		assertEquals(createNotificationCommand.getContent(), notification.getContent());
		assertEquals(createNotificationCommand.getTitle(), notification.getTitle());
	}

	@Test
	void whenMapperFromCreateEmailNotificationCommand_shouldReturnEmailNotification() {
		// Arrange
		CreateEmailNotificationCommand createEmailNotificationCommand = CreateEmailNotificationCommand.Builder
				.newBuilder()
				.subject("subject_1")
				.body("body1")
				.to("<EMAIL>")
				.build();
		UUID id = UUID.randomUUID();
		// Act
		EmailNotification notification = mapper.createEmailNotificationToEmailNotification(
				createEmailNotificationCommand, id);
		// Assert
		assertEquals(id, notification.getId());
		assertEquals(createEmailNotificationCommand.getTo(), notification.getTo());
		assertEquals(createEmailNotificationCommand.getSubject(), notification.getSubject());
		assertEquals(createEmailNotificationCommand.getBody(), notification.getBody());
	}

	@Test
	void whenMapperFromInAppNotification_shouldReturnInAppNotificationResponse() {
		// Arrange
		InAppNotification inAppNotification = InAppNotification.Builder.newBuilder()
				.id(new InAppNotificationId(1234L))
				.action("ACTION_1")
				.content("content1")
				.read(false)
				.title("title")
				.userId(new UserId(123L))
				.tenantId(new TenantId(123L))
				.source("store-service")
				.build();
		// Act
		InAppNotificationResponse response = mapper.inAppNotificationToInAppNotificationResponse(inAppNotification);
		// Assert
		assertEquals(inAppNotification.getAction(), response.getAction());
		assertEquals(inAppNotification.getRead(), response.getRead());
		assertEquals(inAppNotification.getSource(), response.getSource());
		assertEquals(inAppNotification.getTitle(), response.getTitle());
		assertEquals(inAppNotification.getContent(), response.getContent());
	}

	@Test
	void whenMapperFromInAppNotification_shouldReturnNewInAppNotification() {
		// Arrange
		InAppNotification inAppNotification = InAppNotification.Builder.newBuilder()
				.id(new InAppNotificationId(1234L))
				.action("ACTION_1")
				.content("content1")
				.read(false)
				.title("title")
				.userId(new UserId(123L))
				.tenantId(new TenantId(123L))
				.source("store-service")
				.build();
		Instant updatedDate = Instant.now();
		// Act
		InAppNotification newInAppNotification = mapper.inAppNotificationToNewInAppNotification(inAppNotification, true,
				updatedDate);
		// Assert
		assertEquals(inAppNotification.getAction(), newInAppNotification.getAction());
		assertEquals(true, newInAppNotification.getRead());
		assertEquals(inAppNotification.getSource(), newInAppNotification.getSource());
		assertEquals(inAppNotification.getTitle(), newInAppNotification.getTitle());
		assertEquals(inAppNotification.getContent(), newInAppNotification.getContent());
		assertEquals(updatedDate, newInAppNotification.getUpdatedAt());
	}
}
