/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.data.access.adapter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.styl.pacific.domain.valueobject.InAppNotificationId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.notification.service.data.access.entity.InAppNotificationEntity;
import com.styl.pacific.notification.service.data.access.entity.InAppNotificationTrackingEntity;
import com.styl.pacific.notification.service.data.access.mapper.InAppNotificationTrackingDataAccessMapper;
import com.styl.pacific.notification.service.data.access.repository.InAppNotificationDynamoDBRepository;
import com.styl.pacific.notification.service.data.access.repository.InAppNotificationTrackingJpaRepository;
import com.styl.pacific.notification.service.data.access.specification.InAppNotificationTrackingEntitySpecification;
import com.styl.pacific.notification.service.domain.entity.InAppNotification;
import com.styl.pacific.notification.service.domain.entity.InAppNotificationTracking;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.enhanced.dynamodb.Key;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class InAppNotificationRepositoryImplTest {

	@Mock
	private InAppNotificationDynamoDBRepository inAppNotificationDynamoDBRepository;

	@Mock
	private InAppNotificationTrackingJpaRepository inAppNotificationTrackingJpaRepository;

	@InjectMocks
	private InAppNotificationRepositoryImpl inAppNotificationRepository;

	private static final Long TENANT_ID = 123L;
	private static final Long USER_ID = 123L;

	@Test
	void testSaveInAppNotificationShouldOk() {
		// Arrange
		InAppNotification inAppNotification = InAppNotification.Builder.newBuilder()
				.id(new InAppNotificationId(1234L))
				.action("ACTION_1")
				.content("content1")
				.read(false)
				.title("title")
				.userId(new UserId(USER_ID))
				.tenantId(new TenantId(TENANT_ID))
				.source("store-service")
				.build();
		// Act
		inAppNotificationRepository.save(inAppNotification);
		// Assert
		verify(inAppNotificationDynamoDBRepository, times(1)).save(any());
	}

	@Test
	void testDeleteInAppNotificationShouldOk() {
		// Arrange
		InAppNotification inAppNotification = InAppNotification.Builder.newBuilder()
				.id(new InAppNotificationId(1234L))
				.action("ACTION_1")
				.content("content1")
				.read(false)
				.title("title")
				.userId(new UserId(USER_ID))
				.tenantId(new TenantId(TENANT_ID))
				.source("store-service")
				.build();
		// Act
		inAppNotificationRepository.deleteInAppNotification(inAppNotification);
		// Assert
		verify(inAppNotificationDynamoDBRepository, times(1)).deleteById(any());
	}

	@Test
	void findInAppNotificationShouldOk() {
		// Arrange
		InAppNotificationEntity entity = InAppNotificationEntity.builder()
				.id(1234L)
				.action("ACTION_1")
				.partitionKey("1234-1234")
				.content("content1")
				.read(false)
				.title("title")
				.userId(USER_ID)
				.tenantId(TENANT_ID)
				.source("store-service")
				.build();
		when(inAppNotificationDynamoDBRepository.findById(any(Key.class))).thenReturn(Optional.of(entity));
		// Act
		Optional<InAppNotification> optional = inAppNotificationRepository.findInAppNotificationBy(TENANT_ID, USER_ID,
				1234L);
		// Assert
		assertTrue(optional.isPresent());
		assertEquals(entity.getTenantId(), optional.get()
				.getTenantId()
				.getValue());
		assertEquals(entity.getUserId(), optional.get()
				.getUserId()
				.getValue());
		assertEquals(entity.getSource(), optional.get()
				.getSource());
		assertEquals(entity.getTitle(), optional.get()
				.getTitle());
		assertEquals(entity.getAction(), optional.get()
				.getAction());
		assertEquals(entity.getContent(), optional.get()
				.getContent());
		assertEquals(entity.getRead(), optional.get()
				.getRead());
	}

	@Test
	void testSaveInAppNotificationTracking() {
		// Arrange
		InAppNotificationTracking inAppNotificationTracking = InAppNotificationTracking.builder()
				.tenantId(new TenantId(TENANT_ID))
				.userId(new UserId(USER_ID))
				.hasNewNotification(true)
				.build();
		// Act
		inAppNotificationRepository.save(inAppNotificationTracking);
		// Assert
		verify(inAppNotificationTrackingJpaRepository, times(1)).save(any());
	}

	@Test
	void testFindInAppNotificationTracking() {
		// Arrange
		InAppNotificationTracking inAppNotificationTracking = InAppNotificationTracking.builder()
				.tenantId(new TenantId(TENANT_ID))
				.userId(new UserId(USER_ID))
				.hasNewNotification(true)
				.build();
		InAppNotificationTrackingEntity entity = InAppNotificationTrackingDataAccessMapper.INSTANCE
				.inAppNotificationTrackingToEntity(inAppNotificationTracking);
		when(inAppNotificationTrackingJpaRepository.findOne(any(InAppNotificationTrackingEntitySpecification.class)))
				.thenReturn(Optional.of(entity));
		// Act
		Optional<InAppNotificationTracking> optional = inAppNotificationRepository.findInAppNotificationTrackingBy(
				USER_ID);
		// Assert
		assertTrue(optional.isPresent());
		assertEquals(entity.getTenantId(), optional.get()
				.getTenantId()
				.getValue());
		assertEquals(entity.getUserId(), optional.get()
				.getUserId()
				.getValue());
		assertEquals(entity.getHasNewNotification(), optional.get()
				.getHasNewNotification());
	}
}
