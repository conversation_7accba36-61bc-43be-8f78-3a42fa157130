CREATE
    COLLATION IF NOT EXISTS email_case_insensitive_unicode(
        provider = icu,
        locale = 'und-u-ks-level2',
        DETERMINISTIC = TRUE
    );

CREATE
    TABLE
        IF NOT EXISTS payment_transactions(
            id BIGSERIAL NOT NULL CONSTRAINT payment_transactions_pk PRIMARY KEY,
            tenant_id BIGINT NOT NULL,
            payment_method_id BIGINT NOT NULL,
            payment_processor_id VARCHAR(36) NOT NULL,
            payment_session_id VARCHAR(36) NOT NULL,
            payment_transaction_type VARCHAR(36) NOT NULL,
            payment_reference VARCHAR(128),
            customer_id VARCHAR(64),
            customer_email VARCHAR(255) COLLATE email_case_insensitive_unicode,
            customer_name VARCHAR(255),
            currency_code VARCHAR(3) NOT NULL,
            fee BIGINT,
            amount BIGINT NOT NULL,
            net_amount BIGINT NOT NULL,
            system_source VARCHAR(128),
            merchant_name VARCHAR(128),
            description VARCHAR(1000),
            applied_surcharge_rate NUMERIC(
                5,
                2
            ),
            applied_fixed_surcharge BIGINT,
            processor_params JSONB,
            session_data JSONB,
            session_version BIGINT NOT NULL,
            settlement_data JSONB,
            extra_settlement_data JSONB,
            is_async BOOLEAN NOT NULL,
            initiated_at TIMESTAMP(6) NOT NULL,
            paid_at TIMESTAMP(6) NOT NULL,
            created_by BIGINT,
            created_at TIMESTAMP(6) DEFAULT NOW()
        );

CREATE
    UNIQUE INDEX IF NOT EXISTS payment_transactions_payment_session_id_uidx ON
    payment_transactions(payment_session_id);

CREATE
    INDEX IF NOT EXISTS payment_transactions_tenant_id_customer_id_idx ON
    payment_transactions(
        tenant_id,
        customer_id
    );
