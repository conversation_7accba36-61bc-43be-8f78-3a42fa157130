/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.data.access.dynamo.sessions.repository;

import com.styl.pacific.domain.valueobject.PaymentSessionId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.payment.service.core.features.sessions.entities.PaymentSession;
import com.styl.pacific.payment.service.core.features.sessions.repository.PaymentSessionRepository;
import com.styl.pacific.payment.service.data.access.dynamo.sessions.idgenerator.PaymentSessionKeyGenerator;
import com.styl.pacific.payment.service.data.access.dynamo.sessions.mapper.PaymentSessionEntityMapper;
import com.styl.pacific.payment.shared.exceptions.PaymentSessionOptimisticException;
import jakarta.validation.constraints.NotNull;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.services.dynamodb.model.ConditionalCheckFailedException;

@Component
@RequiredArgsConstructor
@Slf4j
public class PaymentSessionRepositoryImpl implements PaymentSessionRepository {

	private final DynamoPaymentSessionRepository paymentSessionRepository;

	@Override
	public PaymentSession save(PaymentSession paymentSession) {
		final var partitionKey = PaymentSessionKeyGenerator.getPartitionKey(paymentSession.getTenantId(), paymentSession
				.getId());

		try {
			final var updatingEntity = paymentSessionRepository.findById(Key.builder()
					.partitionValue(partitionKey)
					.build())
					.map(entity -> {
						PaymentSessionEntityMapper.INSTANCE.toUpdateEntity(entity, paymentSession);
						return entity;
					})
					.orElseGet(() -> PaymentSessionEntityMapper.INSTANCE.toNewEntity(paymentSession, partitionKey));
			return PaymentSessionEntityMapper.INSTANCE.toModel(paymentSessionRepository.update(updatingEntity));
		} catch (ConditionalCheckFailedException exception) {
			throw new PaymentSessionOptimisticException();
		}
	}

	@Override
	public Optional<PaymentSession> getSessionBySessionId(@NonNull TenantId tenantId,
			@NotNull PaymentSessionId paymentSessionId) {
		return paymentSessionRepository.findById(Key.builder()
				.partitionValue(PaymentSessionKeyGenerator.getPartitionKey(tenantId, paymentSessionId))
				.build())
				.map(PaymentSessionEntityMapper.INSTANCE::toModel);

	}
}
