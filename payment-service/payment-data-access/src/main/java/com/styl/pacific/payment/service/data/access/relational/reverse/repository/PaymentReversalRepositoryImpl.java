/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.data.access.relational.reverse.repository;

import com.styl.pacific.data.access.jpa.querydsl.WhereBuilder;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.IdempotencyKey;
import com.styl.pacific.domain.valueobject.PaymentReversalId;
import com.styl.pacific.domain.valueobject.PaymentTransactionId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.payment.service.core.features.reversal.PaymentReversalRepository;
import com.styl.pacific.payment.service.core.features.reversal.entities.PaymentReversal;
import com.styl.pacific.payment.service.core.features.reversal.request.FilterPaymentReverseQuery;
import com.styl.pacific.payment.service.core.features.reversal.request.PaymentReversePaginationQuery;
import com.styl.pacific.payment.service.data.access.relational.reverse.entities.QPaymentReversalEntity;
import com.styl.pacific.payment.service.data.access.relational.reverse.mapper.PaymentReversalEntityMapper;
import com.styl.pacific.payment.service.data.access.relational.transactions.repository.JpaPaymentTransactionRepository;
import com.styl.pacific.payment.shared.exceptions.PaymentTransactionNotFoundException;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@RequiredArgsConstructor
public class PaymentReversalRepositoryImpl implements PaymentReversalRepository {

	private final JpaPaymentTransactionRepository jpaPaymentTransactionRepository;
	private final JpaPaymentReverseRepository jpaPaymentReverseRepository;

	@Override
	public PaymentReversal saveReversal(PaymentReversal paymentReversal) {
		final var transactionEntity = jpaPaymentTransactionRepository.getOneByTenantIdAndId(paymentReversal
				.getTenantId()
				.getValue(), paymentReversal.getTransaction()
						.getId()
						.getValue())
				.orElseThrow(PaymentTransactionNotFoundException::new);
		return PaymentReversalEntityMapper.INSTANCE.toModel(jpaPaymentReverseRepository.save(
				PaymentReversalEntityMapper.INSTANCE.toEntity(paymentReversal, transactionEntity)));
	}

	@Override
	@Transactional(readOnly = true)
	public Optional<PaymentReversal> findReversalByTenantIdAndTransactionIdAndIdempotencyKey(TenantId tenantId,
			PaymentTransactionId transactionId, IdempotencyKey idempotencyKey) {
		return jpaPaymentReverseRepository.getOneByTenantIdAndPaymentTransactionEntityIdAndIdempotencyKey(tenantId
				.getValue(), transactionId.getValue(), idempotencyKey.getValue())
				.map(PaymentReversalEntityMapper.INSTANCE::toModel);

	}

	@Override
	public Paging<PaymentReversal> queryPaymentReverse(PaymentReversePaginationQuery query) {
		final var filter = Optional.ofNullable(query.getFilter())
				.orElseGet(() -> FilterPaymentReverseQuery.builder()
						.build());
		final var pageable = PageRequest.of(query.getPage(), query.getSize(), Sort.Direction.valueOf(query
				.getSortDirection()), query.getSortFields()
						.toArray(String[]::new));

		final var predicate = WhereBuilder.build()
				.and(QPaymentReversalEntity.paymentReversalEntity.tenantId.eq(filter.getByTenantId()
						.getValue()))
				.applyIf(filter.getByPaymentTransactionId() != null, where -> where.and(
						QPaymentReversalEntity.paymentReversalEntity.paymentTransactionEntity.id.eq(filter
								.getByPaymentTransactionId()
								.getValue())));

		final var pageResult = jpaPaymentReverseRepository.findAllWithBase(predicate, pageable);

		return new Paging<>(pageResult.getContent()
				.stream()
				.map(PaymentReversalEntityMapper.INSTANCE::toModel)
				.toList(), pageResult.getTotalElements(), pageResult.getTotalPages(), pageResult.getPageable()
						.getPageNumber(), pageResult.getSort()
								.stream()
								.map(Sort.Order::toString)
								.toList());

	}

	@Override
	public Optional<PaymentReversal> findReversalByTenantIdAndReversalId(TenantId tenantId,
			PaymentReversalId paymentReversalId) {
		return jpaPaymentReverseRepository.getOneByTenantIdAndId(tenantId.getValue(), paymentReversalId.getValue())
				.map(PaymentReversalEntityMapper.INSTANCE::toModel);

	}
}
