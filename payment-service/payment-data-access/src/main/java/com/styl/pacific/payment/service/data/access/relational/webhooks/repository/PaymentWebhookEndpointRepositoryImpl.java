/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.data.access.relational.webhooks.repository;

import com.styl.pacific.data.access.jpa.querydsl.WhereBuilder;
import com.styl.pacific.domain.valueobject.PaymentMethodId;
import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.domain.valueobject.PaymentWebhookEndpointId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.payment.service.core.features.webhooks.PaymentWebhookEndpointRepository;
import com.styl.pacific.payment.service.core.features.webhooks.entities.PaymentWebhookEndpoint;
import com.styl.pacific.payment.service.data.access.relational.webhooks.entities.QPaymentWebhookEndpointEntity;
import com.styl.pacific.payment.service.data.access.relational.webhooks.mapper.PaymentWebhookEndpointEntityMapper;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
@RequiredArgsConstructor
public class PaymentWebhookEndpointRepositoryImpl implements PaymentWebhookEndpointRepository {

	private final JpaPaymentWebhookEndpointRepository repository;

	@Override
	public List<PaymentWebhookEndpoint> getWebhookEndpoints(TenantId tenantId, PaymentMethodId paymentMethodId,
			PaymentProcessorId processorId, Boolean isActive) {
		return repository.findAllWithBase(WhereBuilder.build()
				.and(QPaymentWebhookEndpointEntity.paymentWebhookEndpointEntity.tenantId.eq(tenantId.getValue()))
				.and(QPaymentWebhookEndpointEntity.paymentWebhookEndpointEntity.paymentMethodId.eq(paymentMethodId
						.getValue()))
				.and(QPaymentWebhookEndpointEntity.paymentWebhookEndpointEntity.paymentProcessorId.eq(processorId))
				.applyIf(isActive != null, where -> where.and(
						QPaymentWebhookEndpointEntity.paymentWebhookEndpointEntity.isActive.eq(isActive))))
				.stream()
				.map(PaymentWebhookEndpointEntityMapper.INSTANCE::toModel)
				.toList();

	}

	@Override
	public List<PaymentWebhookEndpoint> saveAll(List<PaymentWebhookEndpoint> webhookEndpoints) {
		return repository.saveAll(webhookEndpoints.stream()
				.map(PaymentWebhookEndpointEntityMapper.INSTANCE::toEntity)
				.toList())
				.stream()
				.map(PaymentWebhookEndpointEntityMapper.INSTANCE::toModel)
				.toList();
	}

	@Override
	public Optional<PaymentWebhookEndpoint> findPaymentWebhookEndpoint(PaymentProcessorId processorId,
			PaymentWebhookEndpointId endpointId) {
		return repository.findOneWithBase(QPaymentWebhookEndpointEntity.paymentWebhookEndpointEntity.id.eq(endpointId
				.getValue())
				.and(QPaymentWebhookEndpointEntity.paymentWebhookEndpointEntity.paymentProcessorId.eq(processorId)))
				.map(PaymentWebhookEndpointEntityMapper.INSTANCE::toModel);

	}

	@Override
	public List<PaymentWebhookEndpoint> findPaymentWebhookEndpoints(TenantId tenantId,
			PaymentMethodId paymentMethodId) {
		return repository.findAllWithBase(QPaymentWebhookEndpointEntity.paymentWebhookEndpointEntity.tenantId.eq(
				tenantId.getValue())
				.and(QPaymentWebhookEndpointEntity.paymentWebhookEndpointEntity.paymentMethodId.eq(paymentMethodId
						.getValue())
						.and(QPaymentWebhookEndpointEntity.paymentWebhookEndpointEntity.isActive.eq(Boolean.TRUE))))
				.stream()
				.map(PaymentWebhookEndpointEntityMapper.INSTANCE::toModel)
				.toList();
	}

	@Override
	public List<PaymentWebhookEndpoint> findPaymentWebhookEndpoints(TenantId tenantId, PaymentProcessorId processorId,
			Collection<String> eventTypes) {
		if (tenantId == null || processorId == null || CollectionUtils.isEmpty(eventTypes)) {
			return List.of();
		}

		return repository.findAllWithBase(QPaymentWebhookEndpointEntity.paymentWebhookEndpointEntity.tenantId.eq(
				tenantId.getValue())
				.and(QPaymentWebhookEndpointEntity.paymentWebhookEndpointEntity.paymentProcessorId.eq(processorId)
						.and(QPaymentWebhookEndpointEntity.paymentWebhookEndpointEntity.eventType.in(eventTypes))))
				.stream()
				.map(PaymentWebhookEndpointEntityMapper.INSTANCE::toModel)
				.toList();
	}

	@Override
	public PaymentWebhookEndpoint save(PaymentWebhookEndpoint endpoint) {
		return PaymentWebhookEndpointEntityMapper.INSTANCE.toModel(repository.save(
				PaymentWebhookEndpointEntityMapper.INSTANCE.toEntity(endpoint)));

	}

	@Override
	public void deleteById(PaymentWebhookEndpointId webhookEndpointId) {
		if (webhookEndpointId == null || webhookEndpointId.getValue() == null) {
			return;
		}
		repository.deleteById(webhookEndpointId.getValue());

	}
}
