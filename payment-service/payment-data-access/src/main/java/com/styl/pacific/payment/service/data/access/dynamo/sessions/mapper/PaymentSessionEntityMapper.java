/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.data.access.dynamo.sessions.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.payment.service.core.features.sessions.entities.PaymentSession;
import com.styl.pacific.payment.service.data.access.dynamo.sessions.entities.PaymentSessionEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface PaymentSessionEntityMapper {
	PaymentSessionEntityMapper INSTANCE = Mappers.getMapper(PaymentSessionEntityMapper.class);

	@Mapping(target = "paymentSessionId", source = "source.id", qualifiedByName = "paymentSessionIdToString")
	@Mapping(target = "paymentMethodId", source = "source.paymentMethodId", qualifiedByName = "paymentMethodIdToLong")
	@Mapping(target = "settledTransactionId", source = "source.settledTransactionId", qualifiedByName = "paymentTransactionIdToLong")
	@Mapping(target = "tenantId", source = "source.tenantId", qualifiedByName = "tenantIdToLong")
	@Mapping(target = "lockingVersion", ignore = true)
	@Mapping(target = "createdBy", ignore = true)
	@Mapping(target = "updatedBy", ignore = true)
	PaymentSessionEntity toNewEntity(PaymentSession source, String partitionKey);

	@Mapping(target = "id", source = "paymentSessionId", qualifiedByName = "stringToPaymentSessionId")
	@Mapping(target = "paymentMethodId", source = "paymentMethodId", qualifiedByName = "longToPaymentMethodId")
	@Mapping(target = "tenantId", source = "tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "settledTransactionId", source = "settledTransactionId", qualifiedByName = "longToPaymentTransactionId")
	PaymentSession toModel(PaymentSessionEntity source);

	@Mapping(target = "settledTransactionId", source = "settledTransactionId", qualifiedByName = "paymentTransactionIdToLong")
	@Mapping(target = "paymentSessionId", ignore = true)
	@Mapping(target = "paymentMethodId", ignore = true)
	@Mapping(target = "tenantId", ignore = true)
	@Mapping(target = "partitionKey", ignore = true)
	@Mapping(target = "createdBy", ignore = true)
	@Mapping(target = "updatedBy", ignore = true)
	void toUpdateEntity(@MappingTarget PaymentSessionEntity entity, PaymentSession paymentSession);
}
