/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.data.access.relational.transactions.repository;

import com.styl.pacific.data.access.jpa.querydsl.WhereBuilder;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.BaseId;
import com.styl.pacific.domain.valueobject.IdempotencyKey;
import com.styl.pacific.domain.valueobject.PaymentTransactionId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.payment.service.core.features.transactions.PaymentTransactionRepository;
import com.styl.pacific.payment.service.core.features.transactions.entities.PaymentTransaction;
import com.styl.pacific.payment.service.core.features.transactions.request.FilterPaymentTransactionQuery;
import com.styl.pacific.payment.service.core.features.transactions.request.PaymentTransactionPaginationQuery;
import com.styl.pacific.payment.service.data.access.relational.transactions.entities.QPaymentTransactionEntity;
import com.styl.pacific.payment.service.data.access.relational.transactions.mapper.PaymentTransactionEntityMapper;
import com.styl.pacific.payment.shared.exceptions.PaymentTransactionRequiredException;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
@RequiredArgsConstructor
public class PaymentTransactionRepositoryImpl implements PaymentTransactionRepository {

	private final JpaPaymentTransactionRepository repository;

	@Override
	public PaymentTransaction saveTransaction(PaymentTransaction transaction) {
		return PaymentTransactionEntityMapper.INSTANCE.toModel(repository.save(PaymentTransactionEntityMapper.INSTANCE
				.toEntity(transaction)));
	}

	@Override
	public Optional<PaymentTransaction> findTransactionByTenantIdAndId(TenantId tenantId,
			PaymentTransactionId paymentTransactionId) {
		return repository.findOneWithBase(QPaymentTransactionEntity.paymentTransactionEntity.tenantId.eq(tenantId
				.getValue())
				.and(QPaymentTransactionEntity.paymentTransactionEntity.id.eq(paymentTransactionId.getValue())))
				.map(PaymentTransactionEntityMapper.INSTANCE::toModel);

	}

	@Override
	public Paging<PaymentTransaction> queryTransactions(PaymentTransactionPaginationQuery query) {
		final var filter = Optional.ofNullable(query.getFilter())
				.orElseGet(() -> FilterPaymentTransactionQuery.builder()
						.build());

		final var pageable = PageRequest.of(query.getPage(), query.getSize(), Sort.Direction.valueOf(query
				.getSortDirection()), query.getSortFields()
						.toArray(String[]::new));

		final var predicate = WhereBuilder.build()
				.applyIf(filter.getByTenantId() != null && filter.getByTenantId()
						.getValue() != null, where -> where.and(
								QPaymentTransactionEntity.paymentTransactionEntity.tenantId.eq(filter.getByTenantId()
										.getValue())))
				.applyIf(!CollectionUtils.isEmpty(filter.getByTransactionIds()), where -> where.and(
						QPaymentTransactionEntity.paymentTransactionEntity.id.in(filter.getByTransactionIds()
								.stream()
								.map(BaseId::getValue)
								.collect(Collectors.toSet()))))
				.applyIf(filter.getByStatus() != null, where -> where.and(
						QPaymentTransactionEntity.paymentTransactionEntity.transactionStatus.eq(filter.getByStatus())))
				.applyIf(StringUtils.isNotBlank(filter.getByCustomerId()), where -> where.and(
						QPaymentTransactionEntity.paymentTransactionEntity.customerId.eq(filter.getByCustomerId())))
				.applyIf(StringUtils.isNotBlank(filter.getByCustomerEmail()), where -> where.and(
						QPaymentTransactionEntity.paymentTransactionEntity.customerEmail.eq(filter
								.getByCustomerEmail())))
				.applyIf(filter.getByTransactionType() != null, where -> where.and(
						QPaymentTransactionEntity.paymentTransactionEntity.transactionType.eq(filter
								.getByTransactionType())))
				.applyIf(filter.getByCreatedRange() != null && filter.getByCreatedRange()
						.from() != null, where -> where.and(QPaymentTransactionEntity.paymentTransactionEntity.createdAt
								.after(filter.getByCreatedRange()
										.from())))
				.applyIf(filter.getByCreatedRange() != null && filter.getByCreatedRange()
						.to() != null, where -> where.and(QPaymentTransactionEntity.paymentTransactionEntity.createdAt
								.before(filter.getByCreatedRange()
										.to())))
				.applyIf(!CollectionUtils.isEmpty(filter.getBySessionIds()), where -> where.and(
						QPaymentTransactionEntity.paymentTransactionEntity.paymentSessionId.in(filter.getBySessionIds()
								.stream()
								.map(BaseId::getValue)
								.collect(Collectors.toSet()))))
				.applyIf(filter.getByPaymentMethodId() != null, where -> where.and(
						QPaymentTransactionEntity.paymentTransactionEntity.paymentMethodId.eq(filter
								.getByPaymentMethodId()
								.getValue())))
				.applyIf(StringUtils.isNotBlank(filter.getByDeviceId()), where -> where.and(
						QPaymentTransactionEntity.paymentTransactionEntity.deviceId.eq(filter.getByDeviceId())))
				.applyIf(StringUtils.isNotBlank(filter.getByDeviceTransactionNumber()), where -> where.and(
						QPaymentTransactionEntity.paymentTransactionEntity.deviceTransactionNumber.eq(filter
								.getByDeviceTransactionNumber())))
				.applyIf(StringUtils.isNotBlank(filter.getByTransactionNumber()), where -> where.and(
						QPaymentTransactionEntity.paymentTransactionEntity.transactionNumber.eq(filter
								.getByTransactionNumber())))
				.applyIf(filter.getIsOffline() != null, where -> where.and(
						QPaymentTransactionEntity.paymentTransactionEntity.isOffline.eq(filter.getIsOffline())))
				.applyIf(!CollectionUtils.isEmpty(filter.getByCustomerIds()), where -> where.and(
						QPaymentTransactionEntity.paymentTransactionEntity.customerId.in(filter.getByCustomerIds())));

		final var pageResult = repository.findAllWithBase(predicate, pageable);

		return new Paging<>(pageResult.getContent()
				.stream()
				.map(PaymentTransactionEntityMapper.INSTANCE::toModel)
				.toList(), pageResult.getTotalElements(), pageResult.getTotalPages(), pageResult.getPageable()
						.getPageNumber(), pageResult.getSort()
								.stream()
								.map(Sort.Order::toString)
								.toList());

	}

	@Override
	public Optional<PaymentTransaction> findTransactionByTenantIdAndOfflineIdempotencyKey(TenantId tenantId,
			IdempotencyKey offlineIdempotencyKey) {
		if (tenantId == null || offlineIdempotencyKey == null) {
			throw new PaymentTransactionRequiredException("tenantId and offlineIdempotencyKey are required");
		}

		return repository.findOneWithBase(QPaymentTransactionEntity.paymentTransactionEntity.tenantId.eq(tenantId
				.getValue())
				.and(QPaymentTransactionEntity.paymentTransactionEntity.offlineIdempotencyKey.eq(offlineIdempotencyKey
						.getValue())))
				.map(PaymentTransactionEntityMapper.INSTANCE::toModel);

	}
}
