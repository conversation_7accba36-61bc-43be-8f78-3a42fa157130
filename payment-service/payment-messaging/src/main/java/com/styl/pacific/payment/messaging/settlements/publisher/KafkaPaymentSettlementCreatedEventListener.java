/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.messaging.settlements.publisher;

import com.styl.pacific.payment.messaging.settlements.publisher.kafka.KafkaPaymentSettlementCreatedEventPublisher;
import com.styl.pacific.payment.messaging.settlements.publisher.mapper.PaymentSettlementCreatedEventMapper;
import com.styl.pacific.payment.service.core.features.settlements.events.PaymentSettlementCreatedEvent;
import lombok.RequiredArgsConstructor;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class KafkaPaymentSettlementCreatedEventListener {

	private final KafkaPaymentSettlementCreatedEventPublisher kafkaPublisher;

	@EventListener
	@Async
	public void handleEvent(PaymentSettlementCreatedEvent event) {
		kafkaPublisher.publish(PaymentSettlementCreatedEventMapper.INSTANCE.toEvent(event.getEventId(), event
				.getPaymentTransaction()));
	}

}
