/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.messaging.settlements.publisher.kafka;

import com.styl.pacific.kafka.producer.service.KafkaProducer;
import com.styl.pacific.payment.messaging.config.KafkaPaymentPublisherConfigProperties;
import java.io.Serializable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.specific.SpecificRecordBase;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class KafkaPaymentSettlementCommandEventPublisher {
	private final KafkaProducer<Serializable, SpecificRecordBase> kafkaProducer;
	private final KafkaPaymentPublisherConfigProperties publisherConfig;

	public void publish(
			com.styl.pacific.kafka.payments.avro.model.PaymentSettlementCommandRequestEventAvroModel event) {
		final var topicName = publisherConfig.getPaymentSettlementCommandEvent()
				.getTopicName();
		kafkaProducer.send(topicName, event.getId(), event, (result, error) -> {
			if (error == null) {
				log.info("Sent PaymentSettlementCommandRequestEventAvroModel with eventId: {}, tenantId: {}", event
						.getId(), event.getTenantId());
			} else {
				log.error("Error in sending message to topic: [{}] with eventId: {} and tenantId: {}", topicName, event
						.getId(), event.getTenantId());
				log.error(error.getMessage(), error);
			}
		});
	}

}
