/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.messaging.wallets.consumer.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.kafka.wallet.avro.model.WalletTransactionCreatedAvroEvent;
import com.styl.pacific.payment.spi.processors.settlements.events.EWalletTransactionCreatedEvent;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface WalletTransactionCreatedEventMapper {
	WalletTransactionCreatedEventMapper INSTANCE = Mappers.getMapper(WalletTransactionCreatedEventMapper.class);

	@Mapping(target = "tenantId", source = "tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "walletTransactionId", source = "walletTransactionId", qualifiedByName = "longToWalletTransactionId")
	@Mapping(target = "walletId", source = "walletId")
	@Mapping(target = "customerId", source = "customerId", qualifiedByName = "longToUserId")
	@Mapping(target = "sourceWalletId", source = "sourceWalletId")
	@Mapping(target = "destinationWalletId", source = "destinationWalletId")
	@Mapping(target = "paymentSessionId", source = "paymentSessionId", qualifiedByName = "stringToPaymentSessionId")
	@Mapping(target = "walletTransactionCreatedAt", source = "walletTransactionCreatedAt", qualifiedByName = "longToInstant")
	@Mapping(target = "createdAt", source = "createdAt")
	EWalletTransactionCreatedEvent toEWalletTransactionCreatedEvent(WalletTransactionCreatedAvroEvent event);
}
