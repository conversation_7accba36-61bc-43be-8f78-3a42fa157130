/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.rest.webhooks.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.payment.service.core.features.webhooks.entities.PaymentWebhookEndpoint;
import com.styl.pacific.payment.shared.http.webhooks.response.PaymentWebhookEndpointResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface PaymentWebhookEndpointResponseMapper {
	PaymentWebhookEndpointResponseMapper INSTANCE = Mappers.getMapper(PaymentWebhookEndpointResponseMapper.class);

	@Mapping(target = "id", source = "id", qualifiedByName = "paymentWebhookEndpointIdToString")
	@Mapping(target = "paymentMethodId", source = "paymentMethodId", qualifiedByName = "paymentMethodIdToString")
	@Mapping(target = "tenantId", source = "tenantId", qualifiedByName = "tenantIdToString")
	PaymentWebhookEndpointResponse toResponse(PaymentWebhookEndpoint model);
}