/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.rest.sessions.mapper;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.payment.service.core.features.sessions.entities.PaymentSession;
import com.styl.pacific.payment.shared.http.sessions.response.PaymentSessionResponse;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface PaymentSessionResponseMapper extends PaymentSessionParamsSupportMapper {
	PaymentSessionResponseMapper INSTANCE = Mappers.getMapper(PaymentSessionResponseMapper.class);

	@Mapping(target = "paymentSessionId", source = "id", qualifiedByName = "paymentSessionIdToString")
	@Mapping(target = "paymentMethodId", source = "paymentMethodId", qualifiedByName = "paymentMethodIdToString")
	@Mapping(target = "appliedSurchargeRate", source = "appliedSurchargeRate", qualifiedByName = "bigDecimalToString")
	@Mapping(target = "processorParams", expression = "java(mapToProcessorParams(objectMapper, paymentSession.getPaymentProcessorId(), paymentSession.getProcessorParams()))")
	@Mapping(target = "sessionData", expression = "java(mapToSessionData(objectMapper, paymentSession.getPaymentProcessorId(), paymentSession.getSessionData()))")
	PaymentSessionResponse toResponse(PaymentSession paymentSession, @Context ObjectMapper objectMapper);

}