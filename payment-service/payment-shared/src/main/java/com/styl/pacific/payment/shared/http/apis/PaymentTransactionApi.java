/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.shared.http.apis;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.permissions.PacificApiAuthorized;
import com.styl.pacific.domain.permissions.PlatformApiSecurityLevel;
import com.styl.pacific.payment.shared.http.transactions.request.CreateOfflineTransactionRequest;
import com.styl.pacific.payment.shared.http.transactions.request.QueryPaymentTransactionPaginationRequest;
import com.styl.pacific.payment.shared.http.transactions.response.PaymentTransactionResponse;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;

public interface PaymentTransactionApi {

	@GetMapping(path = "/api/payment/transactions/{transactionId}")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	PaymentTransactionResponse getPaymentTransaction(@PathVariable("transactionId") Long transactionId);

	@GetMapping(path = "/api/payment/transactions")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	Paging<PaymentTransactionResponse> queryPaymentTransactions(
			@SpringQueryMap @ModelAttribute @Valid QueryPaymentTransactionPaginationRequest request);

	@PostMapping(path = "/api/payment/transactions/offline")
	@ResponseStatus(HttpStatus.CREATED)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.PRIVATE)
	PaymentTransactionResponse createOfflineTransaction(@RequestBody @Valid CreateOfflineTransactionRequest request);
}
