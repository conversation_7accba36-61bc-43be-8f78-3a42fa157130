/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.shared.http.settlement.response.nets;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.payment.shared.constants.PaymentProcessorConstants;
import com.styl.pacific.payment.shared.http.settlement.response.PaymentSettlementDataResponse;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@JsonTypeName(PaymentProcessorConstants.PaymentProcessorIds.NETS_TERMINAL_PAYMENT)
@JsonIgnoreProperties(ignoreUnknown = true)
public class NetsPaymentSettlementResponse extends PaymentSettlementDataResponse {
	private final String approvalCode;
	private final String merchantId;
	private final String terminalId;
	private final String stan;
	private final String cardName;
	private final String canNumber;
	private final String balance;
	private final String newBalance;
	private final String purchaseAmount;
	private final String batchNumber;
	private final String invoiceNumber;
	private final String rrn;
	private final String tvr;
	private final String entryType;
	private final String issuerName;
	private final String date;
	private final String time;

	@JsonCreator
	@Builder
	public NetsPaymentSettlementResponse(String failureCode, String failureDescription, String approvalCode,
			String merchantId, String terminalId, String stan, String cardName, String canNumber, String balance,
			String newBalance, String purchaseAmount, String batchNumber, String invoiceNumber, String rrn, String tvr,
			String entryType, String issuerName, String date, String time) {
		super(PaymentProcessorId.NETS_TERMINAL_PAYMENT, failureCode, failureDescription);
		this.approvalCode = approvalCode;
		this.merchantId = merchantId;
		this.terminalId = terminalId;
		this.stan = stan;
		this.cardName = cardName;
		this.canNumber = canNumber;
		this.balance = balance;
		this.newBalance = newBalance;
		this.purchaseAmount = purchaseAmount;
		this.batchNumber = batchNumber;
		this.invoiceNumber = invoiceNumber;
		this.rrn = rrn;
		this.tvr = tvr;
		this.entryType = entryType;
		this.issuerName = issuerName;
		this.date = date;
		this.time = time;
	}
}
