/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.shared.http.transactions.response;

import com.styl.pacific.domain.enums.PaymentTransactionStatus;
import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.payment.shared.enums.PaymentTransactionType;
import java.time.Instant;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@Builder
@RequiredArgsConstructor
public class PaymentTransactionLiteResponse {
	private final String id;
	private final String tenantId;
	private final String paymentMethodId;
	private final String paymentSessionId;
	private final String paymentMethodDisplayName;
	private final PaymentProcessorId paymentProcessorId;
	private final PaymentTransactionStatus transactionStatus;
	private final PaymentTransactionType transactionType;
	private final String paymentReference;
	private final String customerId;
	private final String customerEmail;
	private final String customerName;
	private final String currencyCode;
	private final String deviceId;
	private final String transactionNumber;
	private final String deviceTransactionNumber;
	private final Boolean isReversible;
	private final Instant lastReversedAt;
	private final Long refundedAmount;

	private final Long fee;
	private final Long amount;
	private final Long netAmount;
	private final String systemSource;
	private final String description;
	private final String merchantName;

	private final String appliedSurchargeRate;
	private final Long appliedFixedSurcharge;

	private final Boolean isAsync; // Settlement vs Message Queue
	private final Instant paidAt; // Time from Payment Provider Service
	private final Instant initiatedAt; // Time from Payment Session
	private final Instant createdAt;
	private final String createdBy;
}
