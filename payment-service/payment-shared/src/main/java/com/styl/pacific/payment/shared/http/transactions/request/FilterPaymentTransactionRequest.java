/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.shared.http.transactions.request;

import com.styl.pacific.domain.enums.PaymentTransactionStatus;
import com.styl.pacific.domain.range.LongDateTimeRange;
import com.styl.pacific.payment.shared.enums.PaymentTransactionType;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FilterPaymentTransactionRequest {
	private String byCustomerId;
	private Set<String> byCustomerIds;
	private LongDateTimeRange byCreatedRange;
	private Set<Long> byTransactionIds;
	private PaymentTransactionStatus byStatus;
	private String byCustomerEmail;
	private Set<String> bySessionIds;
	private Long byPaymentMethodId;
	private PaymentTransactionType byTransactionType;
	private String byDeviceId;
	private String byTransactionNumber;
	private String byDeviceTransactionNumber;
	private Boolean isOffline;
}