/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.shared.http.transactions.request;

import com.styl.pacific.common.validator.currency.Currency;
import com.styl.pacific.common.validator.rate.ValidRate;
import com.styl.pacific.domain.enums.PaymentTransactionStatus;
import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.payment.shared.http.settlement.request.PaymentSettlementDataRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.Map;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.hibernate.validator.constraints.Length;

@Getter
@Builder
@RequiredArgsConstructor
public class CreateOfflineTransactionRequest {
	@NotEmpty
	@Length(max = 64)
	private final String offlineIdempotencyKey;

	@Length(max = 1000)
	private final String description;

	@NotEmpty
	@Length(max = 128)
	private final String paymentReference;

	@Length(max = 64)
	private final String customerId;

	@Length(max = 64)
	private final String userCardId;

	@Email
	@Length(max = 255)
	private final String customerEmail;

	@Length(max = 255)
	private final String customerName;

	@Length(max = 255)
	private final String deviceId;

	@Currency
	@NotNull
	private final String currencyCode;

	@NotNull
	private final Long fee;

	@NotNull
	private final Long amount;

	@NotNull
	private final Long netAmount;

	@NotNull
	private final Long paymentMethodId;

	@NotNull
	private final PaymentProcessorId paymentProcessorId;

	@ValidRate
	private final BigDecimal surchargeRate;

	@Min(value = 0, message = "Fixed Surcharge greater zero")
	private final Long fixedSurcharge;

	@NotNull
	private final Instant initiatedAt;

	private final String systemSource;

	@NotBlank
	private final String paymentMethodDisplayName;

	@Length(max = 64)
	private final String merchantName;

	@NotNull
	private final PaymentTransactionStatus transactionStatus;

	@NotNull
	private final Instant paidAt;

	@Length(max = 255)
	private final String transactionNumber;

	@Length(max = 255)
	private final String deviceTransactionNumber;

	@Valid
	private final PaymentSettlementDataRequest settlementData;

	private final Map<String, String> extraSettlementData;
}
