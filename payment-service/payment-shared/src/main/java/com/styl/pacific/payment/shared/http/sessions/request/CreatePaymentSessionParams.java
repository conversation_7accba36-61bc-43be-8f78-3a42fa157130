/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.shared.http.sessions.request;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.payment.shared.constants.PaymentProcessorConstants;
import com.styl.pacific.payment.shared.http.sessions.request.cash.CashPaymentSessionParamsRequest;
import com.styl.pacific.payment.shared.http.sessions.request.ewallet.EWalletPaymentSessionParamsRequest;
import com.styl.pacific.payment.shared.http.sessions.request.nets.NetsPaymentSessionParamsRequest;
import com.styl.pacific.payment.shared.http.sessions.request.offline.OfflineWebPaymentSessionParamsRequest;
import com.styl.pacific.payment.shared.http.sessions.request.stripe.StripeWebPaymentSessionParamsRequest;
import com.styl.pacific.payment.shared.http.sessions.request.stripeconnect.StripeConnectWebPaymentSessionParamsRequest;
import lombok.Getter;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = CreatePaymentSessionParams.TYPE_FIELD_NAME, visible = true)
@JsonSubTypes({
		@JsonSubTypes.Type(value = StripeWebPaymentSessionParamsRequest.class, name = PaymentProcessorConstants.PaymentProcessorIds.STRIPE_WEB_PAYMENT),
		@JsonSubTypes.Type(value = StripeConnectWebPaymentSessionParamsRequest.class, name = PaymentProcessorConstants.PaymentProcessorIds.STRIPE_CONNECT_STANDARD_WEB_PAYMENT),
		@JsonSubTypes.Type(value = CashPaymentSessionParamsRequest.class, name = PaymentProcessorConstants.PaymentProcessorIds.CASH_PAYMENT),
		@JsonSubTypes.Type(value = OfflineWebPaymentSessionParamsRequest.class, name = PaymentProcessorConstants.PaymentProcessorIds.OFFLINE_PAYMENT),
		@JsonSubTypes.Type(value = NetsPaymentSessionParamsRequest.class, name = PaymentProcessorConstants.PaymentProcessorIds.NETS_TERMINAL_PAYMENT),
		@JsonSubTypes.Type(value = EWalletPaymentSessionParamsRequest.class, name = PaymentProcessorConstants.PaymentProcessorIds.E_WALLET_PAYMENT), })
@Getter
public abstract class CreatePaymentSessionParams {
	public static final String TYPE_FIELD_NAME = "processorId";
	private final PaymentProcessorId processorId;

	private final Long expiredInMilliseconds;

	protected CreatePaymentSessionParams(PaymentProcessorId processorId, Long expiredInMilliseconds) {
		this.processorId = processorId;
		this.expiredInMilliseconds = expiredInMilliseconds != null ? expiredInMilliseconds : 900_000L;
	}
}
