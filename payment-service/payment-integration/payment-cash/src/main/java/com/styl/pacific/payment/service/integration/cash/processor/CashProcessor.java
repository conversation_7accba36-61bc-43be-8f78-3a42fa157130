/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.integration.cash.processor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.payment.service.integration.cash.config.CashPaymentMethodConfiguration;
import com.styl.pacific.payment.service.integration.cash.config.SystemCashProcessorConfiguration;
import com.styl.pacific.payment.spi.processors.PaymentConfigParser;
import com.styl.pacific.payment.spi.processors.PaymentProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CashProcessor extends PaymentProcessor<CashPaymentMethodConfiguration> {

	public CashProcessor(ObjectMapper objectMapper,
			PaymentConfigParser<CashPaymentMethodConfiguration> cashConfigParser) {
		super(PaymentProcessorId.CASH_PAYMENT, cashConfigParser, new SystemCashProcessorConfiguration(), objectMapper);

	}

}