/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.integration.stripeconnect.sessions.mapper;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.payment.service.integration.stripeconnect.sessions.valueobject.StripeConnectPaymentSessionMetadata;
import com.styl.pacific.payment.spi.processors.sessions.valueobject.PaymentSessionProcessorData;
import java.util.Map;
import java.util.Optional;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface StripeConnectPaymentSessionMetadataMapper {
	StripeConnectPaymentSessionMetadataMapper INSTANCE = Mappers.getMapper(
			StripeConnectPaymentSessionMetadataMapper.class);

	StripeConnectPaymentSessionMetadata toMetadata(PaymentSessionProcessorData sessionProcessorData);

	default Map<String, String> convertToMapValues(ObjectMapper objectMapper,
			StripeConnectPaymentSessionMetadata metadata) {
		return Optional.ofNullable(metadata)
				.map(it -> objectMapper.convertValue(it, new TypeReference<Map<String, String>>() {
				}))
				.orElse(null);
	}

	default Optional<StripeConnectPaymentSessionMetadata> parseMetadata(ObjectMapper objectMapper,
			Map<String, String> metadata) {
		return Optional.ofNullable(metadata)
				.map(it -> objectMapper.convertValue(it, StripeConnectPaymentSessionMetadata.class));
	}
}