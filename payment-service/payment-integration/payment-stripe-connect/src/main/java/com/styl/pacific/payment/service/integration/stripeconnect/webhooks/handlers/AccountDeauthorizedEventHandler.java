/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.integration.stripeconnect.webhooks.handlers;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stripe.model.Account;
import com.stripe.model.Event;
import com.stripe.model.StripeObject;
import com.styl.pacific.payment.service.integration.stripeconnect.accounts.model.StripeConnectedAccountMetadata;
import com.styl.pacific.payment.service.integration.stripeconnect.enums.StripeConnectEventType;
import com.styl.pacific.payment.service.integration.stripeconnect.webhooks.verifier.StripeConnectEventPayloadVerifier;
import com.styl.pacific.payment.spi.processors.accounts.event.PaymentAccountDeauthorizedEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
public class AccountDeauthorizedEventHandler extends StripeConnectEventHandler {

	private final ApplicationEventPublisher applicationEventPublisher;
	private final ObjectMapper mapper;

	public AccountDeauthorizedEventHandler(ApplicationEventPublisher applicationEventPublisher,
			StripeConnectEventPayloadVerifier verifier, ObjectMapper mapper) {
		super(verifier);
		this.applicationEventPublisher = applicationEventPublisher;
		this.mapper = mapper;
	}

	@Override
	public boolean isSupported(StripeConnectEventType eventType) {
		return StripeConnectEventType.ACCOUNT_DEAUTHORIZED_EVENT.equals(eventType);
	}

	@Override
	void process(Event event, StripeObject stripeObject) {
		final var account = (Account) stripeObject;
		final var metadata = mapper.convertValue(account.getMetadata(), StripeConnectedAccountMetadata.class);

		applicationEventPublisher.publishEvent(PaymentAccountDeauthorizedEvent.builder()
				.eventId(event.getId())
				.clientExternalId(event.getAccount())
				.tenantId(metadata.getTenantId())
				.build());
	}
}
