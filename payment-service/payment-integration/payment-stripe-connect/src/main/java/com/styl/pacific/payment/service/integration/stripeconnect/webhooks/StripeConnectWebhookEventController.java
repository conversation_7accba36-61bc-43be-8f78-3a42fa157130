/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.integration.stripeconnect.webhooks;

import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.payment.service.integration.stripeconnect.apis.StripeConnectWebhookEventApi;
import com.styl.pacific.payment.service.integration.stripeconnect.enums.StripeConnectEventType;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class StripeConnectWebhookEventController implements StripeConnectWebhookEventApi {

	private final StripeConnectWebhookCommandService webhookCommandService;

	@Override
	public void handleEvent(String stripeSignature, Long endpointId, String eventType, String eventPayload) {
		webhookCommandService.handleEvent(MapstructCommonDomainMapper.INSTANCE.longToPaymentWebhookEndpointId(
				endpointId), StripeConnectEventType.from(eventType), stripeSignature, eventPayload);
	}
}
