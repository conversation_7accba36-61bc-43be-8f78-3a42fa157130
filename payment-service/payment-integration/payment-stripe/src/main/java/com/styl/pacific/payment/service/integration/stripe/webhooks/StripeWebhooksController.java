/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.integration.stripe.webhooks;

import com.styl.pacific.domain.valueobject.PaymentWebhookEndpointId;
import com.styl.pacific.payment.service.integration.stripe.apis.StripeWebhookEventApi;
import com.styl.pacific.payment.service.integration.stripe.enums.StripeEventType;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class StripeWebhooksController implements StripeWebhookEventApi {
	private final StripeWebhookCommandService webhookCommandService;

	@Override
	public void handleWebhookCheckoutSessionEvent(String stripeSignature, Long endpointId, String event) {
		webhookCommandService.handleEvent(new PaymentWebhookEndpointId(endpointId),
				StripeEventType.CHECKOUT_SESSION_ASYNC_PAYMENT_SUCCEEDED, stripeSignature, event);
	}

	@Override
	public void handleWebhookPaymentIntentSucceedEvent(String stripeSignature, Long endpointId, String event) {
		webhookCommandService.handleEvent(new PaymentWebhookEndpointId(endpointId),
				StripeEventType.PAYMENT_INTENT_SUCCEEDED, stripeSignature, event);
	}
}
