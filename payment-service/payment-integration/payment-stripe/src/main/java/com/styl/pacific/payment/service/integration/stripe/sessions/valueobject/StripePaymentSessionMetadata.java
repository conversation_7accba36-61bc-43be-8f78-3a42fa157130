/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.integration.stripe.sessions.valueobject;

import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.payment.shared.enums.PaymentTransactionType;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.With;

@Getter
@Builder
@RequiredArgsConstructor
@With
public class StripePaymentSessionMetadata {
	private final Long tenantId;
	private final PaymentTransactionType transactionType;
	private final String paymentSessionId;
	private final String paymentMethodId;
	private final PaymentProcessorId paymentProcessorId;
	private final String description;
	private final String paymentReference;
	private final String customerId;
	private final String customerEmail;
	private final String currencyCode;
	private final Long fee;
	private final Long amount;
	private final Long netAmount;
	private final String systemSource;
}
