/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.integration.stripe.clients;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stripe.exception.StripeException;
import com.stripe.model.Event;
import com.stripe.model.StripeObject;
import com.stripe.model.WebhookEndpoint;
import com.stripe.model.checkout.Session;
import com.stripe.net.RequestOptions;
import com.stripe.net.Webhook;
import com.stripe.param.WebhookEndpointCreateParams;
import com.stripe.param.WebhookEndpointUpdateParams;
import com.stripe.param.checkout.SessionCreateParams;
import com.styl.pacific.payment.service.integration.stripe.apis.StripeWebhookEventApi;
import com.styl.pacific.payment.service.integration.stripe.config.StripeApiClientConnectionConfig;
import com.styl.pacific.payment.service.integration.stripe.config.StripePaymentMethodConfiguration;
import com.styl.pacific.payment.service.integration.stripe.enums.SessionStatus;
import com.styl.pacific.payment.service.integration.stripe.exceptions.StripeUnableCreateSessionException;
import com.styl.pacific.payment.service.integration.stripe.exceptions.StripeUnableCreateWebhookEndpointException;
import com.styl.pacific.payment.service.integration.stripe.exceptions.StripeUnableExpireSessionException;
import com.styl.pacific.payment.service.integration.stripe.exceptions.StripeUnableGetSessionException;
import com.styl.pacific.payment.service.integration.stripe.exceptions.StripeUnableUpdateWebhookEndpointException;
import com.styl.pacific.payment.service.integration.stripe.exceptions.StripeWebhookNotFoundException;
import com.styl.pacific.payment.service.integration.stripe.exceptions.StripeWebhookSignatureVerificationFailureException;
import com.styl.pacific.payment.service.integration.stripe.exceptions.StripeWebhookUnsupportedException;
import com.styl.pacific.payment.service.integration.stripe.processor.valueobject.StripePaymentSessionProcessorData;
import com.styl.pacific.payment.spi.processors.sessions.request.PaymentSessionData;
import com.styl.pacific.payment.spi.processors.webhooks.valueobject.PaymentWebhookEndpointConfig;
import com.styl.pacific.utils.stringtemplate.StringTemplateReplaceUtils;
import java.time.Instant;
import java.util.Arrays;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class StripeApiClientImpl implements StripeApiClient {
	private final StripeApiClientConnectionConfig apiClientConnectionConfig;
	private final ObjectMapper objectMapper;

	@Override
	public PaymentSessionData createSession(StripePaymentMethodConfiguration config,
			SessionCreateParams sessionRequest) {
		try {
			final var session = Session.create(sessionRequest, getStripeRequestOptions(config));

			return PaymentSessionData.builder()
					.expiredAt(Instant.ofEpochSecond(session.getExpiresAt()))
					.createdAt(Instant.ofEpochSecond(session.getCreated()))
					.data(objectMapper.convertValue(StripePaymentSessionProcessorData.builder()
							.stripeSessionId(session.getId())
							.paymentUrl(session.getUrl())
							.build(), new TypeReference<>() {
							}))
					.build();

		} catch (StripeException e) {
			log.error(e.getMessage(), e);
			throw new StripeUnableCreateSessionException("Unable to create Stripe payment session");
		}

	}

	@Override
	public void cancelPaymentSession(StripePaymentMethodConfiguration paymentMethodConfig,
			StripePaymentSessionProcessorData sessionData) {
		try {
			final var options = getStripeRequestOptions(paymentMethodConfig);
			final var session = Session.retrieve(sessionData.getStripeSessionId(), options);

			if (!SessionStatus.OPEN.getValue()
					.equals(session.getStatus())) {
				return;
			}
			session.expire(options);
		} catch (StripeException e) {
			log.error(e.getMessage(), e);
			throw new StripeUnableExpireSessionException("Unable to expired Stripe payment session");
		}

	}

	@Override
	public Session getCheckoutPaymentSession(StripePaymentMethodConfiguration paymentMethodConfig,
			String stripeSessionId) {

		try {
			final var options = getStripeRequestOptions(paymentMethodConfig);
			return Session.retrieve(stripeSessionId, options);
		} catch (StripeException e) {
			log.error(e.getMessage(), e);
			throw new StripeUnableGetSessionException("Unable to get Stripe payment session");
		}
	}

	private RequestOptions getStripeRequestOptions(StripePaymentMethodConfiguration config) {
		return apiClientConnectionConfig.generateRequestOption(config.getApiKey());
	}

	@Override
	public WebhookEndpoint createWebhookEndpoint(Long endpointId, String eventType, String tenantDomainUrl,
			StripePaymentMethodConfiguration config, PaymentWebhookEndpointConfig endpoint) {
		try {
			final var event = Arrays.stream(WebhookEndpointCreateParams.EnabledEvent.values())
					.filter(it -> it.getValue()
							.equals(eventType))
					.findFirst()
					.orElseThrow(() -> new StripeWebhookUnsupportedException(String.format("Event is unsupported [%s]",
							eventType)));

			return WebhookEndpoint.create(WebhookEndpointCreateParams.builder()
					.setApiVersion(WebhookEndpointCreateParams.ApiVersion.VERSION_2024_06_20)
					.addEnabledEvent(event)
					.setUrl(StringTemplateReplaceUtils.replaceParams(StripeWebhookEventApi
							.getStripeWebhookEndpointUrlPattern(), Map.of("tenantDomainURL", tenantDomainUrl,
									"eventType", endpoint.getEventType(), "endpointId", endpointId.toString())))
					.putAllMetadata(objectMapper.convertValue(endpoint, new TypeReference<>() {
					}))
					.build(), getStripeRequestOptions(config));
		} catch (StripeException e) {
			log.error(e.getMessage(), e);
			throw new StripeUnableCreateWebhookEndpointException("Unable to create webhook endpoint");
		}
	}

	@Override
	public Event verifyAndDecode(PaymentWebhookEndpointConfig webhookEndpoint, String signature, String payload) {
		try {
			return Webhook.constructEvent(payload, signature, webhookEndpoint.getSigningSecretKey());
		} catch (Exception e) {
			log.error("Verify event failed", e);
			throw new StripeWebhookSignatureVerificationFailureException(e.getMessage());
		}
	}

	@Override
	public StripeObject getValidStripeObject(Event event) {
		return event.getDataObjectDeserializer()
				.getObject()
				.orElseThrow(() -> new StripeWebhookNotFoundException("Stripe Object not found"));
	}

	@Override
	public WebhookEndpoint disableWebhookEndpoint(StripePaymentMethodConfiguration config, String externalClientId) {
		try {
			WebhookEndpoint resource = WebhookEndpoint.retrieve(externalClientId, getStripeRequestOptions(config));
			WebhookEndpointUpdateParams params = WebhookEndpointUpdateParams.builder()
					.setDisabled(true)
					.build();
			return resource.update(params, getStripeRequestOptions(config));
		} catch (StripeException e) {
			log.error(e.getMessage(), e);
			throw new StripeUnableUpdateWebhookEndpointException("Unable to disable webhook endpoint");
		}
	}
}
