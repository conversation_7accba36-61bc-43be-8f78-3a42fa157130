/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.core.features.methods.service;

import com.styl.pacific.domain.valueobject.PaymentMethodId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.payment.service.core.features.accounts.PaymentConnectedAccountQueryService;
import com.styl.pacific.payment.service.core.features.methods.PaymentMethodCommandService;
import com.styl.pacific.payment.service.core.features.methods.PaymentMethodRepository;
import com.styl.pacific.payment.service.core.features.methods.entities.PaymentMethod;
import com.styl.pacific.payment.service.core.features.methods.mapper.PaymentMethodMapper;
import com.styl.pacific.payment.service.core.features.methods.request.UpsertPaymentMethodCommand;
import com.styl.pacific.payment.service.core.features.tenants.TenantQueryService;
import com.styl.pacific.payment.shared.exceptions.PaymentMethodException;
import com.styl.pacific.payment.shared.exceptions.PaymentMethodNotFoundException;
import com.styl.pacific.payment.shared.exceptions.PaymentMethodUnableCreateException;
import com.styl.pacific.payment.shared.exceptions.PaymentProcessorNotFoundException;
import com.styl.pacific.payment.shared.exceptions.PaymentTenantInvalidException;
import com.styl.pacific.payment.spi.processors.PaymentMethodConfigurationValidator;
import com.styl.pacific.payment.spi.processors.PaymentProcessorManager;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class PaymentMethodCommandServiceImpl implements PaymentMethodCommandService {

	private final List<PaymentMethodConfigurationValidator> configurationProcessors;
	private final PaymentProcessorManager paymentProcessorManager;
	private final PaymentMethodRepository methodRepository;
	private final TenantQueryService tenantQueryService;
	private final PaymentConnectedAccountQueryService connectedAccountQueryService;

	@Override
	public PaymentMethod createPaymentMethod(@NotNull TenantId tenantId, @NotNull UpsertPaymentMethodCommand command) {
		final var processor = paymentProcessorManager.getProcessorById(command.getProcessorId())
				.orElseThrow(PaymentProcessorNotFoundException::new);

		if (!processor.getSystemProcessorConfiguration()
				.getAcceptedApplications()
				.containsAll(command.getAcceptedApplications())) {
			throw new PaymentMethodUnableCreateException(String.format(
					"Unable to create payment method due to missing out accepted application config. [%s]", processor
							.getSystemProcessorConfiguration()
							.getAcceptedApplications()
							.stream()
							.map(Enum::name)
							.collect(Collectors.joining(","))));
		}

		final var processorId = command.getProcessorId();
		final var configurationProcessor = configurationProcessors.stream()
				.filter(paymentMethodConfigurationValidator -> paymentMethodConfigurationValidator
						.getPaymentProcessorId()
						.equals(processorId))
				.findFirst()
				.orElseThrow(PaymentProcessorNotFoundException::new);

		configurationProcessor.validateCreateRequest(command.getProcessorConfig());
		command = command.withIsActive(configurationProcessor.validateActivationRequest(command.getIsActive()));

		final var tenantSetting = tenantQueryService.getTenantSetting(tenantId)
				.orElseThrow(PaymentTenantInvalidException::new);

		if (!command.getCurrencyCode()
				.equals(tenantSetting.getCurrency()
						.getCurrencyCode())) {
			throw new PaymentMethodUnableCreateException(String.format(
					"Unable to create payment method due to not the same currency with tenant setting. [%s]",
					tenantSetting.getCurrency()
							.getCurrencyCode()));
		}

		final var method = methodRepository.savePaymentMethod(PaymentMethodMapper.INSTANCE.toNewEntity(tenantId,
				command));
		return method.withTransactionReversible(processor.getSystemProcessorConfiguration()
				.getTransactionReversible());
	}

	@Override
	public PaymentMethod updatePaymentMethod(TenantId tenantId, PaymentMethodId methodId,
			UpsertPaymentMethodCommand command) {

		final var method = methodRepository.getPaymentMethod(tenantId, methodId)
				.orElseThrow(PaymentMethodNotFoundException::new);

		final var connectedAccountOptional = Optional.ofNullable(command.getConnectedAccountId())
				.map(connectedAccountId -> connectedAccountQueryService.getPaymentConnectedAccount(tenantId,
						connectedAccountId));

		final var configurationProcessor = configurationProcessors.stream()
				.filter(paymentMethodConfigurationValidator -> paymentMethodConfigurationValidator
						.getPaymentProcessorId()
						.equals(method.getProcessorId()))
				.findFirst()
				.orElseThrow(PaymentProcessorNotFoundException::new);

		final var config = configurationProcessor.validateUpdateRequest(method.getProcessorConfig(), command
				.getProcessorConfig());

		final var tenantSetting = tenantQueryService.getTenantSetting(tenantId)
				.orElseThrow(PaymentTenantInvalidException::new);

		if (!command.getCurrencyCode()
				.equals(tenantSetting.getCurrency()
						.getCurrencyCode())) {
			throw new PaymentMethodUnableCreateException(String.format(
					"Unable to create payment method due to not the same currency with tenant setting. [%s]",
					tenantSetting.getCurrency()
							.getCurrencyCode()));
		}

		final var isActiveRequest = Boolean.TRUE.equals(command.getIsActive());
		command = command.withIsActive(connectedAccountOptional.map(it -> it.isActive() && isActiveRequest)
				.orElseGet(() -> configurationProcessor.validateActivationRequest(isActiveRequest)));

		final var updatedMethod = methodRepository.savePaymentMethod(PaymentMethodMapper.INSTANCE.toUpdateEntity(method,
				command.withProcessorConfig(config), connectedAccountOptional.orElse(null)));
		return updatedMethod.withTransactionReversible(paymentProcessorManager.getProcessorById(updatedMethod
				.getProcessorId())
				.orElseThrow(PaymentProcessorNotFoundException::new)
				.getSystemProcessorConfiguration()
				.getTransactionReversible());
	}

	@Override
	public void deletePaymentMethodById(TenantId tenantId, PaymentMethodId paymentMethodId) {
		if (paymentMethodId == null || paymentMethodId.getValue() == null) {
			throw new PaymentMethodException("Payment method id is mandatory");
		}

		if (!methodRepository.existPaymentMethodByTenantIdAndId(tenantId, paymentMethodId)) {
			throw new PaymentMethodNotFoundException();
		}

		methodRepository.deletePaymentMethodById(paymentMethodId);
	}

	@Override
	public void updatePaymentMethods(List<PaymentMethod> methods) {
		methodRepository.savePaymentMethods(methods);
	}
}
