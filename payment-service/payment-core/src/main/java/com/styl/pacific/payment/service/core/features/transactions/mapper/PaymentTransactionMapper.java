/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.core.features.transactions.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.payment.service.core.features.methods.entities.PaymentMethod;
import com.styl.pacific.payment.service.core.features.sessions.entities.PaymentSession;
import com.styl.pacific.payment.service.core.features.transactions.entities.PaymentTransaction;
import com.styl.pacific.payment.spi.processors.settlements.request.SettlePaymentTransactionCommand;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface PaymentTransactionMapper {
	PaymentTransactionMapper INSTANCE = Mappers.getMapper(PaymentTransactionMapper.class);

	@Mapping(target = "id", source = "source.settledTransactionId")
	@Mapping(target = "paymentSessionId", source = "source.id")
	@Mapping(target = "initiatedAt", source = "source.createdAt")
	@Mapping(target = "tenantId", source = "source.tenantId")
	@Mapping(target = "sessionVersion", source = "source.lockingVersion")
	@Mapping(target = "paidAt", source = "command.paidAt")
	@Mapping(target = "isAsync", source = "command.isAsync")
	@Mapping(target = "transactionNumber", source = "command.transactionNumber")
	@Mapping(target = "transactionStatus", source = "command.transactionStatus")
	@Mapping(target = "settlementData.data", source = "command.settlementData")
	@Mapping(target = "extraSettlementData.data", source = "command.extraSettlementData")
	@Mapping(target = "isReversible", source = "command.reversible")
	@Mapping(target = "terminalId", source = "command.terminalId")
	@Mapping(target = "userCardId", source = "command.userCardId")
	@Mapping(target = "paymentMethodDisplayName", source = "paymentMethod.displayName")
	@Mapping(target = "description", source = "source.description")
	@Mapping(target = "currencyCode", source = "source.currencyCode")
	@Mapping(target = "version", ignore = true)
	@Mapping(target = "lastReversedAt", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "createdBy", ignore = true)
	@Mapping(target = "refundedAmount", ignore = true)
	@Mapping(target = "reversedIdempotencyKeys", ignore = true)
	@Mapping(target = "offlineIdempotencyKey", ignore = true)
	@Mapping(target = "isOffline", ignore = true)
	PaymentTransaction toTransactionSettlement(PaymentSession source, PaymentMethod paymentMethod,
			SettlePaymentTransactionCommand command);

}
