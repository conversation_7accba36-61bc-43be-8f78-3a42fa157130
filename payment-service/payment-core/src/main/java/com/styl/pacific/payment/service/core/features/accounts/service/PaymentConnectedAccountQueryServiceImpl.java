/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.core.features.accounts.service;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.PaymentConnectedAccountId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.payment.service.core.features.accounts.PaymentConnectedAccountQueryService;
import com.styl.pacific.payment.service.core.features.accounts.PaymentConnectedAccountRepository;
import com.styl.pacific.payment.service.core.features.accounts.entities.PaymentConnectedAccount;
import com.styl.pacific.payment.service.core.features.accounts.request.PaymentConnectedAccountPaginationQuery;
import com.styl.pacific.payment.shared.exceptions.PaymentConnectedAccountNotFoundException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class PaymentConnectedAccountQueryServiceImpl implements PaymentConnectedAccountQueryService {
	private final PaymentConnectedAccountRepository repository;

	@Override
	public PaymentConnectedAccount getPaymentConnectedAccount(TenantId tenantId,
			PaymentConnectedAccountId connectedAccountId) {
		return repository.findByTenantIdAndId(tenantId, connectedAccountId)
				.orElseThrow(PaymentConnectedAccountNotFoundException::new);
	}

	@Override
	public Paging<PaymentConnectedAccount> queryPaymentConnectedAccounts(PaymentConnectedAccountPaginationQuery query) {
		return repository.queryPaymentConnectedAccounts(query);
	}

	@Override
	public PaymentConnectedAccount getPaymentConnectedAccount(TenantId tenantId, String clientExternalId) {
		return repository.findByTenantIdAndClientExternalId(tenantId, clientExternalId)
				.orElseThrow(PaymentConnectedAccountNotFoundException::new);
	}
}
