/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.core.features.tenants.entities;

import com.styl.pacific.domain.dto.CountryResponse;
import com.styl.pacific.domain.valueobject.TenantId;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.With;

@Getter
@Builder
@With
@RequiredArgsConstructor
public class Tenant {
	private final TenantId id;

	private final String realmId;

	private final String name;

	private final String email;

	private final String phoneNumber;

	private final String addressLine1;

	private final String addressLine2;

	private final String city;

	private final CountryResponse country;

	private final String postalCode;

	private final Long createdAt;

	private final Long updatedAt;
}
