/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.core.features.accounts.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.time.Instant;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConnectedAccountPaymentMethodConfig {

	private final String paymentConnectedAccountId;
	private final Boolean isActive;
	private final String tenantId;
	private final String clientExternalId;
	private final Instant createdAt;
	private final Instant updatedAt;

	@JsonCreator
	@Builder
	public ConnectedAccountPaymentMethodConfig(String paymentConnectedAccountId, Boolean isActive, String tenantId,
			String clientExternalId, Instant createdAt, Instant updatedAt) {
		this.paymentConnectedAccountId = paymentConnectedAccountId;
		this.isActive = isActive;
		this.tenantId = tenantId;
		this.clientExternalId = clientExternalId;
		this.createdAt = createdAt;
		this.updatedAt = updatedAt;
	}
}
