/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.core.features.sessions.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.payment.service.core.features.methods.entities.PaymentMethod;
import com.styl.pacific.payment.service.core.features.sessions.entities.PaymentSession;
import com.styl.pacific.payment.service.core.features.sessions.idgenerator.PaymentSessionIdGenerator;
import com.styl.pacific.payment.service.core.features.sessions.request.CreatePaymentSessionCommand;
import com.styl.pacific.payment.shared.enums.PaymentSessionStatus;
import com.styl.pacific.payment.spi.processors.sessions.valueobject.PaymentSessionProcessorData;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Optional;
import org.mapstruct.AfterMapping;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface PaymentSessionMapper {
	PaymentSessionMapper INSTANCE = Mappers.getMapper(PaymentSessionMapper.class);

	@Mapping(target = "tenantId", source = "tenantId")
	@Mapping(target = "appliedSurchargeRate", source = "method.surchargeRate")
	@Mapping(target = "appliedFixedSurcharge", source = "method.fixedSurcharge")
	@Mapping(target = "currencyCode", source = "method.currencyCode")
	@Mapping(target = "description", source = "command.description")
	@Mapping(target = "id", ignore = true)
	@Mapping(target = "status", ignore = true)
	@Mapping(target = "lockingVersion", ignore = true)
	@Mapping(target = "settledTransactionId", ignore = true)
	@Mapping(target = "expiredAt", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "updatedAt", ignore = true)
	@Mapping(target = "createdBy", ignore = true)
	@Mapping(target = "updatedBy", ignore = true)
	@Mapping(target = "sessionData", ignore = true)
	PaymentSession toNewPaymentSession(PaymentMethod method, CreatePaymentSessionCommand command, TenantId tenantId,
			@Context PaymentSessionIdGenerator sessionIdGenerator);

	@AfterMapping
	default void afterMappingNewPaymentSession(CreatePaymentSessionCommand command,
			@MappingTarget PaymentSession.PaymentSessionBuilder builder,
			@Context PaymentSessionIdGenerator sessionIdGenerator) {
		builder.id(sessionIdGenerator.generate());
		builder.status(PaymentSessionStatus.PENDING);
		builder.expiredAt(Instant.now()
				.plus(Optional.ofNullable(command.getExpiredInMilliseconds())
						.map(Duration::ofMillis)
						.orElseGet(() -> Duration.ofHours(1))
						.toMillis(), ChronoUnit.MILLIS));
	}

	@Mapping(target = "paymentSessionId", source = "id", qualifiedByName = "paymentSessionIdToString")
	@Mapping(target = "paymentMethodId", source = "paymentMethodId", qualifiedByName = "paymentMethodIdToString")
	@Mapping(target = "tenantId", source = "tenantId", qualifiedByName = "tenantIdToLong")
	PaymentSessionProcessorData toSessionProcessorData(PaymentSession session);
}
