/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.spi.processors.settlements.connector;

import com.styl.pacific.payment.spi.processors.methods.valueobject.PaymentMethodConfiguration;
import com.styl.pacific.payment.spi.processors.methods.valueobject.SimplePaymentMethodConfiguration;
import com.styl.pacific.payment.spi.processors.webhooks.request.ClientWebhookEndpointResponse;
import com.styl.pacific.payment.spi.processors.webhooks.valueobject.PaymentWebhookEndpointConfig;

public interface PaymentWebhookConnector {
	ClientWebhookEndpointResponse createWebhookEndpoint(Long endpointId, String evenType, String defaultDomainUrl,
			PaymentMethodConfiguration processorConfig, PaymentWebhookEndpointConfig endpoint);

	void disableWebhookEndpoint(SimplePaymentMethodConfiguration processorConfig, String externalClientId);
}
