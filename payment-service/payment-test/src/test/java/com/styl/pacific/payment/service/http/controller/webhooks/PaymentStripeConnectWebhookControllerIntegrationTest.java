/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.http.controller.webhooks;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stripe.Stripe;
import com.stripe.model.Account;
import com.stripe.model.Event;
import com.stripe.model.PaymentIntent;
import com.stripe.model.checkout.Session;
import com.styl.pacific.common.test.utils.HeaderGenerator;
import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.kafka.payments.avro.model.PaymentTransactionStatus;
import com.styl.pacific.payment.messaging.settlements.publisher.kafka.KafkaPaymentSettlementCommandEventPublisher;
import com.styl.pacific.payment.service.config.IntegrationTestConfiguration;
import com.styl.pacific.payment.service.config.PaymentConnectedIntegrationSupporter;
import com.styl.pacific.payment.service.config.PaymentIntegrationTestContainer;
import com.styl.pacific.payment.service.config.PaymentMethodInitIntegrationSupporter;
import com.styl.pacific.payment.service.config.PaymentSessionInitIntegrationSupporter;
import com.styl.pacific.payment.service.core.features.accounts.PaymentConnectedAccountCommandService;
import com.styl.pacific.payment.service.core.features.accounts.PaymentConnectedAccountQueryService;
import com.styl.pacific.payment.service.core.features.accounts.request.ConnectPaymentAccountCommand;
import com.styl.pacific.payment.service.core.features.methods.PaymentMethodCommandService;
import com.styl.pacific.payment.service.core.features.methods.request.UpsertPaymentMethodCommand;
import com.styl.pacific.payment.service.core.features.sessions.PaymentSessionCommandService;
import com.styl.pacific.payment.service.core.features.sessions.mapper.PaymentSessionMapper;
import com.styl.pacific.payment.service.core.features.sessions.request.CreatePaymentSessionCommand;
import com.styl.pacific.payment.service.core.features.webhooks.valueobject.PaymentWebhookClientData;
import com.styl.pacific.payment.service.data.access.clients.tenants.TenantClient;
import com.styl.pacific.payment.service.integration.ewallet.clients.wallets.WalletTransactionClient;
import com.styl.pacific.payment.service.integration.stripe.clients.StripeApiClient;
import com.styl.pacific.payment.service.integration.stripe.constants.StripeEventConstants;
import com.styl.pacific.payment.service.integration.stripe.settlements.valueobject.StripePaymentSettlementData;
import com.styl.pacific.payment.service.integration.stripeconnect.accounts.model.StripeConnectedAccountMetadata;
import com.styl.pacific.payment.service.integration.stripeconnect.clients.StripeConnectApiClient;
import com.styl.pacific.payment.service.integration.stripeconnect.config.StripeConnectPaymentMethodConfiguration;
import com.styl.pacific.payment.service.integration.stripeconnect.constants.StripeConnectEventConstants;
import com.styl.pacific.payment.service.integration.stripeconnect.processor.valueobject.StripeConnectPaymentSessionProcessorData;
import com.styl.pacific.payment.service.integration.stripeconnect.sessions.mapper.StripeConnectPaymentSessionMetadataMapper;
import com.styl.pacific.payment.shared.enums.AcceptedApplication;
import com.styl.pacific.payment.shared.enums.PaymentTransactionType;
import com.styl.pacific.payment.shared.http.sessions.request.stripeconnect.StripeConnectWebPaymentSessionParamsRequest;
import com.styl.pacific.payment.spi.processors.PaymentWebhookEndpointManager;
import com.styl.pacific.payment.spi.processors.accounts.model.ClientPaymentConnectedAccount;
import com.styl.pacific.payment.spi.processors.accounts.model.ClientPaymentConnectedAccountLink;
import com.styl.pacific.payment.spi.processors.sessions.valueobject.PaymentSessionParams;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Optional;
import java.util.Set;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = IntegrationTestConfiguration.class)
class PaymentStripeConnectWebhookControllerIntegrationTest extends PaymentIntegrationTestContainer {

	@MockitoBean
	private StripeApiClient stripeApiClient;

	@MockitoBean
	private TenantClient tenantClient;

	@MockitoBean
	private WalletTransactionClient walletTransactionClient;

	@MockitoBean
	private StripeConnectApiClient stripeConnectApiClient;

	@MockitoBean
	private PaymentWebhookEndpointManager webhookEndpointManager;

	@Autowired
	private ObjectMapper objectMapper;

	@Autowired
	private PaymentMethodCommandService methodCommandService;

	@MockitoBean
	private KafkaPaymentSettlementCommandEventPublisher kafkaPaymentSettlementCommandEventPublisher;

	@Autowired
	private PaymentSessionCommandService sessionCommandService;

	@Autowired
	private PaymentConnectedAccountQueryService connectedAccountQueryService;

	@Autowired
	private PaymentConnectedAccountCommandService connectedAccountCommandService;

	private PaymentMethodInitIntegrationSupporter paymentMethodSupporter;

	private PaymentSessionInitIntegrationSupporter paymentSessionSupporter;

	private PaymentConnectedIntegrationSupporter connectedIntegrationSupporter;

	@BeforeEach
	void setUp() {
		paymentMethodSupporter = new PaymentMethodInitIntegrationSupporter(tenantClient, methodCommandService);
		paymentSessionSupporter = new PaymentSessionInitIntegrationSupporter(stripeApiClient, stripeConnectApiClient,
				objectMapper, sessionCommandService);
		connectedIntegrationSupporter = new PaymentConnectedIntegrationSupporter(methodCommandService,
				connectedAccountCommandService, stripeConnectApiClient);
	}

	@Test
	void testSettleStripePaymentSessionViaWebhookPaymentIntentEventSuccessfullyWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);
		final var settlementCommandEventCaptor = ArgumentCaptor.forClass(
				com.styl.pacific.kafka.payments.avro.model.PaymentSettlementCommandRequestEventAvroModel.class);

		final var stripePaymentMethod = connectedIntegrationSupporter.connect(paymentMethodSupporter.initPaymentMethod(
				tenantId, UpsertPaymentMethodCommand.builder()
						.displayName("Stripe Connect Payment Method 10000")
						.processorId(PaymentProcessorId.STRIPE_CONNECT_STANDARD_WEB_PAYMENT)
						.acceptedApplications(Set.of(AcceptedApplication.CUSTOMER_PORTAL))
						.iconPath("http://localhost/image.jpg")
						.description("Stripe Payment Description 10000")
						.paymentInstruction("Stripe Payment Instruction 10000")
						.surchargeRate(BigDecimal.valueOf(0.1))
						.fixedSurcharge(10L)
						.currencyCode("SGD")
						.surchargeTitle("Stripe Payment Title 10000")
						.build()));

		final var stripeParams = StripeConnectWebPaymentSessionParamsRequest.builder()
				.cancelRedirectURL("https://www.cancel.org/current")
				.successRedirectURL("https://www.success.org/current")
				.build();

		final var session = paymentSessionSupporter.initStripeConnectPaymentSession(tenantId,
				CreatePaymentSessionCommand.builder()
						.paymentProcessorId(stripePaymentMethod.getProcessorId())
						.paymentMethodId(stripePaymentMethod.getId())
						.description("Payment Order #1234")
						.paymentReference("Payment Reference 01")
						.transactionType(PaymentTransactionType.PURCHASE)
						.amount(2454L)
						.netAmount(2222L)
						.fee(232L)
						.currencyCode("SGD")
						.customerId("1")
						.customerEmail("<EMAIL>")
						.customerName("Customer Name 001")
						.systemSource("System Source 01")
						.merchantName("Merchant Name")
						.expiredInMilliseconds(Duration.ofMinutes(16)
								.toMillis())
						.processorParams(PaymentSessionParams.builder()
								.params(objectMapper.convertValue(stripeParams, new TypeReference<>() {
								}))
								.build())
						.build());

		final var metadata = StripeConnectPaymentSessionMetadataMapper.INSTANCE.toMetadata(PaymentSessionMapper.INSTANCE
				.toSessionProcessorData(session));
		final var sessionData = objectMapper.convertValue(session.getSessionData()
				.getData(), StripeConnectPaymentSessionProcessorData.class);

		final var stripeSession = new Session();
		stripeSession.setId(sessionData.getStripeSessionId());
		stripeSession.setPaymentStatus("paid");
		stripeSession.setPaymentIntent("payment-int-" + stripeSession.getId());
		stripeSession.setExpiresAt(Instant.now()
				.plus(16, ChronoUnit.MINUTES)
				.getEpochSecond());
		stripeSession.setMetadata(objectMapper.convertValue(metadata, new TypeReference<>() {
		}));

		when(stripeConnectApiClient.getCheckoutPaymentSession(any(StripeConnectPaymentMethodConfiguration.class), eq(
				stripeSession.getId()))).thenReturn(stripeSession);

		final var event = new Event();
		event.setId("event-" + stripeSession.getId());
		event.setType(StripeEventConstants.PAYMENT_INTENT_SUCCEEDED);
		event.setApiVersion(Stripe.API_VERSION);
		when(stripeConnectApiClient.verifyAndDecode(any(), any(), any())).thenReturn(event);

		final var stripeObject = new PaymentIntent();
		stripeObject.setId("payment-int-" + stripeSession.getId());
		stripeObject.setCreated(Instant.now()
				.getEpochSecond());
		stripeObject.setMetadata(objectMapper.convertValue(metadata, new TypeReference<>() {
		}));
		when(stripeConnectApiClient.getValidStripeObject(any())).thenReturn(stripeObject);

		when(webhookEndpointManager.getWebhookEndpointConfig(any(), any())).thenReturn(Optional.of(
				PaymentWebhookClientData.builder()
						.build()));

		final var body = "mocked event body";

		// Act & Assert
		webClient.post()
				.uri("/webhooks/payment/stripe-connect/{endpointId}?"
						+ StripeEventConstants.StripeEventParamsConstants.PAYMENT_INTENT_SUCCEEDED, 12345L)
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(body)
				.headers(header -> HeaderGenerator.generateStripeHeader(header, "ABC"))
				.exchange()
				.expectStatus()
				.isOk()
				.expectBody(Void.class)
				.consumeWith(response -> {
					verify(kafkaPaymentSettlementCommandEventPublisher, times(1)).publish(settlementCommandEventCaptor
							.capture());
					final var settlementCommand = settlementCommandEventCaptor.getValue();

					Assertions.assertEquals(session.getId()
							.getValue(), settlementCommand.getPaymentSessionId());
					Assertions.assertEquals(session.getTenantId()
							.getValue(), settlementCommand.getTenantId());
					Assertions.assertEquals(PaymentTransactionStatus.SUCCEEDED, settlementCommand
							.getTransactionStatus());
					Assertions.assertTrue(settlementCommand.getIsAsync());
					Assertions.assertEquals(Instant.ofEpochSecond(stripeObject.getCreated())
							.toEpochMilli(), settlementCommand.getPaidAt());

					final var actualSettlementData = objectMapper.convertValue(settlementCommand.getSettlementData(),
							StripePaymentSettlementData.class);
					Assertions.assertEquals(PaymentProcessorId.STRIPE_CONNECT_STANDARD_WEB_PAYMENT, actualSettlementData
							.getProcessorId());
					Assertions.assertEquals(stripeObject.getId(), actualSettlementData.getStripePaymentIntent());
					Assertions.assertEquals(StripeEventConstants.PAYMENT_INTENT_SUCCEEDED, actualSettlementData
							.getStripeEventType());
					Assertions.assertEquals(event.getId(), actualSettlementData.getStripeEventId());
				});

	}

	@Test
	void testSettleStripePaymentSessionViaWebhookCheckoutSessionEventSuccessfullyWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);
		final var settlementCommandEventCaptor = ArgumentCaptor.forClass(
				com.styl.pacific.kafka.payments.avro.model.PaymentSettlementCommandRequestEventAvroModel.class);

		final var stripePaymentMethod = connectedIntegrationSupporter.connect(paymentMethodSupporter.initPaymentMethod(
				tenantId, UpsertPaymentMethodCommand.builder()
						.displayName("Stripe Connect Payment Method 10000")
						.processorId(PaymentProcessorId.STRIPE_CONNECT_STANDARD_WEB_PAYMENT)
						.acceptedApplications(Set.of(AcceptedApplication.CUSTOMER_PORTAL))
						.iconPath("http://localhost/image.jpg")
						.description("Stripe Payment Description 10000")
						.paymentInstruction("Stripe Payment Instruction 10000")
						.surchargeRate(BigDecimal.valueOf(0.1))
						.fixedSurcharge(10L)
						.currencyCode("SGD")
						.surchargeTitle("Stripe Payment Title 10000")
						.build()));

		final var stripeParams = StripeConnectWebPaymentSessionParamsRequest.builder()
				.cancelRedirectURL("https://www.cancel.org/current")
				.successRedirectURL("https://www.success.org/current")
				.build();

		final var session = paymentSessionSupporter.initStripeConnectPaymentSession(tenantId,
				CreatePaymentSessionCommand.builder()
						.paymentProcessorId(stripePaymentMethod.getProcessorId())
						.paymentMethodId(stripePaymentMethod.getId())
						.description("Payment Order #1234")
						.paymentReference("Payment Reference 01")
						.transactionType(PaymentTransactionType.PURCHASE)
						.amount(2454L)
						.netAmount(2222L)
						.fee(232L)
						.currencyCode("SGD")
						.customerId("1")
						.customerEmail("<EMAIL>")
						.customerName("Customer Name 001")
						.systemSource("System Source 01")
						.merchantName("Merchant Name")
						.expiredInMilliseconds(Duration.ofMinutes(16)
								.toMillis())
						.processorParams(PaymentSessionParams.builder()
								.params(objectMapper.convertValue(stripeParams, new TypeReference<>() {
								}))
								.build())
						.build());

		final var metadata = StripeConnectPaymentSessionMetadataMapper.INSTANCE.toMetadata(PaymentSessionMapper.INSTANCE
				.toSessionProcessorData(session));
		final var sessionData = objectMapper.convertValue(session.getSessionData()
				.getData(), StripeConnectPaymentSessionProcessorData.class);

		final var stripeSession = new Session();
		stripeSession.setId(sessionData.getStripeSessionId());
		stripeSession.setPaymentStatus("paid");
		stripeSession.setPaymentIntent("payment-int-" + stripeSession.getId());
		stripeSession.setExpiresAt(Instant.now()
				.plus(16, ChronoUnit.MINUTES)
				.getEpochSecond());
		stripeSession.setMetadata(objectMapper.convertValue(metadata, new TypeReference<>() {
		}));

		when(stripeConnectApiClient.getCheckoutPaymentSession(any(StripeConnectPaymentMethodConfiguration.class), eq(
				stripeSession.getId()))).thenReturn(stripeSession);

		final var event = new Event();
		event.setId("event-" + stripeSession.getId());
		event.setType(StripeEventConstants.CHECKOUT_SESSION_ASYNC_PAYMENT_SUCCEEDED);
		event.setApiVersion(Stripe.API_VERSION);
		when(stripeConnectApiClient.verifyAndDecode(any(), any(), any())).thenReturn(event);

		final var stripeObject = new Session();
		stripeObject.setPaymentIntent("payment-int-" + stripeSession.getId());
		stripeObject.setCreated(Instant.now()
				.getEpochSecond());
		stripeObject.setMetadata(objectMapper.convertValue(metadata, new TypeReference<>() {
		}));
		when(stripeConnectApiClient.getValidStripeObject(any())).thenReturn(stripeObject);

		when(webhookEndpointManager.getWebhookEndpointConfig(any(), any())).thenReturn(Optional.of(
				PaymentWebhookClientData.builder()
						.build()));

		final var body = "mocked event body";

		// Act & Assert
		webClient.post()
				.uri("/webhooks/payment/stripe-connect/{endpointId}?"
						+ StripeEventConstants.StripeEventParamsConstants.CHECKOUT_SESSION_ASYNC_PAYMENT_SUCCEEDED,
						12345L)
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(body)
				.headers(header -> HeaderGenerator.generateStripeHeader(header, "ABC"))
				.exchange()
				.expectStatus()
				.isOk()
				.expectBody(Void.class)
				.consumeWith(response -> {
					verify(kafkaPaymentSettlementCommandEventPublisher, times(1)).publish(settlementCommandEventCaptor
							.capture());
					final var settlementCommand = settlementCommandEventCaptor.getValue();

					Assertions.assertEquals(session.getId()
							.getValue(), settlementCommand.getPaymentSessionId());
					Assertions.assertEquals(session.getTenantId()
							.getValue(), settlementCommand.getTenantId());
					Assertions.assertEquals(PaymentTransactionStatus.SUCCEEDED, settlementCommand
							.getTransactionStatus());
					Assertions.assertTrue(settlementCommand.getIsAsync());
					Assertions.assertEquals(Instant.ofEpochSecond(stripeObject.getCreated())
							.toEpochMilli(), settlementCommand.getPaidAt());

					final var actualSettlementData = objectMapper.convertValue(settlementCommand.getSettlementData(),
							StripePaymentSettlementData.class);
					Assertions.assertEquals(PaymentProcessorId.STRIPE_CONNECT_STANDARD_WEB_PAYMENT, actualSettlementData
							.getProcessorId());
					Assertions.assertEquals(stripeObject.getPaymentIntent(), actualSettlementData
							.getStripePaymentIntent());
					Assertions.assertEquals(StripeEventConstants.CHECKOUT_SESSION_ASYNC_PAYMENT_SUCCEEDED,
							actualSettlementData.getStripeEventType());
					Assertions.assertEquals(event.getId(), actualSettlementData.getStripeEventId());
				});

	}

	@Test
	void testActivateConnectedAccountSuccessfullyWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);

		final var paymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Stripe Connect Payment Method 10000")
				.processorId(PaymentProcessorId.STRIPE_CONNECT_STANDARD_WEB_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.CUSTOMER_PORTAL))
				.isActive(Boolean.FALSE)
				.description("Stripe Connect Payment Description 10000")
				.paymentInstruction("Stripe Connect Payment Instruction 10000")
				.surchargeRate(new BigDecimal("0.01"))
				.fixedSurcharge(100L)
				.currencyCode("SGD")
				.surchargeTitle("Stripe Connect Payment Title 10000")
				.build());

		final var accountRequest = ClientPaymentConnectedAccount.builder()
				.clientExternalId("str-conn-ex-id-" + paymentMethod.getId()
						.getValue())
				.createdAt(Instant.now())
				.build();

		final var accountLinkRequest = ClientPaymentConnectedAccountLink.builder()
				.redirectUrl("https://stripe.connect.com/onboard-url")
				.expiredAt(Instant.now())
				.createdAt(Instant.now())
				.build();

		when(stripeConnectApiClient.createStripeConnectedAccount(any())).thenReturn(accountRequest);
		when(stripeConnectApiClient.createStripeConnectedAccountLink(any())).thenReturn(accountLinkRequest);

		final var connectedAccount = connectedAccountCommandService.connect(tenantId, ConnectPaymentAccountCommand
				.builder()
				.paymentMethodId(paymentMethod.getId())
				.returnUrl("https://example.com/returnUrl")
				.refreshUrl("https://example.com/refreshUrl")
				.build())
				.getConnectedAccount();

		final var event = new Event();
		event.setId("event-" + paymentMethod.getId());
		event.setAccount(connectedAccount.getClientExternalId());
		event.setType(StripeConnectEventConstants.ACCOUNT_UPDATED);
		event.setApiVersion(Stripe.API_VERSION);
		when(stripeConnectApiClient.verifyAndDecode(any(), any(), any())).thenReturn(event);

		final var stripeObject = new Account();
		stripeObject.setId(connectedAccount.getClientExternalId());
		stripeObject.setDetailsSubmitted(true);
		stripeObject.setCreated(Instant.now()
				.getEpochSecond());
		stripeObject.setEmail("<EMAIL>");
		stripeObject.setMetadata(objectMapper.convertValue(StripeConnectedAccountMetadata.builder()
				.tenantId(tenantId.getValue())
				.build(), new TypeReference<>() {
				}));

		when(stripeConnectApiClient.getValidStripeObject(any())).thenReturn(stripeObject);

		when(webhookEndpointManager.getWebhookEndpointConfig(any(), any())).thenReturn(Optional.of(
				PaymentWebhookClientData.builder()
						.build()));

		final var body = "mocked event body";

		// Act & Assert
		webClient.post()
				.uri("/webhooks/payment/stripe-connect/{endpointId}?event="
						+ StripeConnectEventConstants.ACCOUNT_UPDATED, 12345L)
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(body)
				.headers(header -> HeaderGenerator.generateStripeHeader(header, "ABC"))
				.exchange()
				.expectStatus()
				.isOk()
				.expectBody(Void.class)
				.consumeWith(response -> {
					final var actualConnectedAccount = connectedAccountQueryService.getPaymentConnectedAccount(tenantId,
							connectedAccount.getClientExternalId());

					Assertions.assertEquals(stripeObject.getEmail(), actualConnectedAccount.getOwnerEmail());
					Assertions.assertEquals(stripeObject.getId(), actualConnectedAccount.getClientExternalId());
					Assertions.assertEquals(PaymentProcessorId.STRIPE_CONNECT_STANDARD_WEB_PAYMENT,
							actualConnectedAccount.getProcessorId());
					Assertions.assertTrue(actualConnectedAccount.isActive());
				});
	}

}
