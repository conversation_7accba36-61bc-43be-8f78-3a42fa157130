/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.http.controller.transactions;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stripe.model.checkout.Session;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.test.utils.GenerateHttpHeader;
import com.styl.pacific.common.test.utils.HeaderGenerator;
import com.styl.pacific.domain.dto.CurrencyResponse;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.enums.PaymentTransactionStatus;
import com.styl.pacific.domain.range.LongDateTimeRange;
import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.payment.messaging.offline.publisher.kafka.KafkaOfflineTransactionCreatedEventPublisher;
import com.styl.pacific.payment.rest.sessions.mapper.PaymentSessionRequestMapper;
import com.styl.pacific.payment.rest.transactions.mapper.PaymentOfflineTransactionRequestMapper;
import com.styl.pacific.payment.service.config.IntegrationTestConfiguration;
import com.styl.pacific.payment.service.config.PaymentConnectedIntegrationSupporter;
import com.styl.pacific.payment.service.config.PaymentIntegrationTestContainer;
import com.styl.pacific.payment.service.config.PaymentMethodInitIntegrationSupporter;
import com.styl.pacific.payment.service.config.PaymentSessionInitIntegrationSupporter;
import com.styl.pacific.payment.service.config.PaymentSettlementInitIntegrationSupporter;
import com.styl.pacific.payment.service.core.features.accounts.PaymentConnectedAccountCommandService;
import com.styl.pacific.payment.service.core.features.methods.PaymentMethodCommandService;
import com.styl.pacific.payment.service.core.features.methods.request.UpsertPaymentMethodCommand;
import com.styl.pacific.payment.service.core.features.sessions.PaymentSessionCommandService;
import com.styl.pacific.payment.service.core.features.sessions.request.CreatePaymentSessionCommand;
import com.styl.pacific.payment.service.core.features.settlements.PaymentSettlementCommandService;
import com.styl.pacific.payment.service.core.features.settlements.request.SettlePaymentSessionCommand;
import com.styl.pacific.payment.service.core.features.transactions.PaymentOfflineTransactionService;
import com.styl.pacific.payment.service.core.features.transactions.PaymentTransactionQueryService;
import com.styl.pacific.payment.service.data.access.clients.tenants.TenantClient;
import com.styl.pacific.payment.service.integration.ewallet.clients.wallets.WalletClient;
import com.styl.pacific.payment.service.integration.ewallet.clients.wallets.WalletTransactionClient;
import com.styl.pacific.payment.service.integration.ewallet.settlements.valueobject.EWalletPaymentSettlementData;
import com.styl.pacific.payment.service.integration.netsterminal.config.NetsTerminalPaymentMethodConfiguration;
import com.styl.pacific.payment.service.integration.stripe.clients.StripeApiClient;
import com.styl.pacific.payment.service.integration.stripe.config.StripePaymentMethodConfiguration;
import com.styl.pacific.payment.service.integration.stripe.processor.valueobject.StripePaymentSessionProcessorData;
import com.styl.pacific.payment.service.integration.stripe.sessions.valueobject.StripePaymentSessionMetadata;
import com.styl.pacific.payment.service.integration.stripeconnect.clients.StripeConnectApiClient;
import com.styl.pacific.payment.service.integration.stripeconnect.config.StripeConnectPaymentMethodConfiguration;
import com.styl.pacific.payment.shared.enums.AcceptedApplication;
import com.styl.pacific.payment.shared.enums.NetsFamilyCardType;
import com.styl.pacific.payment.shared.enums.PaymentTransactionType;
import com.styl.pacific.payment.shared.http.methods.request.stripe.StripePaymentMethodConfigurationRequest;
import com.styl.pacific.payment.shared.http.sessions.request.stripe.StripeWebPaymentSessionParamsRequest;
import com.styl.pacific.payment.shared.http.sessions.request.stripeconnect.StripeConnectWebPaymentSessionParamsRequest;
import com.styl.pacific.payment.shared.http.sessions.response.stripe.StripePaymentSessionDataResponse;
import com.styl.pacific.payment.shared.http.sessions.response.stripe.StripeWebPaymentSessionParamsResponse;
import com.styl.pacific.payment.shared.http.sessions.response.stripeconnect.StripeConnectPaymentSessionDataResponse;
import com.styl.pacific.payment.shared.http.sessions.response.stripeconnect.StripeConnectPaymentSessionParamsResponse;
import com.styl.pacific.payment.shared.http.settlement.request.SettlePaymentSessionRequest;
import com.styl.pacific.payment.shared.http.settlement.request.SimplePaymentSettlementRequest;
import com.styl.pacific.payment.shared.http.settlement.request.nets.NetsPaymentSettlementSessionRequest;
import com.styl.pacific.payment.shared.http.settlement.response.SimplePaymentSettlementResponse;
import com.styl.pacific.payment.shared.http.settlement.response.nets.NetsPaymentSettlementResponse;
import com.styl.pacific.payment.shared.http.transactions.request.CreateOfflineTransactionRequest;
import com.styl.pacific.payment.shared.http.transactions.request.FilterPaymentTransactionRequest;
import com.styl.pacific.payment.shared.http.transactions.request.QueryPaymentTransactionPaginationRequest;
import com.styl.pacific.payment.shared.http.transactions.response.PaymentTransactionResponse;
import com.styl.pacific.payment.spi.processors.methods.valueobject.SimplePaymentMethodConfiguration;
import com.styl.pacific.payment.spi.processors.sessions.valueobject.PaymentSessionParams;
import com.styl.pacific.wallet.service.enums.TransactionCategory;
import com.styl.pacific.wallet.service.enums.TransactionType;
import com.styl.pacific.wallet.service.requests.transaction.GetWalletTransactionRequest;
import com.styl.pacific.wallet.service.requests.wallet.CreateCreditOfflineTransactionRequest;
import com.styl.pacific.wallet.service.responses.transaction.WalletTransactionResponse;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.commons.util.StringUtils;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.util.LinkedMultiValueMap;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = IntegrationTestConfiguration.class)
class PaymentTransactionControllerIntegrationTest extends PaymentIntegrationTestContainer {

	@MockitoBean
	private StripeApiClient stripeApiClient;

	@MockitoBean
	private StripeConnectApiClient stripeConnectApiClient;

	@MockitoBean
	private TenantClient tenantClient;

	@MockitoBean
	private WalletTransactionClient walletTransactionClient;

	@Autowired
	private ObjectMapper objectMapper;

	@Autowired
	private PaymentMethodCommandService methodCommandService;

	@Autowired
	private PaymentSessionCommandService sessionCommandService;

	@Autowired
	private PaymentSettlementCommandService settlementCommandService;

	@Autowired
	private PaymentTransactionQueryService transactionQueryService;

	@Autowired
	private PaymentConnectedAccountCommandService connectedAccountCommandService;

	@Autowired
	private PaymentOfflineTransactionService offlineTransactionService;

	@MockitoBean
	private KafkaOfflineTransactionCreatedEventPublisher kafkaOfflineTransactionCreatedEventPublisher;

	@MockitoBean
	private WalletClient walletClient;

	private PaymentMethodInitIntegrationSupporter paymentMethodSupporter;

	private PaymentSessionInitIntegrationSupporter paymentSessionSupporter;

	private PaymentSettlementInitIntegrationSupporter paymentSettlementSupporter;

	private PaymentConnectedIntegrationSupporter connectedIntegrationSupporter;

	@BeforeEach
	void setUp() {
		paymentMethodSupporter = new PaymentMethodInitIntegrationSupporter(tenantClient, methodCommandService);
		paymentSessionSupporter = new PaymentSessionInitIntegrationSupporter(stripeApiClient, stripeConnectApiClient,
				objectMapper, sessionCommandService);
		paymentSettlementSupporter = new PaymentSettlementInitIntegrationSupporter(settlementCommandService);
		connectedIntegrationSupporter = new PaymentConnectedIntegrationSupporter(methodCommandService,
				connectedAccountCommandService, stripeConnectApiClient);

	}

	@Test
	void testGetStripePaymentTransactionWhenValidRequest() {
		final var tenantId = new TenantId(10_000L);
		final var stripePaymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Stripe Payment Method 10000")
				.processorId(PaymentProcessorId.STRIPE_WEB_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.CUSTOMER_PORTAL))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Stripe Payment Description 10000")
				.paymentInstruction("Stripe Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeRate(BigDecimal.valueOf(0.1234))
				.surchargeTitle("Stripe Payment Title 10000")
				.processorConfig(SimplePaymentMethodConfiguration.builder()
						.configs(objectMapper.convertValue(StripePaymentMethodConfigurationRequest.builder()
								.apiKey("API KEY 1234")
								.build(), new TypeReference<>() {
								}))
						.build())
				.build());

		final var stripeParams = StripeWebPaymentSessionParamsRequest.builder()
				.cancelRedirectURL("https://www.cancel.org/current")
				.successRedirectURL("https://www.success.org/current")
				.build();

		final var session = paymentSessionSupporter.initStripePaymentSession(tenantId, CreatePaymentSessionCommand
				.builder()
				.paymentProcessorId(stripePaymentMethod.getProcessorId())
				.paymentMethodId(stripePaymentMethod.getId())
				.description("Payment Order #1234")
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(2496L)
				.netAmount(2222L)
				.fee(274L)
				.currencyCode("SGD")
				.customerId("1")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				.deviceId("deviceId")
				.expiredInMilliseconds(Duration.ofMinutes(16)
						.toMillis())
				.processorParams(PaymentSessionParams.builder()
						.params(objectMapper.convertValue(stripeParams, new TypeReference<>() {
						}))
						.build())
				.build());

		final var sessionData = objectMapper.convertValue(session.getSessionData()
				.getData(), StripePaymentSessionProcessorData.class);

		final var metadata = StripePaymentSessionMetadata.builder()
				.paymentSessionId(session.getId()
						.getValue())
				.paymentReference(session.getPaymentReference())
				.build();

		final var stripeSession = new Session();
		stripeSession.setId(sessionData.getStripeSessionId());
		stripeSession.setPaymentStatus("paid");
		stripeSession.setPaymentIntent("payment_intent_001");
		stripeSession.setMetadata(objectMapper.convertValue(metadata, new TypeReference<>() {
		}));

		when(stripeApiClient.getCheckoutPaymentSession(any(StripePaymentMethodConfiguration.class), any(String.class)))
				.thenReturn(stripeSession);

		final var now = Instant.now();
		final var transaction = paymentSettlementSupporter.settleTransaction(tenantId, SettlePaymentSessionCommand
				.builder()
				.paidAt(now)
				.transactionNumber("txn no 09")
				.transactionStatus(PaymentTransactionStatus.SUCCEEDED)
				.paymentSessionId(session.getId())
				.isAsync(Boolean.FALSE)
				.build());

		// Act & Assert
		webClient.get()
				.uri("/api/payment/transactions/{transactionId}", transaction.getId()
						.getValue())
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isOk()
				.expectBody(PaymentTransactionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(stripePaymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentProcessorId.STRIPE_WEB_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals("1", actual.getCustomerId());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(transaction.getAmount(), actual.getAmount());
					Assertions.assertEquals(transaction.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(transaction.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(transaction.getDescription(), actual.getDescription());
					Assertions.assertEquals(transaction.getDeviceId(), actual.getDeviceId());
					Assertions.assertEquals(transaction.getTransactionNumber(), actual.getTransactionNumber());
					Assertions.assertEquals(transaction.getPaidAt()
							.toEpochMilli(), actual.getPaidAt()
									.toEpochMilli());

					Assertions.assertEquals(stripeSession.getPaymentIntent(), actual.getTransactionNumber());

					Assertions.assertEquals(PaymentTransactionStatus.SUCCEEDED, actual.getTransactionStatus());

					Assertions.assertEquals(session.getAmount(), actual.getAmount());
					Assertions.assertEquals(session.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(session.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(session.getDeviceId(), actual.getDeviceId());

					Assertions.assertEquals(Boolean.FALSE, actual.getIsAsync());
					Assertions.assertEquals(session.getDescription(), actual.getDescription());

					final var actualProcessorParams = (StripeWebPaymentSessionParamsResponse) actual
							.getProcessorParams();
					Assertions.assertNotNull(actualProcessorParams);
					Assertions.assertEquals(stripeParams.getSuccessRedirectURL(), actualProcessorParams
							.getSuccessRedirectURL());
					Assertions.assertEquals(stripeParams.getCancelRedirectURL(), actualProcessorParams
							.getCancelRedirectURL());
					Assertions.assertEquals(PaymentProcessorId.STRIPE_WEB_PAYMENT, actualProcessorParams
							.getProcessorId());

					final var actualProcessorData = (StripePaymentSessionDataResponse) actual.getSessionData();
					Assertions.assertNotNull(actualProcessorData);
					Assertions.assertEquals(stripeSession.getId(), actualProcessorData.getStripeSessionId());
					Assertions.assertEquals("https://checkout.stripe.com/c/pay/cs_test", actualProcessorData
							.getPaymentUrl());
				});
	}

	@Test
	void testGetStripeConnectPaymentTransactionWhenValidRequest() {
		final var tenantId = new TenantId(10_000L);
		final var stripePaymentMethod = connectedIntegrationSupporter.connect(paymentMethodSupporter.initPaymentMethod(
				tenantId, UpsertPaymentMethodCommand.builder()
						.displayName("Stripe Connect Payment Method 10000")
						.processorId(PaymentProcessorId.STRIPE_CONNECT_STANDARD_WEB_PAYMENT)
						.acceptedApplications(Set.of(AcceptedApplication.CUSTOMER_PORTAL))
						.iconPath("http://localhost/image.jpg")
						.description("Stripe Payment Description 10000")
						.paymentInstruction("Stripe Payment Instruction 10000")
						.surchargeRate(BigDecimal.valueOf(0.123))
						.fixedSurcharge(10L)
						.currencyCode("SGD")
						.surchargeTitle("Stripe Payment Title 10000")
						.build()));

		final var stripeParams = StripeConnectWebPaymentSessionParamsRequest.builder()
				.cancelRedirectURL("https://www.cancel.org/current")
				.successRedirectURL("https://www.success.org/current")
				.build();

		final var session = paymentSessionSupporter.initStripeConnectPaymentSession(tenantId,
				CreatePaymentSessionCommand.builder()
						.paymentProcessorId(stripePaymentMethod.getProcessorId())
						.paymentMethodId(stripePaymentMethod.getId())
						.description("Payment Order #1234")
						.paymentReference("Payment Reference 01")
						.transactionType(PaymentTransactionType.PURCHASE)
						.amount(2505L)
						.netAmount(2222L)
						.fee(283L)
						.currencyCode("SGD")
						.customerId("1")
						.customerEmail("<EMAIL>")
						.customerName("Customer Name 001")
						.systemSource("System Source 01")
						.merchantName("Merchant Name")
						.deviceId("deviceId")
						.expiredInMilliseconds(Duration.ofMinutes(16)
								.toMillis())
						.processorParams(PaymentSessionParams.builder()
								.params(objectMapper.convertValue(stripeParams, new TypeReference<>() {
								}))
								.build())
						.build());

		final var sessionData = objectMapper.convertValue(session.getSessionData()
				.getData(), StripePaymentSessionProcessorData.class);

		final var metadata = StripePaymentSessionMetadata.builder()
				.paymentSessionId(session.getId()
						.getValue())
				.paymentReference(session.getPaymentReference())
				.build();

		final var stripeSession = new Session();
		stripeSession.setId(sessionData.getStripeSessionId());
		stripeSession.setPaymentStatus("paid");
		stripeSession.setPaymentIntent("payment_intent_id_01");
		stripeSession.setExpiresAt(Instant.now()
				.plus(16, ChronoUnit.MINUTES)
				.getEpochSecond());
		stripeSession.setMetadata(objectMapper.convertValue(metadata, new TypeReference<>() {
		}));

		when(stripeConnectApiClient.getCheckoutPaymentSession(any(StripeConnectPaymentMethodConfiguration.class), eq(
				stripeSession.getId()))).thenReturn(stripeSession);
		final var now = Instant.now();
		final var transaction = paymentSettlementSupporter.settleTransaction(tenantId, SettlePaymentSessionCommand
				.builder()
				.paidAt(now)
				.transactionNumber("txn no 12345")
				.transactionStatus(PaymentTransactionStatus.SUCCEEDED)
				.paymentSessionId(session.getId())
				.isAsync(Boolean.FALSE)
				.build());

		// Act & Assert
		webClient.get()
				.uri("/api/payment/transactions/{transactionId}", transaction.getId()
						.getValue())
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isOk()
				.expectBody(PaymentTransactionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(stripePaymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentProcessorId.STRIPE_CONNECT_STANDARD_WEB_PAYMENT, actual
							.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals("1", actual.getCustomerId());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(transaction.getAmount(), actual.getAmount());
					Assertions.assertEquals(transaction.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(transaction.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(transaction.getDescription(), actual.getDescription());
					Assertions.assertEquals(transaction.getDeviceId(), actual.getDeviceId());
					Assertions.assertEquals(transaction.getTransactionNumber(), actual.getTransactionNumber());
					Assertions.assertEquals(transaction.getPaidAt()
							.toEpochMilli(), actual.getPaidAt()
									.toEpochMilli());

					Assertions.assertEquals(stripeSession.getPaymentIntent(), actual.getTransactionNumber());

					Assertions.assertEquals(PaymentTransactionStatus.SUCCEEDED, actual.getTransactionStatus());

					Assertions.assertEquals(session.getAmount(), actual.getAmount());
					Assertions.assertEquals(session.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(session.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(session.getDeviceId(), actual.getDeviceId());

					Assertions.assertEquals(Boolean.FALSE, actual.getIsAsync());
					Assertions.assertEquals(session.getDescription(), actual.getDescription());

					final var actualProcessorParams = (StripeConnectPaymentSessionParamsResponse) actual
							.getProcessorParams();
					Assertions.assertNotNull(actualProcessorParams);
					Assertions.assertEquals(stripeParams.getSuccessRedirectURL(), actualProcessorParams
							.getSuccessRedirectURL());
					Assertions.assertEquals(stripeParams.getCancelRedirectURL(), actualProcessorParams
							.getCancelRedirectURL());
					Assertions.assertEquals(PaymentProcessorId.STRIPE_CONNECT_STANDARD_WEB_PAYMENT,
							actualProcessorParams.getProcessorId());

					final var actualProcessorData = (StripeConnectPaymentSessionDataResponse) actual.getSessionData();
					Assertions.assertNotNull(actualProcessorData);
					Assertions.assertEquals(stripeSession.getId(), actualProcessorData.getStripeSessionId());
					Assertions.assertEquals("https://checkout.stripe.com/c/pay/cs_test", actualProcessorData
							.getPaymentUrl());
				});
	}

	@Test
	void testGetOfflinePaymentTransactionSuccessfullyWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);
		final var now = Instant.now();
		final var paymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Offline Payment Method 10000")
				.processorId(PaymentProcessorId.OFFLINE_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.POS))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Offline Payment Description 10000")
				.paymentInstruction("Cash Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeRate(BigDecimal.valueOf(0.123))
				.surchargeTitle("Offline Payment Title 10000")
				.build());

		final var session = paymentSessionSupporter.initStripePaymentSession(tenantId, CreatePaymentSessionCommand
				.builder()
				.paymentProcessorId(paymentMethod.getProcessorId())
				.paymentMethodId(paymentMethod.getId())
				.description("Payment Order #1234")
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(2495L)
				.netAmount(2222L)
				.fee(273L)
				.currencyCode("SGD")
				.customerId("1")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				.expiredInMilliseconds(Duration.ofMinutes(16)
						.toMillis())
				.build());

		final var transaction = paymentSettlementSupporter.settleTransaction(tenantId,
				PaymentSessionRequestMapper.INSTANCE.toSettlePaymentSessionCommand(session.getId()
						.getValue(), SettlePaymentSessionRequest.builder()
								.transactionStatus(PaymentTransactionStatus.SUCCEEDED)
								.transactionNumber("Transaction Number 001")
								.paidAt(now)
								.settlementData(SimplePaymentSettlementRequest.builder()
										.processorId(PaymentProcessorId.OFFLINE_PAYMENT)
										.data(Map.of("key1", "val1"))
										.build())
								.extraSettlementData(Map.of("key2", "val2"))
								.build(), Boolean.FALSE, objectMapper));

		// Act & Assert
		webClient.get()
				.uri("/api/payment/transactions/{transactionId}", transaction.getId()
						.getValue())
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isOk()
				.expectBody(PaymentTransactionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(paymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentProcessorId.OFFLINE_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals("1", actual.getCustomerId());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(transaction.getAmount(), actual.getAmount());
					Assertions.assertEquals(transaction.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(transaction.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(transaction.getDescription(), actual.getDescription());
					Assertions.assertEquals(transaction.getDeviceId(), actual.getDeviceId());
					Assertions.assertEquals(transaction.getTransactionNumber(), actual.getTransactionNumber());
					Assertions.assertEquals(transaction.getPaidAt()
							.toEpochMilli(), actual.getPaidAt()
									.toEpochMilli());

					Assertions.assertEquals(PaymentTransactionStatus.SUCCEEDED, actual.getTransactionStatus());

					Assertions.assertEquals(session.getAmount(), actual.getAmount());
					Assertions.assertEquals(session.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(session.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(session.getDeviceId(), actual.getDeviceId());

					Assertions.assertEquals(Boolean.FALSE, actual.getIsAsync());
					Assertions.assertEquals(session.getDescription(), actual.getDescription());

					final var settlementResponse = (SimplePaymentSettlementResponse) actual.getSettlementData();
					Assertions.assertNotNull(settlementResponse);
					Assertions.assertEquals("val1", settlementResponse.getData()
							.get("key1"));

					final var extraSettlementResponse = actual.getExtraSettlementData();
					Assertions.assertNotNull(extraSettlementResponse);
					Assertions.assertEquals("val2", extraSettlementResponse.get("key2"));
				});
	}

	@Test
	void testGetCashPaymentTransactionSuccessfullyWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);
		final var now = Instant.now();
		final var paymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Cash Payment Method 10000")
				.processorId(PaymentProcessorId.CASH_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.POS))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Cash Payment Description 10000")
				.paymentInstruction("Cash Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeRate(BigDecimal.valueOf(0.123))
				.surchargeTitle("Offline Payment Title 10000")
				.build());

		final var session = paymentSessionSupporter.initStripePaymentSession(tenantId, CreatePaymentSessionCommand
				.builder()
				.paymentProcessorId(paymentMethod.getProcessorId())
				.paymentMethodId(paymentMethod.getId())
				.description("Payment Order #1234")
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(2495L)
				.netAmount(2222L)
				.fee(273L)
				.currencyCode("SGD")
				.customerId("1")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				.expiredInMilliseconds(Duration.ofMinutes(16)
						.toMillis())
				.build());

		final var transaction = paymentSettlementSupporter.settleTransaction(tenantId,
				PaymentSessionRequestMapper.INSTANCE.toSettlePaymentSessionCommand(session.getId()
						.getValue(), SettlePaymentSessionRequest.builder()
								.transactionStatus(PaymentTransactionStatus.SUCCEEDED)
								.transactionNumber("Transaction Number 001")
								.paidAt(now)
								.settlementData(SimplePaymentSettlementRequest.builder()
										.processorId(PaymentProcessorId.CASH_PAYMENT)
										.data(Map.of("key1", "val1"))
										.build())
								.extraSettlementData(Map.of("key2", "val2"))
								.build(), Boolean.FALSE, objectMapper));

		// Act & Assert
		webClient.get()
				.uri("/api/payment/transactions/{transactionId}", transaction.getId()
						.getValue())
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isOk()
				.expectBody(PaymentTransactionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(paymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentProcessorId.CASH_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals("1", actual.getCustomerId());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(transaction.getAmount(), actual.getAmount());
					Assertions.assertEquals(transaction.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(transaction.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(transaction.getDescription(), actual.getDescription());
					Assertions.assertEquals(transaction.getDeviceId(), actual.getDeviceId());
					Assertions.assertEquals(transaction.getTransactionNumber(), actual.getTransactionNumber());
					Assertions.assertEquals(transaction.getPaidAt()
							.toEpochMilli(), actual.getPaidAt()
									.toEpochMilli());

					Assertions.assertEquals(PaymentTransactionStatus.SUCCEEDED, actual.getTransactionStatus());

					Assertions.assertEquals(session.getAmount(), actual.getAmount());
					Assertions.assertEquals(session.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(session.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(session.getDeviceId(), actual.getDeviceId());

					Assertions.assertEquals(Boolean.FALSE, actual.getIsAsync());
					Assertions.assertEquals(session.getDescription(), actual.getDescription());

					final var settlementResponse = (SimplePaymentSettlementResponse) actual.getSettlementData();
					Assertions.assertNotNull(settlementResponse);
					Assertions.assertEquals("val1", settlementResponse.getData()
							.get("key1"));

					final var extraSettlementResponse = actual.getExtraSettlementData();
					Assertions.assertNotNull(extraSettlementResponse);
					Assertions.assertEquals("val2", extraSettlementResponse.get("key2"));
				});
	}

	@Test
	void testGetNetsPaymentTransactionSuccessfullyWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);
		final var now = Instant.now();
		final var paymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Nets Payment Method 10000")
				.processorId(PaymentProcessorId.NETS_TERMINAL_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.POS))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Nets Payment Description 10000")
				.paymentInstruction("Nets Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeRate(BigDecimal.valueOf(0.123))
				.surchargeTitle("Nets Payment Title 10000")
				.processorConfig(SimplePaymentMethodConfiguration.builder()
						.configs(objectMapper.convertValue(NetsTerminalPaymentMethodConfiguration.builder()
								.netsFamilyCardType(NetsFamilyCardType.CREDIT_CARD)
								.build(), new TypeReference<>() {
								}))
						.build())
				.build());

		final var session = paymentSessionSupporter.initStripePaymentSession(tenantId, CreatePaymentSessionCommand
				.builder()
				.paymentProcessorId(paymentMethod.getProcessorId())
				.paymentMethodId(paymentMethod.getId())
				.description("Payment Order #1234")
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(2495L)
				.netAmount(2222L)
				.fee(273L)
				.currencyCode("SGD")
				.customerId("1")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				.expiredInMilliseconds(Duration.ofMinutes(16)
						.toMillis())
				.build());

		final var settlementData = NetsPaymentSettlementSessionRequest.builder()
				.approvalCode("123")
				.merchantId("123")
				.terminalId("terminalId")
				.stan("stan")
				.cardName("cardName")
				.canNumber("canNumber")
				.balance("balance")
				.newBalance("newBalance")
				.purchaseAmount("purchaseAmount")
				.batchNumber("batchNumber")
				.invoiceNumber("invoiceNumber")
				.rrn("rrn")
				.tvr("tvr")
				.entryType("entryType")
				.issuerName("issuerName")
				.date("date")
				.time("time")
				.build();

		final var transaction = paymentSettlementSupporter.settleTransaction(tenantId,
				PaymentSessionRequestMapper.INSTANCE.toSettlePaymentSessionCommand(session.getId()
						.getValue(), SettlePaymentSessionRequest.builder()
								.transactionStatus(PaymentTransactionStatus.SUCCEEDED)
								.transactionNumber("Transaction Number 00001")
								.paidAt(now)
								.settlementData(settlementData)
								.extraSettlementData(Map.of("key2", "val2"))
								.build(), Boolean.FALSE, objectMapper));

		// Act & Assert
		webClient.get()
				.uri("/api/payment/transactions/{transactionId}", transaction.getId()
						.getValue())
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isOk()
				.expectBody(PaymentTransactionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(paymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentProcessorId.NETS_TERMINAL_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals("1", actual.getCustomerId());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(transaction.getAmount(), actual.getAmount());
					Assertions.assertEquals(transaction.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(transaction.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(transaction.getDescription(), actual.getDescription());
					Assertions.assertEquals(transaction.getDeviceId(), actual.getDeviceId());
					Assertions.assertEquals(transaction.getTransactionNumber(), actual.getTransactionNumber());
					Assertions.assertEquals(transaction.getPaidAt()
							.toEpochMilli(), actual.getPaidAt()
									.toEpochMilli());
					Assertions.assertEquals(PaymentTransactionStatus.SUCCEEDED, actual.getTransactionStatus());
					Assertions.assertEquals(session.getAmount(), actual.getAmount());
					Assertions.assertEquals(session.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(session.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(Boolean.FALSE, actual.getIsAsync());
					Assertions.assertEquals(session.getDescription(), actual.getDescription());
					Assertions.assertEquals(session.getDeviceId(), actual.getDeviceId());

					Assertions.assertEquals(PaymentProcessorId.NETS_TERMINAL_PAYMENT, actual.getSettlementData()
							.getProcessorId());
					final var actualSettlementData = (NetsPaymentSettlementResponse) actual.getSettlementData();
					Assertions.assertEquals(settlementData.getApprovalCode(), actualSettlementData.getApprovalCode());
					Assertions.assertEquals(settlementData.getMerchantId(), actualSettlementData.getMerchantId());
					Assertions.assertEquals(settlementData.getTerminalId(), actualSettlementData.getTerminalId());
					Assertions.assertEquals(settlementData.getStan(), actualSettlementData.getStan());
					Assertions.assertEquals(settlementData.getCardName(), actualSettlementData.getCardName());
					Assertions.assertEquals(settlementData.getCanNumber(), actualSettlementData.getCanNumber());
					Assertions.assertEquals(settlementData.getBalance(), actualSettlementData.getBalance());
					Assertions.assertEquals(settlementData.getNewBalance(), actualSettlementData.getNewBalance());
					Assertions.assertEquals(settlementData.getPurchaseAmount(), actualSettlementData
							.getPurchaseAmount());
					Assertions.assertEquals(settlementData.getBatchNumber(), actualSettlementData.getBatchNumber());
					Assertions.assertEquals(settlementData.getInvoiceNumber(), actualSettlementData.getInvoiceNumber());
					Assertions.assertEquals(settlementData.getRrn(), actualSettlementData.getRrn());
					Assertions.assertEquals(settlementData.getTvr(), actualSettlementData.getTvr());
					Assertions.assertEquals(settlementData.getEntryType(), actualSettlementData.getEntryType());
					Assertions.assertEquals(settlementData.getIssuerName(), actualSettlementData.getIssuerName());
					Assertions.assertEquals(settlementData.getDate(), actualSettlementData.getDate());
					Assertions.assertEquals(settlementData.getTime(), actualSettlementData.getTime());

					final var extraSettlementResponse = actual.getExtraSettlementData();
					Assertions.assertNotNull(extraSettlementResponse);
					Assertions.assertEquals("val2", extraSettlementResponse.get("key2"));
				});
	}

	@Test
	void testGetEWalletPaymentTransactionSuccessfullyWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);
		final var now = Instant.now();
		final var paymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Nets Payment Method 10000")
				.processorId(PaymentProcessorId.E_WALLET_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.POS))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Nets Payment Description 10000")
				.paymentInstruction("Nets Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeRate(BigDecimal.valueOf(0.123))
				.surchargeTitle("Nets Payment Title 10000")
				.build());

		final var session = paymentSessionSupporter.initStripePaymentSession(tenantId, CreatePaymentSessionCommand
				.builder()
				.paymentProcessorId(paymentMethod.getProcessorId())
				.paymentMethodId(paymentMethod.getId())
				.description("Payment Order #1234")
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(2495L)
				.netAmount(2222L)
				.fee(273L)
				.currencyCode("SGD")
				.customerId("1")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				.expiredInMilliseconds(Duration.ofMinutes(16)
						.toMillis())
				.build());

		when(walletTransactionClient.getWalletTransaction(any(GetWalletTransactionRequest.class))).thenReturn(
				WalletTransactionResponse.builder()
						.transactionId("wallet-transaction-id-1234")
						.amount(BigInteger.valueOf(2495))
						.build());

		final var transaction = paymentSettlementSupporter.settleTransaction(tenantId,
				PaymentSessionRequestMapper.INSTANCE.toSettlePaymentSessionCommand(session.getId()
						.getValue(), SettlePaymentSessionRequest.builder()
								.transactionStatus(PaymentTransactionStatus.SUCCEEDED)
								.transactionNumber("Transaction Number 00001")
								.paidAt(now)
								.settlementData(SimplePaymentSettlementRequest.builder()
										.processorId(PaymentProcessorId.E_WALLET_PAYMENT)
										.data(Map.of("key1", "val1"))
										.build())
								.extraSettlementData(Map.of("key2", "val2"))
								.build(), Boolean.FALSE, objectMapper));

		// Act & Assert
		webClient.get()
				.uri("/api/payment/transactions/{transactionId}", transaction.getId()
						.getValue())
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isOk()
				.expectBody(PaymentTransactionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(paymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentProcessorId.E_WALLET_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals("1", actual.getCustomerId());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(transaction.getAmount(), actual.getAmount());
					Assertions.assertEquals(transaction.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(transaction.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(transaction.getDescription(), actual.getDescription());
					Assertions.assertEquals(transaction.getDeviceId(), actual.getDeviceId());
					Assertions.assertEquals(transaction.getTransactionNumber(), actual.getTransactionNumber());
					Assertions.assertEquals(transaction.getPaidAt()
							.toEpochMilli(), actual.getPaidAt()
									.toEpochMilli());
					Assertions.assertEquals(PaymentTransactionStatus.SUCCEEDED, actual.getTransactionStatus());
					Assertions.assertEquals(session.getAmount(), actual.getAmount());
					Assertions.assertEquals(session.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(session.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(Boolean.FALSE, actual.getIsAsync());
					Assertions.assertEquals(session.getDescription(), actual.getDescription());
					Assertions.assertEquals(session.getDeviceId(), actual.getDeviceId());

					Assertions.assertEquals(PaymentProcessorId.E_WALLET_PAYMENT, actual.getSettlementData()
							.getProcessorId());

					final var extraSettlementResponse = actual.getExtraSettlementData();
					Assertions.assertNotNull(extraSettlementResponse);
					Assertions.assertEquals("val2", extraSettlementResponse.get("key2"));
				});
	}

	@Test
	void testGetNetsPaymentTransactionSuccessfullyWithFailureStatusWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);
		final var now = Instant.now();
		final var paymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Nets Payment Method 10000")
				.processorId(PaymentProcessorId.NETS_TERMINAL_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.POS))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Nets Payment Description 10000")
				.paymentInstruction("Nets Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeRate(BigDecimal.valueOf(0.123))
				.surchargeTitle("Nets Payment Title 10000")
				.processorConfig(SimplePaymentMethodConfiguration.builder()
						.configs(objectMapper.convertValue(NetsTerminalPaymentMethodConfiguration.builder()
								.netsFamilyCardType(NetsFamilyCardType.NETS_CARD)
								.build(), new TypeReference<>() {
								}))
						.build())
				.build());

		final var session = paymentSessionSupporter.initStripePaymentSession(tenantId, CreatePaymentSessionCommand
				.builder()
				.paymentProcessorId(paymentMethod.getProcessorId())
				.paymentMethodId(paymentMethod.getId())
				.description("Payment Order #1234")
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(2495L)
				.netAmount(2222L)
				.fee(273L)
				.currencyCode("SGD")
				.customerId("1")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				.expiredInMilliseconds(Duration.ofMinutes(16)
						.toMillis())
				.build());

		final var settlementData = NetsPaymentSettlementSessionRequest.builder()
				.failureCode("Error Code 1234")
				.failureDescription("Error Code Description")
				.approvalCode("123")
				.merchantId("123")
				.terminalId("terminalId")
				.stan("stan")
				.cardName("cardName")
				.canNumber("canNumber")
				.balance("balance")
				.newBalance("newBalance")
				.purchaseAmount("purchaseAmount")
				.batchNumber("batchNumber")
				.invoiceNumber("invoiceNumber")
				.rrn("rrn")
				.tvr("tvr")
				.entryType("entryType")
				.issuerName("issuerName")
				.date("date")
				.time("time")
				.build();

		final var transaction = paymentSettlementSupporter.settleTransaction(tenantId,
				PaymentSessionRequestMapper.INSTANCE.toSettlePaymentSessionCommand(session.getId()
						.getValue(), SettlePaymentSessionRequest.builder()
								.transactionStatus(PaymentTransactionStatus.FAILED)
								.transactionNumber("Transaction Number 00001")
								.paidAt(now)
								.settlementData(settlementData)
								.extraSettlementData(Map.of("key2", "val2"))
								.build(), Boolean.FALSE, objectMapper));

		// Act & Assert
		webClient.get()
				.uri("/api/payment/transactions/{transactionId}", transaction.getId()
						.getValue())
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isOk()
				.expectBody(PaymentTransactionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(paymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentProcessorId.NETS_TERMINAL_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals("1", actual.getCustomerId());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(transaction.getAmount(), actual.getAmount());
					Assertions.assertEquals(transaction.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(transaction.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(transaction.getDescription(), actual.getDescription());
					Assertions.assertEquals(transaction.getDeviceId(), actual.getDeviceId());
					Assertions.assertEquals(transaction.getTransactionNumber(), actual.getTransactionNumber());
					Assertions.assertEquals(transaction.getPaidAt()
							.toEpochMilli(), actual.getPaidAt()
									.toEpochMilli());
					Assertions.assertEquals(PaymentTransactionStatus.FAILED, actual.getTransactionStatus());
					Assertions.assertEquals(session.getAmount(), actual.getAmount());
					Assertions.assertEquals(session.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(session.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(Boolean.FALSE, actual.getIsAsync());
					Assertions.assertEquals(session.getDescription(), actual.getDescription());
					Assertions.assertEquals(session.getDeviceId(), actual.getDeviceId());

					Assertions.assertEquals(PaymentProcessorId.NETS_TERMINAL_PAYMENT, actual.getSettlementData()
							.getProcessorId());
					final var actualSettlementData = (NetsPaymentSettlementResponse) actual.getSettlementData();
					Assertions.assertEquals(settlementData.getFailureCode(), actualSettlementData.getFailureCode());
					Assertions.assertEquals(settlementData.getFailureDescription(), actualSettlementData
							.getFailureDescription());

					Assertions.assertEquals(settlementData.getApprovalCode(), actualSettlementData.getApprovalCode());
					Assertions.assertEquals(settlementData.getMerchantId(), actualSettlementData.getMerchantId());
					Assertions.assertEquals(settlementData.getTerminalId(), actualSettlementData.getTerminalId());
					Assertions.assertEquals(settlementData.getStan(), actualSettlementData.getStan());
					Assertions.assertEquals(settlementData.getCardName(), actualSettlementData.getCardName());
					Assertions.assertEquals(settlementData.getCanNumber(), actualSettlementData.getCanNumber());
					Assertions.assertEquals(settlementData.getBalance(), actualSettlementData.getBalance());
					Assertions.assertEquals(settlementData.getNewBalance(), actualSettlementData.getNewBalance());
					Assertions.assertEquals(settlementData.getPurchaseAmount(), actualSettlementData
							.getPurchaseAmount());
					Assertions.assertEquals(settlementData.getBatchNumber(), actualSettlementData.getBatchNumber());
					Assertions.assertEquals(settlementData.getInvoiceNumber(), actualSettlementData.getInvoiceNumber());
					Assertions.assertEquals(settlementData.getRrn(), actualSettlementData.getRrn());
					Assertions.assertEquals(settlementData.getTvr(), actualSettlementData.getTvr());
					Assertions.assertEquals(settlementData.getEntryType(), actualSettlementData.getEntryType());
					Assertions.assertEquals(settlementData.getIssuerName(), actualSettlementData.getIssuerName());
					Assertions.assertEquals(settlementData.getDate(), actualSettlementData.getDate());
					Assertions.assertEquals(settlementData.getTime(), actualSettlementData.getTime());

					final var extraSettlementResponse = actual.getExtraSettlementData();
					Assertions.assertNotNull(extraSettlementResponse);
					Assertions.assertEquals("val2", extraSettlementResponse.get("key2"));
				});
	}

	@Test
	void testQueryStripeConnectPaymentTransactionWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var tenantId = new TenantId(10_110L);
		final var now = Instant.now();

		final var stripePaymentMethod1 = connectedIntegrationSupporter.connect(paymentMethodSupporter.initPaymentMethod(
				tenantId, UpsertPaymentMethodCommand.builder()
						.displayName("Stripe Connect Payment Method 10000")
						.processorId(PaymentProcessorId.STRIPE_CONNECT_STANDARD_WEB_PAYMENT)
						.acceptedApplications(Set.of(AcceptedApplication.CUSTOMER_PORTAL))
						.iconPath("http://localhost/image.jpg")
						.description("Stripe Payment Description 10000")
						.paymentInstruction("Stripe Payment Instruction 10000")
						.surchargeRate(BigDecimal.valueOf(0.123))
						.fixedSurcharge(10L)
						.currencyCode("SGD")
						.surchargeTitle("Stripe Payment Title 10000")
						.build()));

		final var stripeParams1 = StripeConnectWebPaymentSessionParamsRequest.builder()
				.cancelRedirectURL("https://www.cancel.org/current")
				.successRedirectURL("https://www.success.org/current")
				.build();

		final var session1 = paymentSessionSupporter.initStripeConnectPaymentSession(tenantId,
				CreatePaymentSessionCommand.builder()
						.paymentProcessorId(stripePaymentMethod1.getProcessorId())
						.paymentMethodId(stripePaymentMethod1.getId())
						.description("Payment Order #1234")
						.paymentReference("Payment Reference 01")
						.transactionType(PaymentTransactionType.PURCHASE)
						.amount(2505L)
						.netAmount(2222L)
						.fee(283L)
						.currencyCode("SGD")
						.customerId("1")
						.customerEmail("<EMAIL>")
						.customerName("Customer Name 001")
						.systemSource("System Source 01")
						.merchantName("Merchant Name")
						.deviceId("deviceId")
						.expiredInMilliseconds(Duration.ofMinutes(16)
								.toMillis())
						.processorParams(PaymentSessionParams.builder()
								.params(objectMapper.convertValue(stripeParams1, new TypeReference<>() {
								}))
								.build())
						.build());

		final var sessionData1 = objectMapper.convertValue(session1.getSessionData()
				.getData(), StripePaymentSessionProcessorData.class);

		final var metadata1 = StripePaymentSessionMetadata.builder()
				.paymentSessionId(session1.getId()
						.getValue())
				.paymentReference(session1.getPaymentReference())
				.build();

		final var stripeSession1 = new Session();
		stripeSession1.setId(sessionData1.getStripeSessionId());
		stripeSession1.setPaymentStatus("paid");
		stripeSession1.setPaymentIntent("payment_intent_id_01");
		stripeSession1.setExpiresAt(Instant.now()
				.plus(16, ChronoUnit.MINUTES)
				.getEpochSecond());
		stripeSession1.setMetadata(objectMapper.convertValue(metadata1, new TypeReference<>() {
		}));

		when(stripeConnectApiClient.getCheckoutPaymentSession(any(StripeConnectPaymentMethodConfiguration.class), eq(
				stripeSession1.getId()))).thenReturn(stripeSession1);

		final var transaction1 = paymentSettlementSupporter.settleTransaction(tenantId, SettlePaymentSessionCommand
				.builder()
				.paidAt(now)
				.transactionNumber("txn no 12345678")
				.transactionStatus(PaymentTransactionStatus.SUCCEEDED)
				.paymentSessionId(session1.getId())
				.isAsync(Boolean.FALSE)
				.build());

		final var stripePaymentMethod2 = connectedIntegrationSupporter.connect(paymentMethodSupporter.initPaymentMethod(
				tenantId, UpsertPaymentMethodCommand.builder()
						.displayName("Stripe Connect Payment Method 10000")
						.processorId(PaymentProcessorId.STRIPE_CONNECT_STANDARD_WEB_PAYMENT)
						.acceptedApplications(Set.of(AcceptedApplication.CUSTOMER_PORTAL))
						.iconPath("http://localhost/image.jpg")
						.description("Stripe Payment Description 10000")
						.paymentInstruction("Stripe Payment Instruction 10000")
						.surchargeRate(BigDecimal.valueOf(0.123))
						.fixedSurcharge(10L)
						.currencyCode("SGD")
						.surchargeTitle("Stripe Payment Title 10000")
						.build()));

		final var stripeParams2 = StripeConnectWebPaymentSessionParamsRequest.builder()
				.cancelRedirectURL("https://www.cancel.org/current")
				.successRedirectURL("https://www.success.org/current")
				.build();

		final var session2 = paymentSessionSupporter.initStripeConnectPaymentSession(tenantId,
				CreatePaymentSessionCommand.builder()
						.paymentProcessorId(stripePaymentMethod2.getProcessorId())
						.paymentMethodId(stripePaymentMethod2.getId())
						.description("Payment Order #1234")
						.paymentReference("Payment Reference 01")
						.transactionType(PaymentTransactionType.PURCHASE)
						.amount(2505L)
						.netAmount(2222L)
						.fee(283L)
						.currencyCode("SGD")
						.customerId("1")
						.customerEmail("<EMAIL>")
						.customerName("Customer Name 001")
						.systemSource("System Source 01")
						.merchantName("Merchant Name")
						.deviceId("deviceId")
						.expiredInMilliseconds(Duration.ofMinutes(16)
								.toMillis())
						.processorParams(PaymentSessionParams.builder()
								.params(objectMapper.convertValue(stripeParams2, new TypeReference<>() {
								}))
								.build())
						.build());

		final var sessionData2 = objectMapper.convertValue(session2.getSessionData()
				.getData(), StripePaymentSessionProcessorData.class);

		final var metadata2 = StripePaymentSessionMetadata.builder()
				.paymentSessionId(session2.getId()
						.getValue())
				.paymentReference(session2.getPaymentReference())
				.build();

		final var stripeSession2 = new Session();
		stripeSession2.setId(sessionData2.getStripeSessionId());
		stripeSession2.setPaymentStatus("paid");
		stripeSession2.setPaymentIntent("payment_intent_id_02");
		stripeSession2.setExpiresAt(Instant.now()
				.plus(16, ChronoUnit.MINUTES)
				.getEpochSecond());
		stripeSession2.setMetadata(objectMapper.convertValue(metadata2, new TypeReference<>() {
		}));

		when(stripeConnectApiClient.getCheckoutPaymentSession(any(StripeConnectPaymentMethodConfiguration.class), eq(
				stripeSession2.getId()))).thenReturn(stripeSession2);
		final var transaction2 = paymentSettlementSupporter.settleTransaction(tenantId, SettlePaymentSessionCommand
				.builder()
				.paidAt(now)
				.transactionNumber("txn no 12345678")
				.transactionStatus(PaymentTransactionStatus.SUCCEEDED)
				.paymentSessionId(session2.getId())
				.isAsync(Boolean.FALSE)
				.build());

		final var request = QueryPaymentTransactionPaginationRequest.builder()
				.filter(FilterPaymentTransactionRequest.builder()
						.build())
				.page(0)
				.size(10)
				.sortDirection("ASC")
				.sortFields(List.of("id"))
				.build();

		final Map<String, String> requestParamMap = new HashMap<>();

		requestParamMap.put("size", String.valueOf(request.getSize()));
		requestParamMap.put("page", String.valueOf(request.getPage()));
		requestParamMap.put("sortDirection", "ASC");
		requestParamMap.put("sortFields", String.join(",", request.getSortFields()));

		// Act & Assert
		webClient.get()
				.uri(uriBuilder -> {
					final LinkedMultiValueMap<String, String> requestParams = new LinkedMultiValueMap<>(requestParamMap
							.entrySet()
							.stream()
							.filter(it -> org.apache.commons.lang3.StringUtils.isNotBlank(it.getValue()))
							.collect(Collectors.toMap(Map.Entry::getKey, it -> List.of(it.getValue()))));

					uriBuilder.path("/api/payment/transactions");
					uriBuilder.queryParams(requestParams);
					return uriBuilder.build();
				})
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(Paging.class)
				.consumeWith(response -> {
					final var paymentTransactionResponse = Optional.ofNullable(response.getResponseBody())
							.map(Paging::getContent)
							.map(ls -> (List<PaymentTransactionResponse>) ls.stream()
									.map(it -> objectMapper.convertValue(it, PaymentTransactionResponse.class))
									.toList())
							.orElseGet(List::of);

					Assertions.assertEquals(2, paymentTransactionResponse.size());

					final AtomicInteger index = new AtomicInteger(0);
					paymentTransactionResponse.forEach(actual -> {
						final var transaction = index.get() % 2 == 0 ? transaction1 : transaction2;
						final var sessionData = index.get() % 2 == 0 ? sessionData1 : sessionData2;
						final var stripeSession = index.get() % 2 == 0 ? stripeSession1 : stripeSession2;
						final var stripePaymentMethod = index.get() % 2 == 0
								? stripePaymentMethod1
								: stripePaymentMethod2;
						final var stripeParams = index.get() % 2 == 0 ? stripeParams1 : stripeParams2;
						final var session = index.getAndIncrement() % 2 == 0 ? session1 : session2;

						Assertions.assertNotNull(actual);
						Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
						Assertions.assertEquals(stripePaymentMethod.getId()
								.getValue()
								.toString(), actual.getPaymentMethodId());
						Assertions.assertEquals(PaymentProcessorId.STRIPE_CONNECT_STANDARD_WEB_PAYMENT, actual
								.getPaymentProcessorId());
						Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
						Assertions.assertEquals("1", actual.getCustomerId());
						Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
						Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
						Assertions.assertEquals("SGD", actual.getCurrencyCode());
						Assertions.assertEquals(transaction.getAmount(), actual.getAmount());
						Assertions.assertEquals(transaction.getNetAmount(), actual.getNetAmount());
						Assertions.assertEquals(transaction.getSystemSource(), actual.getSystemSource());
						Assertions.assertEquals(transaction.getDescription(), actual.getDescription());
						Assertions.assertEquals(stripeSession.getPaymentIntent(), actual.getTransactionNumber());
						Assertions.assertEquals(PaymentTransactionStatus.SUCCEEDED, actual.getTransactionStatus());
						Assertions.assertEquals(transaction.getPaidAt()
								.toEpochMilli(), actual.getPaidAt()
										.toEpochMilli());

						Assertions.assertEquals(session.getAmount(), actual.getAmount());
						Assertions.assertEquals(session.getNetAmount(), actual.getNetAmount());
						Assertions.assertEquals(session.getSystemSource(), actual.getSystemSource());
						Assertions.assertEquals(session.getDeviceId(), actual.getDeviceId());

						Assertions.assertEquals(Boolean.FALSE, actual.getIsAsync());
						Assertions.assertEquals(session.getDescription(), actual.getDescription());

						Assertions.assertEquals(stripePaymentMethod.getFixedSurcharge(), actual
								.getAppliedFixedSurcharge());
						Assertions.assertEquals(0, stripePaymentMethod.getSurchargeRate()
								.compareTo(new BigDecimal(actual.getAppliedSurchargeRate())));
						Assertions.assertEquals("Merchant Name", actual.getMerchantName());

						final var actualProcessorParams = (StripeConnectPaymentSessionParamsResponse) actual
								.getProcessorParams();
						Assertions.assertNotNull(actualProcessorParams);
						Assertions.assertEquals(stripeParams.getSuccessRedirectURL(), actualProcessorParams
								.getSuccessRedirectURL());
						Assertions.assertEquals(stripeParams.getCancelRedirectURL(), actualProcessorParams
								.getCancelRedirectURL());
						Assertions.assertEquals(PaymentProcessorId.STRIPE_CONNECT_STANDARD_WEB_PAYMENT,
								actualProcessorParams.getProcessorId());

						final var actualProcessorData = (StripeConnectPaymentSessionDataResponse) actual
								.getSessionData();
						Assertions.assertNotNull(actualProcessorData);
						Assertions.assertEquals(sessionData.getStripeSessionId(), actualProcessorData
								.getStripeSessionId());
						Assertions.assertEquals("https://checkout.stripe.com/c/pay/cs_test", actualProcessorData
								.getPaymentUrl());
					});

				});
	}

	@Test
	void testQueryStripePaymentTransactionWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var tenantId = new TenantId(10_200L);
		final var now = Instant.now();

		final var stripePaymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Stripe Payment Method 10000")
				.processorId(PaymentProcessorId.STRIPE_WEB_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.CUSTOMER_PORTAL))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Stripe Payment Description 10000")
				.paymentInstruction("Stripe Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeRate(BigDecimal.valueOf(0.1234))
				.surchargeTitle("Stripe Payment Title 10000")
				.processorConfig(SimplePaymentMethodConfiguration.builder()
						.configs(objectMapper.convertValue(StripePaymentMethodConfigurationRequest.builder()
								.apiKey("API KEY 1234")
								.build(), new TypeReference<>() {
								}))
						.build())
				.build());

		final var stripeParams = StripeWebPaymentSessionParamsRequest.builder()
				.cancelRedirectURL("https://www.cancel.org/current")
				.successRedirectURL("https://www.success.org/current")
				.build();

		final var session1 = paymentSessionSupporter.initStripePaymentSession(tenantId, CreatePaymentSessionCommand
				.builder()
				.paymentProcessorId(stripePaymentMethod.getProcessorId())
				.description("Payment Order #1234")
				.paymentMethodId(stripePaymentMethod.getId())
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(2496L)
				.netAmount(2222L)
				.fee(274L)
				.currencyCode("SGD")
				.customerId("1")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				.expiredInMilliseconds(Duration.ofMinutes(16)
						.toMillis())
				.processorParams(PaymentSessionParams.builder()
						.params(objectMapper.convertValue(stripeParams, new TypeReference<>() {
						}))
						.build())
				.build());

		final var sessionData1 = objectMapper.convertValue(session1.getSessionData()
				.getData(), StripePaymentSessionProcessorData.class);

		final var metadata1 = StripePaymentSessionMetadata.builder()
				.paymentSessionId(session1.getId()
						.getValue())
				.paymentReference(session1.getPaymentReference())
				.build();

		final var stripeSession1 = new Session();
		stripeSession1.setId(sessionData1.getStripeSessionId());
		stripeSession1.setPaymentStatus("paid");
		stripeSession1.setPaymentIntent("payment_intent_id_01");
		stripeSession1.setMetadata(objectMapper.convertValue(metadata1, new TypeReference<>() {
		}));

		when(stripeApiClient.getCheckoutPaymentSession(any(StripePaymentMethodConfiguration.class), eq(stripeSession1
				.getId()))).thenReturn(stripeSession1);

		final var session2 = paymentSessionSupporter.initStripePaymentSession(tenantId, CreatePaymentSessionCommand
				.builder()
				.paymentProcessorId(stripePaymentMethod.getProcessorId())
				.description("Payment Order #12345678")
				.paymentMethodId(stripePaymentMethod.getId())
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(2496L)
				.netAmount(2222L)
				.fee(274L)
				.currencyCode("SGD")
				.customerId("1")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				.deviceId("device 0909")
				.expiredInMilliseconds(Duration.ofMinutes(16)
						.toMillis())
				.processorParams(PaymentSessionParams.builder()
						.params(objectMapper.convertValue(stripeParams, new TypeReference<>() {
						}))
						.build())
				.build());

		final var sessionData2 = objectMapper.convertValue(session2.getSessionData()
				.getData(), StripePaymentSessionProcessorData.class);

		final var metadata2 = StripePaymentSessionMetadata.builder()
				.paymentSessionId(session2.getId()
						.getValue())
				.paymentReference(session2.getPaymentReference())
				.build();
		final var stripeSession2 = new Session();
		stripeSession2.setId(sessionData2.getStripeSessionId());
		stripeSession2.setPaymentStatus("paid");
		stripeSession2.setPaymentIntent("payment_intent_id_02");
		stripeSession2.setMetadata(objectMapper.convertValue(metadata2, new TypeReference<>() {
		}));

		when(stripeApiClient.getCheckoutPaymentSession(any(StripePaymentMethodConfiguration.class), eq(stripeSession2
				.getId()))).thenReturn(stripeSession2);

		final var transaction1 = paymentSettlementSupporter.settleTransaction(tenantId, SettlePaymentSessionCommand
				.builder()
				.paidAt(now)
				.paymentSessionId(session1.getId())
				.transactionStatus(PaymentTransactionStatus.SUCCEEDED)
				.isAsync(Boolean.FALSE)
				.build());

		final var transaction2 = paymentSettlementSupporter.settleTransaction(tenantId, SettlePaymentSessionCommand
				.builder()
				.paidAt(now)
				.paymentSessionId(session2.getId())
				.transactionStatus(PaymentTransactionStatus.SUCCEEDED)
				.isAsync(Boolean.FALSE)
				.build());

		final var request = QueryPaymentTransactionPaginationRequest.builder()
				.filter(FilterPaymentTransactionRequest.builder()
						.bySessionIds(Set.of(session1.getId()
								.getValue(), session2.getId()
										.getValue()))
						.byPaymentMethodId(stripePaymentMethod.getId()
								.getValue())
						.byCreatedRange(LongDateTimeRange.builder()
								.from(now.minus(5, ChronoUnit.MINUTES)
										.toEpochMilli())
								.to(now.plus(5, ChronoUnit.MINUTES)
										.toEpochMilli())
								.build())
						.byCustomerId("1")
						.byCustomerEmail("<EMAIL>")
						.byTransactionType(PaymentTransactionType.PURCHASE)
						.build())
				.page(0)
				.size(10)
				.sortDirection("ASC")
				.sortFields(List.of("id"))
				.build();

		final Map<String, String> requestParamMap = new HashMap<>();

		requestParamMap.put("filter.bySessionIds", String.join(",", request.getFilter()
				.getBySessionIds()));
		requestParamMap.put("filter.byPaymentMethodId", request.getFilter()
				.getByPaymentMethodId()
				.toString());
		requestParamMap.put("filter.byCreatedRange.to", request.getFilter()
				.getByCreatedRange()
				.getTo()
				.toString());
		requestParamMap.put("filter.byCreatedRange.from", request.getFilter()
				.getByCreatedRange()
				.getFrom()
				.toString());
		requestParamMap.put("filter.byCustomerId", request.getFilter()
				.getByCustomerId());
		requestParamMap.put("filter.byCustomerEmail", request.getFilter()
				.getByCustomerEmail());
		requestParamMap.put("filter.byTransactionType", request.getFilter()
				.getByTransactionType()
				.name());

		requestParamMap.put("size", String.valueOf(request.getSize()));
		requestParamMap.put("page", String.valueOf(request.getPage()));
		requestParamMap.put("sortDirection", "ASC");
		requestParamMap.put("sortFields", String.join(",", request.getSortFields()));

		// Act & Assert
		webClient.get()
				.uri(uriBuilder -> {
					final LinkedMultiValueMap<String, String> requestParams = new LinkedMultiValueMap<>(requestParamMap
							.entrySet()
							.stream()
							.filter(it -> org.apache.commons.lang3.StringUtils.isNotBlank(it.getValue()))
							.collect(Collectors.toMap(Map.Entry::getKey, it -> List.of(it.getValue()))));

					uriBuilder.path("/api/payment/transactions");
					uriBuilder.queryParams(requestParams);
					return uriBuilder.build();
				})
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(Paging.class)
				.consumeWith(response -> {
					final var paymentTransactionResponse = Optional.ofNullable(response.getResponseBody())
							.map(Paging::getContent)
							.map(ls -> (List<PaymentTransactionResponse>) ls.stream()
									.map(it -> objectMapper.convertValue(it, PaymentTransactionResponse.class))
									.toList())
							.orElseGet(List::of);

					Assertions.assertEquals(2, paymentTransactionResponse.size());

					final AtomicInteger index = new AtomicInteger(0);
					paymentTransactionResponse.forEach(actual -> {
						final var transaction = index.get() % 2 == 0 ? transaction1 : transaction2;
						final var sessionData = index.get() % 2 == 0 ? sessionData1 : sessionData2;
						final var stripeSession = index.get() % 2 == 0 ? stripeSession1 : stripeSession2;
						final var session = index.getAndIncrement() % 2 == 0 ? session1 : session2;

						Assertions.assertNotNull(actual);
						Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
						Assertions.assertEquals(stripePaymentMethod.getId()
								.getValue()
								.toString(), actual.getPaymentMethodId());
						Assertions.assertEquals(PaymentProcessorId.STRIPE_WEB_PAYMENT, actual.getPaymentProcessorId());
						Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
						Assertions.assertEquals("1", actual.getCustomerId());
						Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
						Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
						Assertions.assertEquals("SGD", actual.getCurrencyCode());
						Assertions.assertEquals(transaction.getAmount(), actual.getAmount());
						Assertions.assertEquals(transaction.getNetAmount(), actual.getNetAmount());
						Assertions.assertEquals(transaction.getSystemSource(), actual.getSystemSource());
						Assertions.assertEquals(transaction.getDescription(), actual.getDescription());
						Assertions.assertEquals(stripeSession.getPaymentIntent(), actual.getTransactionNumber());
						Assertions.assertEquals(PaymentTransactionStatus.SUCCEEDED, actual.getTransactionStatus());
						Assertions.assertEquals(transaction.getPaidAt()
								.toEpochMilli(), actual.getPaidAt()
										.toEpochMilli());

						Assertions.assertEquals(session.getAmount(), actual.getAmount());
						Assertions.assertEquals(session.getNetAmount(), actual.getNetAmount());
						Assertions.assertEquals(session.getSystemSource(), actual.getSystemSource());
						Assertions.assertEquals(session.getDeviceId(), actual.getDeviceId());

						Assertions.assertEquals(Boolean.FALSE, actual.getIsAsync());
						Assertions.assertEquals(session.getDescription(), actual.getDescription());

						Assertions.assertEquals(stripePaymentMethod.getFixedSurcharge(), actual
								.getAppliedFixedSurcharge());
						Assertions.assertEquals(0, stripePaymentMethod.getSurchargeRate()
								.compareTo(new BigDecimal(actual.getAppliedSurchargeRate())));
						Assertions.assertEquals("Merchant Name", actual.getMerchantName());

						final var actualProcessorParams = (StripeWebPaymentSessionParamsResponse) actual
								.getProcessorParams();
						Assertions.assertNotNull(actualProcessorParams);
						Assertions.assertEquals(stripeParams.getSuccessRedirectURL(), actualProcessorParams
								.getSuccessRedirectURL());
						Assertions.assertEquals(stripeParams.getCancelRedirectURL(), actualProcessorParams
								.getCancelRedirectURL());
						Assertions.assertEquals(PaymentProcessorId.STRIPE_WEB_PAYMENT, actualProcessorParams
								.getProcessorId());

						final var actualProcessorData = (StripePaymentSessionDataResponse) actual.getSessionData();
						Assertions.assertNotNull(actualProcessorData);
						Assertions.assertEquals(sessionData.getStripeSessionId(), actualProcessorData
								.getStripeSessionId());
						Assertions.assertEquals("https://checkout.stripe.com/c/pay/cs_test", actualProcessorData
								.getPaymentUrl());
					});

				});
	}

	@Test
	void testQueryNetsPaymentTransactionWithDeviceIdTransactionNumberWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var tenantId = new TenantId(10_000L);
		final var now = Instant.now();
		final var paymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Nets Payment Method 10000")
				.processorId(PaymentProcessorId.NETS_TERMINAL_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.POS))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Nets Payment Description 10000")
				.paymentInstruction("Nets Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeRate(BigDecimal.valueOf(0.123))
				.surchargeTitle("Nets Payment Title 10000")
				.processorConfig(SimplePaymentMethodConfiguration.builder()
						.configs(objectMapper.convertValue(NetsTerminalPaymentMethodConfiguration.builder()
								.netsFamilyCardType(NetsFamilyCardType.CREDIT_CARD)
								.build(), new TypeReference<>() {
								}))
						.build())
				.build());

		final var session1 = paymentSessionSupporter.initStripePaymentSession(tenantId, CreatePaymentSessionCommand
				.builder()
				.paymentProcessorId(paymentMethod.getProcessorId())
				.paymentMethodId(paymentMethod.getId())
				.description("Payment Order #1234")
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(2495L)
				.netAmount(2222L)
				.fee(273L)
				.currencyCode("SGD")
				.customerId("1")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				.deviceId("1234")
				.expiredInMilliseconds(Duration.ofMinutes(16)
						.toMillis())
				.build());

		final var settlementData1 = NetsPaymentSettlementSessionRequest.builder()
				.approvalCode("123")
				.merchantId("123")
				.terminalId("terminalId")
				.stan("stan")
				.cardName("cardName")
				.canNumber("canNumber")
				.balance("balance")
				.newBalance("newBalance")
				.purchaseAmount("purchaseAmount")
				.batchNumber("batchNumber")
				.invoiceNumber("invoiceNumber")
				.rrn("rrn")
				.tvr("tvr")
				.entryType("entryType")
				.issuerName("issuerName")
				.date("date")
				.time("time")
				.build();

		final var transaction1 = paymentSettlementSupporter.settleTransaction(tenantId,
				PaymentSessionRequestMapper.INSTANCE.toSettlePaymentSessionCommand(session1.getId()
						.getValue(), SettlePaymentSessionRequest.builder()
								.transactionStatus(PaymentTransactionStatus.SUCCEEDED)
								.transactionNumber("Transaction Number 00001")
								.paidAt(now)
								.transactionNumber("transactionNumber-001")
								.deviceTransactionNumber("deviceTransactionNumber-001")
								.settlementData(settlementData1)
								.extraSettlementData(Map.of("key2", "val2"))
								.build(), Boolean.FALSE, objectMapper));

		final var request = QueryPaymentTransactionPaginationRequest.builder()
				.filter(FilterPaymentTransactionRequest.builder()
						.byDeviceId(session1.getDeviceId())
						.byDeviceTransactionNumber(transaction1.getDeviceTransactionNumber())
						.byTransactionNumber(transaction1.getTransactionNumber())
						.build())
				.page(0)
				.size(10)
				.sortDirection("ASC")
				.sortFields(List.of("id"))
				.build();

		final Map<String, String> requestParamMap = new HashMap<>();

		requestParamMap.put("filter.byDeviceId", request.getFilter()
				.getByDeviceId());
		requestParamMap.put("filter.byDeviceTransactionNumber", request.getFilter()
				.getByDeviceTransactionNumber());
		requestParamMap.put("filter.byTransactionNumber", request.getFilter()
				.getByTransactionNumber());

		requestParamMap.put("size", String.valueOf(request.getSize()));
		requestParamMap.put("page", String.valueOf(request.getPage()));
		requestParamMap.put("sortDirection", "ASC");
		requestParamMap.put("sortFields", String.join(",", request.getSortFields()));

		// Act & Assert
		webClient.get()
				.uri(uriBuilder -> {
					final LinkedMultiValueMap<String, String> requestParams = new LinkedMultiValueMap<>(requestParamMap
							.entrySet()
							.stream()
							.filter(it -> org.apache.commons.lang3.StringUtils.isNotBlank(it.getValue()))
							.collect(Collectors.toMap(Map.Entry::getKey, it -> List.of(it.getValue()))));

					uriBuilder.path("/api/payment/transactions");
					uriBuilder.queryParams(requestParams);
					return uriBuilder.build();
				})
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(Paging.class)
				.consumeWith(response -> {
					final var paymentTransactionResponse = Optional.ofNullable(response.getResponseBody())
							.map(Paging::getContent)
							.map(ls -> (List<PaymentTransactionResponse>) ls.stream()
									.map(it -> objectMapper.convertValue(it, PaymentTransactionResponse.class))
									.toList())
							.orElseGet(List::of);
					Assertions.assertEquals(1, paymentTransactionResponse.size());

					final var actual = paymentTransactionResponse.getFirst();

					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(paymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentProcessorId.NETS_TERMINAL_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals("1", actual.getCustomerId());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(transaction1.getAmount(), actual.getAmount());
					Assertions.assertEquals(transaction1.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(transaction1.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(transaction1.getDescription(), actual.getDescription());
					Assertions.assertEquals(PaymentTransactionStatus.SUCCEEDED, actual.getTransactionStatus());
					Assertions.assertEquals(transaction1.getPaidAt()
							.toEpochMilli(), actual.getPaidAt()
									.toEpochMilli());
					Assertions.assertEquals(transaction1.getDeviceTransactionNumber(), actual
							.getDeviceTransactionNumber());
					Assertions.assertEquals(transaction1.getTransactionNumber(), actual.getTransactionNumber());

					Assertions.assertEquals(session1.getAmount(), actual.getAmount());
					Assertions.assertEquals(session1.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(session1.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(session1.getDeviceId(), actual.getDeviceId());

					Assertions.assertEquals(Boolean.FALSE, actual.getIsAsync());
					Assertions.assertEquals(session1.getDescription(), actual.getDescription());

				});
	}

	@Test
	void testCreateOfflinePaymentTransactionSuccessfullyWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(13_000L);
		final var now = Instant.now();
		final var paymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Offline Payment Method 10000")
				.processorId(PaymentProcessorId.OFFLINE_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.POS))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Offline Payment Description 10000")
				.paymentInstruction("Cash Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeRate(BigDecimal.valueOf(0.123))
				.surchargeTitle("Offline Payment Title 10000")
				.build());

		final var request = CreateOfflineTransactionRequest.builder()
				.offlineIdempotencyKey("offline-idempotency-key-01")
				.description("description")
				.paymentReference("paymentReference")
				.customerId("1234567890001")
				.userCardId("card-id-1234567890001")
				.customerEmail("<EMAIL>")
				.customerName("customer-1234567890001-name")
				.deviceId("device-id-1234567890001")
				.currencyCode("SGD")
				.amount(2495L)
				.netAmount(2222L)
				.fee(273L)
				.paymentMethodId(paymentMethod.getId()
						.getValue())
				.paymentProcessorId(PaymentProcessorId.OFFLINE_PAYMENT)
				.surchargeRate(paymentMethod.getSurchargeRate())
				.initiatedAt(Instant.now())
				.paymentMethodDisplayName(paymentMethod.getDisplayName())
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				.transactionStatus(PaymentTransactionStatus.SUCCEEDED)
				.paidAt(Instant.now())
				.transactionNumber("transaction-number-customer-1234567890001")
				.deviceTransactionNumber("device-number-customer-1234567890001")
				.build();

		final var offlinePaymentTransactionCreatedEventCaptor = ArgumentCaptor.forClass(
				com.styl.pacific.kafka.payments.avro.model.PaymentOfflineTransactionCreatedEventAvroModel.class);

		// Act & Assert
		webClient.post()
				.uri("/api/payment/transactions/offline")
				.contentType(MediaType.APPLICATION_JSON)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.bodyValue(request)
				.exchange()
				.expectStatus()
				.isCreated()
				.expectBody(PaymentTransactionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(paymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentProcessorId.OFFLINE_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals(request.getPaymentReference(), actual.getPaymentReference());
					Assertions.assertEquals(request.getCustomerId(), actual.getCustomerId());
					Assertions.assertEquals(request.getCustomerEmail(), actual.getCustomerEmail());
					Assertions.assertEquals(request.getCustomerName(), actual.getCustomerName());
					Assertions.assertEquals(request.getCurrencyCode(), actual.getCurrencyCode());
					Assertions.assertEquals(request.getAmount(), actual.getAmount());
					Assertions.assertEquals(request.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(request.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(request.getDescription(), actual.getDescription());
					Assertions.assertEquals(request.getDeviceId(), actual.getDeviceId());
					Assertions.assertEquals(request.getUserCardId(), actual.getUserCardId());
					Assertions.assertEquals(request.getTransactionNumber(), actual.getTransactionNumber());
					Assertions.assertEquals(request.getPaidAt()
							.toEpochMilli(), actual.getPaidAt()
									.toEpochMilli());
					Assertions.assertEquals(request.getOfflineIdempotencyKey(), actual.getOfflineIdempotencyKey());
					Assertions.assertEquals(PaymentTransactionStatus.SUCCEEDED, actual.getTransactionStatus());

					final var transaction = transactionQueryService.getTransactionByTenantIdAndId(tenantId,
							MapstructCommonDomainMapper.INSTANCE.longToPaymentTransactionId(Long.valueOf(actual
									.getId())));

					verify(kafkaOfflineTransactionCreatedEventPublisher, times(1)).publish(
							offlinePaymentTransactionCreatedEventCaptor.capture());
					final var offlineTransactionCreatedEvent = offlinePaymentTransactionCreatedEventCaptor.getValue();

					Assertions.assertEquals(actual.getPaymentSessionId(), offlineTransactionCreatedEvent
							.getPaymentSessionId());
					Assertions.assertEquals(paymentMethod.getId()
							.getValue(), offlineTransactionCreatedEvent.getPaymentMethodId());
					Assertions.assertEquals(PaymentProcessorId.OFFLINE_PAYMENT.name(), offlineTransactionCreatedEvent
							.getPaymentProcessorId());
					Assertions.assertEquals(transaction.getPaymentReference(), offlineTransactionCreatedEvent
							.getPaymentReference());
					Assertions.assertEquals(transaction.getCustomerId(), offlineTransactionCreatedEvent
							.getCustomerId());
					Assertions.assertEquals(transaction.getCustomerEmail(), offlineTransactionCreatedEvent
							.getCustomerEmail());
					Assertions.assertEquals(transaction.getCustomerName(), offlineTransactionCreatedEvent
							.getCustomerName());
					Assertions.assertEquals(transaction.getCurrencyCode(), offlineTransactionCreatedEvent
							.getCurrencyCode());
					Assertions.assertEquals(transaction.getDeviceId(), offlineTransactionCreatedEvent.getDeviceId());
					Assertions.assertEquals(transaction.getAmount(), offlineTransactionCreatedEvent.getAmount());
					Assertions.assertEquals(transaction.getNetAmount(), offlineTransactionCreatedEvent.getNetAmount());
					Assertions.assertEquals(transaction.getFee(), offlineTransactionCreatedEvent.getFee());
					Assertions.assertEquals(transaction.getSystemSource(), offlineTransactionCreatedEvent
							.getSystemSource());
					Assertions.assertEquals(transaction.getDescription(), offlineTransactionCreatedEvent
							.getDescription());
					Assertions.assertEquals(com.styl.pacific.kafka.payments.avro.model.PaymentTransactionType.PURCHASE,
							offlineTransactionCreatedEvent.getTransactionType());
					Assertions.assertEquals(transaction.getPaidAt()
							.toEpochMilli(), offlineTransactionCreatedEvent.getPaidAt());
					Assertions.assertEquals(transaction.getAppliedFixedSurcharge(), offlineTransactionCreatedEvent
							.getAppliedFixedSurcharge());

					Assertions.assertEquals(0, transaction.getAppliedSurchargeRate()
							.compareTo(offlineTransactionCreatedEvent.getAppliedSurchargeRate()));

					Assertions.assertEquals(transaction.getOfflineIdempotencyKey()
							.getValue(), offlineTransactionCreatedEvent.getOfflineIdempotencyKey());
					Assertions.assertEquals(transaction.getUserCardId(), offlineTransactionCreatedEvent
							.getUserCardId());

				});
	}

	@Test
	void testCreateCashOfflinePaymentTransactionSuccessfullyWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(14_000L);
		final var now = Instant.now();
		final var paymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Cash Payment Method 10000")
				.processorId(PaymentProcessorId.CASH_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.POS))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Cash Payment Description 10000")
				.paymentInstruction("Cash Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeRate(BigDecimal.valueOf(0.123))
				.surchargeTitle("Cash Payment Title 10000")
				.build());

		final var request = CreateOfflineTransactionRequest.builder()
				.offlineIdempotencyKey("cash-idempotency-key-01")
				.description("description")
				.paymentReference("paymentReference")
				.customerId("1234567890001")
				.userCardId("card-id-1234567890002")
				.customerEmail("<EMAIL>")
				.customerName("customer-1234567890002-name")
				.deviceId("device-id-1234567890001")
				.currencyCode("SGD")
				.amount(2495L)
				.netAmount(2222L)
				.fee(273L)
				.paymentMethodId(paymentMethod.getId()
						.getValue())
				.paymentProcessorId(PaymentProcessorId.CASH_PAYMENT)
				.surchargeRate(paymentMethod.getSurchargeRate())
				.initiatedAt(Instant.now())
				.paymentMethodDisplayName(paymentMethod.getDisplayName())
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				.transactionStatus(PaymentTransactionStatus.SUCCEEDED)
				.paidAt(Instant.now())
				.transactionNumber("transaction-number-customer-1234567890002")
				.deviceTransactionNumber("device-number-customer-1234567890002")
				.build();

		final var offlinePaymentTransactionCreatedEventCaptor = ArgumentCaptor.forClass(
				com.styl.pacific.kafka.payments.avro.model.PaymentOfflineTransactionCreatedEventAvroModel.class);

		// Act & Assert
		webClient.post()
				.uri("/api/payment/transactions/offline")
				.contentType(MediaType.APPLICATION_JSON)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.bodyValue(request)
				.exchange()
				.expectStatus()
				.isCreated()
				.expectBody(PaymentTransactionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(paymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentProcessorId.CASH_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals(request.getPaymentReference(), actual.getPaymentReference());
					Assertions.assertEquals(request.getCustomerId(), actual.getCustomerId());
					Assertions.assertEquals(request.getCustomerEmail(), actual.getCustomerEmail());
					Assertions.assertEquals(request.getCustomerName(), actual.getCustomerName());
					Assertions.assertEquals(request.getCurrencyCode(), actual.getCurrencyCode());
					Assertions.assertEquals(request.getAmount(), actual.getAmount());
					Assertions.assertEquals(request.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(request.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(request.getDescription(), actual.getDescription());
					Assertions.assertEquals(request.getDeviceId(), actual.getDeviceId());
					Assertions.assertEquals(request.getTransactionNumber(), actual.getTransactionNumber());
					Assertions.assertEquals(request.getUserCardId(), actual.getUserCardId());
					Assertions.assertEquals(request.getPaidAt()
							.toEpochMilli(), actual.getPaidAt()
									.toEpochMilli());
					Assertions.assertEquals(request.getOfflineIdempotencyKey(), actual.getOfflineIdempotencyKey());
					Assertions.assertEquals(PaymentTransactionStatus.SUCCEEDED, actual.getTransactionStatus());

					final var transaction = transactionQueryService.getTransactionByTenantIdAndId(tenantId,
							MapstructCommonDomainMapper.INSTANCE.longToPaymentTransactionId(Long.valueOf(actual
									.getId())));

					verify(kafkaOfflineTransactionCreatedEventPublisher, times(1)).publish(
							offlinePaymentTransactionCreatedEventCaptor.capture());
					final var offlineTransactionCreatedEvent = offlinePaymentTransactionCreatedEventCaptor.getValue();

					Assertions.assertEquals(actual.getPaymentSessionId(), offlineTransactionCreatedEvent
							.getPaymentSessionId());
					Assertions.assertEquals(paymentMethod.getId()
							.getValue(), offlineTransactionCreatedEvent.getPaymentMethodId());
					Assertions.assertEquals(PaymentProcessorId.CASH_PAYMENT.name(), offlineTransactionCreatedEvent
							.getPaymentProcessorId());
					Assertions.assertEquals(transaction.getPaymentReference(), offlineTransactionCreatedEvent
							.getPaymentReference());
					Assertions.assertEquals(transaction.getCustomerId(), offlineTransactionCreatedEvent
							.getCustomerId());
					Assertions.assertEquals(transaction.getCustomerEmail(), offlineTransactionCreatedEvent
							.getCustomerEmail());
					Assertions.assertEquals(transaction.getCustomerName(), offlineTransactionCreatedEvent
							.getCustomerName());
					Assertions.assertEquals(transaction.getCurrencyCode(), offlineTransactionCreatedEvent
							.getCurrencyCode());
					Assertions.assertEquals(transaction.getDeviceId(), offlineTransactionCreatedEvent.getDeviceId());
					Assertions.assertEquals(transaction.getAmount(), offlineTransactionCreatedEvent.getAmount());
					Assertions.assertEquals(transaction.getNetAmount(), offlineTransactionCreatedEvent.getNetAmount());
					Assertions.assertEquals(transaction.getFee(), offlineTransactionCreatedEvent.getFee());
					Assertions.assertEquals(transaction.getSystemSource(), offlineTransactionCreatedEvent
							.getSystemSource());
					Assertions.assertEquals(transaction.getDescription(), offlineTransactionCreatedEvent
							.getDescription());
					Assertions.assertEquals(com.styl.pacific.kafka.payments.avro.model.PaymentTransactionType.PURCHASE,
							offlineTransactionCreatedEvent.getTransactionType());
					Assertions.assertEquals(transaction.getPaidAt()
							.toEpochMilli(), offlineTransactionCreatedEvent.getPaidAt());
					Assertions.assertEquals(transaction.getAppliedFixedSurcharge(), offlineTransactionCreatedEvent
							.getAppliedFixedSurcharge());

					Assertions.assertEquals(0, transaction.getAppliedSurchargeRate()
							.compareTo(offlineTransactionCreatedEvent.getAppliedSurchargeRate()));

					Assertions.assertEquals(transaction.getOfflineIdempotencyKey()
							.getValue(), offlineTransactionCreatedEvent.getOfflineIdempotencyKey());
					Assertions.assertEquals(transaction.getUserCardId(), offlineTransactionCreatedEvent
							.getUserCardId());

				});
	}

	@Test
	void testCreateNetsOfflinePaymentTransactionSuccessfullyWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(14_000L);
		final var paymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Nets Payment Method 10000")
				.processorId(PaymentProcessorId.NETS_TERMINAL_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.POS))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Nets Payment Description 10000")
				.paymentInstruction("Nets Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeRate(BigDecimal.valueOf(0.123))
				.surchargeTitle("Nets Payment Title 10000")
				.processorConfig(SimplePaymentMethodConfiguration.builder()
						.configs(objectMapper.convertValue(NetsTerminalPaymentMethodConfiguration.builder()
								.netsFamilyCardType(NetsFamilyCardType.CREDIT_CARD)
								.build(), new TypeReference<>() {
								}))
						.build())
				.build());

		final var settlementData = NetsPaymentSettlementSessionRequest.builder()
				.approvalCode("123")
				.merchantId("123")
				.terminalId("terminalId")
				.stan("stan")
				.cardName("cardName")
				.canNumber("canNumber")
				.balance("balance")
				.newBalance("newBalance")
				.purchaseAmount("purchaseAmount")
				.batchNumber("batchNumber")
				.invoiceNumber("invoiceNumber")
				.rrn("rrn")
				.tvr("tvr")
				.entryType("entryType")
				.issuerName("issuerName")
				.date("date")
				.time("time")
				.build();

		final var request = CreateOfflineTransactionRequest.builder()
				.offlineIdempotencyKey("nets-idempotency-key-01")
				.description("description")
				.paymentReference("paymentReference")
				.customerId("1234567890001")
				.userCardId("card-id-1234567890002")
				.customerEmail("<EMAIL>")
				.customerName("customer-1234567890002-name")
				.deviceId("device-id-1234567890001")
				.currencyCode("SGD")
				.amount(2495L)
				.netAmount(2222L)
				.fee(273L)
				.paymentMethodId(paymentMethod.getId()
						.getValue())
				.paymentProcessorId(PaymentProcessorId.NETS_TERMINAL_PAYMENT)
				.surchargeRate(paymentMethod.getSurchargeRate())
				.initiatedAt(Instant.now())
				.paymentMethodDisplayName(paymentMethod.getDisplayName())
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				.transactionStatus(PaymentTransactionStatus.SUCCEEDED)
				.paidAt(Instant.now())
				.transactionNumber("transaction-number-customer-1234567890002")
				.deviceTransactionNumber("device-number-customer-1234567890002")
				.settlementData(settlementData)
				.build();

		final var offlinePaymentTransactionCreatedEventCaptor = ArgumentCaptor.forClass(
				com.styl.pacific.kafka.payments.avro.model.PaymentOfflineTransactionCreatedEventAvroModel.class);

		// Act & Assert
		webClient.post()
				.uri("/api/payment/transactions/offline")
				.contentType(MediaType.APPLICATION_JSON)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.bodyValue(request)
				.exchange()
				.expectStatus()
				.isCreated()
				.expectBody(PaymentTransactionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(paymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentProcessorId.NETS_TERMINAL_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals(request.getPaymentReference(), actual.getPaymentReference());
					Assertions.assertEquals(request.getCustomerId(), actual.getCustomerId());
					Assertions.assertEquals(request.getCustomerEmail(), actual.getCustomerEmail());
					Assertions.assertEquals(request.getCustomerName(), actual.getCustomerName());
					Assertions.assertEquals(request.getCurrencyCode(), actual.getCurrencyCode());
					Assertions.assertEquals(request.getAmount(), actual.getAmount());
					Assertions.assertEquals(request.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(request.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(request.getDescription(), actual.getDescription());
					Assertions.assertEquals(request.getDeviceId(), actual.getDeviceId());
					Assertions.assertEquals(request.getTransactionNumber(), actual.getTransactionNumber());
					Assertions.assertEquals(request.getUserCardId(), actual.getUserCardId());
					Assertions.assertEquals(request.getPaidAt()
							.toEpochMilli(), actual.getPaidAt()
									.toEpochMilli());
					Assertions.assertEquals(request.getOfflineIdempotencyKey(), actual.getOfflineIdempotencyKey());
					Assertions.assertEquals(PaymentTransactionStatus.SUCCEEDED, actual.getTransactionStatus());

					final var actualSettlementData = (NetsPaymentSettlementResponse) actual.getSettlementData();
					Assertions.assertEquals(settlementData.getApprovalCode(), actualSettlementData.getApprovalCode());
					Assertions.assertEquals(settlementData.getMerchantId(), actualSettlementData.getMerchantId());
					Assertions.assertEquals(settlementData.getTerminalId(), actual.getTerminalId());
					Assertions.assertEquals(settlementData.getTerminalId(), actualSettlementData.getTerminalId());
					Assertions.assertEquals(settlementData.getStan(), actualSettlementData.getStan());
					Assertions.assertEquals(settlementData.getCardName(), actualSettlementData.getCardName());
					Assertions.assertEquals(settlementData.getCanNumber(), actualSettlementData.getCanNumber());
					Assertions.assertEquals(settlementData.getBalance(), actualSettlementData.getBalance());
					Assertions.assertEquals(settlementData.getNewBalance(), actualSettlementData.getNewBalance());
					Assertions.assertEquals(settlementData.getPurchaseAmount(), actualSettlementData
							.getPurchaseAmount());
					Assertions.assertEquals(settlementData.getBatchNumber(), actualSettlementData.getBatchNumber());
					Assertions.assertEquals(settlementData.getInvoiceNumber(), actualSettlementData.getInvoiceNumber());
					Assertions.assertEquals(settlementData.getRrn(), actualSettlementData.getRrn());
					Assertions.assertEquals(settlementData.getTvr(), actualSettlementData.getTvr());
					Assertions.assertEquals(settlementData.getEntryType(), actualSettlementData.getEntryType());
					Assertions.assertEquals(settlementData.getIssuerName(), actualSettlementData.getIssuerName());
					Assertions.assertEquals(settlementData.getDate(), actualSettlementData.getDate());
					Assertions.assertEquals(settlementData.getTime(), actualSettlementData.getTime());

					final var transaction = transactionQueryService.getTransactionByTenantIdAndId(tenantId,
							MapstructCommonDomainMapper.INSTANCE.longToPaymentTransactionId(Long.valueOf(actual
									.getId())));

					verify(kafkaOfflineTransactionCreatedEventPublisher, times(1)).publish(
							offlinePaymentTransactionCreatedEventCaptor.capture());
					final var offlineTransactionCreatedEvent = offlinePaymentTransactionCreatedEventCaptor.getValue();

					Assertions.assertEquals(actual.getPaymentSessionId(), offlineTransactionCreatedEvent
							.getPaymentSessionId());
					Assertions.assertEquals(paymentMethod.getId()
							.getValue(), offlineTransactionCreatedEvent.getPaymentMethodId());
					Assertions.assertEquals(PaymentProcessorId.NETS_TERMINAL_PAYMENT.name(),
							offlineTransactionCreatedEvent.getPaymentProcessorId());
					Assertions.assertEquals(transaction.getPaymentReference(), offlineTransactionCreatedEvent
							.getPaymentReference());
					Assertions.assertEquals(transaction.getCustomerId(), offlineTransactionCreatedEvent
							.getCustomerId());
					Assertions.assertEquals(transaction.getCustomerEmail(), offlineTransactionCreatedEvent
							.getCustomerEmail());
					Assertions.assertEquals(transaction.getCustomerName(), offlineTransactionCreatedEvent
							.getCustomerName());
					Assertions.assertEquals(transaction.getCurrencyCode(), offlineTransactionCreatedEvent
							.getCurrencyCode());
					Assertions.assertEquals(transaction.getAmount(), offlineTransactionCreatedEvent.getAmount());
					Assertions.assertEquals(transaction.getNetAmount(), offlineTransactionCreatedEvent.getNetAmount());
					Assertions.assertEquals(transaction.getFee(), offlineTransactionCreatedEvent.getFee());
					Assertions.assertEquals(transaction.getSystemSource(), offlineTransactionCreatedEvent
							.getSystemSource());
					Assertions.assertEquals(transaction.getDescription(), offlineTransactionCreatedEvent
							.getDescription());
					Assertions.assertEquals(com.styl.pacific.kafka.payments.avro.model.PaymentTransactionType.PURCHASE,
							offlineTransactionCreatedEvent.getTransactionType());
					Assertions.assertEquals(transaction.getPaidAt()
							.toEpochMilli(), offlineTransactionCreatedEvent.getPaidAt());
					Assertions.assertEquals(transaction.getAppliedFixedSurcharge(), offlineTransactionCreatedEvent
							.getAppliedFixedSurcharge());
					Assertions.assertEquals(transaction.getDeviceId(), offlineTransactionCreatedEvent.getDeviceId());
					Assertions.assertEquals(0, transaction.getAppliedSurchargeRate()
							.compareTo(offlineTransactionCreatedEvent.getAppliedSurchargeRate()));

					Assertions.assertEquals(transaction.getOfflineIdempotencyKey()
							.getValue(), offlineTransactionCreatedEvent.getOfflineIdempotencyKey());
					Assertions.assertEquals(transaction.getUserCardId(), offlineTransactionCreatedEvent
							.getUserCardId());

				});
	}

	@Test
	void testQueryTransactionsWithMultipleCustomersWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var tenantId = new TenantId(11_000L);
		final var now = Instant.now();
		final var paymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Nets Payment Method 10000")
				.processorId(PaymentProcessorId.NETS_TERMINAL_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.POS))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Nets Payment Description 10000")
				.paymentInstruction("Nets Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeRate(BigDecimal.valueOf(0.123))
				.surchargeTitle("Nets Payment Title 10000")
				.processorConfig(SimplePaymentMethodConfiguration.builder()
						.configs(objectMapper.convertValue(NetsTerminalPaymentMethodConfiguration.builder()
								.netsFamilyCardType(NetsFamilyCardType.CREDIT_CARD)
								.build(), new TypeReference<>() {
								}))
						.build())
				.build());

		final var customer1 = "11111";
		final var session1 = paymentSessionSupporter.initStripePaymentSession(tenantId, CreatePaymentSessionCommand
				.builder()
				.paymentProcessorId(paymentMethod.getProcessorId())
				.paymentMethodId(paymentMethod.getId())
				.description("Payment Order #1234")
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(2495L)
				.netAmount(2222L)
				.fee(273L)
				.currencyCode("SGD")
				.customerId(customer1)
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				.deviceId("1234")
				.expiredInMilliseconds(Duration.ofMinutes(16)
						.toMillis())
				.build());

		final var settlementData1 = NetsPaymentSettlementSessionRequest.builder()
				.approvalCode("123")
				.merchantId("123")
				.terminalId("terminalId")
				.stan("stan")
				.cardName("cardName")
				.canNumber("canNumber")
				.balance("balance")
				.newBalance("newBalance")
				.purchaseAmount("purchaseAmount")
				.batchNumber("batchNumber")
				.invoiceNumber("invoiceNumber")
				.rrn("rrn")
				.tvr("tvr")
				.entryType("entryType")
				.issuerName("issuerName")
				.date("date")
				.time("time")
				.build();

		final var transaction1 = paymentSettlementSupporter.settleTransaction(tenantId,
				PaymentSessionRequestMapper.INSTANCE.toSettlePaymentSessionCommand(session1.getId()
						.getValue(), SettlePaymentSessionRequest.builder()
								.transactionStatus(PaymentTransactionStatus.SUCCEEDED)
								.transactionNumber("Transaction Number 00001")
								.paidAt(now)
								.transactionNumber("transactionNumber-001")
								.deviceTransactionNumber("deviceTransactionNumber-001")
								.settlementData(settlementData1)
								.extraSettlementData(Map.of("key2", "val2"))
								.build(), Boolean.FALSE, objectMapper));

		final var customer2 = "22222";
		final var session2 = paymentSessionSupporter.initStripePaymentSession(tenantId, CreatePaymentSessionCommand
				.builder()
				.paymentProcessorId(paymentMethod.getProcessorId())
				.paymentMethodId(paymentMethod.getId())
				.description("Payment Order #1234")
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(2495L)
				.netAmount(2222L)
				.fee(273L)
				.currencyCode("SGD")
				.customerId(customer2)
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				.deviceId("1234")
				.expiredInMilliseconds(Duration.ofMinutes(16)
						.toMillis())
				.build());

		final var settlementData2 = NetsPaymentSettlementSessionRequest.builder()
				.approvalCode("123")
				.merchantId("123")
				.terminalId("terminalId")
				.stan("stan")
				.cardName("cardName")
				.canNumber("canNumber")
				.balance("balance")
				.newBalance("newBalance")
				.purchaseAmount("purchaseAmount")
				.batchNumber("batchNumber")
				.invoiceNumber("invoiceNumber")
				.rrn("rrn")
				.tvr("tvr")
				.entryType("entryType")
				.issuerName("issuerName")
				.date("date")
				.time("time")
				.build();

		final var transaction2 = paymentSettlementSupporter.settleTransaction(tenantId,
				PaymentSessionRequestMapper.INSTANCE.toSettlePaymentSessionCommand(session2.getId()
						.getValue(), SettlePaymentSessionRequest.builder()
								.transactionStatus(PaymentTransactionStatus.SUCCEEDED)
								.transactionNumber("Transaction Number 00002")
								.paidAt(now)
								.transactionNumber("transactionNumber-002")
								.deviceTransactionNumber("deviceTransactionNumber-002")
								.settlementData(settlementData2)
								.extraSettlementData(Map.of("key2", "val2"))
								.build(), Boolean.FALSE, objectMapper));

		final var customer3 = "333333";
		final var settlementData3 = NetsPaymentSettlementSessionRequest.builder()
				.approvalCode("123")
				.merchantId("123")
				.terminalId("terminalId")
				.stan("stan")
				.cardName("cardName")
				.canNumber("canNumber")
				.balance("balance")
				.newBalance("newBalance")
				.purchaseAmount("purchaseAmount")
				.batchNumber("batchNumber")
				.invoiceNumber("invoiceNumber")
				.rrn("rrn")
				.tvr("tvr")
				.entryType("entryType")
				.issuerName("issuerName")
				.date("date")
				.time("time")
				.build();

		final var transaction3 = offlineTransactionService.createOfflinePaymentTransaction(tenantId,
				PaymentOfflineTransactionRequestMapper.INSTANCE.toOfflineTransactionCommand(
						CreateOfflineTransactionRequest.builder()
								.offlineIdempotencyKey("nets-idempotency-key-01")
								.description("description")
								.paymentReference("paymentReference")
								.customerId(customer3)
								.userCardId("card-id-1234567890002")
								.customerEmail("<EMAIL>")
								.customerName("customer-1234567890002-name")
								.deviceId("device-id-1234567890001")
								.currencyCode("SGD")
								.amount(2495L)
								.netAmount(2222L)
								.fee(273L)
								.paymentMethodId(paymentMethod.getId()
										.getValue())
								.paymentProcessorId(PaymentProcessorId.NETS_TERMINAL_PAYMENT)
								.surchargeRate(paymentMethod.getSurchargeRate())
								.initiatedAt(Instant.now())
								.paymentMethodDisplayName(paymentMethod.getDisplayName())
								.systemSource("System Source 01")
								.merchantName("Merchant Name")
								.transactionStatus(PaymentTransactionStatus.SUCCEEDED)
								.paidAt(Instant.now())
								.transactionNumber("transaction-number-customer-1234567890002")
								.deviceTransactionNumber("device-number-customer-1234567890002")
								.settlementData(settlementData3)
								.build(), objectMapper));

		final var request = QueryPaymentTransactionPaginationRequest.builder()
				.filter(FilterPaymentTransactionRequest.builder()
						.byCustomerIds(Set.of(customer1, customer2, customer3))
						.build())
				.page(0)
				.size(10)
				.sortDirection("ASC")
				.sortFields(List.of("id"))
				.build();

		final var transactions = List.of(transaction1, transaction2, transaction3);

		final Map<String, String> requestParamMap = new HashMap<>();

		requestParamMap.put("size", String.valueOf(request.getSize()));
		requestParamMap.put("page", String.valueOf(request.getPage()));
		requestParamMap.put("sortDirection", "ASC");
		requestParamMap.put("sortFields", String.join(",", request.getSortFields()));

		// Act & Assert
		webClient.get()
				.uri(uriBuilder -> {
					final LinkedMultiValueMap<String, String> requestParams = new LinkedMultiValueMap<>(requestParamMap
							.entrySet()
							.stream()
							.filter(it -> org.apache.commons.lang3.StringUtils.isNotBlank(it.getValue()))
							.collect(Collectors.toMap(Map.Entry::getKey, it -> List.of(it.getValue()))));

					uriBuilder.path("/api/payment/transactions");
					uriBuilder.queryParams(requestParams);
					return uriBuilder.build();
				})
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(Paging.class)
				.consumeWith(response -> {
					final var paymentTransactionResponse = Optional.ofNullable(response.getResponseBody())
							.map(Paging::getContent)
							.map(ls -> (List<PaymentTransactionResponse>) ls.stream()
									.map(it -> objectMapper.convertValue(it, PaymentTransactionResponse.class))
									.toList())
							.orElseGet(List::of);

					Assertions.assertEquals(3, paymentTransactionResponse.size());

					IntStream.range(0, transactions.size())
							.forEach(index -> {
								final var actual = paymentTransactionResponse.get(index);
								final var transaction = transactions.get(index);

								Assertions.assertNotNull(actual);
								Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
								Assertions.assertEquals(paymentMethod.getId()
										.getValue()
										.toString(), actual.getPaymentMethodId());
								Assertions.assertEquals(PaymentProcessorId.NETS_TERMINAL_PAYMENT, actual
										.getPaymentProcessorId());

								Assertions.assertEquals("SGD", actual.getCurrencyCode());
								Assertions.assertEquals(transaction.getAmount(), actual.getAmount());
								Assertions.assertEquals(transaction.getNetAmount(), actual.getNetAmount());
								Assertions.assertEquals(transaction.getSystemSource(), actual.getSystemSource());
								Assertions.assertEquals(transaction.getDescription(), actual.getDescription());
								Assertions.assertEquals(PaymentTransactionStatus.SUCCEEDED, actual
										.getTransactionStatus());
								Assertions.assertEquals(transaction.getPaidAt()
										.toEpochMilli(), actual.getPaidAt()
												.toEpochMilli());
								Assertions.assertEquals(transaction.getDeviceTransactionNumber(), actual
										.getDeviceTransactionNumber());
								Assertions.assertEquals(transaction.getTransactionNumber(), actual
										.getTransactionNumber());
								Assertions.assertEquals(transaction.getPaymentReference(), actual
										.getPaymentReference());
								Assertions.assertEquals(transaction.getCustomerId(), actual.getCustomerId());
								Assertions.assertEquals(transaction.getCustomerEmail(), actual.getCustomerEmail());
								Assertions.assertEquals(transaction.getCustomerName(), actual.getCustomerName());

								Assertions.assertEquals(transaction.getAmount(), actual.getAmount());
								Assertions.assertEquals(transaction.getNetAmount(), actual.getNetAmount());
								Assertions.assertEquals(transaction.getSystemSource(), actual.getSystemSource());
								Assertions.assertEquals(transaction.getDeviceId(), actual.getDeviceId());
								Assertions.assertEquals(transaction.getDescription(), actual.getDescription());
								Assertions.assertEquals(transaction.getIsAsync(), actual.getIsAsync());
								Assertions.assertEquals(transaction.isOffline(), actual.isOffline());
							});

				});
	}

	@Test
	void testCreateWalletOfflinePaymentTransactionSuccessfullyWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(14_000L);
		final var now = Instant.now();
		final var paymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("EWallet Payment Method 10000")
				.processorId(PaymentProcessorId.E_WALLET_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.POS))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("EWallet Payment Description 10000")
				.paymentInstruction("EWallet Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeRate(BigDecimal.valueOf(0.123))
				.surchargeTitle("EWallet Payment Title 10000")
				.build());

		final var request = CreateOfflineTransactionRequest.builder()
				.offlineIdempotencyKey("eWallet-idempotency-key-0001")
				.description("description")
				.paymentReference("paymentReference")
				.customerId("1234567890001")
				.userCardId("card-id-1234567890002")
				.customerEmail("<EMAIL>")
				.customerName("customer-1234567890002-name")
				.deviceId("device-id-1234567890001")
				.currencyCode("SGD")
				.amount(2495L)
				.netAmount(2222L)
				.fee(273L)
				.paymentMethodId(paymentMethod.getId()
						.getValue())
				.paymentProcessorId(PaymentProcessorId.E_WALLET_PAYMENT)
				.surchargeRate(paymentMethod.getSurchargeRate())
				.initiatedAt(Instant.now())
				.paymentMethodDisplayName(paymentMethod.getDisplayName())
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				.transactionStatus(PaymentTransactionStatus.SUCCEEDED)
				.paidAt(Instant.now())
				.deviceTransactionNumber("device-number-customer-1234567890444")
				.build();

		final var offlinePaymentTransactionCreatedEventCaptor = ArgumentCaptor.forClass(
				com.styl.pacific.kafka.payments.avro.model.PaymentOfflineTransactionCreatedEventAvroModel.class);

		final var walletOfflineTransactionCaptor = ArgumentCaptor.forClass(CreateCreditOfflineTransactionRequest.class);

		when(walletClient.payOffline(walletOfflineTransactionCaptor.capture())).thenReturn(WalletTransactionResponse
				.builder()
				.transactionId("55557777")
				.cardId("card-id-1234567890002")
				.walletId("5555")
				.sourceWalletId(6666L)
				.destinationWalletId(7777L)
				.transactionType(TransactionType.CREDIT)
				.transactionCategory(TransactionCategory.PURCHASE)
				.paymentSessionId("payment-session-id-123456")
				.paymentTransactionId("payment-transaction-id-123456")
				.cardId("card-id-1234567890002")
				.currency(CurrencyResponse.builder()
						.currencyCode("SGD")
						.build())
				.amount(BigInteger.valueOf(2495))
				.oldBalance(BigInteger.valueOf(10000))
				.balance(BigInteger.valueOf(12495))
				.isOffline(true)
				.createdAt(Instant.now()
						.toEpochMilli())
				.description("description")
				.build());

		// Act & Assert
		webClient.post()
				.uri("/api/payment/transactions/offline")
				.contentType(MediaType.APPLICATION_JSON)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.bodyValue(request)
				.exchange()
				.expectStatus()
				.isCreated()
				.expectBody(PaymentTransactionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(paymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentProcessorId.E_WALLET_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals(request.getPaymentReference(), actual.getPaymentReference());
					Assertions.assertEquals(request.getCustomerId(), actual.getCustomerId());
					Assertions.assertEquals(request.getCustomerEmail(), actual.getCustomerEmail());
					Assertions.assertEquals(request.getCustomerName(), actual.getCustomerName());
					Assertions.assertEquals(request.getCurrencyCode(), actual.getCurrencyCode());
					Assertions.assertEquals(request.getAmount(), actual.getAmount());
					Assertions.assertEquals(request.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(request.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(request.getDescription(), actual.getDescription());
					Assertions.assertEquals(request.getDeviceId(), actual.getDeviceId());
					Assertions.assertEquals("55557777", actual.getTransactionNumber());
					Assertions.assertEquals(request.getUserCardId(), actual.getUserCardId());
					Assertions.assertEquals(request.getPaidAt()
							.toEpochMilli(), actual.getPaidAt()
									.toEpochMilli());
					Assertions.assertEquals(request.getOfflineIdempotencyKey(), actual.getOfflineIdempotencyKey());
					Assertions.assertEquals(PaymentTransactionStatus.SUCCEEDED, actual.getTransactionStatus());

					final var transaction = transactionQueryService.getTransactionByTenantIdAndId(tenantId,
							MapstructCommonDomainMapper.INSTANCE.longToPaymentTransactionId(Long.valueOf(actual
									.getId())));

					verify(kafkaOfflineTransactionCreatedEventPublisher, times(1)).publish(
							offlinePaymentTransactionCreatedEventCaptor.capture());
					final var offlineTransactionCreatedEvent = offlinePaymentTransactionCreatedEventCaptor.getValue();

					Assertions.assertEquals(actual.getPaymentSessionId(), offlineTransactionCreatedEvent
							.getPaymentSessionId());
					Assertions.assertEquals(paymentMethod.getId()
							.getValue(), offlineTransactionCreatedEvent.getPaymentMethodId());
					Assertions.assertEquals(PaymentProcessorId.E_WALLET_PAYMENT.name(), offlineTransactionCreatedEvent
							.getPaymentProcessorId());
					Assertions.assertEquals(transaction.getPaymentReference(), offlineTransactionCreatedEvent
							.getPaymentReference());
					Assertions.assertEquals(transaction.getCustomerId(), offlineTransactionCreatedEvent
							.getCustomerId());
					Assertions.assertEquals(transaction.getCustomerEmail(), offlineTransactionCreatedEvent
							.getCustomerEmail());
					Assertions.assertEquals(transaction.getCustomerName(), offlineTransactionCreatedEvent
							.getCustomerName());
					Assertions.assertEquals(transaction.getCurrencyCode(), offlineTransactionCreatedEvent
							.getCurrencyCode());
					Assertions.assertEquals(transaction.getDeviceId(), offlineTransactionCreatedEvent.getDeviceId());
					Assertions.assertEquals(transaction.getAmount(), offlineTransactionCreatedEvent.getAmount());
					Assertions.assertEquals(transaction.getNetAmount(), offlineTransactionCreatedEvent.getNetAmount());
					Assertions.assertEquals(transaction.getFee(), offlineTransactionCreatedEvent.getFee());
					Assertions.assertEquals(transaction.getSystemSource(), offlineTransactionCreatedEvent
							.getSystemSource());
					Assertions.assertEquals(transaction.getDescription(), offlineTransactionCreatedEvent
							.getDescription());
					Assertions.assertEquals(com.styl.pacific.kafka.payments.avro.model.PaymentTransactionType.PURCHASE,
							offlineTransactionCreatedEvent.getTransactionType());
					Assertions.assertEquals(transaction.getPaidAt()
							.toEpochMilli(), offlineTransactionCreatedEvent.getPaidAt());
					Assertions.assertEquals(transaction.getAppliedFixedSurcharge(), offlineTransactionCreatedEvent
							.getAppliedFixedSurcharge());

					Assertions.assertEquals(0, transaction.getAppliedSurchargeRate()
							.compareTo(offlineTransactionCreatedEvent.getAppliedSurchargeRate()));

					Assertions.assertEquals(transaction.getOfflineIdempotencyKey()
							.getValue(), offlineTransactionCreatedEvent.getOfflineIdempotencyKey());
					Assertions.assertEquals(transaction.getUserCardId(), offlineTransactionCreatedEvent
							.getUserCardId());

					verify(walletClient, times(1)).payOffline(walletOfflineTransactionCaptor.capture());
					final var createWalletOfflineTransactionRequest = walletOfflineTransactionCaptor.getValue();

					Assertions.assertEquals(actual.getPaymentSessionId(), createWalletOfflineTransactionRequest
							.getPaymentSessionId());
					Assertions.assertEquals(actual.getId(), createWalletOfflineTransactionRequest
							.getPaymentTransactionId()
							.toString());
					Assertions.assertEquals(actual.getOfflineIdempotencyKey(), createWalletOfflineTransactionRequest
							.getIdempotencyKey());
					Assertions.assertEquals(actual.getDeviceId(), createWalletOfflineTransactionRequest.getDeviceId());
					Assertions.assertEquals(actual.getUserCardId(), createWalletOfflineTransactionRequest.getCardId());
					Assertions.assertEquals(actual.getDescription(), createWalletOfflineTransactionRequest
							.getDescription());
					Assertions.assertEquals(actual.getCustomerId(), createWalletOfflineTransactionRequest.getUserId()
							.toString());
					Assertions.assertEquals(actual.getCurrencyCode(), createWalletOfflineTransactionRequest
							.getCurrency());
					Assertions.assertEquals(actual.getAmount(), createWalletOfflineTransactionRequest.getAmount()
							.longValue());

					final var eWalletSettlementData = objectMapper.convertValue(transaction.getSettlementData()
							.getData(), EWalletPaymentSettlementData.class);
					Assertions.assertNotNull(eWalletSettlementData.getWalletTransactionId());
					Assertions.assertNotNull(eWalletSettlementData.getWalletId());
				});
	}

}
