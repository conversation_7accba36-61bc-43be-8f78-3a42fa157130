/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.product.repository;

import com.styl.pacific.catalog.service.data.access.relational.product.entity.ProductAllergenEntity;
import com.styl.pacific.catalog.service.data.access.relational.product.entity.ProductAllergenEntityId;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;

/**
 * <AUTHOR>
 */
public interface ProductAllergenJpaRepository extends JpaRepository<ProductAllergenEntity, ProductAllergenEntityId>,
		QuerydslPredicateExecutor<ProductAllergenEntity> {
	@Modifying
	@Query(value = "DELETE FROM ProductAllergenEntity i WHERE i.id.productId = :productId AND i NOT IN (:entities)")
	void deleteAllNotInByProduct(@Param("productId") Long productId,
			@Param("entities") List<ProductAllergenEntity> entities);

}
