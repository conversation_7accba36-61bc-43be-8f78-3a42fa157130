/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.allergen.mapper;

import com.styl.pacific.catalog.service.data.access.relational.allergen.entity.AllergenEntity;
import com.styl.pacific.catalog.service.data.access.relational.common.mapper.CommonDataAccessMapper;
import com.styl.pacific.catalog.service.domain.allergen.entity.Allergen;
import com.styl.pacific.catalog.service.domain.common.mapper.CommonDataMapper;
import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, uses = { CommonDataAccessMapper.class, CommonDataMapper.class,
		MapstructCommonMapper.class, MapstructCommonDomainMapper.class })
public interface AllergenDataAccessMapper {
	AllergenDataAccessMapper INSTANCE = Mappers.getMapper(AllergenDataAccessMapper.class);

	@Mapping(target = "id", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "tenantId", source = "tenantId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "deletedAt", ignore = true)
	AllergenEntity allergenToEntity(Allergen model);

	@Mapping(target = "id", qualifiedByName = "longToAllergenId")
	@Mapping(target = "tenantId", qualifiedByName = "longToTenantId")
	Allergen allergenEntityToModel(AllergenEntity entity);

}
