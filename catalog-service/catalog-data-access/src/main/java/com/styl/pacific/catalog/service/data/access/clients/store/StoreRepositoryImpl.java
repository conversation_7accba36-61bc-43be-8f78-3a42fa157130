/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.clients.store;

import com.styl.pacific.catalog.service.domain.store.output.repository.StoreRepository;
import com.styl.pacific.common.feign.exception.FeignBadRequestException;
import com.styl.pacific.domain.valueobject.StoreId;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class StoreRepositoryImpl implements StoreRepository {
	private final StoreClient storeClient;

	@Override
	public boolean existsById(StoreId id) {
		try {
			return Optional.ofNullable(storeClient.getStore(id.getValue()))
					.filter(responseEntity -> responseEntity.getStatusCode()
							.is2xxSuccessful())
					.map(ResponseEntity::getBody)
					.isPresent();
		} catch (FeignBadRequestException exception) {
			return false;
		}

	}
}
