/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.product.mapper;

import com.styl.pacific.catalog.service.data.access.relational.common.mapper.CommonDataAccessMapper;
import com.styl.pacific.catalog.service.data.access.relational.product.entity.ProductOptionEntity;
import com.styl.pacific.catalog.service.data.access.relational.product.entity.ProductOptionItemEntity;
import com.styl.pacific.catalog.service.domain.common.mapper.CommonDataMapper;
import com.styl.pacific.catalog.service.domain.product.entity.ProductOption;
import com.styl.pacific.catalog.service.domain.product.entity.ProductOptionItem;
import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class, CommonDataMapper.class,
		CommonDataAccessMapper.class, MapstructCommonDomainMapper.class })
public interface ProductOptionDataAccessMapper {
	ProductOptionDataAccessMapper INSTANCE = Mappers.getMapper(ProductOptionDataAccessMapper.class);

	@Mapping(target = "id", source = "entity.id", qualifiedByName = "longToProductOptionId")
	@Mapping(target = "productId", source = "entity.productId", qualifiedByName = "longToProductId")
	ProductOption productOptionEnityToProductOption(ProductOptionEntity entity);

	@Mapping(target = "id", source = "option.id", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "productId", source = "option.productId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "product.id", source = "option.productId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "deletedAt", ignore = true)
	ProductOptionEntity productOptionToProductOptionEntity(ProductOption option);

	@Mapping(target = "id", source = "entity.id", qualifiedByName = "longToProductOptionItemId")
	@Mapping(target = "additionPrice", source = "entity.additionPrice", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "optionId", source = "entity.optionId", qualifiedByName = "longToProductOptionId", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
	ProductOptionItem productOptionItemEntityToProductItemEntity(ProductOptionItemEntity entity);

	@Mapping(target = "id", source = "item.id", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "optionId", source = "item.optionId", qualifiedByName = "baseLongIdToLong", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
	@Mapping(target = "option.id", source = "item.optionId", qualifiedByName = "baseLongIdToLong", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
	@Mapping(target = "additionPrice", source = "item.additionPrice", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "deletedAt", ignore = true)
	ProductOptionItemEntity productOptionItemToProductOptionItemEntity(ProductOptionItem item);
}
