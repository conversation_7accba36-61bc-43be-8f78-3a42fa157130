/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.product.converter;

import com.fasterxml.jackson.core.type.TypeReference;
import com.styl.pacific.data.access.jpa.converter.BaseJsonToTextConverter;
import jakarta.persistence.Converter;
import java.util.List;

/**
 * <AUTHOR>
 */
@Converter
public class KeywordsDataConverter extends BaseJsonToTextConverter<List<String>> {
	public KeywordsDataConverter() {
		super(new TypeReference<>() {
		});
	}
}
