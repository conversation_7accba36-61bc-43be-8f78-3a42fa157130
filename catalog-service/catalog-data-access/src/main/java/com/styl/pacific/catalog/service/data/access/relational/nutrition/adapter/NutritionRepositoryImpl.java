/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.nutrition.adapter;

import com.querydsl.core.types.Predicate;
import com.styl.pacific.catalog.service.data.access.relational.nutrition.entity.NutritionEntity;
import com.styl.pacific.catalog.service.data.access.relational.nutrition.entity.NutritionTenantEntity;
import com.styl.pacific.catalog.service.data.access.relational.nutrition.mapper.NutritionDataAccessMapper;
import com.styl.pacific.catalog.service.data.access.relational.nutrition.repository.NutritionJpaRepository;
import com.styl.pacific.catalog.service.data.access.relational.nutrition.repository.NutritionTenantJpaRepository;
import com.styl.pacific.catalog.service.data.access.relational.nutrition.specification.NutritionPredicates;
import com.styl.pacific.catalog.service.data.access.relational.nutrition.specification.NutritionTenantPredicates;
import com.styl.pacific.catalog.service.data.access.relational.nutrition.specification.ProductNutritionPredicates;
import com.styl.pacific.catalog.service.data.access.relational.product.repository.ProductNutritionJpaRepository;
import com.styl.pacific.catalog.service.domain.nutrition.dto.NutritionTenantDto;
import com.styl.pacific.catalog.service.domain.nutrition.dto.query.NutritionFilter;
import com.styl.pacific.catalog.service.domain.nutrition.dto.query.NutritionQuery;
import com.styl.pacific.catalog.service.domain.nutrition.dto.query.PaginationNutritionQuery;
import com.styl.pacific.catalog.service.domain.nutrition.entity.Nutrition;
import com.styl.pacific.catalog.service.domain.nutrition.output.repository.NutritionRepository;
import com.styl.pacific.data.access.utils.JpaPageableUtils;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.NutritionId;
import com.styl.pacific.domain.valueobject.TenantId;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class NutritionRepositoryImpl implements NutritionRepository {
	private final NutritionJpaRepository nutritionJpaRepository;
	private final NutritionTenantJpaRepository nutritionTenantJpaRepository;
	private final ProductNutritionJpaRepository productNutritionJpaRepository;

	@Override
	public Optional<Nutrition> findById(TenantId tenantId, NutritionId id) {
		Predicate specification = NutritionPredicates.withTenantIdAndId(Optional.ofNullable(tenantId)
				.map(TenantId::getValue)
				.orElse(null), id.getValue());
		return nutritionJpaRepository.findOne(specification)
				.map(NutritionDataAccessMapper.INSTANCE::toModel);
	}

	@Override
	public boolean existById(TenantId tenantId, NutritionId id) {
		Predicate specification = NutritionPredicates.withTenantIdAndId(Optional.ofNullable(tenantId)
				.map(TenantId::getValue)
				.orElse(null), id.getValue());
		return nutritionJpaRepository.exists(specification);
	}

	@Override
	public Nutrition save(Nutrition nutrition) {
		NutritionEntity entity = NutritionDataAccessMapper.INSTANCE.toEntity(nutrition);
		return NutritionDataAccessMapper.INSTANCE.toModel(nutritionJpaRepository.save(entity));
	}

	@Override
	public Nutrition update(Nutrition nutrition) {
		NutritionEntity entity = NutritionDataAccessMapper.INSTANCE.toEntity(nutrition);
		return NutritionDataAccessMapper.INSTANCE.toModel(nutritionJpaRepository.saveAndFlush(entity));
	}

	@Override
	public void deleteById(TenantId tenantId, NutritionId id) {
		Predicate specification = NutritionPredicates.withTenantIdAndId(Optional.ofNullable(tenantId)
				.map(TenantId::getValue)
				.orElse(null), id.getValue());
		nutritionJpaRepository.findOne(specification)
				.ifPresent(nutritionJpaRepository::delete);
	}

	@Override
	public List<Nutrition> findAll(TenantId tenantId, NutritionQuery query) {
		Sort sort = JpaPageableUtils.createSort(new HashSet<>(query.sortFields()), query.sortDirection(),
				NutritionEntity.SORTABLE_FIELDS, NutritionEntity.FIELD_ID);
		NutritionFilter filter = Optional.ofNullable(query.filter())
				.orElse(NutritionFilter.builder()
						.build());
		Predicate specification = NutritionPredicates.withMultipleCriteria(Optional.ofNullable(tenantId)
				.map(TenantId::getValue)
				.orElse(null), filter.name(), filter.unit());
		return nutritionJpaRepository.findBy(specification, q -> q.sortBy(sort)
				.all())
				.stream()
				.map(NutritionDataAccessMapper.INSTANCE::toModel)
				.toList();
	}

	@Override
	public Paging<Nutrition> findAllPaging(TenantId tenantId, PaginationNutritionQuery query) {
		Pageable pageable = JpaPageableUtils.createPageable(query.getPage(), query.getSize(), query.getSortFields(),
				query.getSortDirection(), NutritionEntity.SORTABLE_FIELDS, NutritionEntity.FIELD_ID);
		NutritionFilter filter = Optional.ofNullable(query.getFilter())
				.orElse(NutritionFilter.builder()
						.build());
		Predicate specification = NutritionPredicates.withMultipleCriteria(Optional.ofNullable(tenantId)
				.map(TenantId::getValue)
				.orElse(null), filter.name(), filter.unit());
		Page<Nutrition> page = nutritionJpaRepository.findAll(specification, pageable)
				.map(NutritionDataAccessMapper.INSTANCE::toModel);
		return Paging.<Nutrition>builder()
				.content(page.getContent())
				.totalElements(page.getTotalElements())
				.totalPages(page.getTotalPages())
				.page(page.getPageable()
						.getPageNumber())
				.sort(page.getPageable()
						.getSort()
						.toList()
						.stream()
						.map(Sort.Order::toString)
						.toList())
				.build();
	}

	@Override
	public boolean isRelatedToProduct(TenantId tenantId, NutritionId id) {
		Predicate specification = ProductNutritionPredicates.withTenantIdAndId(Optional.ofNullable(tenantId)
				.map(TenantId::getValue)
				.orElse(null), id.getValue());
		return productNutritionJpaRepository.exists(specification);
	}

	@Override
	public List<Nutrition> saveAll(List<Nutrition> nutritionList) {
		List<NutritionEntity> nutritionEntityList = nutritionList.stream()
				.map(NutritionDataAccessMapper.INSTANCE::toEntity)
				.toList();
		return nutritionJpaRepository.saveAll(nutritionEntityList)
				.stream()
				.map(NutritionDataAccessMapper.INSTANCE::toModel)
				.toList();
	}

	@Override
	public void saveRecordTriggerTenant(TenantId tenantId) {
		NutritionTenantEntity entity = NutritionTenantEntity.builder()
				.tenantId(tenantId.getValue())
				.build();
		nutritionTenantJpaRepository.save(entity);
	}

	@Override
	public Optional<NutritionTenantDto> findTenantAlreadyTrigger(TenantId tenantId) {
		Predicate specification = NutritionTenantPredicates.withTenantId(Objects.requireNonNull(tenantId)
				.getValue());
		return nutritionTenantJpaRepository.findOne(specification)
				.map(NutritionDataAccessMapper.INSTANCE::toDto);
	}

}
