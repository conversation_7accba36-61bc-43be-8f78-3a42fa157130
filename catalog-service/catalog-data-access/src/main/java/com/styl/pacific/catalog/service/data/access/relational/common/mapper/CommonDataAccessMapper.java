/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.common.mapper;

import com.styl.pacific.catalog.service.data.access.relational.category.entity.CategoryEntity;
import com.styl.pacific.catalog.service.data.access.relational.healthierchoice.entity.HealthierChoiceEntity;
import com.styl.pacific.catalog.service.data.access.relational.nutrition.entity.NutritionEntity;
import com.styl.pacific.catalog.service.data.access.relational.product.entity.ProductEntity;
import com.styl.pacific.catalog.service.data.access.relational.product.entity.ProductImageEntity;
import com.styl.pacific.catalog.service.domain.common.mapper.CommonDataMapper;
import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.domain.valueobject.CategoryId;
import com.styl.pacific.domain.valueobject.HealthierChoiceId;
import com.styl.pacific.domain.valueobject.NutritionId;
import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.domain.valueobject.ProductImageId;
import org.mapstruct.Mapper;
import org.mapstruct.Named;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class, CommonDataMapper.class })
public interface CommonDataAccessMapper {

	@Named("categoryIdToCategoryEntity")
	default CategoryEntity categoryIdToCategoryEntity(CategoryId categoryId) {
		if (categoryId == null) {
			return null;
		}
		return CategoryEntity.builder()
				.id(categoryId.getValue())
				.build();
	}

	@Named("productIdToProductEntity")
	default ProductEntity productIdToProductEntity(ProductId id) {
		if (id == null) {
			return null;
		}
		return ProductEntity.builder()
				.id(id.getValue())
				.build();
	}

	@Named("productImageIdToProductImageEntity")
	default ProductImageEntity productImageIdToProductImageEntity(ProductImageId id) {
		if (id == null) {
			return null;
		}
		return ProductImageEntity.builder()
				.id(id.getValue())
				.build();
	}

	@Named("healthierChoiceIdToHealthierChoiceEntity")
	default HealthierChoiceEntity healthierChoiceIdToHealthierChoiceEntity(HealthierChoiceId id) {
		if (id == null) {
			return null;
		}
		return HealthierChoiceEntity.builder()
				.id(id.getValue())
				.build();
	}

	@Named("nutritionIdToNutritionEntity")
	default NutritionEntity nutritionIdToNutritionEntity(NutritionId id) {
		if (id == null) {
			return null;
		}
		return NutritionEntity.builder()
				.id(id.getValue())
				.build();
	}

}
