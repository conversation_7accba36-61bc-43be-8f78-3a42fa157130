/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.product.specification;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import com.styl.pacific.catalog.service.data.access.relational.product.entity.QProductAllergenEntity;
import java.util.Collection;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ProductAllergenPredicates {
	public static Predicate withProductIdIsNotDeleted(Long productId) {
		BooleanBuilder specifications = new BooleanBuilder();
		if (Objects.nonNull(productId)) {
			specifications.and(withProductId(productId));
		}
		specifications.and(withIsNotDeleted());
		return specifications;
	}

	public static Predicate joinAllergenWithProductId(Long productId) {
		BooleanBuilder specifications = new BooleanBuilder();
		specifications.and(withProductId(productId));
		specifications.and(withAllergenIsNotDeleted());
		return specifications;
	}

	public static Predicate withInProductIds(Collection<Long> productIds) {
		BooleanBuilder specifications = new BooleanBuilder();
		if (Objects.nonNull(productIds) && !productIds.isEmpty()) {
			specifications.and(withContainProductId(productIds));
		} else {
			return withEntityIsNull();
		}
		specifications.and(withIsNotDeleted());
		return specifications;
	}

	public static Predicate joinAllergenWithInProductIds(Collection<Long> productIds) {
		BooleanBuilder specifications = new BooleanBuilder();
		specifications.and(withInProductIds(productIds));
		specifications.and(withAllergenIsNotDeleted());
		return specifications;
	}

	private static Predicate withContainProductId(Collection<Long> productIds) {
		return QProductAllergenEntity.productAllergenEntity.id.productId.in(productIds);
	}

	private static Predicate withIsNotDeleted() {
		return QProductAllergenEntity.productAllergenEntity.deletedAt.isNull();
	}

	private static Predicate withAllergenIsNotDeleted() {
		return QProductAllergenEntity.productAllergenEntity.allergen.deletedAt.isNull();
	}

	private static Predicate withProductId(Long productId) {
		return QProductAllergenEntity.productAllergenEntity.id.productId.eq(productId);
	}

	private static Predicate withEntityIsNull() {
		return QProductAllergenEntity.productAllergenEntity.isNull();
	}
}
