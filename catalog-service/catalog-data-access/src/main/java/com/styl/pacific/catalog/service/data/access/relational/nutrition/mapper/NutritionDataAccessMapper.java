/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.nutrition.mapper;

import com.styl.pacific.catalog.service.data.access.relational.common.mapper.CommonDataAccessMapper;
import com.styl.pacific.catalog.service.data.access.relational.nutrition.entity.NutritionEntity;
import com.styl.pacific.catalog.service.data.access.relational.nutrition.entity.NutritionTenantEntity;
import com.styl.pacific.catalog.service.data.access.relational.product.mapper.ProductNutritionDataAccessMapper;
import com.styl.pacific.catalog.service.domain.common.mapper.CommonDataMapper;
import com.styl.pacific.catalog.service.domain.nutrition.dto.NutritionTenantDto;
import com.styl.pacific.catalog.service.domain.nutrition.entity.Nutrition;
import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, uses = { ProductNutritionDataAccessMapper.class,
		MapstructCommonMapper.class, CommonDataMapper.class, CommonDataAccessMapper.class,
		MapstructCommonDomainMapper.class })
public interface NutritionDataAccessMapper {
	NutritionDataAccessMapper INSTANCE = Mappers.getMapper(NutritionDataAccessMapper.class);

	@Mapping(target = "id", source = "nutrition.id", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "tenantId", source = "nutrition.tenantId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "deletedAt", ignore = true)
	NutritionEntity toEntity(Nutrition nutrition);

	@Mapping(target = "id", source = "entity.id", qualifiedByName = "longToNutritionId")
	@Mapping(target = "tenantId", source = "entity.tenantId", qualifiedByName = "longToTenantId")
	Nutrition toModel(NutritionEntity entity);

	@Mapping(target = "tenantId", qualifiedByName = "longToTenantId")
	NutritionTenantDto toDto(NutritionTenantEntity entity);
}
