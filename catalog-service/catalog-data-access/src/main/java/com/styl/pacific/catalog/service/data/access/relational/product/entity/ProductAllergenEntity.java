/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.product.entity;

import com.styl.pacific.catalog.service.data.access.relational.allergen.entity.AllergenEntity;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.MapsId;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

/**
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "tb_product_allergen")
@SQLDelete(sql = "UPDATE tb_product_allergen SET deleted_at = current_timestamp WHERE  allergen_id = ? AND product_id = ?")
@SQLRestriction("deleted_at IS NULL")
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@SuperBuilder
@EntityListeners(AuditingEntityListener.class)
public class ProductAllergenEntity {

	public static final String FIELD_ID = "id";
	public static final String FIELD_PRODUCT = "product";
	public static final String FIELD_ALLERGEN = "allergen";

	@EmbeddedId
	private ProductAllergenEntityId id;

	@ManyToOne
	@MapsId("productId")
	private ProductEntity product;

	@ManyToOne
	@MapsId("allergenId")
	private AllergenEntity allergen;

	@CreatedDate
	@Temporal(TemporalType.TIMESTAMP)
	@Column(nullable = false, updatable = false)
	protected Instant createdAt;

	@LastModifiedDate
	@Temporal(TemporalType.TIMESTAMP)
	protected Instant updatedAt;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(insertable = false)
	protected Instant deletedAt;
}
