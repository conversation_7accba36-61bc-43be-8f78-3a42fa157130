/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.nutrition.adapter;

import com.styl.pacific.catalog.service.data.access.relational.nutrition.loaders.ClassPathDefaultNutritionDataLoader;
import com.styl.pacific.catalog.service.domain.nutrition.dto.NutritionConfigDto;
import com.styl.pacific.catalog.service.domain.nutrition.output.repository.NutritionConfigRepository;
import java.util.Collections;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class NutritionConfigRepositoryImpl implements NutritionConfigRepository {
	private final ClassPathDefaultNutritionDataLoader nutritionDataLoader;

	@Override
	public List<NutritionConfigDto> findAll() {
		return nutritionDataLoader.loadContent()
				.orElse(Collections.emptyList());
	}
}
