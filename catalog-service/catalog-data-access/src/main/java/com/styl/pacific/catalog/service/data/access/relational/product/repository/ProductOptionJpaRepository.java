/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.product.repository;

import com.styl.pacific.catalog.service.data.access.relational.product.entity.ProductOptionEntity;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;

/**
 * <AUTHOR>
 */

public interface ProductOptionJpaRepository extends JpaRepository<ProductOptionEntity, Long>,
		QuerydslPredicateExecutor<ProductOptionEntity>, ProductOptionQuerydslRepository {
	@Query(value = "SELECT o FROM ProductOptionEntity o WHERE o.id = :id AND o.productId = :productId")
	Optional<ProductOptionEntity> findByIdAndProductId(@Param("id") Long id, @Param("productId") Long productId);

	@Modifying
	@Query(value = "DELETE FROM ProductOptionEntity o WHERE o = ?1")
	void deleteHard(ProductOptionEntity entity);
}
