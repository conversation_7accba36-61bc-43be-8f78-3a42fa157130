ALTER TABLE
    IF EXISTS tb_category ADD COLUMN IF NOT EXISTS migration_id VARCHAR(100);

ALTER TABLE
    IF EXISTS tb_product ADD COLUMN IF NOT EXISTS migration_id VARCHAR(100);

CREATE
    UNIQUE INDEX IF NOT EXISTS idx_tb_category_migration_id ON
    tb_category(
        tenant_id,
        migration_id
    )
WHERE
    migration_id IS NOT NULL;

CREATE
    UNIQUE INDEX IF NOT EXISTS idx_tb_product_migration_id ON
    tb_product(
        tenant_id,
        migration_id
    )
WHERE
    migration_id IS NOT NULL;