/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.application.id.generator;

import com.styl.pacific.catalog.service.domain.healthierchoice.id.generator.HealthierChoiceIdGenerator;
import com.styl.pacific.domain.valueobject.HealthierChoiceId;
import com.styl.pacific.utils.snowflake.id.Snowflake;

/**
 * <AUTHOR>
 */

public class HealthierChoiceIdGeneratorImpl implements HealthierChoiceIdGenerator {
	private final Snowflake snowflake;

	public HealthierChoiceIdGeneratorImpl() {
		super();
		this.snowflake = new Snowflake();
	}

	@Override
	public HealthierChoiceId nextId() {
		return new HealthierChoiceId(snowflake.nextId());
	}

}
