INSERT INTO tb_category (id, tenant_id, name, migration_id , description, parent_id, icon_path, created_at, updated_at)
VALUES ('1', '2', 'Fresh','test',
        'The Nagasaki Lander is the trademarked name of several series of Nagasaki sport bikes, that started with the 1984 ABC800J',
        null, 'bucket:image/loremflickr.com/640/480', '2024-05-14T08:00:31.791Z', '2024-05-14T02:33:54.196Z');
INSERT INTO tb_category (id, tenant_id, name, description, parent_id, icon_path, created_at, updated_at)
VALUES ('2', '6', 'Metal',
        'New ABC 13 9370, 13.3, 5th Gen CoreA5-8250U, 8GB RAM, 256GB SSD, power UHD Graphics, OS 10 Home, OS Office A & J 2016',
        '1', 'bucket:image/loremflickr.com/640/480', '2024-05-14T09:00:46.353Z', '2024-05-14T04:10:59.487Z');
INSERT INTO tb_category (id, tenant_id, name, description, parent_id, icon_path, created_at, updated_at)
VALUES ('3', '1', 'Granite',
        'Andy shoes are designed to keeping in mind durability as well as trends, the most stylish range of shoes & sandals',
        null, 'bucket:image/loremflickr.com/640/480', '2024-05-14T08:32:34.335Z', '2024-05-14T00:28:51.713Z');
INSERT INTO tb_category (id, tenant_id, name, description, parent_id, icon_path, created_at, updated_at)
VALUES ('4', '3', 'Fresh',
        'The Apollotech B340 is an affordable wireless mouse with reliable connectivity, 12 months battery life and modern design',
        null, 'bucket:image/loremflickr.com/640/480', '2024-05-13T18:20:51.078Z', '2024-05-13T11:57:30.384Z');
INSERT INTO tb_category (id, tenant_id, name, description, parent_id, icon_path, created_at, updated_at)
VALUES ('5', '2', 'Fresh',
        'The slim & simple Maple Gaming Keyboard from Dev Byte comes with a sleek body and 7- Color RGB LED Back-lighting for smart functionality',
        null, 'bucket:image/loremflickr.com/640/480', '2024-05-14T02:10:32.749Z', '2024-05-14T00:36:37.412Z');
INSERT INTO tb_category (id, tenant_id, name, description, parent_id, icon_path, created_at, updated_at)
VALUES ('6', '5', 'Steel',
        'New ABC 13 9370, 13.3, 5th Gen CoreA5-8250U, 8GB RAM, 256GB SSD, power UHD Graphics, OS 10 Home, OS Office A & J 2016',
        null, 'bucket:image/loremflickr.com/640/480', '2024-05-13T14:45:37.183Z', '2024-05-14T00:31:25.294Z');
INSERT INTO tb_category (id, tenant_id, name, description, parent_id, icon_path, created_at, updated_at)
VALUES ('7', '2', 'Fresh',
        'The beautiful range of Apple Naturalé that has an exciting mix of natural ingredients. With the Goodness of 100% Natural Ingredients',
        '1', 'bucket:image/loremflickr.com/640/480', '2024-05-13T14:40:27.297Z', '2024-05-14T05:39:34.828Z');
INSERT INTO tb_category (id, tenant_id, name, description, parent_id, icon_path, created_at, updated_at)
VALUES ('8', '9', 'Rubber',
        'The beautiful range of Apple Naturalé that has an exciting mix of natural ingredients. With the Goodness of 100% Natural Ingredients',
        '1', 'bucket:image/loremflickr.com/640/480', '2024-05-14T01:29:59.673Z', '2024-05-14T00:25:39.334Z');
INSERT INTO tb_category (id, tenant_id, name, description, parent_id, icon_path, created_at, updated_at)
VALUES ('9', '5', 'Steel', 'Carbonite web goalkeeper gloves are ergonomically designed to give easy fit', null,
        'bucket:image/loremflickr.com/640/480', '2024-05-13T20:53:21.078Z', '2024-05-13T23:36:05.492Z');
INSERT INTO tb_category (id, tenant_id, name, description, parent_id, icon_path, created_at, updated_at)
VALUES ('10', '3', 'Concrete',
        'Ergonomic executive chair upholstered in bonded black leather and PVC padded seat and back for all-day comfort and support',
        null, 'bucket:image/loremflickr.com/640/480', '2024-05-14T02:59:17.365Z', '2024-05-13T13:09:00.238Z');
INSERT INTO tb_category (id, tenant_id, name, description, parent_id, icon_path, created_at, updated_at)
VALUES ('11', '8', 'Rubber',
        'The slim & simple Maple Gaming Keyboard from Dev Byte comes with a sleek body and 7- Color RGB LED Back-lighting for smart functionality',
        '1', 'bucket:image/loremflickr.com/640/480', '2024-05-13T23:14:03.203Z', '2024-05-13T13:35:27.972Z');
INSERT INTO tb_category (id, tenant_id, name, description, parent_id, icon_path, created_at, updated_at)
VALUES ('12', '8', 'Bronze',
        'The Nagasaki Lander is the trademarked name of several series of Nagasaki sport bikes, that started with the 1984 ABC800J',
        '1', 'bucket:image/loremflickr.com/640/480', '2024-05-13T14:23:23.033Z', '2024-05-14T10:37:37.473Z');
INSERT INTO tb_category (id, tenant_id, name, description, parent_id, icon_path, created_at, updated_at)
VALUES ('13', '2', 'Fresh',
        'The Apollotech B340 is an affordable wireless mouse with reliable connectivity, 12 months battery life and modern design',
        '1', 'bucket:image/loremflickr.com/640/480', '2024-05-14T02:24:08.561Z', '2024-05-13T16:34:24.496Z');
INSERT INTO tb_category (id, tenant_id, name, description, parent_id, icon_path, created_at, updated_at)
VALUES ('14', '2', 'Fresh',
        'Andy shoes are designed to keeping in mind durability as well as trends, the most stylish range of shoes & sandals',
        '1', 'bucket:image/loremflickr.com/640/480', '2024-05-14T10:07:14.976Z', '2024-05-14T08:55:57.489Z');
INSERT INTO tb_category (id, tenant_id, name, description, parent_id, icon_path, created_at, updated_at)
VALUES ('15', '5', 'Rubber',
        'Bostons most advanced compression wear technology increases muscle oxygenation, stabilizes active muscles',
        null, 'bucket:image/loremflickr.com/640/480', '2024-05-13T16:25:31.345Z', '2024-05-14T04:49:40.914Z');
INSERT INTO tb_category (id, tenant_id, name, description, parent_id, icon_path, created_at, updated_at)
VALUES ('16', '8', 'Soft',
        'Andy shoes are designed to keeping in mind durability as well as trends, the most stylish range of shoes & sandals',
        null, 'bucket:image/loremflickr.com/640/480', '2024-05-14T04:03:44.689Z', '2024-05-13T11:34:54.560Z');
INSERT INTO tb_category (id, tenant_id, name, description, parent_id, icon_path, created_at, updated_at)
VALUES ('17', '5', 'Bronze',
        'New range of formal shirts are designed keeping you in mind. With fits and styling that will make you stand apart',
        null, 'bucket:image/loremflickr.com/640/480', '2024-05-14T10:09:59.368Z', '2024-05-13T19:45:36.421Z');
INSERT INTO tb_category (id, tenant_id, name, description, parent_id, icon_path, created_at, updated_at)
VALUES ('18', '2', 'Steel',
        'Andy shoes are designed to keeping in mind durability as well as trends, the most stylish range of shoes & sandals',
        null, 'bucket:image/loremflickr.com/640/480', '2024-05-14T07:53:08.632Z', '2024-05-13T19:05:27.010Z');
INSERT INTO tb_category (id, tenant_id, name, description, parent_id, icon_path, created_at, updated_at)
VALUES ('19', '9', 'Steel',
        'Andy shoes are designed to keeping in mind durability as well as trends, the most stylish range of shoes & sandals',
        null, 'bucket:image/loremflickr.com/640/480', '2024-05-13T16:32:08.569Z', '2024-05-14T05:40:41.204Z');
INSERT INTO tb_category (id, tenant_id, name, description, parent_id, icon_path, created_at, updated_at)
VALUES ('20', '4', 'Bronze',
        'The automobile layout consists of a front-engine design, with transaxle-types transmissions mounted at the rear of the engine and four wheel drive',
        null, 'bucket:image/loremflickr.com/640/480', '2024-05-13T15:05:46.685Z', '2024-05-14T09:56:00.130Z');

INSERT INTO tb_healthier_choice (id, tenant_id, name, symbol_path, description, created_at, updated_at)
VALUES (1, 2, 'Higher in Wholegrains',
        'bucket:image/ch-api.healthhub.sg/api/public/content/f0e5af85f9324fe086b54ea8b1931028?v=84677d70',
        'Products carrying HCS with this tagline contain at least 20% more wholegrains compared to similar products from the same food category.',
        '2024-05-13T15:05:46.685Z', '2024-05-14T09:56:00.130Z');
INSERT INTO tb_healthier_choice (id, tenant_id, name, symbol_path, description, created_at, updated_at)
VALUES (2, 2, 'Higher in Calcium',
        'bucket:image/ch-api.healthhub.sg/api/public/content/7daf6d1bb26b499f9684036051c9e5a9?v=0fb8dcd8',
        'Products carrying HCS with this tagline contain at least 25% more calcium compared to similar products from the same food category.',
        '2024-05-13T15:05:46.685Z', '2024-05-14T09:56:00.130Z');
INSERT INTO tb_healthier_choice (id, tenant_id, name, symbol_path, description, created_at, updated_at)
VALUES (3, 2, 'Lower in Sugar',
        'bucket:image/ch-api.healthhub.sg/api/public/content/f0e5af85f9324fe086b54ea8b1931028?v=84677d70',
        'Products carrying HCS with this tagline contain at least 20% more wholegrains compared to similar products from the same food category.',
        '2024-05-13T15:05:46.685Z', '2024-05-14T09:56:00.130Z');
INSERT INTO tb_healthier_choice (id, tenant_id, name, symbol_path, description, created_at, updated_at)
VALUES (4, 2, 'No Added Sugar',
        'bucket:image/ch-api.healthhub.sg/api/public/content/f0e5af85f9324fe086b54ea8b1931028?v=84677d70',
        'Products carrying HCS with this tagline contain at least 20% more wholegrains compared to similar products from the same food category.',
        '2024-05-13T15:05:46.685Z', '2024-05-14T09:56:00.130Z');

INSERT INTO tb_nutrition (id, tenant_id, name, unit, created_at, updated_at)
VALUES (1, 2, 'Calories', 'gm', '2024-05-13T15:05:46.685Z', '2024-05-14T09:56:00.130Z');
INSERT INTO tb_nutrition (id, tenant_id, name, unit, created_at, updated_at)
VALUES (2, 2, 'Blood Glucose', 'gm', '2024-05-13T15:05:46.685Z', '2024-05-14T09:56:00.130Z');
INSERT INTO tb_nutrition (id, tenant_id, name, unit, created_at, updated_at)
VALUES (3, 3, 'Amino Acids', 'gm', '2024-05-13T15:05:46.685Z', '2024-05-14T09:56:00.130Z');
INSERT INTO tb_nutrition (id, tenant_id, name, unit, created_at, updated_at)
VALUES (4, 4, 'Carbohydrates', 'gm', '2024-05-13T15:05:46.685Z', '2024-05-14T09:56:00.130Z');

INSERT INTO tb_product (id, tenant_id, store_id, category_id, healthier_choice_id, preparation_time, name, unit_price,
                        currency_code, brief_information, description, sku, ingredients, barcode, status, created_at,
                        updated_at)
VALUES (1, 2, 1, 1, 1, 30, 'Product 1', 10000, 'SGD', 'Andy shoes are designed to keeping in mind 1',
        'The automobile layout consists of a front-engine design, with transaxle-types transmissions mounted at the rear of the engine and four wheel drive 1',
        'SKU1', 'ingredient1,ingredient2,ingredient3', '100000', 'ACTIVE', '2024-05-13T15:05:46.685Z',
        '2024-05-14T09:56:00.130Z');
INSERT INTO tb_product (id, tenant_id, store_id, category_id, healthier_choice_id, preparation_time, name, unit_price,
                        currency_code, brief_information, description, sku, ingredients, barcode, status, created_at,
                        updated_at)
VALUES (2, 2, 1, 1, 1, 30, 'Product 2', 20000, 'SGD', 'Andy shoes are designed to keeping in mind 2',
        'The automobile layout consists of a front-engine design, with transaxle-types transmissions mounted at the rear of the engine and four wheel drive 2',
        'SKU2', 'ingredient1,ingredient2,ingredient3', '200000', 'ACTIVE', '2024-05-13T15:05:46.685Z',
        '2024-05-14T09:56:00.130Z');
INSERT INTO tb_product (id, tenant_id, store_id, category_id, healthier_choice_id, preparation_time, name, unit_price,
                        currency_code, brief_information, description, sku, ingredients, barcode, status, created_at,
                        updated_at)
VALUES (3, 2, 1, 1, 2, 30, 'Product 3', 30000, 'SGD', 'Andy shoes are designed to keeping in mind 3',
        'The automobile layout consists of a front-engine design, with transaxle-types transmissions mounted at the rear of the engine and four wheel drive 3',
        'SKU3', 'ingredient1,ingredient2,ingredient3', '300000', 'ACTIVE', '2024-05-13T15:05:46.685Z',
        '2024-05-14T09:56:00.130Z');
INSERT INTO tb_product (id, tenant_id, store_id, category_id, healthier_choice_id, preparation_time, name, unit_price,
                        currency_code, brief_information, description, sku, ingredients, barcode, status, created_at,
                        updated_at)
VALUES (4, 2, 1, 2, 2, 30, 'Product 4', 40000, 'SGD', 'Andy shoes are designed to keeping in mind 4',
        'The automobile layout consists of a front-engine design, with transaxle-types transmissions mounted at the rear of the engine and four wheel drive 4',
        'SKU4', 'ingredient1,ingredient2,ingredient3', '400000', 'ACTIVE', '2024-05-13T15:05:46.685Z',
        '2024-05-14T09:56:00.130Z');
INSERT INTO tb_product (id, tenant_id, store_id, category_id, healthier_choice_id, preparation_time, name, unit_price,
                        currency_code, brief_information, description, sku, ingredients, barcode, status, created_at,
                        updated_at)
VALUES (5, 2, 1, 2, 3, 30, 'Product 5', 50000, 'SGD', 'Andy shoes are designed to keeping in mind 5',
        'The automobile layout consists of a front-engine design, with transaxle-types transmissions mounted at the rear of the engine and four wheel drive 5',
        'SKU5', 'ingredient1,ingredient2,ingredient3', '500000', 'ACTIVE', '2024-05-13T15:05:46.685Z',
        '2024-05-14T09:56:00.130Z');
INSERT INTO tb_product (id, tenant_id, store_id, category_id, healthier_choice_id, preparation_time, name, unit_price,
                        currency_code, brief_information, description, sku, ingredients, barcode, status, created_at,
                        updated_at)
VALUES (6, 1, 1, 2, 3, 30, 'Product 6', 60000, 'SGD', 'Andy shoes are designed to keeping in mind 6',
        'The automobile layout consists of a front-engine design, with transaxle-types transmissions mounted at the rear of the engine and four wheel drive 6',
        'SKU6', 'ingredient1,ingredient2,ingredient3', '600000', 'INACTIVE', '2024-05-13T15:05:46.685Z',
        '2024-05-14T09:56:00.130Z');
INSERT INTO tb_product (id, tenant_id, store_id, category_id, healthier_choice_id, preparation_time, name, unit_price,
                        currency_code, brief_information, description, sku, ingredients, barcode, status, created_at,
                        updated_at)
VALUES (7, 1, 1, 3, 4, 30, 'Product 7', 70000, 'SGD', 'Andy shoes are designed to keeping in mind 7',
        'The automobile layout consists of a front-engine design, with transaxle-types transmissions mounted at the rear of the engine and four wheel drive 7',
        'SKU7', 'ingredient1,ingredient2,ingredient3', '700000', 'INACTIVE', '2024-05-13T15:05:46.685Z',
        '2024-05-14T09:56:00.130Z');
INSERT INTO tb_product (id, tenant_id, store_id, category_id, healthier_choice_id, preparation_time, name, unit_price,
                        currency_code, brief_information, description, sku, ingredients, barcode, status, created_at,
                        updated_at)
VALUES (8, 1, 1, 3, 4, 30, 'Product 8', 80000, 'SGD', 'Andy shoes are designed to keeping in mind 8',
        'The automobile layout consists of a front-engine design, with transaxle-types transmissions mounted at the rear of the engine and four wheel drive 8',
        'SKU8', 'ingredient1,ingredient2,ingredient3', '800000', 'INACTIVE', '2024-05-13T15:05:46.685Z',
        '2024-05-14T09:56:00.130Z');
INSERT INTO tb_product (id, tenant_id, store_id, category_id, healthier_choice_id, preparation_time, name, unit_price,
                        currency_code, brief_information, description, sku, ingredients, barcode, status, created_at,
                        updated_at)
VALUES (9, 1, 1, 3, 1, 30, 'Product 9', 90000, 'SGD', 'Andy shoes are designed to keeping in mind 9',
        'The automobile layout consists of a front-engine design, with transaxle-types transmissions mounted at the rear of the engine and four wheel drive 9',
        'SKU9', 'ingredient1,ingredient2,ingredient3', '900000', 'OUT_OF_STOCK', '2024-05-13T15:05:46.685Z',
        '2024-05-14T09:56:00.130Z');
INSERT INTO tb_product (id, tenant_id, store_id, category_id, healthier_choice_id, preparation_time, name, unit_price,
                        currency_code, brief_information, description, sku, ingredients, barcode, status, created_at,
                        updated_at)
VALUES (10, 1, 1, 4, 1, 30, 'Product 10', 100000, 'SGD', 'Andy shoes are designed to keeping in mind 10',
        'The automobile layout consists of a front-engine design, with transaxle-types transmissions mounted at the rear of the engine and four wheel drive 10',
        'SKU10', 'ingredient1,ingredient2,ingredient3', '1000000', 'OUT_OF_STOCK', '2024-05-13T15:05:46.685Z',
        '2024-05-14T09:56:00.130Z');

INSERT INTO tb_product_image (id, product_id, position, image_path, created_at, updated_at)
VALUES (1, 1, 0, 'http://image1.com', '2024-05-13T15:05:46.685Z', '2024-05-14T09:56:00.130Z');
INSERT INTO tb_product_image (id, product_id, position, image_path, created_at, updated_at)
VALUES (2, 1, 1, 'http://image2.com', '2024-05-13T15:05:46.685Z', '2024-05-14T09:56:00.130Z');
INSERT INTO tb_product_image (id, product_id, position, image_path, created_at, updated_at)
VALUES (3, 2, 0, 'http://image3.com', '2024-05-13T15:05:46.685Z', '2024-05-14T09:56:00.130Z');
INSERT INTO tb_product_image (id, product_id, position, image_path, created_at, updated_at)
VALUES (4, 2, 1, 'http://image4.com', '2024-05-13T15:05:46.685Z', '2024-05-14T09:56:00.130Z');

INSERT INTO tb_product_option (id, title, product_id, minimum, maximum, created_at, updated_at)
VALUES (1, 'option 1', 1, 0, 100, '2024-05-13T15:05:46.685Z', '2024-05-14T09:56:00.130Z');
INSERT INTO tb_product_option (id, title, product_id, minimum, maximum, created_at, updated_at)
VALUES (2, 'option 1', 2, 20, 100, '2024-05-13T15:05:46.685Z', '2024-05-14T09:56:00.130Z');
INSERT INTO tb_product_option (id, title, product_id, minimum, maximum, created_at, updated_at)
VALUES (3, 'option 1', 3, 10, 20, '2024-05-13T15:05:46.685Z', '2024-05-14T09:56:00.130Z');
INSERT INTO tb_product_option (id, title, product_id, minimum, maximum, created_at, updated_at)
VALUES (4, 'option 1', 4, 0, 100, '2024-05-13T15:05:46.685Z', '2024-05-14T09:56:00.130Z');
INSERT INTO tb_product_option (id, title, product_id, minimum, maximum, created_at, updated_at)
VALUES (5, 'option 1', 5, 0, 100, '2024-05-13T15:05:46.685Z', '2024-05-14T09:56:00.130Z');

INSERT INTO tb_product_option_item (id, option_id, name, addition_price, created_at, updated_at)
VALUES (1, 1, 'Item 1', 1000, '2024-05-13T15:05:46.685Z', '2024-05-14T09:56:00.130Z');
INSERT INTO tb_product_option_item (id, option_id, name, addition_price, created_at, updated_at)
VALUES (2, 2, 'Item 2', 1000, '2024-05-13T15:05:46.685Z', '2024-05-14T09:56:00.130Z');
INSERT INTO tb_product_option_item (id, option_id, name, addition_price, created_at, updated_at)
VALUES (3, 3, 'Item 3', 1000, '2024-05-13T15:05:46.685Z', '2024-05-14T09:56:00.130Z');
INSERT INTO tb_product_option_item (id, option_id, name, addition_price, created_at, updated_at)
VALUES (4, 4, 'Item 4', 1000, '2024-05-13T15:05:46.685Z', '2024-05-14T09:56:00.130Z');
INSERT INTO tb_product_option_item (id, option_id, name, addition_price, created_at, updated_at)
VALUES (5, 5, 'Item 5', 1000, '2024-05-13T15:05:46.685Z', '2024-05-14T09:56:00.130Z');

INSERT INTO tb_product_nutrition (product_id, nutrition_id, value, created_at, updated_at)
VALUES (1, 1, 100, '2024-05-13T15:05:46.685Z', '2024-05-14T09:56:00.130Z');
INSERT INTO tb_product_nutrition (product_id, nutrition_id, value, created_at, updated_at)
VALUES (1, 2, 101, '2024-05-13T15:05:46.685Z', '2024-05-14T09:56:00.130Z');
INSERT INTO tb_product_nutrition (product_id, nutrition_id, value, created_at, updated_at)
VALUES (2, 3, 102, '2024-05-13T15:05:46.685Z', '2024-05-14T09:56:00.130Z');
INSERT INTO tb_product_nutrition (product_id, nutrition_id, value, created_at, updated_at)
VALUES (2, 4, 103, '2024-05-13T15:05:46.685Z', '2024-05-14T09:56:00.130Z');
INSERT INTO tb_product_nutrition (product_id, nutrition_id, value, created_at, updated_at)
VALUES (3, 1, 103, '2024-05-13T15:05:46.685Z', '2024-05-14T09:56:00.130Z');
INSERT INTO tb_product_nutrition (product_id, nutrition_id, value, created_at, updated_at)
VALUES (3, 2, 103, '2024-05-13T15:05:46.685Z', '2024-05-14T09:56:00.130Z');
INSERT INTO tb_product_nutrition (product_id, nutrition_id, value, created_at, updated_at)
VALUES (4, 3, 104, '2024-05-13T15:05:46.685Z', '2024-05-14T09:56:00.130Z');
INSERT INTO tb_product_nutrition (product_id, nutrition_id, value, created_at, updated_at)
VALUES (4, 4, 104, '2024-05-13T15:05:46.685Z', '2024-05-14T09:56:00.130Z');