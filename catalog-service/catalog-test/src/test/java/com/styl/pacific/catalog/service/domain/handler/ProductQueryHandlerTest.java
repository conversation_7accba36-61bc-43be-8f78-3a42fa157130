/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.domain.handler;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.styl.pacific.catalog.service.domain.category.dto.CategoryStubDto;
import com.styl.pacific.catalog.service.domain.healthierchoice.entity.HealthierChoice;
import com.styl.pacific.catalog.service.domain.healthierchoice.output.repository.HealthierChoiceRepository;
import com.styl.pacific.catalog.service.domain.product.dto.ProductDto;
import com.styl.pacific.catalog.service.domain.product.dto.ProductStubDto;
import com.styl.pacific.catalog.service.domain.product.dto.query.PaginationProductQuery;
import com.styl.pacific.catalog.service.domain.product.dto.query.ProductFilter;
import com.styl.pacific.catalog.service.domain.product.entity.ProductImage;
import com.styl.pacific.catalog.service.domain.product.enums.ProductStatus;
import com.styl.pacific.catalog.service.domain.product.exception.ProductNotFoundException;
import com.styl.pacific.catalog.service.domain.product.handler.ProductQueryHandler;
import com.styl.pacific.catalog.service.domain.product.port.ouput.repository.ProductRepository;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.CategoryId;
import com.styl.pacific.domain.valueobject.HealthierChoiceId;
import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.domain.valueobject.ProductImageId;
import com.styl.pacific.domain.valueobject.StoreId;
import com.styl.pacific.domain.valueobject.TenantId;
import java.math.BigInteger;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@TestInstance(Lifecycle.PER_METHOD)
class ProductQueryHandlerTest {

	private static final Long PRODUCT_ID = 1L;
	private static final Long STORE_ID = 1L;
	private static final Long TENANT_ID = 1L;
	private static final String NAME = "productName";
	private static final String BARCODE = "barcode";
	private static final String SKU = "sku";
	private static final String BRIEF_DESCRIPTION = "briefDescription";
	private static final String DESCRIPTION = "description";
	private static final String INGREDIENTS = "ingredients";
	private static final ProductStatus STATUS = ProductStatus.ACTIVE;
	private static final BigInteger UNIT_PRICE = BigInteger.valueOf(100);
	private static final Instant PRODUCT_CREATED_AT = ZonedDateTime.now()
			.toInstant();
	private static final Instant PRODUCT_UPDATED_AT = ZonedDateTime.now()
			.toInstant();

	private static final Long CATEGORY_ID = 1L;

	private static final Long HEALTHIER_CHOICE_ID = 1L;

	private static final Long IMAGE_ID = 1L;
	private static final String IMAGE_URL = "http://url.com";

	@Mock
	private ProductRepository productRepository;

	@Mock
	private HealthierChoiceRepository healthierChoiceRepository;

	@InjectMocks
	private ProductQueryHandler queryHandler;

	@Test
	void shouldProductResponse_whenGet() {
		// Arrange
		ProductDto productDtoMock = getProductResponse();
		when(productRepository.findDtoById(any(), eq(new ProductId(PRODUCT_ID)))).thenReturn(Optional.of(
				productDtoMock));
		// Act
		ProductDto response = queryHandler.findById(new TenantId(TENANT_ID), new ProductId(PRODUCT_ID));
		// Assert
		verify(productRepository, times(1)).findDtoById(any(), eq(new ProductId(PRODUCT_ID)));
		assertEquals(productDtoMock.id(), response.id());
		assertEquals(productDtoMock.tenantId(), response.tenantId());
		assertEquals(productDtoMock.storeId(), response.storeId());
		assertEquals(productDtoMock.category()
				.id(), response.category()
						.id());
		assertEquals(productDtoMock.healthierChoice()
				.getId(), response.healthierChoice()
						.getId());
		assertEquals(productDtoMock.name(), response.name());
		assertEquals(productDtoMock.sku(), response.sku());
		assertEquals(productDtoMock.briefInformation(), response.briefInformation());
		assertEquals(productDtoMock.description(), response.description());
		assertEquals(productDtoMock.ingredients(), response.ingredients());
		assertEquals(productDtoMock.barcode(), response.barcode());
		assertEquals(productDtoMock.status(), response.status());

		assertEquals(productDtoMock.unitPrice(), response.unitPrice());
		assertEquals(productDtoMock.updatedAt(), response.updatedAt());
		assertEquals(productDtoMock.createdAt(), response.createdAt());
	}

	@Test
    void shouldThrowProductNotFoundException_whenGet() {
        // Arrange
        when(productRepository.findDtoById(any(), eq(new ProductId(PRODUCT_ID)))).thenReturn(Optional.empty());
        String exceptionMessage = String.format("Product %s not found", PRODUCT_ID);
        // Act
        ProductNotFoundException exception = assertThrows(ProductNotFoundException.class, () -> queryHandler.findById(
                new TenantId(TENANT_ID), new ProductId(PRODUCT_ID)));
        // Assert
        verify(productRepository, times(1)).findDtoById(any(), eq(new ProductId(PRODUCT_ID)));
        assertEquals(exceptionMessage, exception.getMessage());
    }

	@Test
	void shouldReturnListProductsResponse_whenFindQuery() {
		// Arrange
		int page = 0;
		int size = 10;
		String sortField = "id";
		String sortDirection = "ASC";
		int totalPages = 1;
		int totalElements = 2;
		List<String> sort = List.of(sortField + ":"
				+ sortDirection);
		PaginationProductQuery query = new PaginationProductQuery(ProductFilter.builder()
				.name(NAME)
				.categoryIds(List.of(CATEGORY_ID))
				.storeId(STORE_ID)
				.barcode(BARCODE)
				.statuses(List.of(STATUS))
				.fromUnitPrice(UNIT_PRICE)
				.toUnitPrice(UNIT_PRICE)
				.build(), size, page, sortDirection, Set.of(sortField));
		List<ProductDto> contentMock = List.of(getProductResponse(), getProductResponse());
		Paging<ProductDto> productsMock = new Paging<>(contentMock, totalElements, totalPages, page, sort);

		when(productRepository.findAllPaging(new TenantId(TENANT_ID), query)).thenReturn(productsMock);
		// Act
		Paging<ProductDto> response = queryHandler.findAllPaging(new TenantId(TENANT_ID), query);
		// Assert
		verify(productRepository, times(1)).findAllPaging(any(), eq(query));
		assertEquals(contentMock.size(), response.getContent()
				.size());
		assertEquals(page, response.getPage());
		assertEquals(sort, response.getSort());
		assertEquals(totalElements, response.getTotalElements());
		assertEquals(totalPages, response.getTotalPages());
	}

	private ProductDto getProductResponse() {
		return ProductDto.builder()
				.id(new ProductId(PRODUCT_ID))
				.tenantId(new TenantId(TENANT_ID))
				.healthierChoice(HealthierChoice.builder()
						.id(new HealthierChoiceId(HEALTHIER_CHOICE_ID))
						.build())
				.category(CategoryStubDto.builder()
						.id(new CategoryId(CATEGORY_ID))
						.build())
				.storeId(new StoreId(STORE_ID))
				.name(NAME)
				.barcode(BARCODE)
				.briefInformation(BRIEF_DESCRIPTION)
				.description(DESCRIPTION)
				.ingredients(INGREDIENTS)
				.status(STATUS)
				.unitPrice(UNIT_PRICE)
				.updatedAt(PRODUCT_UPDATED_AT)
				.createdAt(PRODUCT_CREATED_AT)
				.build();
	}

	private ProductStubDto getProductStubResponse() {
		CategoryStubDto category = CategoryStubDto.builder()
				.id(new CategoryId(CATEGORY_ID))
				.build();
		HealthierChoice healthierChoice = HealthierChoice.builder()
				.id(new HealthierChoiceId(HEALTHIER_CHOICE_ID))
				.build();
		ProductImage image = ProductImage.builder()
				.id(new ProductImageId(IMAGE_ID))
				.imagePath(IMAGE_URL)
				.build();
		return ProductStubDto.builder()
				.id(new ProductId(PRODUCT_ID))
				.category(category)
				.healthierChoice(healthierChoice)
				.storeId(new StoreId(STORE_ID))
				.tenantId(new TenantId(TENANT_ID))
				.name(NAME)
				.sku(SKU)
				.briefInformation(BRIEF_DESCRIPTION)
				.description(DESCRIPTION)
				.ingredients(INGREDIENTS)
				.barcode(BARCODE)
				.status(STATUS)
				.unitPrice(UNIT_PRICE)
				.images(List.of(image))
				.build();
	}
}
