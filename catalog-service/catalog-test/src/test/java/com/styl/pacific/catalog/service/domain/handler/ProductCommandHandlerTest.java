/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.domain.handler;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.styl.pacific.catalog.service.data.access.relational.product.entity.ProductEntity;
import com.styl.pacific.catalog.service.data.access.relational.product.mapper.ProductDataAccessMapper;
import com.styl.pacific.catalog.service.domain.category.entity.Category;
import com.styl.pacific.catalog.service.domain.category.exception.CategoryNotFoundException;
import com.styl.pacific.catalog.service.domain.category.output.repository.CategoryRepository;
import com.styl.pacific.catalog.service.domain.healthierchoice.output.repository.HealthierChoiceRepository;
import com.styl.pacific.catalog.service.domain.product.ProductDomainCore;
import com.styl.pacific.catalog.service.domain.product.ProductDomainCoreImpl;
import com.styl.pacific.catalog.service.domain.product.dto.ProductDto;
import com.styl.pacific.catalog.service.domain.product.dto.command.CreateProductCommand;
import com.styl.pacific.catalog.service.domain.product.dto.command.UpdateProductCommand;
import com.styl.pacific.catalog.service.domain.product.dto.command.image.CreateProductImageCommand;
import com.styl.pacific.catalog.service.domain.product.entity.Product;
import com.styl.pacific.catalog.service.domain.product.entity.ProductImage;
import com.styl.pacific.catalog.service.domain.product.enums.ProductStatus;
import com.styl.pacific.catalog.service.domain.product.exception.ProductNotFoundException;
import com.styl.pacific.catalog.service.domain.product.handler.ProductCommandHandler;
import com.styl.pacific.catalog.service.domain.product.id.generator.ProductIdGenerator;
import com.styl.pacific.catalog.service.domain.product.id.generator.ProductImageIdGenerator;
import com.styl.pacific.catalog.service.domain.product.id.generator.ProductOptionIdGenerator;
import com.styl.pacific.catalog.service.domain.product.port.ouput.repository.ProductRepository;
import com.styl.pacific.catalog.service.domain.store.output.repository.StoreRepository;
import com.styl.pacific.catalog.service.domain.tenant.dto.TenantDto;
import com.styl.pacific.catalog.service.domain.tenant.dto.TenantSettingDto;
import com.styl.pacific.catalog.service.domain.tenant.output.repository.TenantRepository;
import com.styl.pacific.domain.valueobject.CategoryId;
import com.styl.pacific.domain.valueobject.HealthierChoiceId;
import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.domain.valueobject.ProductImageId;
import com.styl.pacific.domain.valueobject.StoreId;
import com.styl.pacific.domain.valueobject.TenantId;
import java.math.BigInteger;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_METHOD)
class ProductCommandHandlerTest {
	private static final Long PRODUCT_ID = 1L;
	private static final Long STORE_ID = 1L;
	private static final Long TENANT_ID = 1L;
	private static final String NAME = "productName";
	private static final String BARCODE = "barcode";
	private static final String SKU = "sku";
	private static final String BRIEF_DESCRIPTION = "briefDescription";
	private static final String DESCRIPTION = "description";
	private static final String INGREDIENTS = "ingredients";
	private static final ProductStatus STATUS = ProductStatus.ACTIVE;
	private static final BigInteger UNIT_PRICE = BigInteger.valueOf(100);
	private static final Instant PRODUCT_CREATED_AT = ZonedDateTime.now()
			.toInstant();
	private static final Instant PRODUCT_UPDATED_AT = ZonedDateTime.now()
			.toInstant();

	private static final Long CATEGORY_ID = 1L;
	private static final String CATEGORY_NAME = "Category";
	private static final String CATEGORY_IMAGE_URL = "/category/image";
	private static final String CATEGORY_DESCRIPTION = "description";
	private static final long CATEGORY_PARENT_ID = 2L;
	private static final Instant CATEGORY_CREATED_AT = ZonedDateTime.now()
			.toInstant();
	private static final Instant CATEGORY_UPDATED_AT = ZonedDateTime.now()
			.toInstant();

	private static final Long IMAGE_ID = 1L;
	private static final String IMAGE_URL = "https://url.com";

	private static final Long OPTION_ID = 1L;

	private static final Long HEALTHIER_CHOICE_ID = 1L;

	private static final String CURRENCY_CODE = "SGD";

	@Mock
	private TenantRepository tenantRepository;

	@Mock
	private ProductRepository productRepository;

	@Mock
	private CategoryRepository categoryRepository;

	@Mock
	private StoreRepository storeRepository;

	@Mock
	private HealthierChoiceRepository healthierChoiceRepository;

	@Mock
	private ProductIdGenerator productIdGenerator;

	@Mock
	private ProductOptionIdGenerator optionIdGenerator;

	@Mock
	private ProductImageIdGenerator productImageIdGenerator;

	@Spy
	private ProductDomainCore productDomainCore = new ProductDomainCoreImpl();

	@InjectMocks
	private ProductCommandHandler commandHandler;

	@Test
	void shouldReturnCreateProductResponse_whenCreateWithTrackingQuantity() {
		// Arrange
		CreateProductImageCommand imageCommand = CreateProductImageCommand.builder()
				.imagePath(IMAGE_URL)
				.build();
		CreateProductCommand command = CreateProductCommand.builder()
				.name(NAME)
				.storeId(STORE_ID)
				.healthierChoiceId(HEALTHIER_CHOICE_ID)
				.categoryId(CATEGORY_ID)
				.briefInformation(BRIEF_DESCRIPTION)
				.description(DESCRIPTION)
				.ingredients(INGREDIENTS)
				.unitPrice(UNIT_PRICE)
				.images(List.of(imageCommand))
				.build();

		when(storeRepository.existsById(any())).thenReturn(true);
		when(categoryRepository.existsById(any(), eq(new CategoryId(CATEGORY_ID)))).thenReturn(true);
		when(healthierChoiceRepository.existsById(any(), eq(new HealthierChoiceId(HEALTHIER_CHOICE_ID)))).thenReturn(
				true);
		when(productIdGenerator.nextId()).thenReturn(new ProductId(PRODUCT_ID));
		when(productImageIdGenerator.nextId()).thenReturn(new ProductImageId(IMAGE_ID));

		when(productRepository.saveDto(any(Product.class))).then(invocation -> {
			Product product = invocation.getArgument(0);
			product.setCreatedAt(PRODUCT_CREATED_AT);
			product.setUpdatedAt(PRODUCT_UPDATED_AT);
			ProductEntity entity = ProductDataAccessMapper.INSTANCE.toEntity(product);
			return ProductDataAccessMapper.INSTANCE.toDto(entity);
		});

		TenantDto tenantDto = TenantDto.builder()
				.tenantId(TENANT_ID)
				.settings(TenantSettingDto.builder()
						.currency(TenantSettingDto.CurrencyDto.builder()
								.currencyCode(CURRENCY_CODE)
								.build())
						.build())
				.build();
		when(tenantRepository.findById(anyLong())).thenReturn(Optional.of(tenantDto));
		// Act
		ProductDto productDto = commandHandler.create(new TenantId(TENANT_ID), command);

		// Assert
		verify(categoryRepository, times(1)).existsById(any(), eq(new CategoryId(CATEGORY_ID)));
		verify(productIdGenerator, times(1)).nextId();
		verify(productImageIdGenerator, times(1)).nextId();
		verify(tenantRepository, times(1)).findById(anyLong());

		ArgumentCaptor<Product> productCaptor = ArgumentCaptor.forClass(Product.class);
		verify(productRepository, times(1)).saveDto(productCaptor.capture());
		Product productInput = productCaptor.getValue();
		assertEquals(PRODUCT_ID, productInput.getId()
				.getValue());
		assertEquals(TENANT_ID, productInput.getTenantId()
				.getValue());
		assertEquals(ProductStatus.ACTIVE, productInput.getStatus());
		assertEquals(command.getStoreId(), productInput.getStoreId()
				.getValue());
		assertEquals(command.getCategoryId(), productInput.getCategoryId()
				.getValue());
		assertEquals(command.getName(), productInput.getName());
		assertEquals(command.getBriefInformation(), productInput.getBriefInformation());
		assertEquals(command.getDescription(), productInput.getDescription());
		assertEquals(command.getIngredients(), productInput.getIngredients());
		assertEquals(command.getUnitPrice(), productInput.getUnitPrice()
				.getAmount());
		assertEquals(command.getImages()
				.size(), productInput.getImages()
						.size());

		assertEquals(PRODUCT_ID, productDto.id()
				.getValue());
		assertEquals(TENANT_ID, productDto.tenantId()
				.getValue());
		assertEquals(STORE_ID, productDto.storeId()
				.getValue());
		assertEquals(CATEGORY_ID, productDto.category()
				.id()
				.getValue());
		assertEquals(NAME, productDto.name());
	}

	@Test
	void shouldReturnCreateProductResponse_whenCreateWithUnTrackingQuantity() {
		// Arrange
		CreateProductImageCommand imageCommand = CreateProductImageCommand.builder()
				.imagePath(IMAGE_URL)
				.build();
		CreateProductCommand command = CreateProductCommand.builder()
				.name(NAME)
				.storeId(STORE_ID)
				.healthierChoiceId(HEALTHIER_CHOICE_ID)
				.categoryId(CATEGORY_ID)
				.briefInformation(BRIEF_DESCRIPTION)
				.description(DESCRIPTION)
				.ingredients(INGREDIENTS)
				.unitPrice(UNIT_PRICE)
				.images(List.of(imageCommand))
				.build();

		when(storeRepository.existsById(any())).thenReturn(true);
		when(categoryRepository.existsById(any(), eq(new CategoryId(CATEGORY_ID)))).thenReturn(true);
		when(healthierChoiceRepository.existsById(any(), eq(new HealthierChoiceId(HEALTHIER_CHOICE_ID)))).thenReturn(
				true);
		when(productIdGenerator.nextId()).thenReturn(new ProductId(PRODUCT_ID));
		when(productImageIdGenerator.nextId()).thenReturn(new ProductImageId(IMAGE_ID));

		when(productRepository.saveDto(any(Product.class))).then(invocation -> {
			Product product = invocation.getArgument(0);
			product.setCreatedAt(PRODUCT_CREATED_AT);
			product.setUpdatedAt(PRODUCT_UPDATED_AT);
			ProductEntity entity = ProductDataAccessMapper.INSTANCE.toEntity(product);
			return ProductDataAccessMapper.INSTANCE.toDto(entity);
		});

		TenantDto tenantDto = TenantDto.builder()
				.tenantId(TENANT_ID)
				.settings(TenantSettingDto.builder()
						.currency(TenantSettingDto.CurrencyDto.builder()
								.currencyCode(CURRENCY_CODE)
								.build())
						.build())
				.build();
		when(tenantRepository.findById(anyLong())).thenReturn(Optional.of(tenantDto));
		// Act
		ProductDto productDto = commandHandler.create(new TenantId(TENANT_ID), command);

		// Assert
		verify(categoryRepository, times(1)).existsById(any(), eq(new CategoryId(CATEGORY_ID)));
		verify(productIdGenerator, times(1)).nextId();
		verify(productImageIdGenerator, times(1)).nextId();

		ArgumentCaptor<Product> productCaptor = ArgumentCaptor.forClass(Product.class);
		verify(productRepository, times(1)).saveDto(productCaptor.capture());
		Product productInput = productCaptor.getValue();
		assertEquals(PRODUCT_ID, productInput.getId()
				.getValue());
		assertEquals(TENANT_ID, productInput.getTenantId()
				.getValue());
		assertEquals(ProductStatus.ACTIVE, productInput.getStatus());
		assertEquals(command.getStoreId(), productInput.getStoreId()
				.getValue());
		assertEquals(command.getCategoryId(), productInput.getCategoryId()
				.getValue());
		assertEquals(command.getName(), productInput.getName());
		assertEquals(command.getBriefInformation(), productInput.getBriefInformation());
		assertEquals(command.getDescription(), productInput.getDescription());
		assertEquals(command.getIngredients(), productInput.getIngredients());
		assertEquals(command.getUnitPrice(), productInput.getUnitPrice()
				.getAmount());
		assertEquals(command.getImages()
				.size(), productInput.getImages()
						.size());

		assertEquals(PRODUCT_ID, productDto.id()
				.getValue());
		assertEquals(TENANT_ID, productDto.tenantId()
				.getValue());
		assertEquals(STORE_ID, productDto.storeId()
				.getValue());
		assertEquals(CATEGORY_ID, productDto.category()
				.id()
				.getValue());
		assertEquals(NAME, productDto.name());
	}

	@Test
	void shouldThrowCategoryNotFoundException_whenCreate() {
		// Arrange
		CreateProductImageCommand imageCommand = CreateProductImageCommand.builder()
				.imagePath(IMAGE_URL)
				.build();
		CreateProductCommand command = CreateProductCommand.builder()
				.name(NAME)
				.storeId(STORE_ID)
				.categoryId(CATEGORY_ID)
				.briefInformation(BRIEF_DESCRIPTION)
				.description(DESCRIPTION)
				.ingredients(INGREDIENTS)
				.unitPrice(UNIT_PRICE)
				.images(List.of(imageCommand))
				.build();

		when(categoryRepository.existsById(any(), eq(new CategoryId(CATEGORY_ID)))).thenReturn(false);
		String exceptionMessage = String.format("Category %s not found", CATEGORY_ID);

		TenantDto tenantDto = TenantDto.builder()
				.tenantId(TENANT_ID)
				.build();
		when(tenantRepository.findById(anyLong())).thenReturn(Optional.of(tenantDto));
		// Act
		CategoryNotFoundException exception = assertThrows(CategoryNotFoundException.class, () -> commandHandler.create(
				new TenantId(TENANT_ID), command));
		// Assert
		verify(categoryRepository, times(1)).existsById(any(), eq(new CategoryId(CATEGORY_ID)));
		assertEquals(exceptionMessage, exception.getMessage());

	}

	@Test
	void shouldReturnProductResponse_whenUpdate() {
		// Arrange
		Long newCategoryId = 2L;
		Long newHealthierChoiceId = 2L;
		String newName = "new product";
		String newBriefDescription = "new brief description";
		String newDescription = "new description";
		String newIngredients = "new ingredients";
		String newSku = "new sku";
		String newBarcode = "new barcode";
		BigInteger newUnitPrice = BigInteger.valueOf(200);
		UpdateProductCommand command = UpdateProductCommand.builder()
				.categoryId(newCategoryId)
				.healthierChoiceId(newHealthierChoiceId)
				.name(newName)
				.briefInformation(newBriefDescription)
				.sku(newSku)
				.barcode(newBarcode)
				.description(newDescription)
				.ingredients(newIngredients)
				.unitPrice(newUnitPrice)
				.build();

		Category categoryMock = getCategoryArrange();
		categoryMock.setId(new CategoryId(newCategoryId));
		Product productMock = getProductArrange();

		when(healthierChoiceRepository.existsById(any(), any())).thenReturn(true);
		when(categoryRepository.existsById(any(), eq(new CategoryId(newCategoryId)))).thenReturn(true);
		when(productRepository.findById(any(), eq(new ProductId(PRODUCT_ID)))).thenReturn(Optional.of(productMock));
		when(productRepository.updateDto(any(Product.class))).then(invocation -> {
			Product product = invocation.getArgument(0);
			product.setUpdatedAt(ZonedDateTime.now()
					.toInstant());
			ProductEntity entity = ProductDataAccessMapper.INSTANCE.toEntity(product);
			return ProductDataAccessMapper.INSTANCE.toDto(entity);
		});
		when(productRepository.existBySkuNotId(any(), any(), any())).thenReturn(false);
		// Act
		ProductDto productDto = commandHandler.update(new TenantId(TENANT_ID), new ProductId(PRODUCT_ID), command);
		// Assert
		verify(categoryRepository, times(1)).existsById(any(), eq(new CategoryId(newCategoryId)));
		verify(productRepository, times(1)).findById(any(), eq(new ProductId(PRODUCT_ID)));
		ArgumentCaptor<Product> productCaptor = ArgumentCaptor.forClass(Product.class);
		verify(productRepository, times(1)).updateDto(productCaptor.capture());
		Product productInput = productCaptor.getValue();
		assertEquals(PRODUCT_ID, productInput.getId()
				.getValue());
		assertEquals(newCategoryId, productInput.getCategoryId()
				.getValue());
		assertEquals(newName, productInput.getName());
		assertEquals(newBriefDescription, productInput.getBriefInformation());
		assertEquals(newDescription, productInput.getDescription());
		assertEquals(newIngredients, productInput.getIngredients());
		assertEquals(newUnitPrice, productInput.getUnitPrice()
				.getAmount());
		assertEquals(newSku, productInput.getSku());
		assertEquals(newBarcode, productInput.getBarcode());

		assertEquals(PRODUCT_ID, productDto.id()
				.getValue());
		assertEquals(newCategoryId, productDto.category()
				.id()
				.getValue());
		assertEquals(newName, productDto.name());
		assertEquals(newBriefDescription, productDto.briefInformation());
		assertEquals(newDescription, productDto.description());
		assertEquals(newIngredients, productDto.ingredients());
		assertEquals(newUnitPrice, productDto.unitPrice());
	}

	@Test
	void shouldThrowProductNotFound_whenUpdate() {
		// Arrange
		Long newCategoryId = 2L;
		String newName = "new product";
		String newBriefDescription = "new brief description";
		String newDescription = "new description";
		String newIngredients = "new ingredients";
		BigInteger newUnitPrice = BigInteger.valueOf(200);
		UpdateProductCommand command = UpdateProductCommand.builder()
				.categoryId(newCategoryId)
				.name(newName)
				.briefInformation(newBriefDescription)
				.description(newDescription)
				.ingredients(newIngredients)
				.unitPrice(newUnitPrice)
				.build();

		when(categoryRepository.existsById(any(), eq(new CategoryId(newCategoryId)))).thenReturn(true);
		when(productRepository.findById(any(), eq(new ProductId(PRODUCT_ID)))).thenReturn(Optional.empty());

		String exceptionMessage = String.format("Product %s not found", PRODUCT_ID);
		// Act
		ProductNotFoundException exception = assertThrows(ProductNotFoundException.class, () -> commandHandler.update(
				new TenantId(TENANT_ID), new ProductId(PRODUCT_ID), command));
		// Assert
		verify(productRepository, times(1)).findById(any(), eq(new ProductId(PRODUCT_ID)));
		assertEquals(exceptionMessage, exception.getMessage());
	}

	@Test
	void shouldActiveProduct_whenActivate() {
		// Arrange
		Product productMock = getProductArrange();
		productMock.setStatus(ProductStatus.UNAVAILABLE);
		when(productRepository.findById(any(), any())).thenReturn(Optional.of(productMock));
		// Act
		commandHandler.activate(new TenantId(TENANT_ID), new ProductId(PRODUCT_ID));
		// Assert
		verify(productRepository, times(1)).findById(any(), any());
		verify(productDomainCore, times(1)).activeProduct(any(Product.class));
		ArgumentCaptor<Product> productCaptor = ArgumentCaptor.forClass(Product.class);
		verify(productRepository, times(1)).updateDto(productCaptor.capture());
		Product productInput = productCaptor.getValue();
		assertEquals(ProductStatus.ACTIVE, productInput.getStatus());
	}

	@Test
    void shouldThrowProductNotFound_whenActivate() {
        // Arrange
        when(productRepository.findById(any(), any())).thenReturn(Optional.empty());
        String exceptionMessage = String.format("Product %s not found", PRODUCT_ID);
        // Act
        ProductNotFoundException exception = assertThrows(ProductNotFoundException.class, () -> commandHandler.activate(
                new TenantId(TENANT_ID), new ProductId(PRODUCT_ID)));
        // Assert
        verify(productRepository, times(1)).findById(any(), any());
        assertEquals(exceptionMessage, exception.getMessage());
    }

	@Test
	void shouldInactiveProduct_whenDeactivate() {
		// Arrange
		Product productMock = getProductArrange();
		when(productRepository.findById(any(), any())).thenReturn(Optional.of(productMock));
		when(productRepository.updateDto(any(Product.class))).then(invocation -> {
			Product product = invocation.getArgument(0);
			product.setUpdatedAt(ZonedDateTime.now()
					.toInstant());
			ProductEntity entity = ProductDataAccessMapper.INSTANCE.toEntity(product);
			return ProductDataAccessMapper.INSTANCE.toDto(entity);
		});
		// Act
		commandHandler.deactivate(new TenantId(TENANT_ID), new ProductId(PRODUCT_ID));
		// Assert
		verify(productRepository, times(1)).findById(any(), any());
		verify(productDomainCore, times(1)).inactiveProduct(any(Product.class));
		ArgumentCaptor<Product> productCaptor = ArgumentCaptor.forClass(Product.class);
		verify(productRepository, times(1)).updateDto(productCaptor.capture());
		Product productInput = productCaptor.getValue();
		assertEquals(ProductStatus.UNAVAILABLE, productInput.getStatus());
	}

	@Test
	void shouldArchivedProduct_whenArchive() {
		// Arrange
		Product productMock = getProductArrange();
		when(productRepository.findById(any(), any())).thenReturn(Optional.of(productMock));
		when(productRepository.updateDto(any())).then(invocation -> {
			Product product = invocation.getArgument(0);
			product.setUpdatedAt(ZonedDateTime.now()
					.toInstant());
			ProductEntity entity = ProductDataAccessMapper.INSTANCE.toEntity(product);
			return ProductDataAccessMapper.INSTANCE.toDto(entity);
		});
		// Act
		commandHandler.archive(new TenantId(TENANT_ID), new ProductId(PRODUCT_ID));
		// Assert
		verify(productRepository, times(1)).findById(any(), any());
		verify(productDomainCore, times(1)).archiveProduct(any(Product.class));
		ArgumentCaptor<Product> productCaptor = ArgumentCaptor.forClass(Product.class);
		verify(productRepository, times(1)).updateDto(productCaptor.capture());
		Product productInput = productCaptor.getValue();
		assertEquals(ProductStatus.ARCHIVED, productInput.getStatus());
	}

	@Test
    void shouldThrowProductNotFound_whenDeactivate() {
        // Arrange
        when(productRepository.findById(any(), any())).thenReturn(Optional.empty());
        String exceptionMessage = String.format("Product %s not found", PRODUCT_ID);
        // Act
        ProductNotFoundException exception = assertThrows(ProductNotFoundException.class, () -> commandHandler
                .deactivate(new TenantId(TENANT_ID), new ProductId(PRODUCT_ID)));
        // Assert
        verify(productRepository, times(1)).findById(any(), any());
        assertEquals(exceptionMessage, exception.getMessage());
    }

	@Test
    void shouldDeleteProduct_whenDelete() {
        // Arrange
        when(productRepository.existById(any(), any())).thenReturn(true);
        // Act
        commandHandler.deleteById(new TenantId(TENANT_ID), new ProductId(PRODUCT_ID));
        // Assert
        verify(productRepository, times(1)).existById(any(), any());
        verify(productRepository, times(1)).deleteById(any(), any());
    }

	@Test
    void shouldThrowProductNotFound_whenDelete() {
        // Arrange
        when(productRepository.existById(any(), any())).thenReturn(false);
        String exceptionMessage = String.format("Product %s not found", PRODUCT_ID);
        // Act
        ProductNotFoundException exception = assertThrows(ProductNotFoundException.class, () -> commandHandler
                .deleteById(new TenantId(TENANT_ID) , new ProductId(PRODUCT_ID)));
        // Assert
        verify(productRepository, times(1)).existById(any(), any());
        assertEquals(exceptionMessage, exception.getMessage());
    }

	private Category getCategoryArrange() {
		return Category.builder()
				.id(new CategoryId(CATEGORY_ID))
				.tenantId(new TenantId(TENANT_ID))
				.name(CATEGORY_NAME)
				.iconPath(CATEGORY_IMAGE_URL)
				.description(CATEGORY_DESCRIPTION)
				.parentId(new CategoryId(CATEGORY_PARENT_ID))
				.createdAt(CATEGORY_CREATED_AT)
				.updatedAt(CATEGORY_UPDATED_AT)
				.build();
	}

	private Product getProductArrange() {
		ProductImage image = ProductImage.builder()
				.id(new ProductImageId(IMAGE_ID))
				.productId(new ProductId(PRODUCT_ID))
				.imagePath(IMAGE_URL)
				.build();
		return Product.builder()
				.id(new ProductId(PRODUCT_ID))
				.sku(SKU)
				.tenantId(new TenantId(TENANT_ID))
				.categoryId(new CategoryId(CATEGORY_ID))
				.healthierChoiceId(new HealthierChoiceId(HEALTHIER_CHOICE_ID))
				.storeId(new StoreId(STORE_ID))
				.name(NAME)
				.barcode(BARCODE)
				.briefInformation(BRIEF_DESCRIPTION)
				.description(DESCRIPTION)
				.ingredients(INGREDIENTS)
				.status(STATUS)
				.unitPrice(new Money(UNIT_PRICE))
				.images(List.of(image))
				.updatedAt(PRODUCT_UPDATED_AT)
				.createdAt(PRODUCT_CREATED_AT)
				.build();
	}
}
