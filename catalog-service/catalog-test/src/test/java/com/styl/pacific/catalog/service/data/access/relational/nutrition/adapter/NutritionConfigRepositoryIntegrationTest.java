/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.nutrition.adapter;

import static org.assertj.core.api.Assertions.assertThat;

import com.styl.pacific.catalog.service.config.IntegrationTestConfiguration;
import com.styl.pacific.catalog.service.domain.nutrition.output.repository.NutritionConfigRepository;
import com.styl.pacific.common.test.BaseDataJpaTest;
import io.micrometer.core.instrument.MeterRegistry;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

/**
 * <AUTHOR>
 */
@ContextConfiguration(classes = { IntegrationTestConfiguration.class })
class NutritionConfigRepositoryIntegrationTest extends BaseDataJpaTest {
	@Autowired
	private NutritionConfigRepository nutritionConfigRepository;

	@MockitoBean
	private MeterRegistry meterRegistry;

	@Test
	void shouldReturnNutritionDefaultConfig_whenFindAll() {
		// Arrange
		int expectSize = 52;
		// Act
		var nutritionDefaultConfig = nutritionConfigRepository.findAll();
		// Assert
		assertThat(nutritionDefaultConfig).hasSize(expectSize);
	}
}
