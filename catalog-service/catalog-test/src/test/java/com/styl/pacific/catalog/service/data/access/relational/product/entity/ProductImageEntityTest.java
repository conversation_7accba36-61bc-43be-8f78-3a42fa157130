/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.product.entity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.time.Instant;
import java.time.ZonedDateTime;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
class ProductImageEntityTest {
	private static final Long IMAGE_ID = 1L;
	private static final String IMAGE_URL = "http://url.com";
	private static final Instant IMAGE_CREATED_AT = ZonedDateTime.now()
			.toInstant();
	private static final Instant IMAGE_UPDATED_AT = ZonedDateTime.now()
			.toInstant();
	private static final Instant IMAGE_DELETED_AT = ZonedDateTime.now()
			.toInstant();

	private static final Long PRODUCT_ID = 1L;

	@Test
	void shouldCreate_whenBuilder() {
		// Act
		ProductImageEntity imageEntity = ProductImageEntity.builder()
				.id(IMAGE_ID)
				.imagePath(IMAGE_URL)
				.createdAt(IMAGE_CREATED_AT)
				.updatedAt(IMAGE_UPDATED_AT)
				.deletedAt(IMAGE_DELETED_AT)
				.build();
		// Assert
		assertEquals(IMAGE_ID, imageEntity.getId());
		assertEquals(IMAGE_URL, imageEntity.getImagePath());
		assertEquals(IMAGE_CREATED_AT, imageEntity.getCreatedAt());
		assertEquals(IMAGE_UPDATED_AT, imageEntity.getUpdatedAt());
		assertEquals(IMAGE_DELETED_AT, imageEntity.getDeletedAt());
	}

	@Test
	void shouldReturnProperties_whenSetter() {
		// Arrange
		ProductImageEntity imageEntity = ProductImageEntity.builder()
				.build();
		// Act
		imageEntity.setId(IMAGE_ID);
		imageEntity.setImagePath(IMAGE_URL);
		imageEntity.setProductId(PRODUCT_ID);
		imageEntity.setCreatedAt(IMAGE_CREATED_AT);
		imageEntity.setUpdatedAt(IMAGE_UPDATED_AT);
		imageEntity.setDeletedAt(IMAGE_DELETED_AT);
		// Assert
		assertEquals(IMAGE_ID, imageEntity.getId());
		assertEquals(IMAGE_URL, imageEntity.getImagePath());
		assertEquals(PRODUCT_ID, imageEntity.getProductId());
		assertEquals(IMAGE_CREATED_AT, imageEntity.getCreatedAt());
		assertEquals(IMAGE_UPDATED_AT, imageEntity.getUpdatedAt());
		assertEquals(IMAGE_DELETED_AT, imageEntity.getDeletedAt());
	}

	@Test
	void shouldEqual_whenHashCode() {
		// Arrange
		ProductImageEntity entity1 = getImageEntity();
		ProductImageEntity entity2 = getImageEntity();
		// Act
		int hashCode1 = entity1.hashCode();
		int hashCode2 = entity2.hashCode();
		// Assert
		assertEquals(hashCode1, hashCode2);
	}

	@Test
	void shouldNotEqual_whenHashCode() {
		// Arrange
		ProductImageEntity entity1 = getImageEntity();
		ProductImageEntity entity2 = getImageEntity();
		entity2.setImagePath("http://url2.com");
		// Act
		int hashCode1 = entity1.hashCode();
		int hashCode2 = entity2.hashCode();
		// Assert
		assertNotEquals(hashCode1, hashCode2);
	}

	@Test
	void shouldEqual_whenEquals() {
		// Arrange
		ProductImageEntity entity1 = getImageEntity();
		ProductImageEntity entity2 = getImageEntity();
		// Act
		boolean isEqual = entity1.equals(entity2);
		// Assert
		assertTrue(isEqual);
	}

	@Test
	void shouldNotEqual_whenEquals() {
		// Arrange
		ProductImageEntity entity1 = getImageEntity();
		ProductImageEntity entity2 = getImageEntity();
		entity2.setImagePath("http://url2.com");
		// Act
		boolean isEqual = entity1.equals(entity2);
		// Assert
		assertFalse(isEqual);
	}

	private ProductImageEntity getImageEntity() {
		return ProductImageEntity.builder()
				.id(IMAGE_ID)
				.imagePath(IMAGE_URL)
				.productId(PRODUCT_ID)
				.createdAt(IMAGE_CREATED_AT)
				.updatedAt(IMAGE_UPDATED_AT)
				.deletedAt(IMAGE_DELETED_AT)
				.build();
	}
}
