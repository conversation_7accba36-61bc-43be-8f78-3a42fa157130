/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.domain;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.styl.pacific.catalog.service.domain.nutrition.NutritionServiceImpl;
import com.styl.pacific.catalog.service.domain.nutrition.dto.command.CreateNutritionCommand;
import com.styl.pacific.catalog.service.domain.nutrition.dto.command.UpdateNutritionCommand;
import com.styl.pacific.catalog.service.domain.nutrition.dto.query.NutritionQuery;
import com.styl.pacific.catalog.service.domain.nutrition.entity.Nutrition;
import com.styl.pacific.catalog.service.domain.nutrition.exception.NutritionNotFoundException;
import com.styl.pacific.catalog.service.domain.nutrition.id.generator.NutritionIdGenerator;
import com.styl.pacific.catalog.service.domain.nutrition.output.repository.NutritionRepository;
import com.styl.pacific.catalog.service.domain.tenant.dto.TenantDto;
import com.styl.pacific.catalog.service.domain.tenant.output.repository.TenantRepository;
import com.styl.pacific.domain.valueobject.NutritionId;
import com.styl.pacific.domain.valueobject.TenantId;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class NutritionServiceImplTest {
	private static final Long ID = 1L;
	private static final Long TENANT_ID = 1L;
	private static final String NAME = "nutrition";
	private static final String UNIT = "unit";
	private static final Instant CREATED_AT = ZonedDateTime.now()
			.toInstant();
	private static final Instant UPDATED_AT = ZonedDateTime.now()
			.toInstant();
	private static final String CURRENCY_CODE = "SGD";
	@Mock
	private TenantRepository tenantRepository;
	@Mock
	private NutritionRepository nutritionRepository;
	@Mock
	private NutritionIdGenerator nutritionIdGenerator;
	@InjectMocks
	private NutritionServiceImpl nutritionDomainService;

	@Test
	void shouldReturnListNutritionResponse_whenFindAll() {
		// Arrange
		Nutrition nutritionMock = getNutrition();
		NutritionQuery queryMock = NutritionQuery.builder()
				.build();
		List<Nutrition> nutritionListMock = List.of(nutritionMock);
		when(nutritionRepository.findAll(any(), any())).thenReturn(nutritionListMock);
		// Act
		List<Nutrition> nutritionList = nutritionDomainService.findAll(new TenantId(TENANT_ID), queryMock);
		// Assert
		assertEquals(nutritionListMock.size(), nutritionList.size());
		assertEquals(nutritionMock.getId(), nutritionList.getFirst()
				.getId());
	}

	@Test
	void shouldReturnNutritionResponse_whenFindById() {
		// Arrange
		Nutrition nutritionMock = getNutrition();
		when(nutritionRepository.findById(any(), any())).thenReturn(Optional.of(nutritionMock));
		// Act
		Nutrition nutrition = nutritionDomainService.findById(new TenantId(TENANT_ID), new NutritionId(ID));
		// Assert
		verify(nutritionRepository, times(1)).findById(any(), eq(new NutritionId(ID)));

		assertEquals(nutritionMock.getId(), nutrition.getId());
		assertEquals(nutritionMock.getTenantId(), nutrition.getTenantId());
		assertEquals(nutritionMock.getName(), nutrition.getName());
		assertEquals(nutritionMock.getUnit(), nutrition.getUnit());
		assertEquals(nutritionMock.getCreatedAt(), nutrition.getCreatedAt());
		assertEquals(nutritionMock.getUpdatedAt(), nutrition.getUpdatedAt());
	}

	@Test
    void shouldNutritionNotFound_whenFindById() {
        // Arrange
        when(nutritionRepository.findById(any(), any())).thenReturn(Optional.empty());
        String exceptionMessage = String.format("Nutrition %s not found", ID);
        // Act
        NutritionNotFoundException exception = assertThrows(NutritionNotFoundException.class,
                () -> nutritionDomainService.findById(new TenantId(TENANT_ID), new NutritionId(ID)));
        // Assert
        verify(nutritionRepository, times(1)).findById(any(), eq(new NutritionId(ID)));

        assertEquals(exceptionMessage, exception.getMessage());

    }

	@Test
	void shouldReturnNutritionResponse_whenCreate() {
		// Arrange
		Nutrition nutritionMock = getNutrition();
		CreateNutritionCommand commandMock = CreateNutritionCommand.builder()
				.unit(UNIT)
				.name(NAME)
				.build();
		when(nutritionIdGenerator.nextId()).thenReturn(new NutritionId(ID));
		when(nutritionRepository.save(any(Nutrition.class))).thenReturn(nutritionMock);
		TenantDto tenantDtoMock = TenantDto.builder()
				.tenantId(TENANT_ID)
				.build();
		when(tenantRepository.findById(anyLong())).thenReturn(Optional.of(tenantDtoMock));
		// Act
		Nutrition nutrition = nutritionDomainService.create(new TenantId(TENANT_ID), commandMock);
		// Assert
		verify(nutritionIdGenerator, times(1)).nextId();
		ArgumentCaptor<Nutrition> argumentCaptor = ArgumentCaptor.forClass(Nutrition.class);
		verify(nutritionRepository, times(1)).save(argumentCaptor.capture());
		Nutrition nutritionInput = argumentCaptor.getValue();
		assertEquals(ID, nutritionInput.getId()
				.getValue());
		assertEquals(commandMock.name(), nutritionInput.getName());
		assertEquals(commandMock.unit(), nutritionInput.getUnit());

		assertEquals(nutritionMock.getId(), nutrition.getId());
		assertEquals(nutritionMock.getTenantId(), nutrition.getTenantId());
		assertEquals(nutritionMock.getName(), nutrition.getName());
		assertEquals(nutritionMock.getUnit(), nutrition.getUnit());
		assertEquals(nutritionMock.getCreatedAt(), nutrition.getCreatedAt());
		assertEquals(nutritionMock.getUpdatedAt(), nutrition.getUpdatedAt());
	}

	@Test
	void shouldReturnNutritionResponse_whenUpdate() {
		// Arrange
		Nutrition nutritionMock = getNutrition();
		UpdateNutritionCommand commandMock = UpdateNutritionCommand.builder()
				.unit(UNIT)
				.name(NAME)
				.build();
		when(nutritionRepository.findById(any(), any())).thenReturn(Optional.of(nutritionMock));
		when(nutritionRepository.update(any(Nutrition.class))).then(invocation -> invocation.getArgument(0));
		// Act
		Nutrition nutrition = nutritionDomainService.update(new TenantId(TENANT_ID), new NutritionId(ID), commandMock);
		// Assert
		verify(nutritionRepository, times(1)).findById(any(), eq(new NutritionId(ID)));

		ArgumentCaptor<Nutrition> argumentCaptor = ArgumentCaptor.forClass(Nutrition.class);
		verify(nutritionRepository, times(1)).update(argumentCaptor.capture());
		Nutrition nutritionInput = argumentCaptor.getValue();
		assertEquals(nutritionMock.getId(), nutritionInput.getId());
		assertEquals(commandMock.name(), nutritionInput.getName());
		assertEquals(commandMock.unit(), nutritionInput.getUnit());

		assertEquals(nutritionMock.getId(), nutrition.getId());
		assertEquals(nutritionMock.getTenantId(), nutrition.getTenantId());
		assertEquals(nutritionMock.getName(), nutrition.getName());
		assertEquals(nutritionMock.getUnit(), nutrition.getUnit());
		assertEquals(nutritionMock.getCreatedAt(), nutrition.getCreatedAt());
		assertEquals(nutritionMock.getUpdatedAt(), nutrition.getUpdatedAt());
	}

	@Test
	void shouldNutritionNotFound_whenUpdate() {
		// Arrange
		UpdateNutritionCommand commandMock = UpdateNutritionCommand.builder()
				.name(NAME)
				.unit(UNIT)
				.build();
		when(nutritionRepository.findById(any(), any())).thenReturn(Optional.empty());
		String exceptionMessage = String.format("Nutrition %s not found", ID);
		// Act
		NutritionNotFoundException exception = assertThrows(NutritionNotFoundException.class,
				() -> nutritionDomainService.update(new TenantId(TENANT_ID), new NutritionId(ID), commandMock));
		// Assert
		verify(nutritionRepository, times(1)).findById(any(), eq(new NutritionId(ID)));

		assertEquals(exceptionMessage, exception.getMessage());

	}

	@Test
    void shouldInvolved_whenDelete() {
        // Arrange
        when(nutritionRepository.existById(any(), any())).thenReturn(true);
        // Act
        nutritionDomainService.deleteById(new TenantId(TENANT_ID), new NutritionId(ID));
        // Assert
        verify(nutritionRepository, times(1)).existById(any(), any());
        verify(nutritionRepository, times(1)).deleteById(any(), any());

    }

	@Test
    void shouldNutritionNotFound_whenDelete() {
        // Arrange
        when(nutritionRepository.existById(any(), any())).thenReturn(false);
        String exceptionMessage = String.format("Nutrition %s not found", ID);
        // Act
        NutritionNotFoundException exception = assertThrows(NutritionNotFoundException.class,
                () -> nutritionDomainService.deleteById(new TenantId(TENANT_ID), new NutritionId(ID)));
        // Assert
        verify(nutritionRepository, times(1)).existById(any(), eq(new NutritionId(ID)));

        assertEquals(exceptionMessage, exception.getMessage());

    }

	private Nutrition getNutrition() {

		return Nutrition.builder()
				.id(new NutritionId(ID))
				.tenantId(new TenantId(TENANT_ID))
				.name(NAME)
				.unit(UNIT)
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.build();
	}
}
