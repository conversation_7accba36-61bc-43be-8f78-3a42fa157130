/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.domain;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.styl.pacific.catalog.service.domain.healthierchoice.HealthierChoiceServiceImpl;
import com.styl.pacific.catalog.service.domain.healthierchoice.dto.command.CreateHealthierChoiceCommand;
import com.styl.pacific.catalog.service.domain.healthierchoice.dto.command.UpdateHealthierChoiceCommand;
import com.styl.pacific.catalog.service.domain.healthierchoice.dto.query.HealthierChoiceFilter;
import com.styl.pacific.catalog.service.domain.healthierchoice.dto.query.HealthierChoiceQuery;
import com.styl.pacific.catalog.service.domain.healthierchoice.entity.HealthierChoice;
import com.styl.pacific.catalog.service.domain.healthierchoice.exception.HealthierChoiceNotFoundException;
import com.styl.pacific.catalog.service.domain.healthierchoice.id.generator.HealthierChoiceIdGenerator;
import com.styl.pacific.catalog.service.domain.healthierchoice.output.repository.HealthierChoiceRepository;
import com.styl.pacific.catalog.service.domain.product.port.ouput.repository.ProductRepository;
import com.styl.pacific.catalog.service.domain.tenant.dto.TenantDto;
import com.styl.pacific.catalog.service.domain.tenant.output.repository.TenantRepository;
import com.styl.pacific.domain.valueobject.HealthierChoiceId;
import com.styl.pacific.domain.valueobject.TenantId;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class HealthierChoiceServiceImplTest {

	private static final Long ID = 1L;
	private static final String NAME = "Less than 100 calories";
	private static final String SYMBOL_URL = "https://ch-api.healthhub.sg/api/public/content/82aa9caf456a4a869a29c14dc6594073?v=f628ae31";
	private static final String DESCRIPTION = "Products carrying HCS with this tagline are crisps/chips where each individually packaged portion is less than or equal to 100 calories. ";
	private static final Instant CREATED_AT = ZonedDateTime.now()
			.toInstant();
	private static final Instant UPDATED_AT = ZonedDateTime.now()
			.toInstant();

	private static final Long TENANT_ID = 1L;
	private static final String CURRENCY_CODE = "SGD";

	@Mock
	private TenantRepository tenantRepository;
	@Mock
	private HealthierChoiceRepository healthierChoiceRepository;
	@Mock
	private ProductRepository productRepository;
	@Mock
	private HealthierChoiceIdGenerator healthierChoiceIdGenerator;
	@InjectMocks
	private HealthierChoiceServiceImpl healthierChoiceDomainService;

	@Test
	void shouldReturnListHealthierChoiceResponse_whenFindAll() {
		// Arrange
		HealthierChoiceQuery queryMock = HealthierChoiceQuery.builder()
				.filter(HealthierChoiceFilter.builder()
						.build())
				.build();
		HealthierChoice healthierChoice1Mock = getHealthierChoice();
		HealthierChoice healthierChoice2Mock = getHealthierChoice();
		List<HealthierChoice> healthierChoicesMock = List.of(healthierChoice1Mock, healthierChoice2Mock);
		when(healthierChoiceRepository.findAll(any(), any())).thenReturn(healthierChoicesMock);
		// Act
		List<HealthierChoice> actual = healthierChoiceDomainService.findAll(new TenantId(TENANT_ID), queryMock);
		// Assert
		verify(healthierChoiceRepository, times(1)).findAll(any(), any());
		assertEquals(healthierChoicesMock.size(), actual.size());
		assertEquals(healthierChoice1Mock.getId(), actual.getFirst()
				.getId());
		assertEquals(healthierChoice2Mock.getId(), actual.get(1)
				.getId());
	}

	@Test
	void shouldReturnHealthierChoiceResponse_whenCreate() {
		// Arrange
		CreateHealthierChoiceCommand commandMock = CreateHealthierChoiceCommand.builder()
				.name(NAME)
				.description(DESCRIPTION)
				.symbolPath(SYMBOL_URL)
				.build();
		when(healthierChoiceRepository.save(any(HealthierChoice.class))).then(invocation -> {
			HealthierChoice healthierChoiceInput = invocation.getArgument(0);
			healthierChoiceInput.setCreatedAt(CREATED_AT);
			healthierChoiceInput.setUpdatedAt(UPDATED_AT);
			return healthierChoiceInput;
		});
		when(healthierChoiceIdGenerator.nextId()).thenReturn(new HealthierChoiceId(ID));
		TenantDto tenantDtoMock = TenantDto.builder()
				.tenantId(TENANT_ID)
				.build();
		when(tenantRepository.findById(anyLong())).thenReturn(Optional.of(tenantDtoMock));
		// Act
		HealthierChoice response = healthierChoiceDomainService.create(new TenantId(TENANT_ID), commandMock);
		// Assert
		verify(healthierChoiceIdGenerator, times(1)).nextId();

		ArgumentCaptor<HealthierChoice> healthierChoiceCaptor = ArgumentCaptor.forClass(HealthierChoice.class);
		verify(healthierChoiceRepository, times(1)).save(healthierChoiceCaptor.capture());
		HealthierChoice input = healthierChoiceCaptor.getValue();
		assertEquals(commandMock.name(), input.getName());
		assertEquals(commandMock.description(), input.getDescription());
		assertEquals(commandMock.symbolPath(), input.getSymbolPath());

		assertEquals(ID, response.getId()
				.getValue());
		assertEquals(TENANT_ID, response.getTenantId()
				.getValue());
		assertEquals(NAME, response.getName());
		assertEquals(DESCRIPTION, response.getDescription());
		assertEquals(SYMBOL_URL, response.getSymbolPath());
		assertEquals(CREATED_AT, response.getCreatedAt());
		assertEquals(UPDATED_AT, response.getUpdatedAt());
	}

	@Test
	void shouldReturnHealthierChoiceResponse_whenFindById() {
		// Arrange
		HealthierChoice healthierChoiceMock = getHealthierChoice();
		when(healthierChoiceRepository.findById(any(), any())).thenReturn(Optional.of(healthierChoiceMock));
		// Act
		HealthierChoice response = healthierChoiceDomainService.findById(new TenantId(TENANT_ID), new HealthierChoiceId(
				ID));
		// Assert
		verify(healthierChoiceRepository, times(1)).findById(any(), eq(new HealthierChoiceId(ID)));

		assertEquals(healthierChoiceMock.getId(), response.getId());
		assertEquals(healthierChoiceMock.getTenantId(), response.getTenantId());
		assertEquals(healthierChoiceMock.getName(), response.getName());
		assertEquals(healthierChoiceMock.getDescription(), response.getDescription());
		assertEquals(healthierChoiceMock.getSymbolPath(), response.getSymbolPath());
		assertEquals(healthierChoiceMock.getCreatedAt(), response.getCreatedAt());
		assertEquals(healthierChoiceMock.getUpdatedAt(), response.getUpdatedAt());
	}

	@Test
	void shouldThrowHealthierChoiceNotFound_whenFindById() {
		// Arrange
		when(healthierChoiceRepository.findById(any(), any())).thenReturn(Optional.empty());
		String exceptionMessage = String.format("Healthier Choice with id: %d not found", ID);
		// Act
		HealthierChoiceNotFoundException exception = assertThrows(HealthierChoiceNotFoundException.class,
				() -> healthierChoiceDomainService.findById(new TenantId(TENANT_ID), new HealthierChoiceId(ID)));
		// Assert
		verify(healthierChoiceRepository, times(1)).findById(any(), eq(new HealthierChoiceId(ID)));
		assertEquals(exceptionMessage, exception.getMessage());
	}

	@Test
	void shouldReturnHealthierChoiceResponse_whenUpdate() {
		// Arrange
		UpdateHealthierChoiceCommand commandMock = UpdateHealthierChoiceCommand.builder()
				.name(NAME)
				.description(DESCRIPTION)
				.symbolPath(SYMBOL_URL)
				.build();

		HealthierChoice healthierChoiceMock = getHealthierChoice();
		when(healthierChoiceRepository.findById(any(), any())).thenReturn(Optional.of(healthierChoiceMock));
		when(healthierChoiceRepository.update(any(HealthierChoice.class))).then(invocation -> invocation.getArgument(
				0));
		// Act
		HealthierChoice response = healthierChoiceDomainService.update(new TenantId(TENANT_ID), new HealthierChoiceId(
				ID), commandMock);
		// Assert
		verify(healthierChoiceRepository, times(1)).findById(any(), eq(new HealthierChoiceId(ID)));
		ArgumentCaptor<HealthierChoice> healthierChoiceArgumentCaptor = ArgumentCaptor.forClass(HealthierChoice.class);
		verify(healthierChoiceRepository, times(1)).update(healthierChoiceArgumentCaptor.capture());
		HealthierChoice input = healthierChoiceArgumentCaptor.getValue();
		assertEquals(ID, input.getId()
				.getValue());
		assertEquals(TENANT_ID, input.getTenantId()
				.getValue());
		assertEquals(commandMock.name(), input.getName());
		assertEquals(commandMock.description(), input.getDescription());
		assertEquals(commandMock.symbolPath(), input.getSymbolPath());

		assertEquals(healthierChoiceMock.getId(), response.getId());
		assertEquals(healthierChoiceMock.getTenantId(), response.getTenantId());
		assertEquals(commandMock.name(), response.getName());
		assertEquals(commandMock.description(), response.getDescription());
		assertEquals(commandMock.symbolPath(), response.getSymbolPath());
		assertEquals(healthierChoiceMock.getCreatedAt(), response.getCreatedAt());
		assertEquals(healthierChoiceMock.getUpdatedAt(), response.getUpdatedAt());
	}

	@Test
	void shouldThrowHealthierChoiceNotFound_whenUpdate() {
		// Arrange
		String exceptionMessage = String.format("Healthier Choice with id: %d not found", ID);
		UpdateHealthierChoiceCommand commandMock = UpdateHealthierChoiceCommand.builder()
				.name(NAME)
				.description(DESCRIPTION)
				.symbolPath(SYMBOL_URL)
				.build();

		when(healthierChoiceRepository.findById(any(), any())).thenReturn(Optional.empty());
		// Act
		HealthierChoiceNotFoundException exception = assertThrows(HealthierChoiceNotFoundException.class,
				() -> healthierChoiceDomainService.update(new TenantId(TENANT_ID), new HealthierChoiceId(ID),
						commandMock));
		// Assert
		verify(healthierChoiceRepository, times(1)).findById(any(), eq(new HealthierChoiceId(ID)));
		assertEquals(exceptionMessage, exception.getMessage());
	}

	@Test
	void shouldSuccess_whenDelete() {
		// Arrange
		when(healthierChoiceRepository.existsById(any(), any())).thenReturn(true);
		when(productRepository.existByQuery(any(), any())).thenReturn(false);
		// Act
		healthierChoiceDomainService.deleteById(new TenantId(TENANT_ID), new HealthierChoiceId(ID));
		// Assert
		verify(healthierChoiceRepository, times(1)).deleteById(any(), eq(new HealthierChoiceId(ID)));
	}

	@Test
	void shouldThrowHealthierChoiceNotFound_whenDelete() {
		// Arrange
		String exceptionMessage = String.format("Healthier Choice with id: %d not found", ID);

		when(healthierChoiceRepository.existsById(any(), any())).thenReturn(false);
		// Act
		HealthierChoiceNotFoundException exception = assertThrows(HealthierChoiceNotFoundException.class,
				() -> healthierChoiceDomainService.deleteById(new TenantId(TENANT_ID), new HealthierChoiceId(ID)));
		// Assert
		verify(healthierChoiceRepository, times(1)).existsById(any(), eq(new HealthierChoiceId(ID)));
		verify(healthierChoiceRepository, times(0)).deleteById(any(), any());
		assertEquals(exceptionMessage, exception.getMessage());
	}

	private HealthierChoice getHealthierChoice() {
		return HealthierChoice.builder()
				.id(new HealthierChoiceId(ID))
				.name(NAME)
				.tenantId(new TenantId(TENANT_ID))
				.description(DESCRIPTION)
				.symbolPath(SYMBOL_URL)
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.build();
	}

}
