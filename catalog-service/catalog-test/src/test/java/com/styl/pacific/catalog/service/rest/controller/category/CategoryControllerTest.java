/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.rest.controller.category;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.aws.s3.config.PresignerConfiguration;
import com.styl.pacific.aws.s3.config.S3ConfigProperties;
import com.styl.pacific.aws.s3.mapper.PresignerContextProvider;
import com.styl.pacific.catalog.service.config.MvcTestConfiguration;
import com.styl.pacific.catalog.service.domain.category.CategoryService;
import com.styl.pacific.catalog.service.domain.category.dto.CategoryStubDto;
import com.styl.pacific.catalog.service.domain.category.dto.command.CreateCategoryCommand;
import com.styl.pacific.catalog.service.domain.category.dto.command.UpdateCategoryCommand;
import com.styl.pacific.catalog.service.domain.category.dto.query.CategoryFilter;
import com.styl.pacific.catalog.service.domain.category.dto.query.CategoryQuery;
import com.styl.pacific.catalog.service.domain.category.entity.Category;
import com.styl.pacific.catalog.service.domain.category.exception.CategoryNotFoundException;
import com.styl.pacific.catalog.service.domain.common.exception.CatalogDomainException;
import com.styl.pacific.catalog.service.presenter.exception.handler.CatalogExceptionHandler;
import com.styl.pacific.catalog.service.presenter.rest.category.CategoryController;
import com.styl.pacific.catalog.service.shared.http.category.request.CreateCategoryRequest;
import com.styl.pacific.common.constant.PacificRestConstants;
import com.styl.pacific.domain.dto.Content;
import com.styl.pacific.domain.valueobject.CategoryId;
import com.styl.pacific.domain.valueobject.TenantId;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Random;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * <AUTHOR>
 */
@DirtiesContext
@WebMvcTest(controllers = CategoryController.class)
@Import({ CategoryController.class, CatalogExceptionHandler.class, PresignerConfiguration.class,
		S3ConfigProperties.class, PresignerContextProvider.class })
@ContextConfiguration(classes = { MvcTestConfiguration.class })
class CategoryControllerTest {

	private static final String CATEGORY_PATH = "/api/catalog/categories";
	private static final Long CATEGORY_ID = 1L;
	private static final String CATEGORY_NAME = "Category";
	private static final String CATEGORY_IMAGE_URL = "bucket:image/category/image";
	private static final String CATEGORY_DESCRIPTION = "Category description";
	private static final Long CATEGORY_EXIST_ID = 2L;

	private static final Instant CATEGORY_CREATED_AT = ZonedDateTime.now()
			.toInstant();
	private static final Instant CATEGORY_UPDATED_AT = ZonedDateTime.now()
			.toInstant();
	private static final Long TENANT_ID = 1L;

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private ObjectMapper objectMapper;

	@MockitoBean
	private CategoryService categoryService;

	@Test
	void shouldReturnListCategoriesResponseWith200_whenFindCategoriesQuery() throws Exception {
		// Arrange
		CategoryStubDto response1 = getCategoryStub();
		CategoryStubDto response2 = getCategoryStub();
		CategoryStubDto response3 = getCategoryStub();
		List<CategoryStubDto> responseMock = List.of(response1, response2, response3);

		when(categoryService.findAll(any(), any(CategoryQuery.class))).thenReturn(Content.<CategoryStubDto>builder()
				.content(responseMock)
				.build());
		CategoryQuery query = CategoryQuery.builder()
				.filter(CategoryFilter.builder()
						.name(CATEGORY_NAME)
						.parentId(CATEGORY_EXIST_ID)
						.build())

				.build();

		String uri = UriComponentsBuilder.fromPath(CATEGORY_PATH)

				.queryParam("filter.name", query.filter()
						.name())

				.queryParam("filter.parentId", (query.filter()
						.parentId() != null
								? Long.toString(query.filter()
										.parentId())
								: null))

				.toUriString();

		// Act
		ResultActions resultActions = mockMvc.perform(get(uri).contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders()));
		// Assert
		resultActions.andExpect(status().isOk())
				.andExpect(content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(jsonPath("$.content").isArray())
				.andExpect(jsonPath("$.content.length()").value(responseMock.size()));

		ArgumentCaptor<CategoryQuery> captor = ArgumentCaptor.forClass(CategoryQuery.class);
		verify(categoryService, times(1)).findAll(any(), captor.capture());
		CategoryQuery queryCaptor = captor.getValue();
		assertEquals(query.filter()
				.name(), queryCaptor.filter()
						.name());
		assertEquals(query.filter()
				.parentId(), queryCaptor.filter()
						.parentId());
	}

	@Test
	void shouldReturnListCategoriesTreeResponseWith200_whenFindCategoriesTreeQuery() throws Exception {
		// Arrange
		Category response1 = getCategory();
		Category response2 = getCategory();
		Category response3 = getCategory();
		List<Category> responseMock = List.of(response1, response2, response3);

		when(categoryService.findAllTree(any(), any())).thenReturn(Content.<Category>builder()
				.content(responseMock)
				.build());

		String uri = UriComponentsBuilder.fromPath(CATEGORY_PATH)
				.pathSegment("tree")
				.toUriString();

		// Act
		ResultActions resultActions = mockMvc.perform(get(uri).contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders()));
		// Assert
		resultActions.andExpect(status().isOk())
				.andExpect(content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(jsonPath("$.content").isArray())
				.andExpect(jsonPath("$.content.length()").value(responseMock.size()));

		verify(categoryService, times(1)).findAllTree(any(), any());
	}

	@Test
	void shouldReturn200_whenGetCategory() throws Exception {
		// Arrange
		Category response = getCategory();
		when(categoryService.findById(any(), any())).thenReturn(response);
		// Act
		ResultActions resultActions = mockMvc.perform(get(CATEGORY_PATH + "/{categoryId}", CATEGORY_ID).contentType(
				MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders()));
		// Assert
		resultActions.andExpect(status().isOk())
				.andExpect(content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(jsonPath("$.id").value(response.getId()
						.getValue()))
				.andExpect(jsonPath("$.name").value(response.getName()))
				.andExpect(jsonPath("$.icon.path").value(response.getIconPath()))
				.andExpect(jsonPath("$.description").value(response.getDescription()))
				.andExpect(jsonPath("$.parentId").value(response.getParentId()
						.getValue()))
				.andExpect(jsonPath("$.tenantId").value(response.getTenantId()
						.getValue()))
				.andExpect(jsonPath("$.createdAt").value(response.getCreatedAt()
						.toEpochMilli()))
				.andExpect(jsonPath("$.updatedAt").value(response.getUpdatedAt()
						.toEpochMilli()));

		verify(categoryService, times(1)).findById(eq(new TenantId(TENANT_ID)), eq(new CategoryId(CATEGORY_ID)));
	}

	@Test
    void shouldReturn404_whenGetCategory() throws Exception {
        // Arrange
        when(categoryService.findById(any(), any())).thenThrow(CategoryNotFoundException.class);
        // Act
        ResultActions resultActions = mockMvc.perform(
                get(CATEGORY_PATH + "/{categoryId}", CATEGORY_ID).contentType(MediaType.APPLICATION_JSON)
                        .headers(getHttpHeaders()));
        // Assert
        resultActions.andExpect(status().isBadRequest())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").exists());
        verify(categoryService, times(1)).findById(eq(new TenantId(TENANT_ID)), eq(new CategoryId(CATEGORY_ID)));

    }

	@Test
    void shouldReturn400_whenGetCategory() throws Exception {
        // Arrange
        when(categoryService.findById(any(), any())).thenThrow(CatalogDomainException.class);
        // Act
        ResultActions resultActions = mockMvc.perform(
                get(CATEGORY_PATH + "/{categoryId}", CATEGORY_ID).contentType(MediaType.APPLICATION_JSON)
                        .headers(getHttpHeaders()));
        // Assert
        resultActions.andExpect(status().isBadRequest())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").exists());
        verify(categoryService, times(1)).findById(eq(new TenantId(TENANT_ID)), eq(new CategoryId(CATEGORY_ID)));

    }

	@Test
	void shouldCreateAndReturn201_whenCreateCategory() throws Exception {
		// Arrange
		CreateCategoryRequest request = CreateCategoryRequest.builder()
				.name(CATEGORY_NAME)
				.description(CATEGORY_DESCRIPTION)
				.iconPath(CATEGORY_IMAGE_URL)
				.parentId(CATEGORY_EXIST_ID.toString())
				.build();
		CategoryStubDto category = getCategoryStub();
		when(categoryService.create(eq(new TenantId(TENANT_ID)), any(CreateCategoryCommand.class))).thenReturn(
				category);
		// Act
		ResultActions resultActions = mockMvc.perform(post(CATEGORY_PATH).headers(getHttpHeaders())
				.content(objectMapper.writeValueAsString(request))
				.contentType(MediaType.APPLICATION_JSON));
		// Assert
		ArgumentCaptor<CreateCategoryCommand> captor = ArgumentCaptor.forClass(CreateCategoryCommand.class);
		verify(categoryService, times(1)).create(eq(new TenantId(TENANT_ID)), captor.capture());
		CreateCategoryCommand commandCaptor = captor.getValue();
		assertEquals(CATEGORY_NAME, commandCaptor.name());
		assertEquals(CATEGORY_DESCRIPTION, commandCaptor.description());
		assertEquals(CATEGORY_IMAGE_URL, commandCaptor.iconPath());
		assertEquals(CATEGORY_EXIST_ID, commandCaptor.parentId());
		resultActions.andExpect(status().isCreated())
				.andExpect(content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(jsonPath("$.id").value(CATEGORY_ID))
				.andExpect(jsonPath("$.name").value(request.name()))
				.andExpect(jsonPath("$.description").value(request.description()))
				.andExpect(jsonPath("$.tenantId").value(TENANT_ID))
				.andExpect(jsonPath("$.icon.path").value(request.iconPath()));

	}

	@Test
	void shouldCreateAndReturn400_whenCreateCategory() throws Exception {
		// Arrange
		CreateCategoryCommand command = CreateCategoryCommand.builder()
				.name(CATEGORY_NAME)
				.description(CATEGORY_DESCRIPTION)
				.iconPath(CATEGORY_IMAGE_URL)
				.parentId(CATEGORY_EXIST_ID)
				.build();
		when(categoryService.create(eq(new TenantId(TENANT_ID)), any(CreateCategoryCommand.class))).thenThrow(
				CatalogDomainException.class);
		// Act
		ResultActions resultActions = mockMvc.perform(post(CATEGORY_PATH).headers(getHttpHeaders())
				.content(objectMapper.writeValueAsString(command))
				.contentType(MediaType.APPLICATION_JSON));
		// Assert
		resultActions.andExpect(status().isBadRequest())
				.andExpect(content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(jsonPath("$.code").exists());
		ArgumentCaptor<CreateCategoryCommand> captor = ArgumentCaptor.forClass(CreateCategoryCommand.class);
		verify(categoryService, times(1)).create(eq(new TenantId(TENANT_ID)), captor.capture());
		CreateCategoryCommand commandCaptor = captor.getValue();
		assertEquals(CATEGORY_NAME, commandCaptor.name());
		assertEquals(CATEGORY_DESCRIPTION, commandCaptor.description());
		assertEquals(CATEGORY_IMAGE_URL, commandCaptor.iconPath());
		assertEquals(CATEGORY_EXIST_ID, commandCaptor.parentId());
	}

	@Test
	void shouldUpdateAndReturn200_whenUpdateCategory() throws Exception {
		// Arrange
		CategoryStubDto response = getCategoryStub();
		UpdateCategoryCommand command = new UpdateCategoryCommand(CATEGORY_NAME, CATEGORY_IMAGE_URL,
				CATEGORY_DESCRIPTION, CATEGORY_EXIST_ID);
		when(categoryService.update(eq(new TenantId(TENANT_ID)), eq(new CategoryId(CATEGORY_ID)), any(
				UpdateCategoryCommand.class))).thenReturn(response);
		// Act
		ResultActions resultActions = mockMvc.perform(put(CATEGORY_PATH + "/{categoryId}", CATEGORY_ID).headers(
				getHttpHeaders())
				.content(objectMapper.writeValueAsString(command))
				.contentType(MediaType.APPLICATION_JSON));
		// Assert
		resultActions.andExpect(status().isOk())
				.andExpect(content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(jsonPath("$.id").value(response.id()
						.getValue()))
				.andExpect(jsonPath("$.name").value(response.name()))
				.andExpect(jsonPath("$.icon.path").value(response.iconPath()))
				.andExpect(jsonPath("$.description").value(response.description()))
				.andExpect(jsonPath("$.parentId").value(Optional.ofNullable(response.parentId())
						.map(CategoryId::getValue)
						.orElse(null)))
				.andExpect(jsonPath("$.tenantId").value(response.tenantId()
						.getValue()))
				.andExpect(jsonPath("$.createdAt").value(response.createdAt()
						.toEpochMilli()))
				.andExpect(jsonPath("$.updatedAt").value(response.updatedAt()
						.toEpochMilli()));
		ArgumentCaptor<UpdateCategoryCommand> captor = ArgumentCaptor.forClass(UpdateCategoryCommand.class);
		verify(categoryService, times(1)).update(eq(new TenantId(TENANT_ID)), eq(new CategoryId(CATEGORY_ID)), captor
				.capture());
		UpdateCategoryCommand commandCaptor = captor.getValue();
		assertEquals(CATEGORY_NAME, commandCaptor.name());
		assertEquals(CATEGORY_DESCRIPTION, commandCaptor.description());
		assertEquals(CATEGORY_IMAGE_URL, commandCaptor.iconPath());
		assertEquals(CATEGORY_EXIST_ID, commandCaptor.parentId());
	}

	@Test
	void shouldReturn404_whenUpdateCategory() throws Exception {
		// Arrange
		UpdateCategoryCommand command = new UpdateCategoryCommand(CATEGORY_NAME, CATEGORY_IMAGE_URL,
				CATEGORY_DESCRIPTION, CATEGORY_EXIST_ID);
		when(categoryService.update(eq(new TenantId(TENANT_ID)), eq(new CategoryId(CATEGORY_ID)), any(
				UpdateCategoryCommand.class))).thenThrow(CategoryNotFoundException.class);
		// Act
		ResultActions resultActions = mockMvc.perform(put(CATEGORY_PATH + "/{categoryId}", CATEGORY_ID).headers(
				getHttpHeaders())
				.content(objectMapper.writeValueAsString(command))
				.contentType(MediaType.APPLICATION_JSON));
		// Assert
		resultActions.andExpect(status().isBadRequest())
				.andExpect(content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(jsonPath("$.code").exists());
		ArgumentCaptor<UpdateCategoryCommand> captor = ArgumentCaptor.forClass(UpdateCategoryCommand.class);
		verify(categoryService, times(1)).update(eq(new TenantId(TENANT_ID)), eq(new CategoryId(CATEGORY_ID)), captor
				.capture());
		UpdateCategoryCommand commandCaptor = captor.getValue();
		assertEquals(CATEGORY_NAME, commandCaptor.name());
		assertEquals(CATEGORY_DESCRIPTION, commandCaptor.description());
		assertEquals(CATEGORY_IMAGE_URL, commandCaptor.iconPath());
		assertEquals(CATEGORY_EXIST_ID, commandCaptor.parentId());
	}

	@Test
	void shouldReturn400_whenUpdateCategory() throws Exception {
		// Arrange
		UpdateCategoryCommand command = new UpdateCategoryCommand(CATEGORY_NAME, CATEGORY_IMAGE_URL,
				CATEGORY_DESCRIPTION, CATEGORY_EXIST_ID);
		when(categoryService.update(eq(new TenantId(TENANT_ID)), eq(new CategoryId(CATEGORY_ID)), any(
				UpdateCategoryCommand.class))).thenThrow(CatalogDomainException.class);
		// Act
		ResultActions resultActions = mockMvc.perform(put(CATEGORY_PATH + "/{categoryId}", CATEGORY_ID).headers(
				getHttpHeaders())
				.content(objectMapper.writeValueAsString(command))
				.contentType(MediaType.APPLICATION_JSON));
		// Assert
		resultActions.andExpect(status().isBadRequest())
				.andExpect(content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(jsonPath("$.code").exists());
		ArgumentCaptor<UpdateCategoryCommand> captor = ArgumentCaptor.forClass(UpdateCategoryCommand.class);
		verify(categoryService, times(1)).update(eq(new TenantId(TENANT_ID)), eq(new CategoryId(CATEGORY_ID)), captor
				.capture());
		UpdateCategoryCommand commandCaptor = captor.getValue();
		assertEquals(CATEGORY_NAME, commandCaptor.name());
		assertEquals(CATEGORY_DESCRIPTION, commandCaptor.description());
		assertEquals(CATEGORY_IMAGE_URL, commandCaptor.iconPath());
		assertEquals(CATEGORY_EXIST_ID, commandCaptor.parentId());
	}

	@Test
	void shouldDeleteAndReturn204_whenDeleteCategory() throws Exception {
		// Act
		ResultActions resultActions = mockMvc.perform(delete(CATEGORY_PATH + "/{categoryId}", CATEGORY_ID).headers(
				getHttpHeaders())
				.contentType(MediaType.APPLICATION_JSON));
		// Assert
		resultActions.andExpect(status().isNoContent());
		verify(categoryService, times(1)).deleteById(eq(new TenantId(TENANT_ID)), eq(new CategoryId(CATEGORY_ID)));

	}

	@Test
	void shouldReturn404_whenDeleteCategory() throws Exception {
		// Arrange
		doThrow(CategoryNotFoundException.class).when(categoryService)
				.deleteById(eq(new TenantId(TENANT_ID)), any());
		// Act
		ResultActions resultActions = mockMvc.perform(delete(CATEGORY_PATH + "/{categoryId}", CATEGORY_ID).headers(
				getHttpHeaders())
				.contentType(MediaType.APPLICATION_JSON));
		// Assert
		resultActions.andExpect(status().isBadRequest());
		verify(categoryService, times(1)).deleteById(eq(new TenantId(TENANT_ID)), eq(new CategoryId(CATEGORY_ID)));

	}

	@Test
	void shouldReturn400_whenDeleteCategory() throws Exception {
		// Arrange
		doThrow(CatalogDomainException.class).when(categoryService)
				.deleteById(eq(new TenantId(TENANT_ID)), any());
		// Act
		ResultActions resultActions = mockMvc.perform(delete(CATEGORY_PATH + "/{categoryId}", CATEGORY_ID).headers(
				getHttpHeaders())
				.contentType(MediaType.APPLICATION_JSON));
		// Assert
		resultActions.andExpect(status().isBadRequest());
		verify(categoryService, times(1)).deleteById(eq(new TenantId(TENANT_ID)), eq(new CategoryId(CATEGORY_ID)));
	}

	private Category getCategory() {
		return Category.builder()
				.id(new CategoryId(CATEGORY_ID))
				.tenantId(new TenantId(TENANT_ID))
				.name(CATEGORY_NAME)
				.iconPath(CATEGORY_IMAGE_URL)
				.createdAt(CATEGORY_CREATED_AT)
				.description(CATEGORY_DESCRIPTION)
				.parentId(new CategoryId(CATEGORY_EXIST_ID))
				.updatedAt(CATEGORY_UPDATED_AT)
				.build();
	}

	private CategoryStubDto getCategoryStub() {
		return CategoryStubDto.builder()
				.id(new CategoryId(CATEGORY_ID))
				.tenantId(new TenantId(TENANT_ID))
				.name(CATEGORY_NAME)
				.iconPath(CATEGORY_IMAGE_URL)
				.createdAt(CATEGORY_CREATED_AT)
				.description(CATEGORY_DESCRIPTION)
				.updatedAt(CATEGORY_UPDATED_AT)
				.build();
	}

	private HttpHeaders getHttpHeaders() {
		Map<String, String> map = new HashMap<>();
		map.put(PacificRestConstants.PlatformHeader.HEADER_X_REQUEST_ID, Long.toString(new Random().nextLong()));
		map.put(PacificRestConstants.PlatformHeader.HEADER_X_TENANT_ID, Long.toString(TENANT_ID));
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.setAll(map);
		return httpHeaders;
	}
}
