/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.rest.controller.healthierchoice.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.styl.pacific.aws.s3.config.PresignerConfiguration;
import com.styl.pacific.aws.s3.mapper.PresignerContextProvider;
import com.styl.pacific.catalog.service.config.S3Configuration;
import com.styl.pacific.catalog.service.domain.healthierchoice.dto.command.CreateHealthierChoiceCommand;
import com.styl.pacific.catalog.service.domain.healthierchoice.dto.command.UpdateHealthierChoiceCommand;
import com.styl.pacific.catalog.service.domain.healthierchoice.entity.HealthierChoice;
import com.styl.pacific.catalog.service.presenter.rest.healthierchoice.mapper.HealthierChoiceRestMapper;
import com.styl.pacific.catalog.service.shared.http.healthierchoice.request.CreateHealthierChoiceRequest;
import com.styl.pacific.catalog.service.shared.http.healthierchoice.request.UpdateHealthierChoiceRequest;
import com.styl.pacific.catalog.service.shared.http.healthierchoice.response.HealthierChoiceResponse;
import com.styl.pacific.domain.valueobject.HealthierChoiceId;
import com.styl.pacific.domain.valueobject.TenantId;
import java.time.Instant;
import java.time.ZonedDateTime;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

/**
 * <AUTHOR>
 */
@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = { PresignerConfiguration.class, PresignerContextProvider.class, S3Configuration.class })
class HealthierChoiceRestMapperTest {
	private static final Long ID = 1L;
	private static final Long TENANT_ID = 1L;
	private static final String NAME = "Less than 100 calories";
	private static final String SYMBOL_URL = "bucket:image/ch-api.healthhub.sg/api/public/content/82aa9caf456a4a869a29c14dc6594073?v=f628ae31";
	private static final String DESCRIPTION = "Products carrying HCS with this tagline are crisps/chips where each individually packaged portion is less than or equal to 100 calories. ";
	private static final Instant CREATED_AT = ZonedDateTime.now()
			.toInstant();
	private static final Instant UPDATED_AT = ZonedDateTime.now()
			.toInstant();

	@Test
	void shouldReturnHealthierChoiceResponse_whenMapFromModel() {
		// Arrange
		HealthierChoice modelMock = HealthierChoice.builder()
				.id(new HealthierChoiceId(ID))
				.tenantId(new TenantId(TENANT_ID))
				.name(NAME)
				.symbolPath(SYMBOL_URL)
				.description(DESCRIPTION)
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.build();
		// Act
		HealthierChoiceResponse response = HealthierChoiceRestMapper.INSTANCE.healthierChoiceToResponse(modelMock);
		// Assert
		assertEquals(modelMock.getId()
				.getValue()
				.toString(), response.id());
		assertEquals(modelMock.getTenantId()
				.getValue()
				.toString(), response.tenantId());
		assertEquals(modelMock.getName(), response.name());
		assertEquals(modelMock.getSymbolPath(), response.symbol()
				.path());
		assertEquals(modelMock.getDescription(), response.description());
		assertEquals(modelMock.getCreatedAt()
				.toEpochMilli(), response.createdAt());
		assertEquals(modelMock.getUpdatedAt()
				.toEpochMilli(), response.updatedAt());
	}

	@Test
	void shouldReturnCreateCommand_whenMapFromRequest() {
		// Arrange
		CreateHealthierChoiceRequest requestMock = new CreateHealthierChoiceRequest(NAME, SYMBOL_URL, DESCRIPTION);
		// Act
		CreateHealthierChoiceCommand command = HealthierChoiceRestMapper.INSTANCE.createHealthierChoiceRequestToCommand(
				requestMock);
		// Assert
		assertEquals(requestMock.name()
				.trim(), command.name());
		assertEquals(requestMock.symbolPath(), command.symbolPath());
		assertEquals(requestMock.description()
				.trim(), command.description());
	}

	@Test
	void shouldReturnUpdateCommand_whenMapFromRequest() {
		// Arrange
		UpdateHealthierChoiceRequest requestMock = new UpdateHealthierChoiceRequest(NAME, SYMBOL_URL, DESCRIPTION);
		// Act
		UpdateHealthierChoiceCommand command = HealthierChoiceRestMapper.INSTANCE.updateHealthierChoiceRequestToCommand(
				requestMock);
		// Assert
		assertEquals(requestMock.name()
				.trim(), command.name());
		assertEquals(requestMock.symbolPath(), command.symbolPath());
		assertEquals(requestMock.description()
				.trim(), command.description());
	}
}
