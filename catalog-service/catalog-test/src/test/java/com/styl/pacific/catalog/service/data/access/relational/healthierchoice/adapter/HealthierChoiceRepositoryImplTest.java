/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.healthierchoice.adapter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.querydsl.core.types.Predicate;
import com.styl.pacific.catalog.service.data.access.relational.healthierchoice.entity.HealthierChoiceEntity;
import com.styl.pacific.catalog.service.data.access.relational.healthierchoice.mapper.HealthierChoiceDataAccessMapper;
import com.styl.pacific.catalog.service.data.access.relational.healthierchoice.repository.HealthierChoiceJpaRepository;
import com.styl.pacific.catalog.service.domain.healthierchoice.dto.query.HealthierChoiceFilter;
import com.styl.pacific.catalog.service.domain.healthierchoice.dto.query.HealthierChoiceQuery;
import com.styl.pacific.catalog.service.domain.healthierchoice.entity.HealthierChoice;
import com.styl.pacific.domain.valueobject.HealthierChoiceId;
import com.styl.pacific.domain.valueobject.TenantId;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class HealthierChoiceRepositoryImplTest {
	private static final Long ID = 1L;
	private static final Long TENANT_ID = 1L;
	private static final String NAME = "Less than 100 calories";
	private static final String SYMBOL_URL = "https://ch-api.healthhub.sg/api//content/82aa9caf456a4a869a29c14dc6594073?v=f628ae31";
	private static final String DESCRIPTION = "Products carrying HCS with this tagline are crisps/chips where each individually packaged portion is less than or equal to 100 calories. ";
	private static final Instant CREATED_AT = ZonedDateTime.now()
			.toInstant();
	private static final Instant UPDATED_AT = ZonedDateTime.now()
			.toInstant();
	private static final Instant DELETED_AT = ZonedDateTime.now()
			.toInstant();

	@Mock
	private HealthierChoiceJpaRepository healthierChoiceJpaRepository;
	@Spy
	private HealthierChoiceDataAccessMapper dataAccessMapper = HealthierChoiceDataAccessMapper.INSTANCE;
	@InjectMocks
	private HealthierChoiceRepositoryImpl healthierChoiceRepository;

	@Test
	void shouldReturnListHealthierChoices_whenFindAll() {
		// Arrange
		HealthierChoiceQuery query = HealthierChoiceQuery.builder()
				.filter(HealthierChoiceFilter.builder()
						.build())
				.build();
		HealthierChoiceEntity entityMock = getEntity();
		List<HealthierChoiceEntity> entitiesMock = List.of(entityMock);
		when(healthierChoiceJpaRepository.findBy(any(Predicate.class), any())).thenReturn(entitiesMock);
		// Act
		List<HealthierChoice> healthierChoices = healthierChoiceRepository.findAll(new TenantId(TENANT_ID), query);
		// Assert
		verify(healthierChoiceJpaRepository, times(1)).findBy(any(Predicate.class), any());
		assertEquals(entitiesMock.size(), healthierChoices.size());
		assertEquals(entitiesMock.getFirst()
				.getId(), healthierChoices.getFirst()
						.getId()
						.getValue());
	}

	@Test
	void shouldReturnHealthierChoice_whenFindById() {
		// Arrange
		HealthierChoiceEntity entityMock = getEntity();
		when(healthierChoiceJpaRepository.findOne(any(Predicate.class))).thenReturn(Optional.of(entityMock));
		// Act
		Optional<HealthierChoice> healthierChoiceFind = healthierChoiceRepository.findById(new TenantId(TENANT_ID),
				new HealthierChoiceId(ID));
		// Assert
		verify(healthierChoiceJpaRepository, times(1)).findOne(any(Predicate.class));
		assertTrue(healthierChoiceFind.isPresent());

		HealthierChoice healthierChoice = healthierChoiceFind.get();
		assertEquals(entityMock.getId(), healthierChoice.getId()
				.getValue());
		assertEquals(entityMock.getTenantId(), healthierChoice.getTenantId()
				.getValue());
		assertEquals(entityMock.getName(), healthierChoice.getName());
		assertEquals(entityMock.getDescription(), healthierChoice.getDescription());
		assertEquals(entityMock.getSymbolPath(), healthierChoice.getSymbolPath());
		assertEquals(entityMock.getCreatedAt(), healthierChoice.getCreatedAt());
		assertEquals(entityMock.getUpdatedAt(), healthierChoice.getUpdatedAt());
	}

	@Test
	void shouldReturnHealthierChoice_whenSave() {
		// Arrange
		HealthierChoice healthierChoiceMock = getModel();
		when(healthierChoiceJpaRepository.save(any(HealthierChoiceEntity.class))).then(invocation -> invocation
				.getArgument(0));
		// Act
		HealthierChoice healthierChoiceSave = healthierChoiceRepository.save(healthierChoiceMock);
		// Assert
		verify(healthierChoiceJpaRepository, times(1)).save(any(HealthierChoiceEntity.class));
		assertEquals(healthierChoiceMock.getId(), healthierChoiceSave.getId());
		assertEquals(healthierChoiceMock.getTenantId(), healthierChoiceSave.getTenantId());
		assertEquals(healthierChoiceMock.getDescription(), healthierChoiceSave.getDescription());
		assertEquals(healthierChoiceMock.getSymbolPath(), healthierChoiceSave.getSymbolPath());
		assertEquals(healthierChoiceMock.getName(), healthierChoiceSave.getName());
		assertEquals(healthierChoiceMock.getCreatedAt(), healthierChoiceSave.getCreatedAt());
		assertEquals(healthierChoiceMock.getUpdatedAt(), healthierChoiceSave.getUpdatedAt());
	}

	@Test
	void shouldReturnHealthierChoice_whenUpdate() {
		// Arrange
		HealthierChoice healthierChoiceMock = getModel();
		when(healthierChoiceJpaRepository.saveAndFlush(any(HealthierChoiceEntity.class))).then(invocation -> invocation
				.getArgument(0));
		// Act
		HealthierChoice healthierChoiceSave = healthierChoiceRepository.update(healthierChoiceMock);
		// Assert
		verify(healthierChoiceJpaRepository, times(1)).saveAndFlush(any(HealthierChoiceEntity.class));
		assertEquals(healthierChoiceMock.getId(), healthierChoiceSave.getId());
		assertEquals(healthierChoiceMock.getTenantId(), healthierChoiceSave.getTenantId());
		assertEquals(healthierChoiceMock.getDescription(), healthierChoiceSave.getDescription());
		assertEquals(healthierChoiceMock.getSymbolPath(), healthierChoiceSave.getSymbolPath());
		assertEquals(healthierChoiceMock.getName(), healthierChoiceSave.getName());
		assertEquals(healthierChoiceMock.getCreatedAt(), healthierChoiceSave.getCreatedAt());
		assertEquals(healthierChoiceMock.getUpdatedAt(), healthierChoiceSave.getUpdatedAt());
	}

	@Test
    void shouldInvolve_whenDeleteById() {
        // Arrange
        when(healthierChoiceJpaRepository.findOne(any(Predicate.class))).thenReturn(Optional.of(getEntity()));
        // Act
        healthierChoiceRepository.deleteById(new TenantId(TENANT_ID), new HealthierChoiceId(ID));
        // Assert
        verify(healthierChoiceJpaRepository, times(1)).findOne(any(Predicate.class));
        verify(healthierChoiceJpaRepository, times(1)).delete(any(HealthierChoiceEntity.class));
    }

	private HealthierChoice getModel() {
		return HealthierChoice.builder()
				.id(new HealthierChoiceId(ID))
				.name(NAME)
				.tenantId(new TenantId(TENANT_ID))
				.symbolPath(SYMBOL_URL)
				.description(DESCRIPTION)
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.build();
	}

	private HealthierChoiceEntity getEntity() {
		return HealthierChoiceEntity.builder()
				.id(ID)
				.name(NAME)
				.tenantId(TENANT_ID)
				.symbolPath(SYMBOL_URL)
				.description(DESCRIPTION)
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.deletedAt(DELETED_AT)
				.build();
	}
}
