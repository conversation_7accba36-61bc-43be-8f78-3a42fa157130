/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.rest.controller.healthierchoice;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.aws.s3.config.PresignerConfiguration;
import com.styl.pacific.aws.s3.config.S3ConfigProperties;
import com.styl.pacific.aws.s3.mapper.PresignerContextProvider;
import com.styl.pacific.catalog.service.config.MvcTestConfiguration;
import com.styl.pacific.catalog.service.domain.healthierchoice.HealthierChoiceService;
import com.styl.pacific.catalog.service.domain.healthierchoice.dto.command.CreateHealthierChoiceCommand;
import com.styl.pacific.catalog.service.domain.healthierchoice.dto.command.UpdateHealthierChoiceCommand;
import com.styl.pacific.catalog.service.domain.healthierchoice.entity.HealthierChoice;
import com.styl.pacific.catalog.service.presenter.exception.handler.CatalogExceptionHandler;
import com.styl.pacific.catalog.service.presenter.rest.healthierchoice.HealthierChoiceController;
import com.styl.pacific.catalog.service.shared.http.healthierchoice.request.CreateHealthierChoiceRequest;
import com.styl.pacific.catalog.service.shared.http.healthierchoice.request.HealthierChoiceFilterRequest;
import com.styl.pacific.catalog.service.shared.http.healthierchoice.request.HealthierChoicesQueryRequest;
import com.styl.pacific.common.constant.PacificRestConstants;
import com.styl.pacific.domain.valueobject.HealthierChoiceId;
import com.styl.pacific.domain.valueobject.TenantId;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * <AUTHOR>
 */
@WebMvcTest(controllers = HealthierChoiceController.class)
@Import({ HealthierChoiceController.class, CatalogExceptionHandler.class, PresignerConfiguration.class,
		S3ConfigProperties.class, PresignerContextProvider.class })
@ContextConfiguration(classes = { MvcTestConfiguration.class })
class HealthierChoiceControllerTest {

	private static final Long ID = 1L;
	private static final Long TENANT_ID = 1L;
	private static final String NAME = "Less than 100 calories";
	private static final String SYMBOL_URL = "bucket:image/ch_apihealthhub/api/S82aa9caf456a4a869a29c14dc6594073.png";
	private static final String DESCRIPTION = "Products carrying HCS with this tagline are crisps/chips where each individually packaged portion is less than or equal to 100 calories. ";
	private static final Instant CREATED_AT = ZonedDateTime.now()
			.toInstant();
	private static final Instant UPDATED_AT = ZonedDateTime.now()
			.toInstant();

	private static final String HEALTHIER_CHOICE_PATH = "/api/catalog/healthier-choices";
	@Autowired
	private ObjectMapper objectMapper;

	@Autowired
	private MockMvc mockMvc;

	@MockitoBean
	private HealthierChoiceService healthierChoiceService;

	@Test
	void shouldReturn200WithListHealthierChoices_whenGet() throws Exception {
		// Arrange
		HealthierChoicesQueryRequest requestMock = HealthierChoicesQueryRequest.builder()
				.filter(HealthierChoiceFilterRequest.builder()
						.name(NAME)
						.build())
				.build();
		HealthierChoice healthierChoiceResMock = getHealthierChoice();
		List<HealthierChoice> listHealthierChoices = List.of(healthierChoiceResMock);
		when(healthierChoiceService.findAll(any(), any())).thenReturn(listHealthierChoices);

		String uri = UriComponentsBuilder.fromPath(HEALTHIER_CHOICE_PATH)
				.queryParam("filter.name", requestMock.filter()
						.name())
				.toUriString();
		// Act
		ResultActions result = mockMvc.perform(get(uri).headers(getHttpHeaders()));
		// Assert
		verify(healthierChoiceService, times(1)).findAll(any(), any());

		result.andExpect(status().isOk())

				.andExpect(content().contentType(MediaType.APPLICATION_JSON))

				.andExpect(jsonPath("$.content.length()").value(listHealthierChoices.size()))

				.andExpect(jsonPath("$.content[0].id").value(healthierChoiceResMock.getId()
						.getValue()));
	}

	@Test
	void shouldReturn200WithHealthierChoiceResponse_whenGetById() throws Exception {
		// Arrange
		HealthierChoice healthierChoiceResMock = getHealthierChoice();
		when(healthierChoiceService.findById(any(), any())).thenReturn(healthierChoiceResMock);
		Map<String, Object> uriPath = new HashMap<>();
		uriPath.put("id", ID);
		String uri = UriComponentsBuilder.fromPath(HEALTHIER_CHOICE_PATH)
				.pathSegment("{id}")
				.buildAndExpand(uriPath)
				.toUriString();
		// Act
		ResultActions result = mockMvc.perform(get(uri).headers(getHttpHeaders()));
		// Assert
		verify(healthierChoiceService, times(1)).findById(any(), eq(new HealthierChoiceId(ID)));

		result.andExpect(status().isOk())
				.andExpect(content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(jsonPath("$.id").value(healthierChoiceResMock.getId()
						.getValue()))
				.andExpect(jsonPath("$.tenantId").value(healthierChoiceResMock.getTenantId()
						.getValue()))
				.andExpect(jsonPath("$.name").value(healthierChoiceResMock.getName()))
				.andExpect(jsonPath("$.description").value(healthierChoiceResMock.getDescription()))
				.andExpect(jsonPath("$.symbol.path").value(healthierChoiceResMock.getSymbolPath()))
				.andExpect(jsonPath("$.createdAt").value(healthierChoiceResMock.getCreatedAt()
						.toEpochMilli()))
				.andExpect(jsonPath("$.updatedAt").value(healthierChoiceResMock.getUpdatedAt()
						.toEpochMilli()));
	}

	@Test
	void shouldReturn201WithHealthierChoiceResponse_whenPostCreate() throws Exception {
		// Arrange
		HealthierChoice healthierChoiceMock = getHealthierChoice();
		healthierChoiceMock.setName(healthierChoiceMock.getName()
				.trim());
		healthierChoiceMock.setDescription(healthierChoiceMock.getDescription()
				.trim());
		when(healthierChoiceService.create(any(), any(CreateHealthierChoiceCommand.class))).thenReturn(
				healthierChoiceMock);
		CreateHealthierChoiceRequest commandMock = new CreateHealthierChoiceRequest(NAME, SYMBOL_URL, DESCRIPTION);

		String uri = UriComponentsBuilder.fromPath(HEALTHIER_CHOICE_PATH)
				.toUriString();
		// Act
		ResultActions result = mockMvc.perform(post(uri).headers(getHttpHeaders())
				.content(objectMapper.writeValueAsString(commandMock))
				.contentType(MediaType.APPLICATION_JSON));
		// Assert
		ArgumentCaptor<CreateHealthierChoiceCommand> argumentCaptor = ArgumentCaptor.forClass(
				CreateHealthierChoiceCommand.class);
		verify(healthierChoiceService, times(1)).create(eq(new TenantId(TENANT_ID)), argumentCaptor.capture());
		CreateHealthierChoiceCommand commandInput = argumentCaptor.getValue();
		assertEquals(commandMock.name(), commandInput.name());
		assertEquals(commandMock.description()
				.trim(), commandInput.description());
		assertEquals(commandMock.symbolPath(), commandInput.symbolPath());

		result.andExpect(status().isCreated())
				.andExpect(content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(jsonPath("$.id").value(healthierChoiceMock.getId()
						.getValue()))
				.andExpect(jsonPath("$.tenantId").value(healthierChoiceMock.getTenantId()
						.getValue()))
				.andExpect(jsonPath("$.name").value(healthierChoiceMock.getName()
						.trim()))
				.andExpect(jsonPath("$.description").value(healthierChoiceMock.getDescription()
						.trim()))
				.andExpect(jsonPath("$.symbol.path").value(healthierChoiceMock.getSymbolPath()))
				.andExpect(jsonPath("$.createdAt").value(healthierChoiceMock.getCreatedAt()
						.toEpochMilli()))
				.andExpect(jsonPath("$.updatedAt").value(healthierChoiceMock.getUpdatedAt()
						.toEpochMilli()));
	}

	@Test
	void shouldReturn200WithHealthierChoiceResponse_whenPutUpdate() throws Exception {
		// Arrange
		HealthierChoice healthierChoiceResMock = getHealthierChoice();
		healthierChoiceResMock.setName(healthierChoiceResMock.getName()
				.trim());
		healthierChoiceResMock.setDescription(healthierChoiceResMock.getDescription()
				.trim());
		when(healthierChoiceService.update(any(), any(), any(UpdateHealthierChoiceCommand.class))).thenReturn(
				healthierChoiceResMock);
		UpdateHealthierChoiceCommand commandMock = UpdateHealthierChoiceCommand.builder()
				.name(NAME)
				.description(DESCRIPTION)
				.symbolPath(SYMBOL_URL)
				.build();

		Map<String, Object> uriPath = new HashMap<>();
		uriPath.put("id", ID);
		String uri = UriComponentsBuilder.fromPath(HEALTHIER_CHOICE_PATH)
				.pathSegment("{id}")
				.buildAndExpand(uriPath)
				.toUriString();
		// Act
		ResultActions result = mockMvc.perform(put(uri).headers(getHttpHeaders())
				.content(objectMapper.writeValueAsString(commandMock))
				.contentType(MediaType.APPLICATION_JSON));
		// Assert
		ArgumentCaptor<UpdateHealthierChoiceCommand> argumentCaptor = ArgumentCaptor.forClass(
				UpdateHealthierChoiceCommand.class);
		verify(healthierChoiceService, times(1)).update(any(), eq(new HealthierChoiceId(ID)), argumentCaptor.capture());
		UpdateHealthierChoiceCommand commandInput = argumentCaptor.getValue();
		assertEquals(commandMock.name()
				.trim(), commandInput.name());
		assertEquals(commandMock.description()
				.trim(), commandInput.description());
		assertEquals(commandMock.symbolPath(), commandInput.symbolPath());

		result.andExpect(status().isOk())
				.andExpect(content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(jsonPath("$.id").value(healthierChoiceResMock.getId()
						.getValue()))
				.andExpect(jsonPath("$.tenantId").value(healthierChoiceResMock.getTenantId()
						.getValue()))
				.andExpect(jsonPath("$.name").value(healthierChoiceResMock.getName()
						.trim()))
				.andExpect(jsonPath("$.description").value(healthierChoiceResMock.getDescription()
						.trim()))
				.andExpect(jsonPath("$.symbol.path").value(healthierChoiceResMock.getSymbolPath()))
				.andExpect(jsonPath("$.createdAt").value(healthierChoiceResMock.getCreatedAt()
						.toEpochMilli()))
				.andExpect(jsonPath("$.updatedAt").value(healthierChoiceResMock.getUpdatedAt()
						.toEpochMilli()));
	}

	@Test
	void shouldReturn204_whenDelete() throws Exception {
		// Arrange
		Map<String, Object> uriPath = new HashMap<>();
		uriPath.put("id", ID);
		String uri = UriComponentsBuilder.fromPath(HEALTHIER_CHOICE_PATH)
				.pathSegment("{id}")
				.buildAndExpand(uriPath)
				.toUriString();
		// Act
		ResultActions result = mockMvc.perform(delete(uri).headers(getHttpHeaders()));
		// Assert
		result.andExpect(status().isNoContent());
	}

	private HealthierChoice getHealthierChoice() {
		return HealthierChoice.builder()
				.id(new HealthierChoiceId(ID))
				.tenantId(new TenantId(TENANT_ID))
				.name(NAME)
				.description(DESCRIPTION)
				.symbolPath(SYMBOL_URL)
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.build();
	}

	private HttpHeaders getHttpHeaders() {
		Map<String, String> map = new HashMap<>();
		map.put(PacificRestConstants.PlatformHeader.HEADER_X_REQUEST_ID, Long.toString(new Random().nextLong()));
		map.put(PacificRestConstants.PlatformHeader.HEADER_X_TENANT_ID, Long.toString(TENANT_ID));
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.setAll(map);
		return httpHeaders;
	}
}
