/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.presenter.rest.nutrition;

import com.styl.pacific.application.rest.context.RequestContext;
import com.styl.pacific.catalog.service.domain.nutrition.NutritionService;
import com.styl.pacific.catalog.service.domain.nutrition.dto.command.CreateNutritionCommand;
import com.styl.pacific.catalog.service.domain.nutrition.dto.command.UpdateNutritionCommand;
import com.styl.pacific.catalog.service.domain.nutrition.dto.query.NutritionQuery;
import com.styl.pacific.catalog.service.domain.nutrition.dto.query.PaginationNutritionQuery;
import com.styl.pacific.catalog.service.domain.nutrition.entity.Nutrition;
import com.styl.pacific.catalog.service.presenter.rest.nutrition.mapper.NutritionRestMapper;
import com.styl.pacific.catalog.service.shared.http.nutrition.NutritionApi;
import com.styl.pacific.catalog.service.shared.http.nutrition.request.CreateNutritionRequest;
import com.styl.pacific.catalog.service.shared.http.nutrition.request.NutritionQueryRequest;
import com.styl.pacific.catalog.service.shared.http.nutrition.request.PaginationNutritionQueryRequest;
import com.styl.pacific.catalog.service.shared.http.nutrition.request.UpdateNutritionRequest;
import com.styl.pacific.catalog.service.shared.http.nutrition.response.ListNutritionResponse;
import com.styl.pacific.catalog.service.shared.http.nutrition.response.NutritionResponse;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.NutritionId;
import com.styl.pacific.domain.valueobject.TenantId;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@RequiredArgsConstructor
public class NutritionController implements NutritionApi {
	private final NutritionService nutritionService;
	private final RequestContext requestContext;

	@Override
	public ListNutritionResponse findAll(NutritionQueryRequest request) {
		NutritionQuery query = NutritionRestMapper.INSTANCE.toQuery(request);
		List<Nutrition> nutritionList = nutritionService.findAll(Optional.ofNullable(requestContext.getTenantId())
				.map(TenantId::new)
				.orElse(null), query);
		return new ListNutritionResponse(nutritionList.stream()
				.map(NutritionRestMapper.INSTANCE::toResponse)
				.toList());
	}

	@Override
	public Paging<NutritionResponse> findAllPaging(PaginationNutritionQueryRequest query) {
		PaginationNutritionQuery paginationQuery = NutritionRestMapper.INSTANCE.toQuery(query);
		Paging<Nutrition> nutritionPaging = nutritionService.findAllPaging(Optional.ofNullable(requestContext
				.getTenantId())
				.map(TenantId::new)
				.orElse(null), paginationQuery);
		return Paging.<NutritionResponse>builder()
				.content(nutritionPaging.getContent()
						.stream()
						.map(NutritionRestMapper.INSTANCE::toResponse)
						.toList())
				.totalElements(nutritionPaging.getTotalElements())
				.totalPages(nutritionPaging.getTotalPages())
				.page(nutritionPaging.getPage())
				.sort(nutritionPaging.getSort())
				.build();
	}

	@Override
	public NutritionResponse findById(String id) {
		Nutrition nutrition = nutritionService.findById(Optional.ofNullable(requestContext.getTenantId())
				.map(TenantId::new)
				.orElse(null), Optional.of(NumberUtils.isCreatable(id))
						.filter(Boolean::booleanValue)
						.map(t -> NumberUtils.createLong(id))
						.map(NutritionId::new)
						.orElse(null));
		return NutritionRestMapper.INSTANCE.toResponse(nutrition);
	}

	@Override
	public NutritionResponse create(CreateNutritionRequest request) {
		CreateNutritionCommand command = NutritionRestMapper.INSTANCE.toCommand(request);
		Nutrition nutrition = nutritionService.create(Optional.ofNullable(requestContext.getTenantId())
				.map(TenantId::new)
				.orElse(null), command);
		return NutritionRestMapper.INSTANCE.toResponse(nutrition);
	}

	@Override
	public NutritionResponse update(String id, UpdateNutritionRequest request) {
		UpdateNutritionCommand command = NutritionRestMapper.INSTANCE.toCommand(request);
		Nutrition nutrition = nutritionService.update(Optional.ofNullable(requestContext.getTenantId())
				.map(TenantId::new)
				.orElse(null), Optional.of(NumberUtils.isCreatable(id))
						.filter(Boolean::booleanValue)
						.map(t -> NumberUtils.createLong(id))
						.map(NutritionId::new)
						.orElse(null), command);
		return NutritionRestMapper.INSTANCE.toResponse(nutrition);
	}

	@Override
	public void deleteById(String id) {
		nutritionService.deleteById(Optional.ofNullable(requestContext.getTenantId())
				.map(TenantId::new)
				.orElse(null), Optional.of(NumberUtils.isCreatable(id))
						.filter(Boolean::booleanValue)
						.map(t -> NumberUtils.createLong(id))
						.map(NutritionId::new)
						.orElse(null));
	}
}
