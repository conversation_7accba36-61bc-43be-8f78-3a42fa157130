/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.domain.category.handler;

import com.styl.pacific.catalog.service.domain.category.dto.CategoryStubDto;
import com.styl.pacific.catalog.service.domain.category.dto.command.CreateCategoryCommand;
import com.styl.pacific.catalog.service.domain.category.dto.command.UpdateCategoryCommand;
import com.styl.pacific.catalog.service.domain.category.dto.query.CategoryFilter;
import com.styl.pacific.catalog.service.domain.category.dto.query.PaginationCategoryQuery;
import com.styl.pacific.catalog.service.domain.category.entity.Category;
import com.styl.pacific.catalog.service.domain.category.exception.CategoryNotFoundException;
import com.styl.pacific.catalog.service.domain.category.id.generator.CategoryIdGenerator;
import com.styl.pacific.catalog.service.domain.category.mapper.CategoryDataMapper;
import com.styl.pacific.catalog.service.domain.category.output.repository.CategoryRepository;
import com.styl.pacific.catalog.service.domain.common.exception.CatalogDomainException;
import com.styl.pacific.catalog.service.domain.product.dto.query.ProductFilter;
import com.styl.pacific.catalog.service.domain.product.port.ouput.repository.ProductRepository;
import com.styl.pacific.catalog.service.domain.tenant.dto.TenantDto;
import com.styl.pacific.catalog.service.domain.tenant.output.repository.TenantRepository;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.CategoryId;
import com.styl.pacific.domain.valueobject.TenantId;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class CategoryCommandHandler {
	private final Logger log = LoggerFactory.getLogger(this.getClass());
	private final CategoryRepository categoryRepository;
	private final ProductRepository productRepository;
	private final TenantRepository tenantRepository;
	private final CategoryIdGenerator categoryIdGenerator;

	@Transactional
	public CategoryStubDto create(TenantId tenantId, @Valid CreateCategoryCommand command) {
		TenantDto tenant = tenantCheck(tenantId);
		var category = CategoryDataMapper.INSTANCE.toModel(tenant.getTenantId(), command);
		category.setId(categoryIdGenerator.nextId());
		validateCategory(tenantId, null, category.getParentId(), category.getName());
		return categoryRepository.save(category);
	}

	@Transactional
	public CategoryStubDto update(TenantId tenantId, CategoryId id, @Valid UpdateCategoryCommand command) {
		Category category = categoryCheck(tenantId, id);
		updateFieldCategory(category, command);
		validateCategory(tenantId, category.getId(), category.getParentId(), category.getName());
		return categoryRepository.update(category);
	}

	private void updateFieldCategory(Category category, UpdateCategoryCommand command) {
		if (Objects.equals(category.getId()
				.getValue(), command.parentId())) {
			log.warn("Category can't assign parent to itself");
			throw new CatalogDomainException("Category can't assign parent to itself");
		}
		CategoryDataMapper.INSTANCE.updateModel(category, command);
	}

	private void validateCategory(TenantId tenantId, CategoryId id, CategoryId parentId, String name) {
		checkParentId(tenantId, parentId);
		//		checkExistName(tenantId, id, name);
	}

	private void checkParentId(TenantId tenantId, CategoryId parentId) {
		if (Objects.nonNull(parentId)) {
			Category parent = categoryCheck(tenantId, parentId);
			if (Objects.nonNull(parent.getParentId())) {
				throw new CatalogDomainException("Category parent is the last level");
			}
		}
	}

	private void checkExistName(TenantId tenantId, CategoryId id, String name) {
		CategoryFilter filter = CategoryFilter.builder()
				.name(name)
				.build();
		int page = -1;
		Integer totalPage = null;
		do {
			page++;
			PaginationCategoryQuery query = new PaginationCategoryQuery(filter, 20, page, null, null);
			Paging<CategoryStubDto> paging = categoryRepository.findPagingAll(tenantId, query);
			if (Objects.isNull(totalPage)) {
				totalPage = paging.getTotalPages();
			}
			if (paging.getContent()
					.stream()
					.anyMatch(category -> category.name()
							.equals(name.trim()) && (Objects.isNull(id) || !category.id()
									.equals(id)))) {
				log.warn("Category {} already exists", name);
				throw new CatalogDomainException(String.format("Category %s already exists", name));
			}

		} while ((page + 1) < totalPage);
	}

	@Transactional
	public void deleteById(TenantId tenantId, CategoryId id) {
		Category category = categoryCheck(tenantId, id);
		// Check if there are any categories that have related with
		boolean childrenExist = categoryRepository.existsByParentId(tenantId, id);
		if (childrenExist) {
			log.warn("Can't delete category {}", category.getId()
					.getValue());
			throw new CatalogDomainException(String.format(
					"Can't delete category %s because some sub-category have related to", category.getId()
							.getValue()));
		}

		// Check if there are any products that have related with
		boolean productsExist = productRepository.existByQuery(tenantId, ProductFilter.builder()
				.categoryIds(List.of(category.getId()
						.getValue()))
				.build());
		if (productsExist) {
			log.warn("Can't delete category {}", category.getId()
					.getValue());
			throw new CatalogDomainException(String.format(
					"Can't delete category %s because some products have related to", category.getId()
							.getValue()));
		}
		categoryRepository.deleteById(tenantId, category.getId());
		log.info("Category with {} is deleted", category.getId()
				.getValue());
	}

	private Category categoryCheck(TenantId tenantId, CategoryId id) {
		return categoryRepository.findById(tenantId, id)
				.orElseThrow(() -> {
					log.warn("Category not found with id: {}", id);
					return new CategoryNotFoundException("Category not found with id: " + id.getValue());
				});
	}

	private TenantDto tenantCheck(TenantId tenantId) {
		return tenantRepository.findById(tenantId.getValue())
				.orElseThrow(() -> {
					log.warn("Tenant {} not found", tenantId);
					return new CatalogDomainException(String.format("Tenant %s not found", tenantId.getValue()));
				});
	}

}
