/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.domain.product;

import com.styl.pacific.catalog.service.domain.common.exception.CatalogDomainException;
import com.styl.pacific.catalog.service.domain.nutrition.exception.NutritionNotFoundException;
import com.styl.pacific.catalog.service.domain.nutrition.output.repository.NutritionRepository;
import com.styl.pacific.catalog.service.domain.product.dto.ProductNutritionDto;
import com.styl.pacific.catalog.service.domain.product.dto.command.nutrition.UpdateProductNutritionCommand;
import com.styl.pacific.catalog.service.domain.product.entity.ProductNutrition;
import com.styl.pacific.catalog.service.domain.product.exception.ProductNotFoundException;
import com.styl.pacific.catalog.service.domain.product.exception.ProductNutritionValueInvalidException;
import com.styl.pacific.catalog.service.domain.product.mapper.ProductNutritionDataMapper;
import com.styl.pacific.catalog.service.domain.product.port.input.service.ProductNutritionService;
import com.styl.pacific.catalog.service.domain.product.port.ouput.repository.ProductNutritionRepository;
import com.styl.pacific.catalog.service.domain.product.port.ouput.repository.ProductRepository;
import com.styl.pacific.domain.valueobject.NutritionId;
import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.utils.collection.CollectionUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 */
@Service
@Validated
@RequiredArgsConstructor
public class ProductNutritionServiceImpl implements ProductNutritionService {
	private static final Logger log = LoggerFactory.getLogger(ProductNutritionServiceImpl.class);
	private final ProductRepository productRepository;
	private final ProductNutritionRepository productNutritionRepository;
	private final NutritionRepository nutritionRepository;

	@Override
	@Transactional(readOnly = true)
	public List<ProductNutritionDto> findAllByProductId(TenantId tenantId, ProductId productId) {
		productExistCheck(tenantId, productId);
		return productNutritionRepository.findAllByProductId(productId);
	}

	@Override
	@Transactional
	public List<ProductNutritionDto> updateNutrition(TenantId tenantId, ProductId productId,
			List<UpdateProductNutritionCommand> command) {
		productExistCheck(tenantId, productId);
		if (CollectionUtils.hasDuplicates(command, UpdateProductNutritionCommand::getNutritionId)) {
			throw new CatalogDomainException("Duplicate nutrition items.");
		}
		command.forEach(nutritionItem -> nutritionExistsCheck(tenantId, new NutritionId(nutritionItem
				.getNutritionId())));
		List<ProductNutrition> productNutritionList = productNutritionRepository.findAllByProductIdInNutritionList(
				productId, command.stream()
						.map(updateProductNutritionCommand -> new NutritionId(updateProductNutritionCommand
								.getNutritionId()))
						.toList());
		var result = updateProductNutrition(productId, productNutritionList, command);
		return productNutritionRepository.updateByProductId(productId, result);
	}

	private List<ProductNutrition> updateProductNutrition(ProductId productId, List<ProductNutrition> nutrition,
			List<UpdateProductNutritionCommand> updateNutritionCommands) {
		List<ProductNutrition> productNutritionList = new ArrayList<>();
		Map<Long, ProductNutrition> nutritionMap = nutrition.stream()
				.collect(Collectors.toMap(productNutrition -> productNutrition.getNutritionId()
						.getValue(), Function.identity()));
		for (UpdateProductNutritionCommand updateProductNutritionCommand : updateNutritionCommands) {
			if (Objects.isNull(updateProductNutritionCommand.getNutritionId()) || !nutritionMap.containsKey(
					updateProductNutritionCommand.getNutritionId())) {
				if (Objects.isNull(updateProductNutritionCommand.getValue())) {
					log.warn("Nutrition value is required for nutrition id: {}", updateProductNutritionCommand
							.getNutritionId());
					throw new ProductNutritionValueInvalidException(String.format(
							"Nutrition value is required for nutrition id: %s", updateProductNutritionCommand
									.getNutritionId()));
				}
				ProductNutrition productNutrition = ProductNutritionDataMapper.INSTANCE
						.productNutritionCommandToProductNutrition(updateProductNutritionCommand);
				productNutrition.setProductId(productId);
				productNutritionList.add(productNutrition);
			} else {
				ProductNutrition productNutrition = nutritionMap.get(updateProductNutritionCommand.getNutritionId());
				ProductNutritionDataMapper.INSTANCE.productNutritionCommandToProductNutrition(productNutrition,
						updateProductNutritionCommand);
				productNutritionList.add(productNutrition);
			}
		}
		return productNutritionList;
	}

	private void productExistCheck(TenantId tenantId, ProductId productId) {
		var exist = productRepository.existById(tenantId, productId);
		if (!exist) {
			log.warn("Product {} not found", productId);
			throw new ProductNotFoundException(String.format("Product %s not found", productId));
		}
	}

	private void nutritionExistsCheck(TenantId tenantId, NutritionId id) {
		boolean exist = nutritionRepository.existById(tenantId, id);
		if (!exist) {
			log.warn("Nutrition {} not found", id);
			throw new NutritionNotFoundException(String.format("Nutrition %s not found", id.getValue()));
		}
	}
}
