/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.domain.product.dto.query.option;

import jakarta.validation.Valid;
import java.util.Collection;
import java.util.Collections;
import lombok.Builder;

/**
 * <AUTHOR>
 */
@Builder
public record ProductOptionQuery(@Valid ProductOptionFilter filter,
		String sortDirection,
		Collection<String> sortFields) {
	public static class ProductOptionQueryBuilder {
		ProductOptionQueryBuilder() {
			this.sortDirection = "ASC";
			this.sortFields = Collections.emptySet();
		}
	}
}
