/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.domain.product.port.input.service;

import com.styl.pacific.catalog.service.domain.product.dto.ProductNutritionDto;
import com.styl.pacific.catalog.service.domain.product.dto.command.nutrition.UpdateProductNutritionCommand;
import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.domain.valueobject.TenantId;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ProductNutritionService {
	List<ProductNutritionDto> findAllByProductId(@NotNull(message = "tenantId must not be null") TenantId tenantId,
			@NotNull(message = "productId must not be null") ProductId productId);

	List<ProductNutritionDto> updateNutrition(@NotNull(message = "tenantId must not be null") TenantId tenantId,
			@NotNull(message = "productId must not be null") ProductId productId,
			@NotNull List<UpdateProductNutritionCommand> command);
}
