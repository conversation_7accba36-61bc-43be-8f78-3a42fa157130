/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.domain.product.mapper;

import com.styl.pacific.catalog.service.domain.common.mapper.CommonDataMapper;
import com.styl.pacific.catalog.service.domain.product.dto.command.nutrition.UpdateProductNutritionCommand;
import com.styl.pacific.catalog.service.domain.product.entity.ProductNutrition;
import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, uses = { CommonDataMapper.class, MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface ProductNutritionDataMapper {

	ProductNutritionDataMapper INSTANCE = Mappers.getMapper(ProductNutritionDataMapper.class);

	@Mapping(target = "productId", ignore = true)
	@Mapping(target = "nutritionId", source = "command.nutritionId", qualifiedByName = "longToNutritionId")
	ProductNutrition productNutritionCommandToProductNutrition(UpdateProductNutritionCommand command);

	@Mapping(target = "productId", ignore = true)
	@Mapping(target = "nutritionId", ignore = true)
	@Mapping(target = "value", source = "command.value", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
	void productNutritionCommandToProductNutrition(@MappingTarget ProductNutrition productNutrition,
			UpdateProductNutritionCommand command);
}
