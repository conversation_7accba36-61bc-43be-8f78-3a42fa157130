/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.domain.category.output.repository;

import com.styl.pacific.catalog.service.domain.category.dto.CategoryStubDto;
import com.styl.pacific.catalog.service.domain.category.dto.query.CategoryQuery;
import com.styl.pacific.catalog.service.domain.category.dto.query.PaginationCategoryQuery;
import com.styl.pacific.catalog.service.domain.category.dto.query.TreeCategoryQuery;
import com.styl.pacific.catalog.service.domain.category.entity.Category;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.CategoryId;
import com.styl.pacific.domain.valueobject.TenantId;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface CategoryRepository {
	Optional<Category> findById(TenantId tenantId, CategoryId id);

	List<CategoryStubDto> findAllByParentId(TenantId tenantId, CategoryId parentId);

	List<CategoryStubDto> findAll(TenantId tenantId, CategoryQuery query);

	List<Category> findTreeAll(TenantId tenantId, TreeCategoryQuery query);

	Paging<CategoryStubDto> findPagingAll(TenantId tenantId, PaginationCategoryQuery query);

	CategoryStubDto save(Category category);

	CategoryStubDto update(Category category);

	void deleteById(TenantId tenantId, CategoryId id);

	boolean existsById(TenantId tenantId, CategoryId id);

	boolean existsByParentId(TenantId tenantId, CategoryId parentId);

	boolean existsByMigrationId(TenantId tenantId, String migrationId);
}
