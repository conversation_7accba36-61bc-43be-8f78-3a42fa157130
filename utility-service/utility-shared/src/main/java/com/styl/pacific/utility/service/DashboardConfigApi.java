/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service;

import com.styl.pacific.domain.permissions.PacificApiAuthorized;
import com.styl.pacific.domain.permissions.PacificApiPermissionKey;
import com.styl.pacific.domain.permissions.PlatformApiSecurityLevel;
import com.styl.pacific.utility.service.api.dto.DashboardConfigResponse;
import com.styl.pacific.utility.service.api.dto.DashboardTokenResponse;
import com.styl.pacific.utility.service.api.dto.UpdateDashboardRequest;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
public interface DashboardConfigApi {

	@PostMapping(path = "/api/utility/dashboards/configuration")
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.CONFIGURATION_MGMT_DASHBOARD_UPDATE)
	ResponseEntity<DashboardConfigResponse> updateDashboardConfig(@RequestBody @Valid UpdateDashboardRequest request);

	@GetMapping(path = "/api/utility/dashboards/configuration")
	@PacificApiAuthorized
	ResponseEntity<DashboardConfigResponse> getDashboardConfig(boolean tenantOnly);

	@GetMapping(path = "/api/utility/dashboards/{id}/token")
	@PacificApiAuthorized
	ResponseEntity<DashboardTokenResponse> getDashboardToken(@PathVariable long id);

}
