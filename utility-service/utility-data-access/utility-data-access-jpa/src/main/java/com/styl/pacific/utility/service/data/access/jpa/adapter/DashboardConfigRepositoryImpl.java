/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service.data.access.jpa.adapter;

import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.utility.service.data.access.jpa.mapper.DashboardConfigDataAccessMapper;
import com.styl.pacific.utility.service.data.access.jpa.repository.DashboardConfigJpaRepository;
import com.styl.pacific.utility.service.domain.dashboard.entity.DashboardConfig;
import com.styl.pacific.utility.service.domain.dashboard.port.output.DashboardConfigRepository;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */

@AllArgsConstructor
@Component
public class DashboardConfigRepositoryImpl implements DashboardConfigRepository {

	private final DashboardConfigJpaRepository dashboardConfigJpaRepository;

	@Override
	@Transactional(readOnly = true)
	public Optional<DashboardConfig> getDashboardConfig(TenantId tenantId) {
		return dashboardConfigJpaRepository.findById(tenantId.getValue())
				.map(DashboardConfigDataAccessMapper.INSTANCE::toDomain);
	}

	@Override
	public DashboardConfig saveDashboardConfig(DashboardConfig dashboardConfig) {
		return DashboardConfigDataAccessMapper.INSTANCE.toDomain(dashboardConfigJpaRepository.save(
				DashboardConfigDataAccessMapper.INSTANCE.toEntity(dashboardConfig)));
	}
}
