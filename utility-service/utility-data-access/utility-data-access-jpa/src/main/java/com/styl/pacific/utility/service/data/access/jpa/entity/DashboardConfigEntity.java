/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service.data.access.jpa.entity;

import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.List;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Type;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@NoArgsConstructor
@Table(name = "tb_dashboard_config")
public class DashboardConfigEntity {

	@Id
	private Long id;

	private String baseUrl;

	private String secretKey;

	private String dashboardId;

	private String dashboardParams;

	private int dashboardHeight;

	@Type(JsonBinaryType.class)
	@Column(name = "reports", columnDefinition = "jsonb")
	private List<ReportConfig> reports;

	@Data
	public static class ReportConfig {
		private String reportName;

		private String reportId;

		private String reportParams;

		private int dashboardHeight;
	}
}
