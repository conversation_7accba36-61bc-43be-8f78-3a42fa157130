/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service.data.access.jpa.adapter;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.BaseId;
import com.styl.pacific.domain.valueobject.DataSyncJobId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.utility.datasync.core.jobs.DataSyncJobRepository;
import com.styl.pacific.utility.datasync.core.jobs.entities.DataSyncJob;
import com.styl.pacific.utility.datasync.core.jobs.request.DataSyncJobPaginationQuery;
import com.styl.pacific.utility.datasync.core.jobs.request.FilterDataSyncJobQuery;
import com.styl.pacific.utility.service.data.access.jpa.mapper.DataSyncJobEntityMapper;
import com.styl.pacific.utility.service.data.access.jpa.repository.DataSyncJobJpaRepository;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class DataSyncJobRepositoryImpl implements DataSyncJobRepository {

	private final DataSyncJobJpaRepository repository;

	@Override
	public DataSyncJob save(DataSyncJob job) {
		final var jobEntity = Optional.ofNullable(job.getId())
				.map(BaseId::getValue)
				.map(jobId -> {
					final var existing = repository.findById(jobId)
							.orElseGet(() -> DataSyncJobEntityMapper.INSTANCE.toNewJobEntity(job));
					return DataSyncJobEntityMapper.INSTANCE.toUpdateJobEntity(existing, job);
				})
				.orElseGet(() -> DataSyncJobEntityMapper.INSTANCE.toNewJobEntity(job));

		return DataSyncJobEntityMapper.INSTANCE.toModel(repository.save(jobEntity));
	}

	@Override
	public Optional<DataSyncJob> getOneByTenantIdAndJobId(TenantId tenantId, DataSyncJobId jobId) {
		if (jobId == null || jobId.getValue() == null) {
			return Optional.empty();
		}
		return repository.findOneByTenantIdAndId(tenantId.getValue(), jobId.getValue())
				.map(DataSyncJobEntityMapper.INSTANCE::toModel);

	}

	public boolean existsJobId(DataSyncJobId jobId) {
		return repository.existsById(jobId.getValue());
	}

	@Override
	public Paging<DataSyncJob> findPagingDataSyncJob(DataSyncJobPaginationQuery query) {

		final var specs = DataSyncJobEntityMapper.INSTANCE.toDataSyncJpaSpecification(query.getFilter());
		final var pageRequest = PageRequest.of(query.getPage(), query.getSize(), Sort.by(Sort.Direction.fromString(query
				.getSortDirection()), query.getSortFields()
						.toArray(new String[0])));

		final var pageResult = repository.findAll(specs, pageRequest);

		return new Paging<>(pageResult.getContent()
				.stream()
				.map(DataSyncJobEntityMapper.INSTANCE::toModel)
				.toList(), pageResult.getTotalElements(), pageResult.getTotalPages(), pageResult.getPageable()
						.getPageNumber(), pageResult.getSort()
								.stream()
								.map(Sort.Order::toString)
								.toList());
	}

	@Override
	public boolean exists(FilterDataSyncJobQuery query) {
		return repository.exists(DataSyncJobEntityMapper.INSTANCE.toDataSyncJpaSpecification(query));

	}

}
