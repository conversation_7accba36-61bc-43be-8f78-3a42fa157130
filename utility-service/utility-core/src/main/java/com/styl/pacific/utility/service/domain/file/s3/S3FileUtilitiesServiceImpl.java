/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service.domain.file.s3;

import com.styl.pacific.aws.s3.config.S3ConfigProperties;
import com.styl.pacific.aws.s3.presigner.Presigner;
import com.styl.pacific.aws.s3.uti.PresignerUtils;
import com.styl.pacific.aws.s3.uti.S3FileUtils;
import com.styl.pacific.aws.s3.uti.S3ObjectKey;
import com.styl.pacific.utility.service.domain.FileIdGenerator;
import com.styl.pacific.utility.service.domain.file.FileType;
import com.styl.pacific.utility.service.domain.file.FileUtilitiesService;
import com.styl.pacific.utility.service.domain.file.PresignedUrl;
import com.styl.pacific.utility.service.domain.file.UploadFilePath;
import java.io.InputStream;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.CopyObjectRequest;
import software.amazon.awssdk.services.s3.model.HeadObjectRequest;
import software.amazon.awssdk.services.s3.model.HeadObjectResponse;
import software.amazon.awssdk.services.s3.model.NoSuchKeyException;

@AllArgsConstructor
@Component
public class S3FileUtilitiesServiceImpl implements FileUtilitiesService {

	private static final Logger logger = LoggerFactory.getLogger(S3FileUtilitiesServiceImpl.class);

	private final S3ConfigProperties s3ConfigProperties;

	private final FileIdGenerator fileIdGenerator;

	private final S3Client s3Client;

	@Override
	public PresignedUrl getPresignedUrlUploadFile(FileType fileType, String fileName, Map<String, String> metadata) {
		final var result = getUploadFilePath(fileType, fileName);

		Presigner presigner = PresignerUtils.createS3Presigner(s3ConfigProperties.getEndpoint(), s3ConfigProperties
				.getRegion(), s3ConfigProperties.getAccessKey(), s3ConfigProperties.getSecretKey());
		String presignedUrl = PresignerUtils.createPresignedUrlForUpload(presigner, s3ConfigProperties.getUpload()
				.getBucketName(), StreamSupport.stream(result.path()
						.spliterator(), true)
						.map(Path::toString)
						.collect(Collectors.joining("/")), s3ConfigProperties.getUpload()
								.getSignatureDuration(), metadata);

		return new PresignedUrl(presignedUrl, result.filePath());

	}

	@Override
	public UploadFilePath getUploadFilePath(FileType fileType, String fileName) {
		LocalDate today = LocalDate.now();
		String fileId = fileIdGenerator.nextId();
		String uniqueFileName = String.format("%s-%s", fileId, fileName)
				.replaceAll("[^.\\-a-zA-Z0-9]{4,255}", "_");
		Path path = Paths.get(fileType.getFolderName(), String.valueOf(today.getYear()), String.valueOf(today
				.getMonthValue()), String.valueOf(today.getDayOfMonth()), uniqueFileName);
		String filePath = String.format("%s:%s", s3ConfigProperties.getUpload()
				.getBucketName(), StreamSupport.stream(path.spliterator(), true)
						.map(Path::toString)
						.collect(Collectors.joining("/")));
		return new UploadFilePath(path, filePath);
	}

	@Override
	public HeadObjectResponse getHeadObject(String objectKey) {
		final var s3ObjectKey = S3FileUtils.getS3ObjectKey(objectKey);

		return s3Client.headObject(HeadObjectRequest.builder()
				.bucket(s3ObjectKey.getBucket())
				.key(s3ObjectKey.getKey())
				.build());

	}

	@Override
	public String copyFile(String fromFilePath, String toFilePath) {
		if (logger.isDebugEnabled()) {
			logger.debug("Copying file from %s to %s".formatted(fromFilePath, toFilePath));
		}
		final var fromFile = S3FileUtils.getS3ObjectKey(fromFilePath);
		if (fromFile == null) {
			return null;
		}
		final var toFile = S3FileUtils.getS3ObjectKey(toFilePath);
		if (toFile == null) {
			return null;
		}

		s3Client.copyObject(CopyObjectRequest.builder()
				.sourceBucket(fromFile.getBucket())
				.sourceKey(fromFile.getKey())
				.destinationBucket(toFile.getBucket())
				.destinationKey(toFile.getKey())
				.build());

		return toFilePath;

	}

	@Override
	public String getPresignedUrlDownloadFile(String filePath) {
		Presigner presigner = PresignerUtils.createS3Presigner(s3ConfigProperties.getEndpoint(), s3ConfigProperties
				.getRegion(), s3ConfigProperties.getAccessKey(), s3ConfigProperties.getSecretKey());
		return PresignerUtils.createPresignedUrlForDownload(presigner, filePath, s3ConfigProperties.getUpload()
				.getSignatureDuration());
	}

	@Override
	public boolean exists(String filePath) {
		S3ObjectKey s3ObjectKey = S3FileUtils.getS3ObjectKey(filePath);
		if (s3ObjectKey == null) {
			return false;
		}

		logger.debug("Checking if file exists in S3: [{}] in bucket {}", s3ObjectKey.getKey(), s3ObjectKey.getBucket());
		try {

			return s3Client.headObject(builder -> builder.bucket(s3ObjectKey.getBucket())
					.key(s3ObjectKey.getKey()))
					.sdkHttpResponse()
					.isSuccessful();
		} catch (NoSuchKeyException e) {
			logger.info("File not found in S3: [{}] in bucket {}", s3ObjectKey.getKey(), s3ObjectKey.getBucket());
			return false;
		}
	}

	@Override
	public InputStream getObject(String filePath) {
		S3ObjectKey s3ObjectKey = S3FileUtils.getS3ObjectKey(filePath);
		if (s3ObjectKey == null) {
			return null;
		}
		return s3Client.getObject(builder -> builder.bucket(s3ObjectKey.getBucket())
				.key(s3ObjectKey.getKey()));
	}

}
