/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service.preset.menu.helper;

import com.styl.pacific.catalog.service.shared.http.product.request.PaginationProductQueryRequest;
import com.styl.pacific.catalog.service.shared.http.product.request.ProductFilterRequest;
import com.styl.pacific.catalog.service.shared.http.product.response.ProductResponse;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.order.service.shared.http.common.enums.DateOfWeek;
import com.styl.pacific.order.service.shared.http.menu.request.item.MenuItemFilterRequest;
import com.styl.pacific.order.service.shared.http.menu.request.item.PaginationMenuItemQueryRequest;
import com.styl.pacific.order.service.shared.http.menu.response.MenuItemResponse;
import com.styl.pacific.order.service.shared.http.menu.response.MenuStubResponse;
import com.styl.pacific.store.shared.http.responses.store.StoreResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantResponse;
import com.styl.pacific.utility.data.access.client.MenuClient;
import com.styl.pacific.utility.data.access.client.MenuItemClient;
import com.styl.pacific.utility.data.access.client.ProductClient;
import com.styl.pacific.utility.data.access.client.StoreClient;
import com.styl.pacific.utility.data.access.client.TenantClient;
import com.styl.pacific.utility.service.ThreadLocalMap;
import com.styl.pacific.utility.service.domain.exception.UtilityImportException;
import com.styl.pacific.utility.service.domain.preset.PresetRecord;
import com.styl.pacific.utility.service.preset.menu.entity.MenuItemDataDto;
import com.styl.pacific.utility.service.preset.menu.util.MenuPresetUtils;
import java.time.Instant;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class MenuPresetDataHelper {
	private final TenantClient tenantClient;
	private final MenuClient menuClient;
	private final MenuItemClient menuItemClient;
	private final ProductClient productClient;
	private final StoreClient storeClient;

	public StoreResponse fetchStore(String storeId) {
		ResponseEntity<StoreResponse> response = storeClient.getStore(Long.parseLong(storeId));
		if (response.hasBody()) {
			return response.getBody();
		} else {
			throw new UtilityImportException("Store not found");
		}
	}

	public List<MenuItemResponse> fetchMenuItem(String menuId) {
		List<MenuItemResponse> menuItemList = new ArrayList<>();
		int page = -1;
		Integer totalPage = null;
		do {
			page++;
			PaginationMenuItemQueryRequest request = new PaginationMenuItemQueryRequest(MenuItemFilterRequest.builder()
					.build(), 20, page, null, null);
			Paging<MenuItemResponse> response = menuItemClient.findAllPaging(menuId, request);
			if (Objects.isNull(totalPage)) {
				totalPage = Objects.requireNonNull(response)
						.getTotalPages();
			}
			menuItemList.addAll(Objects.requireNonNull(response)
					.getContent());
		} while ((page + 1) < totalPage);
		return menuItemList;
	}

	public TenantResponse fetchTenant(long tenantId) {
		ResponseEntity<TenantResponse> tenant = tenantClient.getTenant(tenantId);
		if (tenant.hasBody()) {
			return tenant.getBody();
		} else {
			throw new UtilityImportException("Tenant not found");
		}
	}

	public List<ProductResponse> fetchProduct(String storeId) {
		List<ProductResponse> products = new ArrayList<>();
		int page = -1;
		Integer totalPage = null;
		do {
			page++;
			PaginationProductQueryRequest request = new PaginationProductQueryRequest(ProductFilterRequest.builder()
					.storeId(storeId)
					.build(), 20, page, null, null);
			Paging<ProductResponse> response = productClient.findAllPaging(request);
			if (Objects.isNull(totalPage)) {
				totalPage = Objects.requireNonNull(response)
						.getTotalPages();
			}
			products.addAll(Objects.requireNonNull(response)
					.getContent());
		} while ((page + 1) < totalPage);
		return products;
	}

	public MenuStubResponse fetchMenu(String menuId) {
		MenuStubResponse menuStubResponse = menuClient.findById(menuId);
		if (Objects.isNull(menuStubResponse)) {
			throw new UtilityImportException("Menu not found");
		}
		return menuStubResponse;
	}

	public MenuItemDataDto parseData(ThreadLocalMap<String, Integer> headersContext, TenantResponse tenantDto,
			PresetRecord presetRecord) {
		String product = Optional.ofNullable(getValue(headersContext, presetRecord, "product"))
				.filter(StringUtils::isNotBlank)
				.map(String::trim)
				.orElse(null);
		String sku = Optional.ofNullable(getValue(headersContext, presetRecord, "sku"))
				.filter(StringUtils::isNotBlank)
				.map(String::trim)
				.orElse(null);
		Instant startDate = Optional.ofNullable(getValue(headersContext, presetRecord, "start_date"))
				.filter(StringUtils::isNotBlank)
				.map(String::trim)
				.map(s -> MenuPresetUtils.processStartDate(s, tenantDto))
				.orElse(null);
		Instant endDate = Optional.ofNullable(getValue(headersContext, presetRecord, "end_date"))
				.filter(StringUtils::isNotBlank)
				.map(String::trim)
				.map(s -> MenuPresetUtils.processEndDate(s, tenantDto))
				.orElse(null);
		LocalTime startTime = Optional.ofNullable(getValue(headersContext, presetRecord, "start_time"))
				.filter(StringUtils::isNotBlank)
				.map(String::trim)
				.map(MenuPresetUtils::processStartTime)
				.orElse(null);
		LocalTime endTime = Optional.ofNullable(getValue(headersContext, presetRecord, "end_time"))
				.filter(StringUtils::isNotBlank)
				.map(String::trim)
				.map(MenuPresetUtils::processEndTime)
				.orElse(null);
		List<DateOfWeek> availableOn = new ArrayList<>();
		String draftAvailableOn = Optional.ofNullable(getValue(headersContext, presetRecord, "available_on"))
				.filter(StringUtils::isNotBlank)
				.map(String::trim)
				.orElse(null);
		if (StringUtils.isNotBlank(draftAvailableOn)) {
			String[] draftAvailableOns = draftAvailableOn.split(";");
			for (String draftAvailable : draftAvailableOns) {
				try {
					DateOfWeek dateOfWeek = DateOfWeek.valueOf(draftAvailable.toUpperCase());
					availableOn.add(dateOfWeek);
				} catch (Exception e) {
					throw new UtilityImportException("Invalid day of week");
				}
			}
		}
		if (StringUtils.isBlank(product)) {
			throw new UtilityImportException("Product is required");
		}
		if (availableOn.isEmpty()) {
			throw new UtilityImportException("Available on must be at least one day of week");
		}

		return MenuItemDataDto.builder()
				.product(product)
				.sku(sku)
				.startDate(startDate)
				.endDate(endDate)
				.startTime(startTime)
				.endTime(endTime)
				.availableOn(availableOn)
				.build();
	}

	protected String getValue(ThreadLocalMap<String, Integer> headersContext, PresetRecord presetRecord,
			String headerName) {
		return Optional.ofNullable(presetRecord)
				.map(PresetRecord::getData)
				.map(values -> values.get(headersContext.getOrDefault(headerName, -1)))
				.orElse(null);
	}
}
