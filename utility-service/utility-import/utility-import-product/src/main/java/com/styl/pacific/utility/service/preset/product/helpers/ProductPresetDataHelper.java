/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service.preset.product.helpers;

import com.styl.pacific.catalog.service.shared.http.allergen.request.AllergenFilterRequest;
import com.styl.pacific.catalog.service.shared.http.allergen.request.PaginationAllergenQueryRequest;
import com.styl.pacific.catalog.service.shared.http.allergen.response.AllergenResponse;
import com.styl.pacific.catalog.service.shared.http.category.request.CategoryFilterRequest;
import com.styl.pacific.catalog.service.shared.http.category.request.PaginationCategoryQueryRequest;
import com.styl.pacific.catalog.service.shared.http.category.response.CategoryStubResponse;
import com.styl.pacific.catalog.service.shared.http.healthierchoice.request.HealthierChoiceFilterRequest;
import com.styl.pacific.catalog.service.shared.http.healthierchoice.request.PaginationHealthierChoiceQueryRequest;
import com.styl.pacific.catalog.service.shared.http.healthierchoice.response.HealthierChoiceResponse;
import com.styl.pacific.catalog.service.shared.http.nutrition.request.NutritionFilterRequest;
import com.styl.pacific.catalog.service.shared.http.nutrition.request.PaginationNutritionQueryRequest;
import com.styl.pacific.catalog.service.shared.http.nutrition.response.NutritionResponse;
import com.styl.pacific.catalog.service.shared.http.product.request.PaginationProductQueryRequest;
import com.styl.pacific.catalog.service.shared.http.product.request.ProductFilterRequest;
import com.styl.pacific.catalog.service.shared.http.product.response.ProductResponse;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.store.shared.http.requests.store.FindStoresRequest;
import com.styl.pacific.store.shared.http.requests.store.StoresFilterRequest;
import com.styl.pacific.store.shared.http.responses.store.ListStoresResponse;
import com.styl.pacific.store.shared.http.responses.store.StoreResponse;
import com.styl.pacific.utility.data.access.client.AllergenClient;
import com.styl.pacific.utility.data.access.client.CategoryClient;
import com.styl.pacific.utility.data.access.client.HealthierChoiceClient;
import com.styl.pacific.utility.data.access.client.NutritionClient;
import com.styl.pacific.utility.data.access.client.ProductClient;
import com.styl.pacific.utility.data.access.client.ProductInternalClient;
import com.styl.pacific.utility.data.access.client.StoreClient;
import com.styl.pacific.utility.service.ThreadLocalMap;
import com.styl.pacific.utility.service.domain.exception.UtilityImportException;
import com.styl.pacific.utility.service.domain.preset.PresetRecord;
import com.styl.pacific.utility.service.preset.product.dto.ProductDataDTO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class ProductPresetDataHelper {
	private final AllergenClient allergenClient;
	private final HealthierChoiceClient healthierChoiceClient;
	private final CategoryClient categoryClient;
	private final NutritionClient nutritionClient;
	private final StoreClient storeClient;
	private final ProductClient productClient;
	private final ProductInternalClient productInternalClient;

	public List<ProductResponse> fetchProducts() {
		List<ProductResponse> productList = new ArrayList<>();
		int page = -1;
		Integer totalPage = null;
		do {
			page++;
			PaginationProductQueryRequest request = new PaginationProductQueryRequest(ProductFilterRequest.builder()
					.build(), 20, page, null, null);
			Paging<ProductResponse> response = productClient.findAllPaging(request);
			if (Objects.isNull(totalPage)) {
				totalPage = Objects.requireNonNull(response)
						.getTotalPages();
			}
			productList.addAll(Objects.requireNonNull(response)
					.getContent());
		} while ((page + 1) < totalPage);
		return productList;
	}

	public List<StoreResponse> fetchStores() {
		List<StoreResponse> storeList = new ArrayList<>();
		int page = -1;
		Integer totalPage = null;
		do {
			page++;
			FindStoresRequest request = new FindStoresRequest(StoresFilterRequest.builder()
					.build(), 100, page, "ASC", List.of("id"));
			ResponseEntity<ListStoresResponse> response = storeClient.findStores(request);
			if (Objects.isNull(totalPage)) {
				totalPage = Objects.requireNonNull(response.getBody())
						.getTotalPages();
			}
			storeList.addAll(Objects.requireNonNull(response.getBody())
					.getContent());
		} while ((page + 1) < totalPage);
		return storeList;
	}

	public List<CategoryStubResponse> fetchCategories() {
		List<CategoryStubResponse> categoryResponseList = new ArrayList<>();
		int page = -1;
		Integer totalPage = null;
		do {
			page++;
			PaginationCategoryQueryRequest request = new PaginationCategoryQueryRequest(CategoryFilterRequest.builder()
					.build(), 100, page, "ASC", null);
			Paging<CategoryStubResponse> response = categoryClient.findAllPaging(request);
			if (Objects.isNull(totalPage)) {
				totalPage = Objects.requireNonNull(response)
						.getTotalPages();
			}
			categoryResponseList.addAll(Objects.requireNonNull(response)
					.getContent());
		} while ((page + 1) < totalPage);
		return categoryResponseList;
	}

	public List<NutritionResponse> fetchNutrition() {
		List<NutritionResponse> nutritionResponses = new ArrayList<>();
		int page = -1;
		Integer totalPage = null;
		do {
			page++;
			PaginationNutritionQueryRequest request = new PaginationNutritionQueryRequest(NutritionFilterRequest
					.builder()
					.build(), 100, page, "ASC", null);
			Paging<NutritionResponse> response = nutritionClient.findAllPaging(request);
			if (Objects.isNull(totalPage)) {
				totalPage = Objects.requireNonNull(response)
						.getTotalPages();
			}
			nutritionResponses.addAll(Objects.requireNonNull(response)
					.getContent());
		} while ((page + 1) < totalPage);
		return nutritionResponses;
	}

	public List<HealthierChoiceResponse> fetchHealthierChoices() {
		List<HealthierChoiceResponse> nutritionResponses = new ArrayList<>();
		int page = -1;
		Integer totalPage = null;
		do {
			page++;
			Paging<HealthierChoiceResponse> response = healthierChoiceClient.findAllPaging(
					new PaginationHealthierChoiceQueryRequest(HealthierChoiceFilterRequest.builder()
							.build(), 100, page, "ASC", null));
			if (Objects.isNull(totalPage)) {
				totalPage = Objects.requireNonNull(response)
						.getTotalPages();
			}
			nutritionResponses.addAll(Objects.requireNonNull(response)
					.getContent());
		} while ((page + 1) < totalPage);
		return nutritionResponses;
	}

	public List<AllergenResponse> fetchAllergens() {
		List<AllergenResponse> allergenResponses = new ArrayList<>();
		int page = -1;
		Integer totalPage = null;
		do {
			page++;
			Paging<AllergenResponse> response = allergenClient.findAllPaging(new PaginationAllergenQueryRequest(
					AllergenFilterRequest.builder()
							.build(), 100, page, "ASC", null));
			if (Objects.isNull(totalPage)) {
				totalPage = Objects.requireNonNull(response)
						.getTotalPages();
			}
			allergenResponses.addAll(Objects.requireNonNull(response)
					.getContent());
		} while ((page + 1) < totalPage);
		return allergenResponses;
	}

	public ProductDataDTO parseData(ThreadLocalMap<String, Integer> headersContext, PresetRecord presetRecord) {
		String name = Optional.ofNullable(getValue(headersContext, presetRecord, "name"))
				.filter(StringUtils::isNotBlank)
				.map(String::trim)
				.orElse(null);
		String sku = Optional.ofNullable(getValue(headersContext, presetRecord, "sku"))
				.filter(StringUtils::isNotBlank)
				.map(String::trim)
				.orElse(null);
		String category = Optional.ofNullable(getValue(headersContext, presetRecord, "category"))
				.filter(StringUtils::isNotBlank)
				.map(String::trim)
				.orElse(null);
		String store = Optional.ofNullable(getValue(headersContext, presetRecord, "store"))
				.filter(StringUtils::isNotBlank)
				.map(String::trim)
				.orElse(null);
		String healthierChoice = Optional.ofNullable(getValue(headersContext, presetRecord, "healthier_choice"))
				.filter(StringUtils::isNotBlank)
				.map(String::trim)
				.orElse(null);
		String briefInformation = Optional.ofNullable(getValue(headersContext, presetRecord, "brief_information"))
				.filter(StringUtils::isNotBlank)
				.map(String::trim)
				.orElse(null);
		String description = Optional.ofNullable(getValue(headersContext, presetRecord, "description"))
				.filter(StringUtils::isNotBlank)
				.map(String::trim)
				.orElse(null);
		String ingredients = Optional.ofNullable(getValue(headersContext, presetRecord, "ingredients"))
				.filter(StringUtils::isNotBlank)
				.map(String::trim)
				.orElse(null);
		String barcode = Optional.ofNullable(getValue(headersContext, presetRecord, "barcode"))
				.filter(StringUtils::isNotBlank)
				.map(String::trim)
				.orElse(null);
		BigDecimal unitPrice = Optional.ofNullable(getValue(headersContext, presetRecord, "unit_price"))
				.map(String::trim)
				.filter(NumberUtils::isCreatable)
				.map(NumberUtils::createBigDecimal)
				.map(bigDecimal -> bigDecimal.setScale(Money.ScaleConstants.DEFAULT_MONEY_TOLERANCE,
						Money.ScaleConstants.ROUNDING_MODE))
				.orElse(null);
		BigDecimal listingPrice = Optional.ofNullable(getValue(headersContext, presetRecord, "listing_price"))
				.map(String::trim)
				.filter(NumberUtils::isCreatable)
				.map(NumberUtils::createBigDecimal)
				.map(bigDecimal -> bigDecimal.setScale(Money.ScaleConstants.DEFAULT_MONEY_TOLERANCE,
						Money.ScaleConstants.ROUNDING_MODE))
				.orElse(null);
		Integer preparationTime = Optional.ofNullable(getValue(headersContext, presetRecord, "preparation_time"))
				.map(String::trim)
				.filter(NumberUtils::isCreatable)
				.map(NumberUtils::createInteger)
				.orElse(null);
		String nutrition = Optional.ofNullable(getValue(headersContext, presetRecord, "nutrition"))
				.filter(StringUtils::isNotBlank)
				.map(String::trim)
				.orElse(null);
		String allergens = Optional.ofNullable(getValue(headersContext, presetRecord, "allergens"))
				.filter(StringUtils::isNotBlank)
				.map(String::trim)
				.orElse(null);

		if (StringUtils.isBlank(name)) {
			throw new UtilityImportException("Name is required");
		}
		if (Objects.isNull(preparationTime)) {
			throw new UtilityImportException("Preparation time is required");
		}
		if (Objects.isNull(category)) {
			throw new UtilityImportException("Category is required");
		}
		if (Objects.isNull(store)) {
			throw new UtilityImportException("Store is required");
		}
		if (Objects.isNull(unitPrice)) {
			throw new UtilityImportException("Unit price is required");
		}
		return ProductDataDTO.builder()
				.category(category)
				.store(store)
				.healthierChoice(healthierChoice)
				.name(name)
				.sku(sku)
				.briefInformation(briefInformation)
				.description(description)
				.ingredients(ingredients)
				.barcode(barcode)
				.unitPrice(unitPrice)
				.listingPrice(listingPrice)
				.preparationTime(preparationTime)
				.nutrition(nutrition)
				.allergens(allergens)
				.build();
	}

	protected String getValue(ThreadLocalMap<String, Integer> headersContext, PresetRecord presetRecord,
			String headerName) {
		return Optional.ofNullable(presetRecord)
				.map(PresetRecord::getData)
				.map(values -> values.get(headersContext.getOrDefault(headerName, -1)))
				.orElse(null);
	}
}
