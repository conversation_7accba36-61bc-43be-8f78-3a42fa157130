/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.datasync.core.processors.chains;

import com.styl.pacific.catalog.service.shared.http.allergen.response.AllergenResponse;
import com.styl.pacific.user.shared.http.groups.response.UserGroupResponse;
import com.styl.pacific.utility.datasync.core.jobs.entities.DataSyncJob;
import java.util.List;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.With;

@Getter
@RequiredArgsConstructor
@Builder
@With
public class DataSyncChainProcessorContext {
	private final DataSyncJob job;
	private final List<UserGroupResponse> groupData;
	private final List<AllergenResponse> allergenData;
}
