/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.datasync.core.commons.models;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.hibernate.validator.constraints.Length;

@Builder
@RequiredArgsConstructor
@Getter
public class CardDataModel {

	@Length(max = 36, message = "Customer Number maximum is 36 characters")
	private final String customerNo;

	@Email(message = "Email is not well format")
	@Length(max = 50, message = "Email maximum is 50 characters")
	private final String email;

	@NotEmpty(message = "CardId is required")
	@Length(max = 50, message = "Customer Card maximum is 50 characters")
	private final String cardId;

}
