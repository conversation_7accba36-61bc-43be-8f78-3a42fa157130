/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.datasync.core.loaders.customers;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.utility.datasync.core.commons.enums.CustomerDataModelSchema;
import com.styl.pacific.utility.datasync.core.commons.models.CsvDataSchema;
import com.styl.pacific.utility.datasync.core.exceptions.DataSyncDomainException;
import com.styl.pacific.utility.datasync.core.jobs.DataSyncJobCommandService;
import com.styl.pacific.utility.datasync.core.jobs.entities.DataSyncJob;
import com.styl.pacific.utility.datasync.core.jobs.entities.DataSyncRecordTracking;
import com.styl.pacific.utility.datasync.core.loaders.BaseDataSyncCsvLoader;
import com.styl.pacific.utility.datasync.core.loaders.DataSyncChainLoader;
import com.styl.pacific.utility.datasync.core.loaders.chains.DataSyncChainLoaderData;
import com.styl.pacific.utility.datasync.core.records.DataSyncRecordCommandService;
import com.styl.pacific.utility.service.api.dto.DataSyncJobStatus;
import com.styl.pacific.utility.service.api.dto.DataSyncRecordType;
import com.styl.pacific.utility.service.domain.file.FileUtilitiesService;
import com.styl.pacific.utils.string.PathUtils;
import jakarta.validation.Validator;
import java.time.Instant;
import java.util.Arrays;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;

public class CustomerCsvDataSyncChainLoader extends BaseDataSyncCsvLoader implements DataSyncChainLoader {

	public CustomerCsvDataSyncChainLoader(DataSyncJobCommandService jobCommandService,
			DataSyncRecordCommandService recordCommandService, FileUtilitiesService fileUtilitiesService,
			ObjectMapper mapper, Validator validator) {
		super(DataSyncRecordType.CUSTOMER, jobCommandService, recordCommandService, fileUtilitiesService,
				new CustomerCsvRowDataValidator(mapper, validator, fileUtilitiesService));
	}

	@Override
	public CsvDataSchema getDefaultSchema() {
		return CsvDataSchema.builder()
				.headerRowIndex(1)
				.columDefinitions(Arrays.stream(CustomerDataModelSchema.values())
						.map(it -> CsvDataSchema.ColumDefinition.builder()
								.key(it.getHeaderKey())
								.build())
						.toList())
				.build();
	}

	@Override
	protected DataSyncJob updateCsvSchema(DataSyncJob job, CsvDataSchema csvSchema) {
		return job.withCustomerCsvSchema(csvSchema);
	}

	@Override
	public DataSyncChainLoaderData apply(DataSyncChainLoaderData data) {
		final var job = data.getJob();
		final var manifest = job.getManifestContent();

		if (StringUtils.isBlank(manifest.getCustomerCsvPath())) {
			return data;
		}

		if (!PathUtils.isValidAbsolutePath(manifest.getCustomerCsvPath())) {
			throw new DataSyncDomainException(
					"Customer CSV Path must be a well absolute path. Example: /data/file.csv");
		}

		final var customerObjectKey = "%s%s".formatted(job.getArchivedFileDirectory(), manifest.getCustomerCsvPath());
		if (!fileUtilitiesService.exists(customerObjectKey)) {
			throw new DataSyncDomainException("Customer CSV file was not found");
		}

		deleteRecordsIfExisted(job);

		var updatedJob = jobCommandService.updateDataSyncJob(job.withRecordTracking(Optional.ofNullable(job
				.getRecordTracking())
				.map(it -> it.withCustomerCsvLoadingStartedAt(Instant.now()))
				.orElseGet(() -> DataSyncRecordTracking.builder()
						.customerCsvLoadingStartedAt(Instant.now())
						.build())));

		final var result = processCsv(updatedJob, fileUtilitiesService.getObject(customerObjectKey));
		final var hasRecordError = result.getTotalErrorRecords() != 0;

		updatedJob = result.getJob()
				.withStatus(hasRecordError
						? DataSyncJobStatus.ERROR
						: result.getJob()
								.getStatus())
				.withErrorMessage(hasRecordError
						? result.getJob()
								.appendErrorMessage("%s: %s Customer Records has been error format".formatted(Instant
										.now(), result.getTotalErrorRecords()))
						: result.getJob()
								.getErrorMessage());

		updatedJob = jobCommandService.updateDataSyncJob(updatedJob.withRecordTracking(Optional.ofNullable(updatedJob
				.getRecordTracking())
				.map(it -> it.withCustomerCsvLoadingCompletedAt(Instant.now()))
				.orElseGet(() -> DataSyncRecordTracking.builder()
						.customerCsvLoadingCompletedAt(Instant.now())
						.build())));

		return data.withJob(updatedJob);
	}
}
