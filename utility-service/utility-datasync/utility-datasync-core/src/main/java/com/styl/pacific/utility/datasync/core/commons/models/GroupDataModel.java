/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.datasync.core.commons.models;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.hibernate.validator.constraints.Length;

@Builder
@RequiredArgsConstructor
@Getter
public class GroupDataModel {
	@NotEmpty(message = "Group path is required")
	@Pattern(regexp = "^[\\w\\s]{1,100}(?:\\s{0,10}>\\s{0,10}[\\w\\s]{1,100}){0,4}$", message = "Group path should be well format as following: Group 1 > Group 2 > Group 3")
	private final String groupPath;

	@Length(max = 250, message = "Group Description should be limited maximum 250 characters")
	private final String groupDescription;
}
