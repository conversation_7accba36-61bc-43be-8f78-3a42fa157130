/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.datasync.core.jobs.request;

import com.styl.pacific.domain.valueobject.DataSyncJobId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.utility.datasync.core.jobs.entities.DataSyncManifestContent;
import com.styl.pacific.utility.service.api.dto.DataSyncJobStatus;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@Builder
@RequiredArgsConstructor
public class UpdateDataSyncJobCommand {
	private final DataSyncJobId id;

	private final TenantId tenantId;

	private final Boolean isValidChecksum;

	private final DataSyncJobStatus status;

	private final DataSyncManifestContent manifestContent;

	private final String errorMessage;

}