/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service.publisher;

import com.styl.pacific.kafka.notification.avro.model.NotificationAvroChannel;
import com.styl.pacific.kafka.notification.avro.model.NotificationCreatedAvroEvent;
import com.styl.pacific.notification.publisher.ApplicationNotificationEventPublisher;
import com.styl.pacific.notification.service.constant.Action;
import com.styl.pacific.utility.service.api.dto.ImportStatus;
import com.styl.pacific.utility.service.domain.output.publisher.UtilityNotificationQueuePublisher;
import com.styl.pacific.utility.service.domain.preset.Preset;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class UtilityNotificationPublisher implements UtilityNotificationQueuePublisher {

	private final ApplicationNotificationEventPublisher publisher;

	@Override
	public void publishImportEvent(Preset preset, String userId) {
		NotificationCreatedAvroEvent notificationCreatedAvroEvent = NotificationCreatedAvroEvent.newBuilder()
				.setId(UUID.randomUUID())
				.setUserId(Long.valueOf(userId))
				.setTenantId(preset.getTenantId())
				.setAction(preset.getStatus()
						.equals(ImportStatus.COMPLETED) ? Action.IMPORT_SUCCESS : Action.IMPORT_FAIL)
				.setSource("utility-service")
				.setChannels(List.of(NotificationAvroChannel.IN_APP))
				.setData(Map.of("type", preset.getPresetType()
						.name(), "successRecords", String.valueOf(preset.getSuccessRecords()), "fileName", preset
								.getFileName(), "failedRecords", String.valueOf(preset.getFailedRecords())))
				.setCreatedAt(Instant.now()
						.toEpochMilli())
				.build();
		publisher.publish(notificationCreatedAvroEvent);
	}
}