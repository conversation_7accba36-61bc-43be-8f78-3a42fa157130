/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service.rest.mapper;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.utility.datasync.core.records.entities.DataSyncRecord;
import com.styl.pacific.utility.datasync.core.records.request.DataSyncRecordPaginationQuery;
import com.styl.pacific.utility.service.api.dto.DataSyncRecordResponse;
import com.styl.pacific.utility.service.api.dto.QueryDataSyncRecordPaginationRequest;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class, DataSyncRecordRequestMapper.class, DataSyncRecordResponseMapper.class })
public interface DataSyncRecordPaginationMapper {
	DataSyncRecordPaginationMapper INSTANCE = Mappers.getMapper(DataSyncRecordPaginationMapper.class);

	DataSyncRecordPaginationQuery toPagingQuery(QueryDataSyncRecordPaginationRequest source);

	Paging<DataSyncRecordResponse> toPagingResponse(Paging<DataSyncRecord> pageResult, @Context ObjectMapper mapper);
}
