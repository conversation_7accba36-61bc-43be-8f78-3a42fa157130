pull-push-image-auth-prod:
  stage: push-image-prod
  extends: .pull_push_image_prod
  rules:
  - if: $CI_COMMIT_BRANCH =~ /^release\/v.*/
    when: manual
  variables:
    ECR_NAME: "pacific-ii-authorization"
    SERVICE_NAME: "authorization"
  needs: ["build_all_package"]

# FE Backoffice ui
pull-push-image-backoffice-ui-prod:
  stage: push-image-prod
  extends: .pull_push_image_prod
  rules:
  - if: $CI_COMMIT_BRANCH =~ /^release\/v.*/
    when: manual
  variables:
    ECR_NAME: "pacific-ii-backoffice-ui"
    SERVICE_NAME: "backoffice-ui"
  needs: ["build_backoffice_ui_package"]

pull-push-image-catalog-prod:
  stage: push-image-prod
  extends: .pull_push_image_prod
  rules:
  - if: $CI_COMMIT_BRANCH =~ /^release\/v.*/
    when: manual
  variables:
    ECR_NAME: "pacific-ii-catalog"
    SERVICE_NAME: "catalog"
  needs: ["build_all_package"]

pull-push-image-core-gw-prod:
  stage: push-image-prod
  extends: .pull_push_image_prod
  rules:
  - if: $CI_COMMIT_BRANCH =~ /^release\/v.*/
    when: manual
  variables:
    ECR_NAME: "pacific-ii-gateway"
    SERVICE_NAME: "gateway"
  needs: ["build_all_package"]

# FE Customer portal
pull-push-image-customer-portal-prod:
  stage: push-image-prod
  extends: .pull_push_image_prod
  rules:
    - if: $CI_COMMIT_BRANCH =~ /^release\/v.*/
      when: manual
  variables:
    ECR_NAME: "pacific-ii-customer-portal"
    SERVICE_NAME: "customer-portal"
  needs: ["build_customer_portal_package"]

pull-push-image-device-gw-prod:
  stage: push-image-prod
  extends: .pull_push_image_prod
  rules:
  - if: $CI_COMMIT_BRANCH =~ /^release\/v.*/
    when: manual
  variables:
    ECR_NAME: "pacific-ii-device-aggregator"
    SERVICE_NAME: "aggregator"
  needs: ["build_all_package"]

pull-push-image-notification-prod:
  stage: push-image-prod
  extends: .pull_push_image_prod
  rules:
  - if: $CI_COMMIT_BRANCH =~ /^release\/v.*/
    when: manual
  variables:
    ECR_NAME: "pacific-ii-notification"
    SERVICE_NAME: "notification"
  needs: ["build_all_package"]

pull-push-image-order-prod:
  stage: push-image-prod
  extends: .pull_push_image_prod
  rules:
  - if: $CI_COMMIT_BRANCH =~ /^release\/v.*/
    when: manual
  variables:
    ECR_NAME: "pacific-ii-order"
    SERVICE_NAME: "order"
  needs: ["build_all_package"]

pull-push-image-payment-prod:
  stage: push-image-prod
  extends: .pull_push_image_prod
  rules:
  - if: $CI_COMMIT_BRANCH =~ /^release\/v.*/
    when: manual
  variables:
    ECR_NAME: "pacific-ii-payment"
    SERVICE_NAME: "payment"
  needs: ["build_all_package"]

pull-push-image-store-prod:
  stage: push-image-prod
  extends: .pull_push_image_prod
  rules:
  - if: $CI_COMMIT_BRANCH =~ /^release\/v.*/
    when: manual
  variables:
    ECR_NAME: "pacific-ii-store"
    SERVICE_NAME: "store"
  needs: ["build_all_package"]

pull-push-image-tenant-prod:
  stage: push-image-prod
  extends: .pull_push_image_prod
  rules:
  - if: $CI_COMMIT_BRANCH =~ /^release\/v.*/
    when: manual
  variables:
    ECR_NAME: "pacific-ii-tenant"
    SERVICE_NAME: "tenant"
  needs: ["build_all_package"]

pull-push-image-user-prod:
  stage: push-image-prod
  extends: .pull_push_image_prod
  rules:
  - if: $CI_COMMIT_BRANCH =~ /^release\/v.*/
    when: manual
  variables:
    ECR_NAME: "pacific-ii-user"
    SERVICE_NAME: "user"
  needs: ["build_all_package"]

pull-push-image-utility-prod:
  stage: push-image-prod
  extends: .pull_push_image_prod
  rules:
  - if: $CI_COMMIT_BRANCH =~ /^release\/v.*/
    when: manual
  variables:
    ECR_NAME: "pacific-ii-utility"
    SERVICE_NAME: "utility"
  needs: ["build_all_package"]

pull-push-image-wallet-prod:
  stage: push-image-prod
  extends: .pull_push_image_prod
  rules:
  - if: $CI_COMMIT_BRANCH =~ /^release\/v.*/
    when: manual
  variables:
    ECR_NAME: "pacific-ii-wallet"
    SERVICE_NAME: "wallet"
  needs: ["build_all_package"]