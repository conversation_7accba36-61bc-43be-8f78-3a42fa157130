# ============================================================================
# Makefile - Simplified Commands
# ============================================================================

.PHONY: help init plan apply destroy clean

help: ## Display this help
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "\033[36m%-20s\033[0m %s\n", $1, $2}' $(MAKEFILE_LIST)

init: ## Initialize Terraform
	terraform init

plan: ## Plan Terraform deployment
	terraform plan

apply: ## Apply Terraform deployment
	./scripts/deploy.sh

destroy: ## Destroy all resources
	./scripts/destroy.sh

clean: ## Clean up temporary files
	rm -f terraform.tfplan
	rm -f destroy-plan
	rm -rf .terraform/

validate: ## Validate Terraform configuration
	terraform validate
	terraform fmt -check

format: ## Format Terraform files
	terraform fmt -recursive

check: ## Run pre-deployment checks
	@echo "Running pre-deployment checks..."
	@terraform validate
	@terraform fmt -check
	@echo "All checks passed!"{