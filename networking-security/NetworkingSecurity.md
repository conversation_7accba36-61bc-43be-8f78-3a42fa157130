# Networking Security

## AWS CloudFront - TLS Termination Edge

### HTTP, TLS, and SSL

| Term                                       | Meaning                                                                                   |
|--------------------------------------------|-------------------------------------------------------------------------------------------|
| HTTP (HyperText Transfer Protocol)         | HyperText Transfer Protocol                                                               |
| HTTPS (HyperText Transfer Protocol Secure) | HTTPS = (HTTP + TLS encryption) Secure version of HTTP using encryption                   |
| SSL (Secure Sockets Layer) - (Deprecated)  | The original standard for encrypted communication (now deprecated)                        |
| TLS (Transport Layer Security)             | The modern, more secure version of SSL — when people say "SSL", they usually mean TLS now |



![TLS.png](assets/TLS.png)

### TLS 1.3 Handshake Flow

1. Client sends "Hello", Client Nonce(Random Number), TLS version, List of cipher suites (algorithms for encryption)
2. Server responses Selected Cipher suite, Server Nonce(Random Number), Digital Server Certificate (X.509 Certificate (Generic), SSL Certificate (X.509 Certificate with Extended Key Usage for Server Authentication))
3. Client send "Hello", TLS version key supported

Key Exchanges:

Client computes:
S = a × B = a × (b × G) = (ab) × G

Server computes:
S = b × A = b × (a × G) = (ab) × G

🎉 Both get the same secret S, without ever sharing a or b.

That S (a point on the curve) is the shared secret.

