/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.shared.http.requests.cashfloat;

import com.styl.pacific.common.validator.currency.Currency;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigInteger;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@Builder
@AllArgsConstructor
public class CreateCashFloatRequest {

	@NotNull
	private final Long storeId;

	@NotNull
	private final Long staffId;

	@NotBlank
	private final String deviceId;

	private final BigInteger openingAmount;

	private final BigInteger closingAmount;

	private final BigInteger desiredAmount;

	@Currency
	private final String currency;

	private final String reason;

	private Long openingTime;

	private Long closingTime;
}
