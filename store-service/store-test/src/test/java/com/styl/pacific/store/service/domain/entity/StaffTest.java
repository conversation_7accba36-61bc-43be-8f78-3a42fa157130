/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.domain.entity;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.styl.pacific.domain.valueobject.StaffId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.store.shared.http.enums.StaffStatus;
import com.styl.pacific.store.shared.http.enums.StaffType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
class StaffTest {

	private Staff staff;

	@BeforeEach
	public void setUp() {

		staff = Staff.builder()
				.id(new StaffId(1L))
				.staffCode("XYZ123")
				.name("staff1")
				.tenantId(new TenantId(1L))
				.cardId("111111111111")
				.pinCode("12345")
				.salt("xyz123")
				.type(StaffType.CASHIER)
				.status(StaffStatus.ACTIVE)
				.build();
	}

	@Test
	void shouldReturnStoreId_whenGetStoreId() {
		// Act
		StaffId staffId = staff.getId();
		// Assert
		assertEquals(new StaffId(1L), staffId);
	}

	@Test
	void shouldReturnTenantId_whenGetTenantId() {
		// Act
		TenantId tenantId = staff.getTenantId();
		// Assert
		assertEquals(new TenantId(1L), tenantId);
	}

	@Test
	void shouldReturnName_whenGetName() {
		// Act
		String name = staff.getName();
		// Assert
		assertEquals("staff1", name);
	}

	@Test
	void shouldReturnStatus_whenGetStatus() {
		// Act
		StaffStatus status = staff.getStatus();
		// Assert
		assertEquals(StaffStatus.ACTIVE, status);
	}

	@Test
	void shouldTrue_whenEqual() {
		// Arrange
		Staff staff1 = Staff.builder()
				.id(new StaffId(1L))
				.staffCode("XYZK123")
				.name("staff123")
				.tenantId(new TenantId(1L))
				.cardId("111111111111")
				.pinCode("12345")
				.salt("xyz123")
				.status(StaffStatus.ACTIVE)
				.build();

		// Assert
		assertThat(staff.equals(staff1)).isTrue();
	}

	@Test
	void shouldFalse_whenEqual() {
		// Arrange
		Staff staff1 = Staff.builder()
				.id(new StaffId(2L))
				.staffCode("XYZK123")
				.name("staff123")
				.tenantId(new TenantId(1L))
				.cardId("111111111111")
				.pinCode("12345")
				.salt("xyz123")
				.status(StaffStatus.ACTIVE)
				.build();

		// Assert
		assertThat(staff.equals(staff1)).isFalse();
	}

	@Test
	void shouldReturnCardId_whenGetCardId() {
		// Act
		String cardId = staff.getCardId();
		// Assert
		assertEquals("111111111111", cardId);
	}

	@Test
	void shouldReturnStaffCode_whenGetStaffCode() {
		// Act
		String staffCode = staff.getStaffCode();
		// Assert
		assertEquals("XYZ123", staffCode);
	}

	@Test
	void shouldReturnPinCode_whenGetPinCode() {
		// Act
		String pinCode = staff.getPinCode();
		// Assert
		assertEquals("12345", pinCode);
	}

	@Test
	void shouldReturnSalt_whenGetSalt() {
		// Act
		String salt = staff.getSalt();
		// Assert
		assertEquals("xyz123", salt);
	}

	@Test
	void shouldReturnType_whenGetType() {
		// Act
		StaffType type = staff.getType();
		// Assert
		assertEquals(StaffType.CASHIER, type);
	}

}
