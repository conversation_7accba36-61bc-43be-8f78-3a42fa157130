/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.domain.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.styl.pacific.domain.valueobject.Address;
import com.styl.pacific.domain.valueobject.StoreId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.store.service.domain.dto.store.CreateStoreCommand;
import com.styl.pacific.store.service.domain.dto.store.UpdateStoreCommand;
import com.styl.pacific.store.service.domain.entity.Store;
import com.styl.pacific.store.shared.http.responses.store.StoreResponse;
import java.time.Instant;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
class StoreDataMapperTest {

	private StoreDataMapper mapper;

	@BeforeEach
	public void setUp() {
		mapper = StoreDataMapper.INSTANCE;
	}

	@Test
	void whenMapperFromCreateStoreCommand_shouldReturnStore() {
		// Arrange
		CreateStoreCommand createStoreCommand = CreateStoreCommand.builder()
				.tenantId(1L)
				.name("Test Store")
				.email("<EMAIL>")
				.phoneNumber("1234567890")
				.addressLine1("123 Test St")
				.addressLine2("Apt 4")
				.city("Test City")
				.country("Test Country")
				.postalCode("12345")
				.build();
		// Act
		Store store = mapper.createStoreCommandToStore(createStoreCommand);
		// Assert
		assertEquals(createStoreCommand.getName(), store.getName());
		assertEquals(createStoreCommand.getTenantId(), store.getTenantId()
				.getValue());
		assertEquals(createStoreCommand.getEmail(), store.getEmail());
		assertEquals(createStoreCommand.getPhoneNumber(), store.getPhoneNumber());
		assertEquals(createStoreCommand.getAddressLine1(), store.getAddress()
				.getAddressLine1());
		assertEquals(createStoreCommand.getAddressLine2(), store.getAddress()
				.getAddressLine2());
		assertEquals(createStoreCommand.getCity(), store.getAddress()
				.getCity());
		assertEquals(createStoreCommand.getPostalCode(), store.getAddress()
				.getPostalCode());
	}

	@Test
	void whenMapperFromStore_shouldReturnStoreResponse() {
		// Arrange
		Store store = Store.builder()
				.id(new StoreId(1L))
				.name("Test Store")
				.email("<EMAIL>")
				.phoneNumber("1234567890")
				.address(new Address("123 Test St", "Apt 4", "Test Country", "Test City", "12345"))
				.createdAt(Instant.now())
				.updatedAt(Instant.now())
				.build();
		// Act
		StoreResponse storeResponse = mapper.storeToStoreResponse(store);
		// Assert
		assertEquals(store.getId()
				.getValue(), storeResponse.getStoreId());
		assertEquals(store.getName(), storeResponse.getName());
		assertEquals(store.getEmail(), storeResponse.getEmail());
		assertEquals(store.getPhoneNumber(), storeResponse.getPhoneNumber());
		assertEquals(store.getAddress()
				.getAddressLine1(), storeResponse.getAddressLine1());
		assertEquals(store.getAddress()
				.getAddressLine2(), storeResponse.getAddressLine2());
		assertEquals(store.getAddress()
				.getCity(), storeResponse.getCity());
		assertEquals(store.getAddress()
				.getPostalCode(), storeResponse.getPostalCode());
	}

	@Test
	void whenMapperFromUpdateStoreCommand_shouldReturnStore() {
		// Arrange
		UpdateStoreCommand updateStoreCommand = UpdateStoreCommand.builder()
				.storeId(1L)
				.name("Updated Store")
				.tenantId(1L)
				.email("<EMAIL>")
				.phoneNumber("76868686886")
				.addressLine1("456 Updated St")
				.addressLine2("Apt 5")
				.city("Updated City")
				.country("Updated Country")
				.postalCode("67676767")
				.build();

		Store storeExisted = Store.builder()
				.id(new StoreId(1L))
				.name("Test Store")
				.email("<EMAIL>")
				.tenantId(new TenantId(1L))
				.phoneNumber("1234567890")
				.address(new Address("123 Test St", "Apt 4", "Test Country", "Test City", "12345"))
				.createdAt(Instant.now())
				.updatedAt(Instant.now())
				.build();

		// Act
		Store store = mapper.updateStoreCommandToStore(updateStoreCommand, storeExisted);
		// Assert
		assertEquals(updateStoreCommand.getStoreId(), store.getId()
				.getValue());
		assertEquals(updateStoreCommand.getTenantId(), store.getTenantId()
				.getValue());
		assertEquals(updateStoreCommand.getName(), store.getName());
		assertEquals(updateStoreCommand.getEmail(), store.getEmail());
		assertEquals(updateStoreCommand.getPhoneNumber(), store.getPhoneNumber());
		assertEquals(updateStoreCommand.getAddressLine1(), store.getAddress()
				.getAddressLine1());
		assertEquals(updateStoreCommand.getAddressLine2(), store.getAddress()
				.getAddressLine2());
		assertEquals(updateStoreCommand.getCity(), store.getAddress()
				.getCity());
		assertEquals(updateStoreCommand.getPostalCode(), store.getAddress()
				.getPostalCode());
	}
}
