<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.styl.pacific.store.service</groupId>
        <artifactId>store-service</artifactId>
        <version>1.2.4</version>
    </parent>
    <artifactId>store-data-access</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>common-data-access</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>common-feign</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>common-mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.store.service</groupId>
            <artifactId>store-domain-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.store.service</groupId>
            <artifactId>store-shared</artifactId>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
    </dependencies>
</project>
