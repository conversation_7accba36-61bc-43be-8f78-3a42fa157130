/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.data.access.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.store.service.data.access.entity.StaffAssignmentEntity;
import com.styl.pacific.store.service.domain.entity.StaffAssignment;
import com.styl.pacific.store.service.domain.mapper.StoreDataCommonMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class, StoreDataCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface StaffAssignmentDataAccessMapper {

	StaffAssignmentDataAccessMapper INSTANCE = Mappers.getMapper(StaffAssignmentDataAccessMapper.class);

	@Mapping(target = "staffId", qualifiedByName = "longToStaffId")
	@Mapping(target = "storeId", qualifiedByName = "longToStoreId")
	@Mapping(target = "tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "id", qualifiedByName = "longToStaffAssignmentId")
	StaffAssignment entityToDomain(StaffAssignmentEntity staffAssignmentEntity);

	@Mapping(target = "deletedAt", ignore = true)
	@Mapping(target = "staffId", qualifiedByName = "staffIdToLong")
	@Mapping(target = "storeId", qualifiedByName = "storeIdToLong")
	@Mapping(target = "tenantId", qualifiedByName = "tenantIdToLong")
	@Mapping(target = "id", qualifiedByName = "staffAssignmentIdToLong")
	StaffAssignmentEntity domainToEntity(StaffAssignment staffAssignment);

	//StaffEntitySpecification findStaffsQueryToStaffEntitySpecification(FindStaffsQuery query);
}
