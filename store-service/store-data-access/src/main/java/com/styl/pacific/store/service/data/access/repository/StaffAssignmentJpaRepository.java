/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.data.access.repository;

import com.styl.pacific.store.service.data.access.entity.StaffAssignmentEntity;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * <AUTHOR>
 *
 */
public interface StaffAssignmentJpaRepository extends JpaRepository<StaffAssignmentEntity, Long>,
		JpaSpecificationExecutor<StaffAssignmentEntity> {
	@Modifying
	@Query(value = "DELETE FROM StaffAssignmentEntity entity WHERE entity.staffId = :staffId AND entity.tenantId = :tenantId")
	void deleteByStaffIdAndTenantId(@Param("staffId") Long staffId, @Param("tenantId") Long tenantId);

	@Query(value = "SELECT entity FROM StaffAssignmentEntity entity WHERE entity.staffId IN :staffIds AND entity.tenantId = :tenantId")
	List<StaffAssignmentEntity> findByStaffIds(@Param("staffIds") List<Long> staffIds,
			@Param("tenantId") Long tenantId);
}
