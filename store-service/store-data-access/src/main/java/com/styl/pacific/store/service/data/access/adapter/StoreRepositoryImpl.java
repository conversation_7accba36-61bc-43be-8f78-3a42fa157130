/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.data.access.adapter;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.store.service.data.access.entity.StoreEntity;
import com.styl.pacific.store.service.data.access.mapper.StoreDataAccessMapper;
import com.styl.pacific.store.service.data.access.repository.StoreJpaRepository;
import com.styl.pacific.store.service.data.access.specification.StoreEntitySpecification;
import com.styl.pacific.store.service.domain.dto.store.FindStoresQuery;
import com.styl.pacific.store.service.domain.entity.Store;
import com.styl.pacific.store.service.domain.output.repository.StoreRepository;
import com.styl.pacific.store.shared.http.requests.store.GetStoreQuery;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Component
public class StoreRepositoryImpl implements StoreRepository {

	private final StoreJpaRepository storeJpaRepository;

	@Override
	public Store save(Store store) {
		StoreEntity storeEntity = StoreDataAccessMapper.INSTANCE.storeToStoreEntity(store);
		StoreEntity result = storeJpaRepository.saveAndFlush(storeEntity);
		return StoreDataAccessMapper.INSTANCE.storeEntityToStore(result);
	}

	@Override
	public Optional<Store> getStore(GetStoreQuery query) {
		StoreEntitySpecification specs = StoreEntitySpecification.builder()
				.tenantId(query.getTenantId())
				.storeIds(List.of(query.getStoreId()))
				.build();
		return storeJpaRepository.findOne(specs)
				.map(StoreDataAccessMapper.INSTANCE::storeEntityToStore);
	}

	@Override
	public Paging<Store> findStores(FindStoresQuery query) {
		StoreEntitySpecification specs = StoreDataAccessMapper.INSTANCE.findStoresQueryToStoreEntitySpecification(query
				.getFilter());

		PageRequest pageRequest = PageRequest.of(query.getPage(), query.getSize(), Direction.fromString(query
				.getSortDirection()), query.getSortFields()
						.toArray(new String[0]));
		Page<Store> result = storeJpaRepository.findAll(specs, pageRequest)
				.map(StoreDataAccessMapper.INSTANCE::storeEntityToStore);
		return new Paging<>(result.getContent(), result.getTotalElements(), result.getTotalPages(), result.getNumber(),
				result.getSort()
						.stream()
						.map(order -> order.getProperty() + ","
								+ order.getDirection()
										.name())
						.collect(Collectors.toList()));
	}

	@Override
	public Optional<Store> findById(Long id) {
		return storeJpaRepository.findById(id)
				.map(StoreDataAccessMapper.INSTANCE::storeEntityToStore);
	}
}
