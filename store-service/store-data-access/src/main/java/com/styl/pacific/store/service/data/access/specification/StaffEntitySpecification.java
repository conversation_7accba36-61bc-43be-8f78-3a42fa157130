/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.data.access.specification;

import com.styl.pacific.data.access.jpa.specification.BaseSpecification;
import com.styl.pacific.store.service.data.access.entity.StaffAssignmentEntity;
import com.styl.pacific.store.service.data.access.entity.StaffEntity;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.criteria.Subquery;
import java.io.Serial;
import java.util.ArrayList;
import java.util.List;
import lombok.Builder;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */
@Builder
public class StaffEntitySpecification extends BaseSpecification<StaffEntity> {
	@Serial
	private static final long serialVersionUID = 1L;

	private Long staffId;

	private Long storeId;

	private String staffCode;

	private String staffCodeEq;

	private String cardId;

	private String email;

	private Long tenantId;

	private String name;

	private List<String> statuses;

	private String type;

	@Override
	public Predicate toPredicate(Root<StaffEntity> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
		List<Predicate> predicates = new ArrayList<>();

		if (isNotNull(staffId)) {
			predicates.add(equals(criteriaBuilder, root.get("id"), staffId));
		}

		if (isNotNull(storeId)) {
			Subquery<StaffAssignmentEntity> subquery = query.subquery(StaffAssignmentEntity.class);
			Root<StaffAssignmentEntity> subQueryRoot = subquery.from(StaffAssignmentEntity.class);
			subquery.select(subQueryRoot)
					.where(criteriaBuilder.equal(root.get("id"), subQueryRoot.get("staffId")), criteriaBuilder.equal(
							subQueryRoot.get("storeId"), storeId));
			predicates.add(criteriaBuilder.exists(subquery));
		}

		if (isNotBlank(cardId)) {
			predicates.add(equals(criteriaBuilder, root.get("cardId"), cardId));
		}

		if (isNotNull(tenantId)) {
			predicates.add(equals(criteriaBuilder, root.get("tenantId"), tenantId));
		}

		if (isNotBlank(staffCode)) {
			predicates.add(like(criteriaBuilder, root.get("staffCode"), staffCode));
		}

		if (isNotBlank(staffCodeEq)) {
			predicates.add(equals(criteriaBuilder, root.get("staffCode"), staffCodeEq));
		}

		if (isNotBlank(email)) {
			predicates.add(like(criteriaBuilder, root.get("email"), email));
		}

		if (isNotBlank(name)) {
			predicates.add(like(criteriaBuilder, root.get("name"), name));
		}

		if (!CollectionUtils.isEmpty(statuses)) {
			predicates.add(inStringList(root.get("status"), statuses));
		}

		if (isNotBlank(type)) {
			predicates.add(equals(criteriaBuilder, root.get("type"), type));
		}

		return and(criteriaBuilder, predicates);
	}
}
