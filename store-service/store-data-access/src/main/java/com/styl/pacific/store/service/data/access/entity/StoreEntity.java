/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.data.access.entity;

import com.styl.pacific.data.access.entity.AuditableEntity;
import com.styl.pacific.domain.enums.StoreStatus;
import com.styl.pacific.store.shared.http.enums.SuspendType;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.SQLRestriction;

/**
 * <AUTHOR>
 */

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@SQLRestriction("deleted_at IS NULL")
@Table(name = "tb_store")
public class StoreEntity extends AuditableEntity {

	@Id
	private Long id;

	private Long tenantId;

	private String phoneNumber;

	private String email;

	private String workingHour;

	private String name;

	private String addressLine1;

	private String addressLine2;

	@Enumerated(EnumType.STRING)
	private StoreStatus status;

	private String city;

	private String country;

	private String postalCode;

	private String note;

	private String migrationId;

	@Enumerated(EnumType.STRING)
	private SuspendType suspendType;

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (!(o instanceof StoreEntity that))
			return false;
		if (!super.equals(o))
			return false;
		return Objects.equals(id, that.id) && Objects.equals(tenantId, that.tenantId) && Objects.equals(phoneNumber,
				that.phoneNumber) && Objects.equals(email, that.email) && Objects.equals(workingHour, that.workingHour)
				&& Objects.equals(name, that.name) && Objects.equals(addressLine1, that.addressLine1) && Objects.equals(
						addressLine2, that.addressLine2) && status == that.status && Objects.equals(city, that.city)
				&& Objects.equals(country, that.country) && Objects.equals(postalCode, that.postalCode) && Objects
						.equals(note, that.note) && suspendType == that.suspendType;
	}

	@Override
	public int hashCode() {
		return Objects.hash(super.hashCode(), id, tenantId, phoneNumber, email, workingHour, name, addressLine1,
				addressLine2, status, city, country, postalCode, note, suspendType);
	}
}
