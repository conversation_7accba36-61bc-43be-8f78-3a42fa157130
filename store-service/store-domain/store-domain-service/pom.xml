<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.styl.pacific.store.service</groupId>
        <artifactId>store-domain</artifactId>
        <version>1.2.4</version>
    </parent>
    <artifactId>store-domain-service</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>common-mapstruct</artifactId>
        </dependency>

        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>common-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.store.service</groupId>
            <artifactId>store-domain-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.store.service</groupId>
            <artifactId>store-shared</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>
    </dependencies>

</project>
