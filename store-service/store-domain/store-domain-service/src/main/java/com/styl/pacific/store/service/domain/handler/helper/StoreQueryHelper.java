/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.domain.handler.helper;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.store.service.domain.dto.store.FindStoresQuery;
import com.styl.pacific.store.service.domain.entity.Store;
import com.styl.pacific.store.service.domain.exception.StoreNotFoundException;
import com.styl.pacific.store.service.domain.mapper.StoreDataMapper;
import com.styl.pacific.store.service.domain.output.repository.StoreRepository;
import com.styl.pacific.store.shared.http.requests.store.GetStoreQuery;
import com.styl.pacific.store.shared.http.responses.store.ListStoresResponse;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
@AllArgsConstructor
public class StoreQueryHelper {

	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	private final StoreRepository storeRepository;

	public Store getStore(GetStoreQuery query) {
		Optional<Store> storeResult = storeRepository.getStore(query);
		if (storeResult.isEmpty()) {
			throw new StoreNotFoundException("Store not found with id: " + query.getStoreId());
		}
		logger.info("Get Store with id {}", query.getStoreId());
		return storeResult.get();
	}

	public ListStoresResponse findStores(FindStoresQuery query) {
		Paging<Store> result = storeRepository.findStores(query);
		return new ListStoresResponse(result.getContent()
				.stream()
				.map(StoreDataMapper.INSTANCE::storeToStoreResponse)
				.toList(), result.getTotalElements(), result.getTotalPages(), result.getPage(), result.getSort());
	}
}
