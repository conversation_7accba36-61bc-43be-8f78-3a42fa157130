/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.domain.handler;

import com.styl.pacific.store.service.domain.dto.staff.FindStaffsQuery;
import com.styl.pacific.store.service.domain.entity.Staff;
import com.styl.pacific.store.service.domain.handler.helper.StaffQueryHelper;
import com.styl.pacific.store.service.domain.mapper.StaffDataMapper;
import com.styl.pacific.store.shared.http.requests.staff.GetStaffQuery;
import com.styl.pacific.store.shared.http.responses.staff.ListStaffResponse;
import com.styl.pacific.store.shared.http.responses.staff.StaffResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 *
 */

@Component
@AllArgsConstructor
public class StaffQueryHandler {

	private final StaffQueryHelper staffQueryHelper;

	public StaffResponse getStaff(GetStaffQuery query) {
		Staff staff = staffQueryHelper.getStaff(query);
		return StaffDataMapper.INSTANCE.staffToStaffResponse(staff);
	}

	public ListStaffResponse findStaff(FindStaffsQuery query) {
		return staffQueryHelper.findStaff(query);
	}
}
