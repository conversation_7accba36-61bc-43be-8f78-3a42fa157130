/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.domain.handler.helper;

import com.styl.pacific.store.service.domain.StaffDomainCore;
import com.styl.pacific.store.service.domain.dto.staff.ActivateStaffCommand;
import com.styl.pacific.store.service.domain.dto.staff.ArchiveStaffCommand;
import com.styl.pacific.store.service.domain.dto.staff.CreateStaffCommand;
import com.styl.pacific.store.service.domain.dto.staff.InActivateStaffCommand;
import com.styl.pacific.store.service.domain.dto.staff.ResetPinCodeCommand;
import com.styl.pacific.store.service.domain.dto.staff.UpdateStaffCommand;
import com.styl.pacific.store.service.domain.entity.Staff;
import com.styl.pacific.store.service.domain.exception.StaffExistedException;
import com.styl.pacific.store.service.domain.exception.StaffNotFoundException;
import com.styl.pacific.store.service.domain.exception.StaffStatusInvalidException;
import com.styl.pacific.store.service.domain.exception.StaffUpdateFailException;
import com.styl.pacific.store.service.domain.exception.StaffVerifyFailException;
import com.styl.pacific.store.service.domain.mapper.StaffDataMapper;
import com.styl.pacific.store.service.domain.output.repository.StaffRepository;
import com.styl.pacific.store.service.domain.utils.SHACryptographic;
import com.styl.pacific.store.shared.http.enums.StaffStatus;
import com.styl.pacific.store.shared.http.requests.staff.GetStaffQuery;
import com.styl.pacific.store.shared.http.responses.staff.StaffResponse;
import java.util.HexFormat;
import java.util.Objects;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */

@Component
@AllArgsConstructor
public class StaffCommandHelper {

	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	private final StaffRepository staffRepository;

	private final StaffDomainCore staffDomainCore;

	public Staff persistNewStaff(CreateStaffCommand command) {
		byte[] salt = SHACryptographic.generateSalt();
		String saltHex = SHACryptographic.formatHex(salt);
		String hashPinCode = SHACryptographic.getHashPassword(command.getPinCode(), salt);
		Staff staff = StaffDataMapper.INSTANCE.createStaffCommandToStaff(command, hashPinCode, saltHex);
		staffDomainCore.initData(staff);
		Staff staffResult = staffRepository.save(staff);
		logger.info("Staff id created with {}", staffResult.getId());
		return staffResult;
	}

	public Staff updateStaff(UpdateStaffCommand command) {
		logger.info("Updating staff with id: {}", command.getStaffId());
		Staff staffExisting = getStaffOrThrowNotFound(GetStaffQuery.builder()
				.staffId(command.getStaffId())
				.tenantId(command.getTenantId())
				.build());
		if (StringUtils.hasText(command.getCardId()) && !Objects.equals(staffExisting.getCardId(), command
				.getCardId())) {
			Optional<Staff> staffExistingByCardId = staffRepository.getStaff(GetStaffQuery.builder()
					.tenantId(command.getTenantId())
					.cardId(command.getCardId())
					.build());
			if (staffExistingByCardId.isPresent()) {
				throw new StaffExistedException("Staff is existed with cardId : " + command.getCardId());
			}
		}
		Staff staff = StaffDataMapper.INSTANCE.updateStaffCommandToStaff(staffExisting, command);
		Staff result = staffRepository.save(staff);
		if (Objects.isNull(result)) {
			throw new StaffUpdateFailException("Failed to save staff with id: " + staff.getId());
		}
		return result;
	}

	public StaffResponse inActivateStaff(InActivateStaffCommand command) {
		Staff staff = getStaffOrThrowNotFound(GetStaffQuery.builder()
				.tenantId(command.getTenantId())
				.staffId(command.getStaffId())
				.build());
		staffDomainCore.inActivate(staff);
		Staff staffResult = staffRepository.save(staff);
		return StaffDataMapper.INSTANCE.staffToStaffResponse(staffResult);
	}

	public StaffResponse activateStaff(ActivateStaffCommand command) {
		Staff staff = getStaffOrThrowNotFound(GetStaffQuery.builder()
				.tenantId(command.getTenantId())
				.staffId(command.getStaffId())
				.build());
		staffDomainCore.activate(staff);
		Staff staffResult = staffRepository.save(staff);
		return StaffDataMapper.INSTANCE.staffToStaffResponse(staffResult);
	}

	public void archiveStaff(ArchiveStaffCommand command) {
		Staff staff = getStaffOrThrowNotFound(GetStaffQuery.builder()
				.tenantId(command.getTenantId())
				.staffId(command.getStaffId())
				.build());
		staffDomainCore.archive(staff);
		staffRepository.save(staff);
	}

	public void resetPinCode(ResetPinCodeCommand command) {
		logger.info("Reset staff with id: {}", command.getStaffId());
		Staff staffExisting = getStaffOrThrowNotFound(GetStaffQuery.builder()
				.staffId(command.getStaffId())
				.tenantId(command.getTenantId())
				.build());
		byte[] salt = SHACryptographic.generateSalt();
		String saltHex = SHACryptographic.formatHex(salt);
		String hashPinCode = SHACryptographic.getHashPassword(command.getPinCode(), salt);
		Staff staff = StaffDataMapper.INSTANCE.resetPinCodeCommandToStaff(staffExisting, hashPinCode, saltHex);
		Staff result = staffRepository.save(staff);
		if (Objects.isNull(result)) {
			throw new StaffUpdateFailException("Failed to reset pinCode with id: " + staff.getId());
		}
	}

	public StaffResponse verifyStaff(Long tenantId, Long storeId, String staffCode, String pinCode) {
		Optional<Staff> staffExisting = staffRepository.getStaffByStaffCodeAndTenantIdAndStoreId(staffCode, tenantId,
				storeId);
		if (staffExisting.isEmpty()) {
			throw new StaffVerifyFailException("The staffCode or pinCode is Incorrect");
		}

		// staff status must be active
		validateActiveStatus(staffExisting.get());

		Staff staff = staffExisting.get();
		byte[] salt = HexFormat.of()
				.parseHex(staff.getSalt());
		String hashPinCode = SHACryptographic.getHashPassword(pinCode, salt);
		if (!Objects.equals(hashPinCode, staff.getPinCode())) {
			throw new StaffVerifyFailException("The staffCode or pinCode is Incorrect");
		}
		return StaffDataMapper.INSTANCE.staffToStaffResponse(staff);
	}

	public StaffResponse verifyStaffCard(Long tenantId, Long storeId, String cardId) {
		Optional<Staff> staffExisting = staffRepository.getStaffByCardIdAndTenantIdAndStoreId(cardId, tenantId,
				storeId);
		if (staffExisting.isEmpty()) {
			throw new StaffVerifyFailException("Verify card fail");
		}
		// staff status must be active
		validateActiveStatus(staffExisting.get());

		Staff staff = staffExisting.get();
		return StaffDataMapper.INSTANCE.staffToStaffResponse(staff);
	}

	private void validateActiveStatus(Staff staff) {
		if (!Objects.equals(StaffStatus.ACTIVE, staff.getStatus())) {
			throw new StaffStatusInvalidException("The staffStatus must be active");
		}
	}

	private Staff getStaffOrThrowNotFound(GetStaffQuery query) {
		Optional<Staff> staffExisting = staffRepository.getStaff(query);
		if (staffExisting.isEmpty()) {
			throw new StaffNotFoundException("Staff not found with id: " + query.getStaffId());
		}
		return staffExisting.get();
	}
}
