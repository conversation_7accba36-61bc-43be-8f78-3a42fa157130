/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.domain.handler.helper;

import com.styl.pacific.domain.enums.StoreStatus;
import com.styl.pacific.store.service.domain.entity.Store;
import com.styl.pacific.store.service.domain.exception.StoreNotActivateException;
import com.styl.pacific.store.service.domain.exception.StoreNotFoundException;
import com.styl.pacific.store.service.domain.output.repository.StoreRepository;
import com.styl.pacific.store.shared.http.requests.store.GetStoreQuery;
import java.util.Objects;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
@AllArgsConstructor
public class StoreValidateHelper {

	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	private final StoreRepository storeRepository;

	public void checkStoreExisted(Long tenantId, Long storeId) {
		getStoreOrThrowNotFound(GetStoreQuery.builder()
				.tenantId(tenantId)
				.storeId(storeId)
				.build());
	}

	public void checkStoreExistedAndValidateActiveStatus(Long tenantId, Long storeId) {
		Store store = getStoreOrThrowNotFound(GetStoreQuery.builder()
				.tenantId(tenantId)
				.storeId(storeId)
				.build());
		if (!Objects.equals(StoreStatus.ACTIVE, store.getStatus())) {
			throw new StoreNotActivateException("Store must activate");
		}
	}

	private Store getStoreOrThrowNotFound(GetStoreQuery query) {
		Optional<Store> storeExisting = storeRepository.getStore(query);
		if (storeExisting.isEmpty()) {
			throw new StoreNotFoundException("Store is not existed with id : " + query.getStoreId());
		}
		return storeExisting.get();
	}
}
