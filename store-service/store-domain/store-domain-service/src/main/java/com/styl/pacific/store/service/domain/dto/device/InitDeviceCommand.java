/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.domain.dto.device;

import com.styl.pacific.store.shared.http.enums.DeviceType;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 *
 */

@Getter
@Builder
public class InitDeviceCommand {

	private final String deviceId;

	private final Long tenantId;

	private String firmwareVersion;

	private String osVersion;

	private DeviceType type;

}
