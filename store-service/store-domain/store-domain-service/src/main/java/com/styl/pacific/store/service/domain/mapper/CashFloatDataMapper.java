/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.domain.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.store.service.domain.dto.cashfloat.CreateCashFloatCommand;
import com.styl.pacific.store.service.domain.dto.cashfloat.UpdateCashFloatCommand;
import com.styl.pacific.store.service.domain.entity.CashFloat;
import com.styl.pacific.store.service.domain.entity.Staff;
import com.styl.pacific.store.shared.http.responses.cashfloat.CashFloatResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class, StoreDataCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface CashFloatDataMapper {

	CashFloatDataMapper INSTANCE = Mappers.getMapper(CashFloatDataMapper.class);

	@Mapping(target = "openingTime", source = "command.openingTime")
	@Mapping(target = "desiredAmount", source = "command.desiredAmount")
	@Mapping(target = "closingTime", source = "command.closingTime")
	@Mapping(target = "closingAmount", source = "command.closingAmount")
	@Mapping(target = "updatedAt", ignore = true)
	@Mapping(target = "id", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "storeId", source = "command.storeId", qualifiedByName = "longToStoreId")
	@Mapping(target = "tenantId", source = "command.tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "deviceId", source = "command.deviceId")
	@Mapping(target = "staffName", source = "staff.name")
	@Mapping(target = "staffCode", source = "staff.staffCode")
	@Mapping(target = "staffId", source = "staff.id")
	@Mapping(target = "openingAmount", source = "command.openingAmount")
	@Mapping(target = "reason", source = "command.reason")
	@Mapping(target = "currency", source = "command.currency")
	CashFloat createCashFloatCommandToCashFloat(CreateCashFloatCommand command, Staff staff);

	@Mapping(target = "storeId", qualifiedByName = "storeIdToLong")
	@Mapping(target = "tenantId", qualifiedByName = "tenantIdToLong")
	@Mapping(target = "staffId", qualifiedByName = "staffIdToLong")
	@Mapping(target = "cashFloatId", source = "cashFloat.id", qualifiedByName = "cashFloatIdToLong")
	@Mapping(target = "currency", source = "currency", qualifiedByName = "currencyCodeToResponse")
	@Mapping(target = "openingTime", qualifiedByName = "instantToLong")
	@Mapping(target = "closingTime", qualifiedByName = "instantToLong")
	CashFloatResponse cashFloatToCashFloatResponse(CashFloat cashFloat);

	@Mapping(target = "storeId", source = "existingCashFloat.storeId")
	@Mapping(target = "tenantId", source = "existingCashFloat.tenantId")
	@Mapping(target = "deviceId", source = "existingCashFloat.deviceId")
	@Mapping(target = "staffId", source = "existingCashFloat.staffId")
	@Mapping(target = "closingAmount", source = "command.closingAmount")
	@Mapping(target = "openingAmount", source = "existingCashFloat.openingAmount")
	@Mapping(target = "closingTime", source = "command.closingTime")
	@Mapping(target = "desiredAmount", source = "command.desiredAmount")
	@Mapping(target = "reason", source = "command.reason")
	@Mapping(target = "staffCode", source = "existingCashFloat.staffCode")
	@Mapping(target = "currency", source = "existingCashFloat.currency")
	@Mapping(target = "createdAt", source = "existingCashFloat.createdAt")
	CashFloat updateCashFloatCommandToCashFloat(CashFloat existingCashFloat, UpdateCashFloatCommand command);
}
