/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.domain.exception;

import com.styl.pacific.domain.exception.GlobalErrorCode;
import com.styl.pacific.domain.processor.ErrorCodeValue;
import java.io.Serial;

/**
 * <AUTHOR>
 */

@ErrorCodeValue(GlobalErrorCode.DEVICE_SESSION_NOT_FOUND)
public class DeviceSessionNotFoundException extends StoreDomainException {

	@Serial
	private static final long serialVersionUID = 1L;

	public DeviceSessionNotFoundException(String message, Throwable cause) {
		super(message, cause);
	}

	public DeviceSessionNotFoundException(String message) {
		super(message);
	}

}
