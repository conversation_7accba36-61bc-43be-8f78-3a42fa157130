/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.nifi.processors.report.repository;

import com.styl.pacific.nifi.exceptions.NifiProcessorException;
import com.styl.pacific.nifi.processors.report.TrackDataRecordRepository;
import com.styl.pacific.nifi.processors.report.models.TrackingDataRecord;
import java.time.Instant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.nifi.dbcp.DBCPService;

@RequiredArgsConstructor
@Slf4j
public class TrackDataRecordRepositoryImpl implements TrackDataRecordRepository {
	private static final String MIGRATION_SQL = "CREATE TABLE IF NOT EXISTS tracking_records ( "
			+ "id BIGSERIAL PRIMARY KEY, "
			+ "tenant_id VARCHAR(255) NOT NULL, "
			+ "pacific_customer_id BIGINT, "
			+ "caribbean_user_id BIGINT, "
			+ "migration_context VARCHAR(100) NOT NULL, "
			+ "migration_status VARCHAR(100) NOT NULL, "
			+ "source_data JSONB, "
			+ "pacific_data JSONB, "
			+ "error_message TEXT, "
			+ "created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP "
			+ ")";
	private static final String INSERT_SQL = "INSERT INTO tracking_records (tenant_id, pacific_customer_id, caribbean_user_id, migration_context, migration_status, source_data, pacific_data, error_message) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
	private final DBCPService dbcpService;

	@Override
	public void migrate() {
		try (final var connection = dbcpService.getConnection()) {
			connection.setAutoCommit(false);
			try (final var statement = connection.createStatement()) {
				statement.execute(MIGRATION_SQL);
				log.info("Tracking records table created");
				connection.commit();
			} catch (Exception e) {
				connection.rollback();
				log.error("Error migrating tracking records table", e);
				throw new NifiProcessorException("Error migrating tracking records table", e);
			}
		} catch (Exception e) {
			log.error("Error getting connection", e);
			throw new NifiProcessorException("Error getting connection", e);
		}
	}

	@Override
	public TrackingDataRecord save(TrackingDataRecord trackingRecord) {
		try (final var connection = dbcpService.getConnection()) {
			connection.setAutoCommit(false);
			try (final var preparedStatement = connection.prepareStatement(INSERT_SQL, new String[] { "id",
					"created_at" })) {
				preparedStatement.setLong(1, trackingRecord.getTenantId()
						.getValue());
				preparedStatement.setObject(2, trackingRecord.getPacificCustomerId() != null
						? trackingRecord.getPacificCustomerId()
								.getValue()
						: null);
				preparedStatement.setObject(3, trackingRecord.getCaribbeanUserId(), java.sql.Types.BIGINT);
				preparedStatement.setString(4, trackingRecord.getMigrationContext()
						.name());
				preparedStatement.setString(5, trackingRecord.getMigrationStatus()
						.name());
				preparedStatement.setObject(6, trackingRecord.getSourceData(), java.sql.Types.OTHER);
				preparedStatement.setObject(7, trackingRecord.getPacificData(), java.sql.Types.OTHER);
				preparedStatement.setString(8, trackingRecord.getErrorMessage());

				preparedStatement.executeUpdate();

				Long id = null;
				Instant createdAt = null;

				try (final var generatedKeys = preparedStatement.getGeneratedKeys()) {
					if (generatedKeys.next()) {
						id = generatedKeys.getLong("id");
						createdAt = generatedKeys.getTimestamp("created_at")
								.toInstant();
					}
				}
				connection.commit();

				trackingRecord.setId(id);
				trackingRecord.setCreatedAt(createdAt);

				log.info("Tracking record saved with ID: {} at {}", trackingRecord.getId(), trackingRecord
						.getCreatedAt());
				return trackingRecord;
			} catch (Exception e) {
				connection.rollback();
				log.error("Error saving tracking record", e);
				throw new NifiProcessorException("Error saving tracking record", e);
			}
		} catch (Exception e) {
			log.error("Error getting connection", e);
			throw new NifiProcessorException("Error getting connection", e);
		}
	}
}
