/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.nifi.base.feign;

import static java.util.concurrent.TimeUnit.SECONDS;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.common.feign.config.CommonFeignRetryer;
import com.styl.pacific.common.feign.exception.PacificFeignErrorDecoder;
import com.styl.pacific.common.feign.utils.CustomQueryMapEncoder;
import feign.Feign;
import feign.Logger;
import feign.RequestInterceptor;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import feign.okhttp.OkHttpClient;
import java.util.Collections;
import lombok.Builder;
import org.springframework.cloud.openfeign.FeignClientProperties;
import org.springframework.cloud.openfeign.support.ResponseEntityDecoder;
import org.springframework.cloud.openfeign.support.SpringMvcContract;
import org.springframework.format.support.DefaultFormattingConversionService;

@Builder
public class FeignClientConfig {
	private final String baseUrl;
	private final String accessToken;
	private final String xTenantId;
	private final ObjectMapper objectMapper;

	public <T> T createClient(Class<T> apiClass) {
		final var config = new FeignClientProperties();
		return Feign.builder()
				.client(new OkHttpClient())
				.encoder(new JacksonEncoder(objectMapper))
				.decoder(new ResponseEntityDecoder(new JacksonDecoder(objectMapper)))
				.requestInterceptor(createAuthInterceptor())
				.queryMapEncoder(new CustomQueryMapEncoder())
				.retryer(new CommonFeignRetryer(250, SECONDS.toMillis(5), 10))
				.logger(new Logger.ErrorLogger())
				.contract(new SpringMvcContract(Collections.emptyList(), new DefaultFormattingConversionService(),
						config))
				.errorDecoder(new PacificFeignErrorDecoder())
				.logLevel(Logger.Level.FULL)
				.target(apiClass, baseUrl);
	}

	private RequestInterceptor createAuthInterceptor() {
		return template -> {
			template.header("Authorization", "Bearer " + accessToken);
			template.header("X-TENANT-ID", xTenantId);
			template.header("Content-Type", "application/json");
		};
	}
}