/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.nifi.processors.groups;

import com.styl.pacific.nifi.base.PacificApiAccessProvider;
import com.styl.pacific.nifi.processors.groups.service.GroupClientServiceImpl;
import com.styl.pacific.nifi.processors.groups.service.GroupServiceImpl;
import com.styl.pacific.user.shared.http.apis.UserGroupApi;
import lombok.Getter;

@Getter
public class GroupProcessorBeanManager {
	private GroupService groupService;

	private GroupClientService groupClientService;

	public GroupProcessorBeanManager initialize() {
		groupClientService = new GroupClientServiceImpl(PacificApiAccessProvider.createApiClient(UserGroupApi.class));
		groupService = new GroupServiceImpl(groupClientService);
		return this;
	}
}
