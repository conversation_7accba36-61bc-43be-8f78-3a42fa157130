/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.nifi.processors.customerallergens;

import com.styl.pacific.nifi.base.BasePacificProcessor;
import com.styl.pacific.nifi.base.mapper.ObjectMapperConfig;
import com.styl.pacific.nifi.base.models.TrackingErrorRecord;
import com.styl.pacific.nifi.base.models.TrackingSuccessRecord;
import com.styl.pacific.nifi.commons.report.MigrationRecordStatus;
import com.styl.pacific.nifi.commons.report.RecordMigrationContext;
import com.styl.pacific.nifi.exceptions.CustomerAllergenAlreadyExistingException;
import com.styl.pacific.nifi.processors.customerallergens.mapper.CustomerAllergenMapper;
import com.styl.pacific.nifi.processors.customerallergens.request.FlowFileCreateCustomerAllergenRequest;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.nifi.annotation.behavior.InputRequirement;
import org.apache.nifi.annotation.behavior.SupportsBatching;
import org.apache.nifi.annotation.documentation.CapabilityDescription;
import org.apache.nifi.annotation.documentation.Tags;
import org.apache.nifi.flowfile.FlowFile;
import org.apache.nifi.processor.ProcessContext;
import org.apache.nifi.processor.ProcessSession;
import org.apache.nifi.processor.ProcessorInitializationContext;
import org.apache.nifi.processor.Relationship;

@SupportsBatching
@Tags({ "pacific" })
@InputRequirement(InputRequirement.Requirement.INPUT_REQUIRED)
@Slf4j
@CapabilityDescription("A Processor for creating Customer Allergen Migration from Caribbean")
public class CreateCustomerAllergensProcessor extends BasePacificProcessor {

	public static final Relationship REL_SUCCESS = new Relationship.Builder().name("success")
			.description("Customer Allergen had been created successfully.")
			.build();

	public static final Relationship REL_EXISTING = new Relationship.Builder().name("existing")
			.description("Customer Allergen has been existing")
			.build();

	public static final Relationship REL_FAILURE = new Relationship.Builder().name("failure")
			.description("Customer Allergen creation has been failed.")
			.build();

	@Override
	protected void init(final ProcessorInitializationContext context) {
		this.relationships = Set.of(REL_SUCCESS, REL_FAILURE, REL_EXISTING);
	}

	@Override
	protected RecordMigrationContext getMigrationContext() {
		return RecordMigrationContext.CUSTOMER_ALLERGEN;
	}

	@Override
	protected void transferToFailure(FlowFile flowFile, ProcessSession session) {
		session.transfer(flowFile, REL_FAILURE);
	}

	@Override
	protected void onProcessTrigger(ProcessContext context, FlowFile flowFile, ProcessSession session) {
		try {
			final var beanManager = new CustomerAllergenBeanManager().initialize();
			final var request = ObjectMapperConfig.getObjectMapper()
					.readValue(readFlowFileBinaryContent(flowFile, session),
							FlowFileCreateCustomerAllergenRequest.class);

			final var customerAllergen = beanManager.getCustomerAllergenService()
					.createCustomerAllergen(CustomerAllergenMapper.INSTANCE.toClearance(request));

			session.putAttribute(flowFile, "pacificCustomerAllergenId", customerAllergen.getId());

			trackOnSuccess(flowFile, session, TrackingSuccessRecord.builder()
					.pacificCustomerId(customerAllergen.getMigrationUserId())
					.caribbeanUserId(request.getChildId())
					.sourceData(ObjectMapperConfig.getObjectMapper()
							.writeValueAsString(request))
					.pacificData(ObjectMapperConfig.getObjectMapper()
							.writeValueAsString(customerAllergen))
					.build());
			session.transfer(flowFile, REL_SUCCESS);
		} catch (CustomerAllergenAlreadyExistingException e) {
			getLogger().info("Customer Allergen already exists", e);
			session.putAttribute(flowFile, "PacificCustomerAllergenAlreadyExisting", e.getMessage());
			trackError(session, flowFile, TrackingErrorRecord.builder()
					.status(MigrationRecordStatus.FAILURE)
					.exception(e)
					.build());
			session.transfer(flowFile, REL_EXISTING);
		} catch (Exception e) {
			getLogger().error("Error creating customer allergen", e);
			trackError(session, flowFile, TrackingErrorRecord.builder()
					.status(MigrationRecordStatus.FAILURE)
					.exception(e)
					.build());

			onError(flowFile, session, e);
		}
	}
}
