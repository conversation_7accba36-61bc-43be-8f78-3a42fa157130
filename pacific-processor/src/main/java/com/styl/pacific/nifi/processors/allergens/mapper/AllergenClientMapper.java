/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.nifi.processors.allergens.mapper;

import com.styl.pacific.catalog.service.shared.http.allergen.request.CreateAllergenRequest;
import com.styl.pacific.catalog.service.shared.http.allergen.response.AllergenResponse;
import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.nifi.mapper.NifiProcessorMapper;
import com.styl.pacific.nifi.processors.allergens.models.Allergen;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class, NifiProcessorMapper.class })
public interface AllergenClientMapper {
	AllergenClientMapper INSTANCE = Mappers.getMapper(AllergenClientMapper.class);

	Allergen toAllergen(AllergenResponse allergenResponse);

	CreateAllergenRequest toCreateAllergenRequest(Allergen allergen);

}