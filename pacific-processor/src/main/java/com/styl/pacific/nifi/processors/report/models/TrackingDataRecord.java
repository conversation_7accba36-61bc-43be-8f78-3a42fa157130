/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.nifi.processors.report.models;

import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.nifi.commons.report.MigrationRecordStatus;
import com.styl.pacific.nifi.commons.report.RecordMigrationContext;
import java.time.Instant;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class TrackingDataRecord {
	private Long id;
	private TenantId tenantId;
	private UserId pacificCustomerId;
	private Long caribbeanUserId;

	private RecordMigrationContext migrationContext;
	private MigrationRecordStatus migrationStatus;
	private String sourceData;
	private String pacificData;
	private String errorMessage;
	private Instant createdAt;
}
