/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.nifi.commons.report;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.nifi.flowfile.FlowFile;

@RequiredArgsConstructor
@Getter
public enum TrackingRecordAttribute {
	PACIFIC_CUSTOMER_ID("reportPacificCustomerId"),
	CARIBBEAN_USER_ID("reportCaribbeanUserId"),
	MIGRATION_CONTEXT("reportMigrationContext"),
	MIGRATION_STATUS("reportMigrationStatus"),
	PACIFIC_DATA("reportPacificData"),
	ERROR_MESSAGE("reportErrorMessage");

	private final String fieldKey;

	public static String getData(FlowFile flowFile, TrackingRecordAttribute attribute) {
		return flowFile.getAttribute(attribute.getFieldKey());
	}
}
