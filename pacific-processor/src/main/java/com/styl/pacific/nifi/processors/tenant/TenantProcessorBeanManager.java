/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.nifi.processors.tenant;

import com.styl.pacific.nifi.base.PacificApiAccessProvider;
import com.styl.pacific.nifi.processors.tenant.service.TenantClientServiceImpl;
import com.styl.pacific.nifi.processors.tenant.service.TenantServiceImpl;
import com.styl.pacific.tenant.service.shared.http.api.TenantApi;
import lombok.Getter;

@Getter
public class TenantProcessorBeanManager {

	private TenantService tenantService;

	private TenantClientService tenantClientService;

	public TenantProcessorBeanManager initialize() {
		tenantClientService = new TenantClientServiceImpl(PacificApiAccessProvider.createApiClient(TenantApi.class));
		tenantService = new TenantServiceImpl(tenantClientService);
		return this;
	}
}
