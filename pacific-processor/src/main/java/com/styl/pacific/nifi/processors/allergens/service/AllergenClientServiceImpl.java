/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.nifi.processors.allergens.service;

import com.styl.pacific.catalog.service.shared.http.allergen.AllergenApi;
import com.styl.pacific.catalog.service.shared.http.allergen.request.AllergenFilterRequest;
import com.styl.pacific.catalog.service.shared.http.allergen.request.AllergenQueryRequest;
import com.styl.pacific.nifi.processors.allergens.AllergenClientService;
import com.styl.pacific.nifi.processors.allergens.mapper.AllergenClientMapper;
import com.styl.pacific.nifi.processors.allergens.models.Allergen;
import java.util.List;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class AllergenClientServiceImpl implements AllergenClientService {

	private final AllergenApi allergenApi;

	@Override
	public List<Allergen> getAllergens(AllergenFilterRequest filter) {
		return allergenApi.findAll(AllergenQueryRequest.builder()
				.filter(filter)
				.build())
				.getContent()
				.stream()
				.map(AllergenClientMapper.INSTANCE::toAllergen)
				.toList();
	}

	@Override
	public Allergen createAllergen(Allergen allergen) {
		return AllergenClientMapper.INSTANCE.toAllergen(allergenApi.create(AllergenClientMapper.INSTANCE
				.toCreateAllergenRequest(allergen)));
	}
}
