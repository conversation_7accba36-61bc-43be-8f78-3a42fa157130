import 'package:barge_connect/commons/constants/index.dart';
import 'package:barge_connect/domains/entities/sailing_logs/sailing_log_entity.dart';
import 'package:barge_connect/pages/create_sailing_log/state.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../commons/components/input_field/app_input_field.dart';

class CreateSailingLogPageController extends GetxController {
  final state = CreateSailingLogState();

  late TextEditingController remarkController;
  late SailingLogValuesController sailingLogValuesController;

  @override
  void onInit() {
    super.onInit();
    remarkController = TextEditingController();
    sailingLogValuesController = SailingLogValuesController();
  }

  @override
  Future<void> onReady() async {
    super.onReady();
  }
}

class SailingLogValuesController {
  final List<SailingLogProperty> _logValues = [];

  void onChangedValue({required SailingLogProperty fieldProperty}) {
    final existingValue = _logValues.firstWhereOrNull((element) => element.key == fieldProperty.key);
    if (existingValue == null) {
      _logValues.addIf(!_logValues.contains(fieldProperty), fieldProperty);
      return;
    }
    _logValues.remove(existingValue);
    _logValues.add(fieldProperty);
  }

  List<SailingLogProperty> get logValues => _logValues;

  void clear() {
    logValues.clear();
  }
}
