import 'package:async/async.dart';
import 'package:barge_connect/commons/repository/index.dart';
import 'package:barge_connect/commons/services/clients/index.dart';
import 'package:barge_connect/commons/services/index.dart';
import 'package:barge_connect/pages/welcome/state.dart';
import 'package:get/get.dart';

import '../../commons/constants/storage.dart';
import '../../config/api_hosts.dart';

class WelcomeController extends GetxController {
  final state = WelcomeState();
  final userAuthService = Get.find<UserAuthenticationService>();
  final simpleRepository = Get.find<SimpleRepository>();
  final _apiHostProvider = Get.find<ApiHostProvider>();

  CancelableOperation<void>? _preCheckTask;

  @override
  Future<void> onReady() async {
    super.onReady();
    await checkPreLogin();
    if (_apiHostProvider.currentApiHost == null || !_apiHostProvider.currentApiHosts.contains(_apiHostProvider.currentApiHost)) {
      _apiHostProvider.selectApiHost(_apiHostProvider.currentApiHosts[0].key);
    }
    state.selectedApiHost.value = _apiHostProvider.currentApiHost;
  }

  Future<void> checkPreLogin() async {
    if (userAuthService.isLogged()) {
      return;
    }
    await _preCheckTask?.cancel();
    _preCheckTask = CancelableOperation.fromFuture(Future.delayed(const Duration(milliseconds: 700), () {
      state.isVisibleLogin.value = true;
      state.isOpenLogin.value = true;
    }));
  }

  Future<bool> checkUserCredentials(String username, String password) async {
    simpleRepository.saveString(SharePreferenceKeys.userAuthUsernameKey, username);
    simpleRepository.saveString(SharePreferenceKeys.userAuthPasswordKey, password);

    var result = await Get.find<UserClientService>().checkUserValidity();
    simpleRepository.remove(SharePreferenceKeys.userAuthUsernameKey);
    simpleRepository.remove(SharePreferenceKeys.userAuthPasswordKey);
    return result;
  }

  void loginSuccessfully(String username, String password) {
    simpleRepository.saveString(SharePreferenceKeys.userAuthUsernameKey, username);
    simpleRepository.saveString(SharePreferenceKeys.userAuthPasswordKey, password);
  }
}
