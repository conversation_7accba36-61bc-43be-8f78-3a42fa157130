import 'package:barge_connect/commons/components/vinta_inkwell/vinta_ink_well.dart';
import 'package:barge_connect/commons/constants/spaces.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../commons/app_page/view.dart';
import '../../commons/components/app_bar/view.dart';
import '../../commons/components/calls_list/view.dart';
import '../../commons/components/side_navigation/controller.dart';
import '../../commons/components/side_navigation/view.dart';
import '../../commons/components/vinta_list_view_ng/models.dart';
import '../../commons/constants/colors.dart';
import '../../commons/utils/app_utils.dart';
import 'controller.dart';

class CallsPage extends AppPage<CallsController> {
  CallsPage({super.key}) {
    Get.put(SideNavigationController());
  }

  @override
  Widget buildUI(BuildContext context) {
    ScreenUtil.init(context);
    return Scaffold(
      appBar: VintaAppBar(title: "NAV_CALLS".tr, hasDrawer: true),
      drawer: Drawer(
        child: SideNavigationDrawer(),
      ),
      backgroundColor: AppColors.colorPrimary11,
      body: Column(
        children: [
          Expanded(
            child: CallsList(listId: "calls-list-${IdGenerator.nextNumber()}"),
          )
        ],
      ),
    );
  }
}

class BosIconButton extends StatelessWidget {
  final Widget icon;
  final Widget label;
  final VoidCallback? onClick;

  BosIconButton({required this.icon, required this.label, this.onClick});

  @override
  Widget build(BuildContext context) {
    return VintaInkWell(
      onTap: onClick,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            icon,
            AppSpaces.spaceH4,
            label,
          ],
        ),
      ),
    );
  }
}

class ForceRefreshCallEvent extends ListViewBaseEvent {
  ForceRefreshCallEvent(super.listId);
}
