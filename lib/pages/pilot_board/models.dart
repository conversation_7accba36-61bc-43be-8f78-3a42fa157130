import 'dart:ui';

import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../commons/constants/colors.dart';
import '../../domains/entities/locations/models.dart';

part 'models.g.dart';

@CopyWith()
class MarkerViewModel {
  final int id;
  final VintaLatLngModel? position;
  final BitmapDescriptor icon;
  final bool isVisible;
  final String? title;
  final String? locationCode;

  MarkerViewModel({
    required this.id,
    required this.position,
    this.icon = BitmapDescriptor.defaultMarker,
    this.isVisible = true,
    this.title,
    this.locationCode,
  });

  @override
  String toString() {
    return 'MarkerViewModel{id: $id, position: $position, isVisible: $isVisible, title: $title, locationCode: $locationCode}';
  }
}

@CopyWith()
class PolylineViewModel {
  final PolylineId id;
  final List<LatLng> points;
  final double distance;
  final int width;
  final Color color;
  final bool isVisible;

  final List<PatternItem> patterns;

  PolylineViewModel({
    required this.id,
    required this.points,
    this.distance = 0,
    this.width = 5,
    this.color = AppColors.colorPrimary01,
    this.patterns = const <PatternItem>[],
    this.isVisible = true,
  });

  @override
  String toString() {
    return 'PolylineViewModel{id: $id, points: $points, distance: $distance, width: $width, color: $color, isVisible: $isVisible, patterns: $patterns}';
  }
}
