import 'dart:convert' show json;

import 'package:drift/drift.dart';

import 'models.dart';

class LatLngConverter extends TypeConverter<VintaLatLngModel, String> {
  const LatLngConverter();

  @override
  VintaLatLngModel fromSql(String fromDb) {

    final map = json.decode(fromDb);
    if (map == null) {
      return VintaLatLngModel(null, null);
    }
    return VintaLatLngModel.fromJson(json.decode(fromDb) as Map<String, dynamic>);
  }

  @override
  String toSql(VintaLatLngModel value) {
    return json.encode(value.toJson());
  }
}