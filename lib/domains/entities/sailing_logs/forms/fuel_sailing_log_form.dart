import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:json_annotation/json_annotation.dart';

import '../sailing_log_entity.dart';
import '../sailing_log_enums.dart';

part 'fuel_sailing_log_form.g.dart';

enum FuelActionType {
  REFUEL,
  STOCKAGE,
}

enum FuelType {
  Oil,
  Gasoline,
  Diesel,
}

enum VolumeUnitType {
  LITER(name: "L"),
  M3(name: "m3"),
  GALLON(name: "<PERSON>allo<PERSON>");

  final String name;

  const VolumeUnitType({required this.name});
}


@JsonSerializable(explicitToJson: true)
@CopyWith()
class FuelSailingLogFormEntity extends SailingLogEntity {
  final FuelActionType fuelActionType;
  final VolumeUnitType volumeUnit;
  final FuelType fuelType;
  final int fuelAmount;


  FuelSailingLogFormEntity({
    this.fuelActionType = FuelActionType.REFUEL,
    this.volumeUnit = VolumeUnitType.LITER,
    this.fuelType = FuelType.Oil,
    this.fuelAmount = 0,
    required super.id,
    required super.lastAuditingInfo,
    required super.eventType,
    required super.entityExternalId,
    required super.formId});


  factory FuelSailingLogFormEntity.fromJson(Map<String, dynamic> json) => _$FuelSailingLogFormFromJson(json);


  @override
  Map<String, dynamic> toJson() => _$FuelSailingLogFormToJson(this);
}
