import 'package:shared_preferences/shared_preferences.dart';

import 'repository.dart';

class SimpleRepository extends Repository {
  final SharedPreferences _prefs;

  SimpleRepository(this._prefs);

  Future<bool> saveString(String key, String value) async {
    return await _prefs.setString(key, value);
  }

  Future<bool> saveBool(String key, bool value) async {
    return await _prefs.setBool(key, value);
  }

  Future<bool> saveList(String key, List<String> value) async {
    return await _prefs.setStringList(key, value);
  }

  String getString(String key) {
    return _prefs.getString(key) ?? '';
  }

  bool getBool(String key) {
    return _prefs.getBool(key) ?? false;
  }

  List<String> getList(String key) {
    return _prefs.getStringList(key) ?? [];
  }

  Future<bool> remove(String key) async {
    return await _prefs.remove(key);
  }

  Future<void> switchKeyValue(String keyA, String keyB) async {
    final valueA = getString(keyA);
    final valueB = getString(keyB);
    saveString(keyB, valueA);
    saveString(keyA, valueB);
  }
}
