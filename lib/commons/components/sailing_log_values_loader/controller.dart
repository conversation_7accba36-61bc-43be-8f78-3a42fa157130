import 'package:barge_connect/domains/entities/sailing_logs/sailing_log_entity.dart';
import 'package:barge_connect/pages/create_sailing_log/controller.dart';
import 'package:get/get.dart';

import '../sailing_log_form_field/utils.dart';
import 'index.dart';

class SailingLogValuesLoaderController extends GetxController {
  final state = SailingLogValuesLoaderState();
  final List<SailingLogProperty> _sailingLogProperties = [];

  SailingLogValuesLoaderController();

  @override
  void onInit() {
    super.onInit();
  }

  void hydrate(
      {required List<SailingLogProperty> sailingLogProperties, required SailingLogValuesController sailingLogValuesController}) {
    if (_sailingLogProperties.length == sailingLogProperties.length) {
      return;
    }
    _sailingLogProperties.clear();
    _sailingLogProperties.addAll(sailingLogProperties);
    state.fieldControllerMap.clear();
    state.fieldControllerMap.addAll(SailingLogFormFieldFactory().generateSailingLogFormFields(
        sailingLogProperties: _sailingLogProperties, sailingLogValuesController: sailingLogValuesController));
  }
}
