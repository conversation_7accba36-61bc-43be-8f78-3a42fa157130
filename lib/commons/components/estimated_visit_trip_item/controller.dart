import 'package:barge_connect/commons/services/clients/rest/routes/bos_route_service.dart';
import 'package:barge_connect/commons/services/my_locations/my_location_service.dart';
import 'package:barge_connect/commons/utils/app_utils.dart';
import 'package:barge_connect/commons/utils/map_camera_support.dart';
import 'package:barge_connect/commons/viewmodels/trips/models.dart';
import 'package:flutter_polyline_points/flutter_polyline_points.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../../domains/entities/locations/models.dart';
import 'models.dart';

class EstimatedVisitTripItemController extends GetxController {
  final _myLocationService = Get.find<MyLocationService>();
  final _routeApiClientService = Get.find<BosRouteClientService>();
  static const averageBargeVelocity = 12.0;

  Future<RouteCalculationResult> calculateRoute(int index, VisitDataModel? previousVisit, VisitDataModel currentVisit) {
    return Future.delayed(const Duration(milliseconds: 1000), () async {
      if (currentVisit.callDataModel == null) {
        return RouteCalculationResult();
      }

      int maxTries = 20;
      final result = (await calculateRoutes(previousVisit, currentVisit, maxTries--));
      if (index == 0) {
        final current = LatLng(_myLocationService.state.currentPosition.value!.location.latitude!,
            _myLocationService.state.currentPosition.value!.location.longitude!);
        final nearestPosition = GeoPointSupport.findNearestLocation(
          current,
          PolylinePoints().decodePolyline(result.polyline!).map((e) => LatLng(e.latitude, e.longitude)).toList(),
        ).position;
        return result.copyWith(
          nearest: VintaLatLngModel(nearestPosition.latitude, nearestPosition.longitude),
        );
      }
      return result;
    });
  }

  Future<RouteCalculationResult> calculateRoutes(
      VisitDataModel? previousVisit, VisitDataModel visitDataModel, int maxTries) async {
    if (_myLocationService.state.currentPosition.value == null) {
      AppUtils.delay(const Duration(seconds: 3));
      if (maxTries == 0) {
        return RouteCalculationResult();
      }
      return calculateRoutes(previousVisit, visitDataModel, maxTries--);
    } else {
      final start = previousVisit == null
          ? LatLng(
              _myLocationService.state.currentPosition.value!.location.latitude!,
              _myLocationService.state.currentPosition.value!.location.longitude!,
            )
          : LatLng(
              previousVisit.callDataModel!.location!.position!.latitude,
              previousVisit.callDataModel!.location!.position!.longitude,
            );
      final end = LatLng(visitDataModel.callDataModel!.location!.position!.latitude,
          visitDataModel.callDataModel!.location!.position!.longitude);
      final result = await _routeApiClientService.findRoutes(start, end).then((routeResult) {
        if (routeResult.isEmpty) {
          return RouteCalculationResult();
        }
        return RouteCalculationResult(
          estimatedCompletionTime: Duration(hours: (routeResult[0].distanceNoStream / averageBargeVelocity).round()),
          distance: routeResult[0].distanceNoStream,
          polyline: routeResult[0].polyline,
          start: routeResult[0].start,
          end: routeResult[0].end,
        );
      });

      return result;
    }
  }
}
