import 'package:barge_connect/commons/viewmodels/calls/models.dart';
import 'package:get/get.dart';

import 'index.dart';

class StowageBoardLayoutDialogController extends GetxController {
  final state = StowageBoardLayoutDialogState();

  StowageBoardLayoutDialogController();

  @override
  Future<void> onReady() async {
    super.onReady();
  }

  void allocate(CallDataModel callDataModel) async {
    state.callDataModel.value = callDataModel;
  }
}
