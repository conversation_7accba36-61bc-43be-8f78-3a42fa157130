import 'package:animated_widgets/widgets/translation_animated.dart';
import 'package:barge_connect/commons/constants/index.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../domains/entities/sailing_logs/sailing_log_entity.dart';
import '../../sailing_log_form_field_create/index.dart';

class UpsertSailingLogFieldDialog extends GetView {
  final Future<void> Function(SailingLogProperty newProperty) onCreateNewFieldProperty;

  const UpsertSailingLogFieldDialog({super.key, required this.onCreateNewFieldProperty});

  @override
  Widget build(BuildContext context) {
    bool isPortrait = MediaQuery.of(context).orientation == Orientation.portrait;
    double horizontalPadding = isPortrait ? 10 : 50;
    return TranslationAnimatedWidget(
      enabled: true,
      curve: Curves.fastOutSlowIn,
      duration: const Duration(milliseconds: 500),
      values: [
        Offset(horizontalPadding, 1.sh),
        Offset(horizontalPadding, 0.3.sh),
        Offset(horizontalPadding, kToolbarHeight),
      ],
      child: GestureDetector(
        onTap: () {
          FocusScope.of(context).requestFocus(FocusNode());
        },
        child: Container(
          height: 1.sh - kToolbarHeight,
          width: 1.sw - horizontalPadding * 2,
          decoration: BoxDecoration(
            color: AppColors.colorPrimary12,
            border: Border.all(color: Colors.grey.shade700),
            borderRadius: const BorderRadius.only(topLeft: Radius.circular(24), topRight: Radius.circular(24)),
            boxShadow: const [
              BoxShadow(
                offset: Offset(0, 1),
                blurRadius: 5,
                spreadRadius: 0.1,
                color: Colors.black54,
              ),
            ],
          ),
          child: SailingLogFormFieldUpsertForm(
            onCreateNewFieldProperty: (SailingLogProperty newProperty) async {
              onCreateNewFieldProperty(newProperty).then((value) => Navigator.of(context).maybePop());
            },
          ),
        ),
      ),
    );
  }
}
