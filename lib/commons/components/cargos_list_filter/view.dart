import 'package:flutter/material.dart';

import '../label_widget/label_widget.dart';
import '../vinta_select/view.dart';

class CargosListFilter extends StatelessWidget {
  const CargosListFilter({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(
          width: 180,
          child: LabelWidget(
            label: "Location / Port / Call",
            labelFontSize: 14,
            child: SizedBox(
              height: 30,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      offset: const Offset(0.8, 1.5),
                      blurRadius: 1,
                      spreadRadius: 0.1,
                      color: Colors.grey.shade900,
                    ),
                  ],
                  borderRadius: BorderRadius.circular(25),
                ),
                child: VintaSelect<String, dynamic>(
                  key: Unique<PERSON><PERSON>(),
                  selectKey: "vinta-locations",
                  forceClean: true,
                  dataProvider: (page) async {
                    return ["GML", "TCIT", "CTL"];
                  },
                  itemWidgetBuilder: (value, {Color? primaryTextColor}) => Text(
                    value,
                    style: const TextStyle(fontWeight: FontWeight.normal, fontSize: 16),
                  ),
                  currentValue: null,
                  onChanged: (statusEntity) {},
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
