import 'package:barge_connect/commons/viewmodels/stowage_board/models2.dart';

import '../../viewmodels/stowage_board/enums.dart';

class StowagePlanUIUtils {
  static const double kCellHeight = 80;
  static const double kCellWidth = 100;

  static String getSlotBayRowIdBadge(StowagePlanSlotDataModel slot) {
    var bayId = "B${slot.slotPosition.bayId + 1}";
    var rowId = "R${slot.slotPosition.rowId + 1}";

    if (SlotSpanDirection.RIGHT == slot.cargoSlotExpansionConfig?.direction) {
      bayId = "$bayId - B${slot.slotPosition.bayId + 2}";
    }

    if (SlotSpanDirection.BOTTOM == slot.cargoSlotExpansionConfig?.direction) {
      rowId = "$rowId - R${slot.slotPosition.rowId + 2}";
    }
    return "$bayId / $rowId";
  }
}
