import 'package:auto_size_text/auto_size_text.dart';
import 'package:barge_connect/commons/components/cargos_list/index.dart';
import 'package:barge_connect/commons/constants/index.dart';
import 'package:barge_connect/commons/services/calls/calls_service.dart';
import 'package:barge_connect/commons/utils/app_extension.dart';
import 'package:barge_connect/commons/viewmodels/cargos/utils.dart';
import 'package:barge_connect/domains/entities/models.dart';
import 'package:barge_connect/generated/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../domains/entities/cargos/enums.dart';
import '../../services/cargos/cargos_action_service.dart';
import '../../utils/app_utils.dart';
import '../../utils/call_utils.dart';
import '../object_status/view.dart';
import '../vinta_inkwell/vinta_ink_well.dart';

class SingleCargoDetail extends StatelessWidget {
  final CargoDataModel cargo;
  final _cargoActionService = Get.find<CargoActionService>();
  final _callsService = Get.find<CallsService>();

  SingleCargoDetail({super.key, required this.cargo});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 5,
      ),
      child: Stack(
        children: [
          Positioned(
            top: 45,
            bottom: 0,
            left: 0,
            right: 0,
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  ContainerPropertyField(
                    title: "CARGO_DETAIL_CONTAINER_NUMBER".tr,
                    widthFactor: 0.8,
                    content: AutoSizeText(
                      cargo.containerNumber ?? "-",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      minFontSize: 9,
                      style: AppTextStyles.textBodyLarge.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  AppSpaces.spaceH4,
                  ContainerPropertyField(
                    title: "CALL_DETAILS_STATUS".tr,
                    widthFactor: 0.8,
                    content: cargo.statusId == null
                        ? const Text("-")
                        : ObjectStatusLoader(statusId: cargo.statusId!, statusType: StatusType.CARGO),
                  ),
                  AppSpaces.spaceH4,
                  Wrap(
                    alignment: WrapAlignment.spaceBetween,
                    crossAxisAlignment: WrapCrossAlignment.start,
                    spacing: 10,
                    children: [
                      ContainerPropertyField(
                        title: "POL",
                        content: AutoSizeText(
                          cargo.cargoActions
                                  .where((action) => CargoActionType.LOADING == action.cargoActionType)
                                  .first
                                  .callDataModel
                                  ?.location
                                  ?.code ??
                              "-",
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          minFontSize: 9,
                          style: AppTextStyles.textBodyLarge,
                        ),
                      ),
                      ContainerPropertyField(
                        title: "POD",
                        content: AutoSizeText(
                          cargo.cargoActions
                                  .where((action) => CargoActionType.DISCHARGING == action.cargoActionType)
                                  .first
                                  .callDataModel
                                  ?.location
                                  ?.code ??
                              "-",
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          minFontSize: 9,
                          style: AppTextStyles.textBodyLarge,
                        ),
                      )
                    ],
                  ),
                  AppSpaces.spaceH4,
                  Wrap(
                    alignment: WrapAlignment.spaceBetween,
                    crossAxisAlignment: WrapCrossAlignment.start,
                    spacing: 10,
                    children: [
                      ContainerPropertyField(
                        title: "Size",
                        content: AutoSizeText(
                          cargo.containerType ?? "-",
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          minFontSize: 9,
                          style: AppTextStyles.textBodyLarge,
                        ),
                      ),
                      ContainerPropertyField(
                        title: "CARGO_DETAIL_WEIGHT".tr,
                        content: AutoSizeText(
                          "${NumberFormatterUtils.removeDecimalZeroFormat(((cargo.weight ?? 0) * 1000).ceilToDouble())} kg",
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          minFontSize: 9,
                          style: AppTextStyles.textBodyLarge,
                        ),
                      ),
                    ],
                  ),
                  AppSpaces.spaceH4,
                  ContainerPropertyField(
                    title: "TEUs",
                    content: AutoSizeText(
                      NumberFormatterUtils.removeDecimalZeroFormat(cargo.teu),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      minFontSize: 9,
                      style: AppTextStyles.textBodyLarge,
                    ),
                  ),
                  AppSpaces.spaceH6,
                  Text("CARGO_DETAIL_CALLS".tr, style: AppTextStyles.textTitleLarge.copyWith(color: Colors.grey.shade500)),
                  FutureBuilder<List<CargoActionDataModel>>(
                    future: Future.delayed(const Duration(milliseconds: 500), () async {
                      final cargoActionEntities = await _cargoActionService.getCargoActions(cargoExternalIds: [cargo.externalId]);
                      final callsMap = (await _callsService.getCallByCallExternalIds(cargoActionEntities
                              .where((e) => e.callExternalId != null)
                              .map((e) => e.callExternalId!)
                              .toSet()
                              .toList()))
                          .toMap((call) => MapEntry(call.externalId, call));
                      return cargoActionEntities
                          .map(
                              (action) => CargoActionDataModelMapper().toModelView(action, call: callsMap[action.callExternalId]))
                          .toList();
                    }),
                    builder: (context, AsyncSnapshot<List<CargoActionDataModel>> snapshot) {
                      switch (snapshot.connectionState) {
                        case ConnectionState.none:
                        case ConnectionState.active:
                        case ConnectionState.waiting:
                          return AppShimmers.generate(
                            structuralPlaceHolder: () => Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  width: 0.8.sw,
                                  height: 16,
                                  decoration: const BoxDecoration(
                                      color: Colors.white, borderRadius: BorderRadius.all(Radius.circular(5))),
                                ),
                                AppSpaces.spaceH4,
                                Container(
                                  width: 0.6.sw,
                                  height: 16,
                                  decoration: const BoxDecoration(
                                      color: Colors.white, borderRadius: BorderRadius.all(Radius.circular(5))),
                                ),
                                AppSpaces.spaceH4,
                                Container(
                                  width: 0.4.sw,
                                  height: 16,
                                  decoration: const BoxDecoration(
                                      color: Colors.white, borderRadius: BorderRadius.all(Radius.circular(5))),
                                ),
                                AppSpaces.spaceH4,
                                Container(
                                  width: 0.2.sw,
                                  height: 16,
                                  decoration: const BoxDecoration(
                                      color: Colors.white, borderRadius: BorderRadius.all(Radius.circular(5))),
                                ),
                              ],
                            ),
                          );
                        case ConnectionState.done:
                      }

                      if (!snapshot.hasData) {
                        return AppShimmers.generate(
                            structuralPlaceHolder: () => Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      width: 0.8.sw,
                                      height: 16,
                                      decoration: const BoxDecoration(
                                          color: Colors.white, borderRadius: BorderRadius.all(Radius.circular(5))),
                                    ),
                                    AppSpaces.spaceH4,
                                    Container(
                                      width: 0.6.sw,
                                      height: 16,
                                      decoration: const BoxDecoration(
                                          color: Colors.white, borderRadius: BorderRadius.all(Radius.circular(5))),
                                    ),
                                    AppSpaces.spaceH4,
                                    Container(
                                      width: 0.4.sw,
                                      height: 16,
                                      decoration: const BoxDecoration(
                                          color: Colors.white, borderRadius: BorderRadius.all(Radius.circular(5))),
                                    ),
                                    AppSpaces.spaceH4,
                                    Container(
                                      width: 0.2.sw,
                                      height: 16,
                                      decoration: const BoxDecoration(
                                          color: Colors.white, borderRadius: BorderRadius.all(Radius.circular(5))),
                                    ),
                                  ],
                                ));
                      }

                      final cargoActions =
                          snapshot.data?.where((cargoAction) => cargoAction.callDataModel != null).toList() ?? [];

                      return cargoActions.isNotEmpty
                          ? Column(
                              children: cargoActions.map((cargoAction) => CargoActionCard(cargoAction: cargoAction)).toList())
                          : Text(
                              "CARGO_DETAIL_NO_CALL".tr,
                              style: AppTextStyles.textTitleMedium,
                            );
                    },
                  ),
                  AppSpaces.spaceH100,
                ],
              ),
            ),
          ),
          Positioned.fill(
            child: Align(
              alignment: Alignment.topCenter,
              child: Column(
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Assets.icons.containerIcon.svg(height: 30, width: 30),
                      AppSpaces.spaceW10,
                      Text(
                        "CARGO_DETAILS".tr,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: AppTextStyles.textHeadlineSmall,
                      ),
                    ],
                  ),
                  AppSpaces.spaceH6,
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 5),
                    child: Divider(height: 2, color: Colors.grey.shade200),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class ContainerPropertyField extends StatelessWidget {
  final String title;
  final Widget? content;
  final double? widthFactor;

  const ContainerPropertyField({super.key, required this.title, this.content, this.widthFactor});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: (widthFactor ?? 0.4).sw,
      child: Wrap(
        alignment: WrapAlignment.start,
        crossAxisAlignment: WrapCrossAlignment.center,
        spacing: 8,
        children: [
          Text("$title:", style: AppTextStyles.textBodyLarge.copyWith(color: Colors.grey.shade500)),
          content ?? Container()
        ],
      ),
    );
  }
}

class CargoActionCard extends StatelessWidget {
  final CargoActionDataModel cargoAction;

  const CargoActionCard({super.key, required this.cargoAction});

  @override
  Widget build(BuildContext context) {
    final call = cargoAction.callDataModel!;
    return Card(
      elevation: 4.0,
      child: VintaInkWell(
        onTap: () {},
        radius: 8,
        child: Container(
          decoration: const BoxDecoration(color: Colors.white, borderRadius: BorderRadius.all(Radius.circular(12))),
          child: Column(
            children: [
              Container(
                height: 32,
                decoration: BoxDecoration(
                    color: ColorCargoCardUtils.getCargoColor(cargoAction.cargoActionType),
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(12))),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    AppSpaces.spaceW6,
                    ColorCargoCardUtils.getCargoActionIcon(cargoAction.cargoActionType).svg(width: 24, height: 24),
                    AppSpaces.spaceW6,
                    Text("CARGO_ACTION_${cargoAction.cargoActionType.name}".tr,
                        maxLines: 1, style: AppTextStyles.textTitleLarge.copyWith(color: Colors.white))
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text("#Call ID: ${call.externalId}",
                        style: AppTextStyles.textBodySmall.copyWith(color: Colors.grey.shade500)),
                    Text("#Voyage ID: ${call.voyage?.voyageNumber} (${call.voyage?.ship?.name})",
                        maxLines: 4, style: AppTextStyles.textBodySmall.copyWith(color: Colors.grey.shade500)),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Assets.icons.portLocations.svg(height: 24, width: 24),
                        AppSpaces.spaceW8,
                        SizedBox(
                          width: 0.45.sw,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              AutoSizeText(
                                call.location?.code ?? "UNKNOWN",
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                minFontSize: 10,
                                style: const TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.normal,
                                ),
                              ),
                              AutoSizeText(
                                call.location?.description ?? "UNKNOWN",
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                minFontSize: 10,
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.normal,
                                ),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                    AppSpaces.spaceH10,
                    SizedBox(
                      width: double.infinity,
                      child: Wrap(
                        alignment: WrapAlignment.spaceBetween,
                        crossAxisAlignment: WrapCrossAlignment.start,
                        children: [
                          CallUtils.generateCallTimestampChangeable(
                              title: "PTA",
                              timestamp: call.pta,
                              tooltip: "PLANNED_TIME_ARRIVAL",
                              onTab: () {},
                              widthFactor: 0.37),
                          CallUtils.generateCallTimestampChangeable(
                              widthFactor: 0.37,
                              title: "PTD",
                              timestamp: call.ptd,
                              tooltip: "PLANNED_TIME_DEPARTURE",
                              onTab: () {}),
                        ],
                      ),
                    ),
                    SizedBox(
                      width: double.infinity,
                      child: Wrap(
                        alignment: WrapAlignment.spaceBetween,
                        crossAxisAlignment: WrapCrossAlignment.start,
                        children: [
                          CallUtils.generateCallTimestampChangeable(
                              title: "ATA", timestamp: call.ata, tooltip: "ACTUAL_TIME_ARRIVAL", onTab: () {}, widthFactor: 0.37),
                          CallUtils.generateCallTimestampChangeable(
                              widthFactor: 0.37,
                              title: "ATD",
                              timestamp: call.atd,
                              tooltip: "ACTUAL_TIME_DEPARTURE",
                              onTab: () {}),
                        ],
                      ),
                    ),
                    Divider(thickness: 1, color: Colors.grey.shade300),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
