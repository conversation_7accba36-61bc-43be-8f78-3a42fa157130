import 'package:barge_connect/config/env_config.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:package_info/package_info.dart';

class AppLicenseBanner extends GetView<AppLicenseBannerController> {
  AppLicenseBanner({super.key}) {
    Get.put(AppLicenseBannerController());
  }

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Column(
        children: [
          Text(
            "Build: ${controller.packageInfo.value?.version}+${controller.packageInfo.value?.buildNumber}",
            style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500, color: Colors.grey.shade600),
          ),
          Text(
            "Schema: ${EnvConfig.dbSchemaVersion}",
            style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500, color: Colors.grey.shade600),
          ),
          Text(
            'All Rights Reserved © 2024',
            style: TextStyle(fontSize: 13, fontWeight: FontWeight.bold, color: Colors.grey.shade600),
          ),
        ],
      ),
    );
  }
}

class AppLicenseBannerController extends GetxController {
  final packageInfo = Rxn<PackageInfo>();

  @override
  void onReady() {
    super.onReady();
    PackageInfo.fromPlatform().then((value) => packageInfo.value = value);
  }
}
