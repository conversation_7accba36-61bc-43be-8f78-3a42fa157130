import 'package:auto_size_text/auto_size_text.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../services/stowage_template/stowage_template_service.dart';
import '../../../viewmodels/calls/models.dart';
import '../../vinta_radio/view.dart';
import 'controller.dart';
import 'enums.dart';

class StowageBoardStackingOptionSelect extends GetView<StowageBoardStackingOptionSelectController> {
  final stowageTemplateService = Get.find<StowageTemplateService>();

  StowageBoardStackingOptionSelect({super.key, required CallDataModel? currentCall}) {
    Get.put(StowageBoardStackingOptionSelectController());
    controller.hydrate(currentCall);
  }

  @override
  Widget build(BuildContext context) {
    final isPortrait = MediaQuery.of(context).orientation == Orientation.portrait;
    final radioButtonWidth = isPortrait ? 0.8.sw : 0.37.sw;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Text(
          "${"STOWAGE_BOARD_STACKING".tr}:",
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.normal, color: Colors.grey.shade500),
        ),
        Padding(
          padding: const EdgeInsets.only(left: 10),
          child: Text(
            "${"STOWAGE_BOARD_STACKING_DIRECTION".tr}:",
            style: TextStyle(fontSize: 14, fontWeight: FontWeight.normal, color: Colors.grey.shade500),
          ),
        ),
        Wrap(
          direction: Axis.horizontal,
          alignment: WrapAlignment.start,
          spacing: 5,
          runSpacing: 5,
          children: StowageStackingDirection.values
              .sorted((a, b) => a.displayOrder.compareTo(b.displayOrder))
              .map(
                (val) => Obx(
                  () => VintaRadioButton<StowageStackingDirection>(
                    width: radioButtonWidth,
                    enabled: val.isEnabled,
                    value: val,
                    groupValue: controller.selectedDirectionOption.value,
                    leadingItemBuilder: () => Text(
                      val.titleKey.tr,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.normal,
                      ),
                    ),
                    onChanged: (selected) {
                      if (selected == null) {
                        return;
                      }
                      controller.selectedDirectionOption.value = selected;
                    },
                  ),
                ),
              )
              .toList(),
        ),
        Padding(
          padding: const EdgeInsets.only(left: 10),
          child: Text(
            "${"STOWAGE_BOARD_STACKING_STABILITY".tr}:",
            style: TextStyle(fontSize: 14, fontWeight: FontWeight.normal, color: Colors.grey.shade500),
          ),
        ),
        Wrap(
          direction: Axis.horizontal,
          alignment: WrapAlignment.start,
          spacing: 5,
          runSpacing: 5,
          children: StowageStackingStability.values
              .sorted((a, b) => a.displayOrder.compareTo(b.displayOrder))
              .map(
                (val) => Obx(
                  () => VintaRadioButton<StowageStackingStability>(
                    width: radioButtonWidth,
                    enabled: val.isEnabled,
                    value: val,
                    groupValue: controller.selectedStabilityOption.value,
                    leadingItemBuilder: () => AutoSizeText(
                      val.titleKey.tr,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      minFontSize: 7,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.normal,
                      ),
                    ),
                    onChanged: (selected) {
                      if (selected == null) {
                        return;
                      }
                      controller.selectedStabilityOption.value = selected;
                    },
                  ),
                ),
              )
              .toList(),
        ),
      ],
    );
  }
}
