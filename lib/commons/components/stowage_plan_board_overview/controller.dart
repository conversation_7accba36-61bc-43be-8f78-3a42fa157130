import 'package:async/async.dart';
import 'package:barge_connect/commons/components/stowage_plan_board_overview/state.dart';
import 'package:barge_connect/commons/services/stowage_board/stowage_board_service.dart';
import 'package:barge_connect/commons/viewmodels/stowage_board/models2.dart';
import 'package:get/get.dart';

class StowagePlanBoardOverviewController extends GetxController {
  final state = StowagePlanBoardOverviewState();
  final stowageBoardService = Get.find<StowageBoardService>();

  late StowagePlanLiteDataModel stowagePlan;
  int? currentTierId;
  late int callExternalId;
  CancelableOperation<void>? _aggregationSlotTaskCancelable;

  void hydrate({
    required int? currentTierId,
    required int callExternalId,
    required StowagePlanLiteDataModel stowagePlan,
  }) {
    this.currentTierId = currentTierId;
    this.callExternalId = callExternalId;
    this.stowagePlan = stowagePlan;
    aggregateSlotMetrics();
  }

  Future<void> aggregateSlotMetrics() async {
    await _aggregationSlotTaskCancelable?.cancel();
    _aggregationSlotTaskCancelable = CancelableOperation.fromFuture(Future.delayed(const Duration(milliseconds: 200), () {
      return stowageBoardService
          .getStowagePlanSlotsByPredicate(
              isFetchCargo: true,
              totalBays: stowagePlan.totalBays,
              totalRows: stowagePlan.totalRows,
              filter: (slot) => slot.stowagePlanId.equals(stowagePlan.id!))
          .then((slots) {
        state.tierSlots.clear();
        state.tierSlots.addAll(slots..sort((a, b) => a.slotId.compareTo(b.slotId)));
      });
    }));
  }
}
