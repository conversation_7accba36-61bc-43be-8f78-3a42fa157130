import 'package:json_annotation/json_annotation.dart';

part 'date_only.g.dart';

@JsonSerializable()
class DateOnly {
  DateOnly({
    required this.year,
    required this.month,
    required this.day,
  });

  final int year;
  final int month;
  final int day;

  /// factory fromDateTime
  factory DateOnly.fromDateTime(DateTime dateTime) => DateOnly(
        year: dateTime.year,
        month: dateTime.month,
        day: dateTime.day,
      );

  /// factory now
  factory DateOnly.now() => DateOnly.fromDateTime(DateTime.now());

  factory DateOnly.fromJson(Map<String, dynamic> json) => _$DateOnlyFromJson(json);

  Map<String, dynamic> toJson() => _$DateOnlyToJson(this);

  @override
  String toString() {
    String addLeadingZeroIfNeeded(int value) {
      if (value < 10) return '0$value';
      return value.toString();
    }

    final String yearLabel = addLeadingZeroIfNeeded(year);
    final String monthLabel = addLeadingZeroIfNeeded(month);
    final String dayLabel = addLeadingZeroIfNeeded(day);

    return '$DateOnly($yearLabel-$monthLabel-$dayLabel)';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DateOnly && runtimeType == other.runtimeType && year == other.year && month == other.month && day == other.day;

  @override
  int get hashCode => year.hashCode ^ month.hashCode ^ day.hashCode;

  DateTime? toDateTime() {
    return DateTime(
      year,
      month,
      day,
    );
  }
}
