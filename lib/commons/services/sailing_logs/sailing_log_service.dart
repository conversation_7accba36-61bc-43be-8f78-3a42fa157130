import 'dart:async';

import 'package:barge_connect/commons/repository/audit_log_repository.dart';
import 'package:barge_connect/commons/repository/sailing_log_repository.dart';
import 'package:barge_connect/commons/services/sailing_logs_template/sailing_log_template_service.dart';
import 'package:barge_connect/commons/utils/app_extension.dart';
import 'package:barge_connect/domains/entities/sailing_logs/sailing_log_entity.dart';
import 'package:get/get.dart';

import '../../viewmodels/pagination/pagination_response.dart';
import '../../viewmodels/sailing_logs/models.dart';

class SailingLogService extends GetxService {
  final SailingLogRepository _sailingLogRepository;
  final SailingLogTemplateService _sailingLogTemplateService;
  final AuditLogRepository _auditLogRepository;

  SailingLogService(
      {required SailingLogRepository sailingLogRepository,
      required SailingLogTemplateService sailingLogTemplateService,
      required AuditLogRepository auditLogRepository})
      : _sailingLogRepository = sailingLogRepository,
        _auditLogRepository = auditLogRepository,
        _sailingLogTemplateService = sailingLogTemplateService;

  Future<SailingLogEntity> upsertSailingLogEntity(SailingLogEntity sailingLogEntity) async {
    await _sailingLogRepository.save(sailingLogEntity);
    await _auditLogRepository.track(sailingLogEntity.lastAuditingInfo);
    return Future(() => sailingLogEntity);
  }

  Future<PaginationResponse<SailingLogModel>> getSailingLogPagination(int currentPage, int size) async {
    return _sailingLogRepository.findSailingLogPagination(currentPage, size).then((pagingResult) async {
      final templateIds = (pagingResult.content ?? []).map((e) => e.formId).toSet().toList();

      final templateEntitiesMap = (await _sailingLogTemplateService.getSailingLogsTemplates(templateIds))
          .map((template) => SailingLogTemplateLiteModel(
              templateId: template.templateId, title: template.title, icon: template.icon, version: template.version))
          .toList()
          .toMap((template) => MapEntry(template.templateId, template));

      return PaginationResponse<SailingLogModel>(
          currentPage,
          pagingResult.totalElements,
          pagingResult.totalPage,
          []
          // (pagingResult.content ?? []).map((entity) {
          //   return SailingLogModel(
          //     id: entity.id,
          //     lastAuditingInfo: entity.lastAuditingInfo,
          //     templateId: entity.templateId,
          //     template: templateEntitiesMap[entity.templateId],
          //     targetEntityType: entity.targetEntityType,
          //     remark: entity.remark,
          //     properties: entity.properties,
          //     createdAt: entity.createdAt,
          //   );
          // }).toList()
      );
    });
  }
}
