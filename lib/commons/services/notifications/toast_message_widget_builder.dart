import 'package:auto_size_text/auto_size_text.dart';
import 'package:barge_connect/commons/constants/index.dart';
import 'package:flutter/material.dart';

import '../../../generated/assets.gen.dart';
import 'content_models.dart';

class ToastMessageWidgetBuilder {
  ToastMessageWidgetBuilder._privateConstructor();

  static Widget build(AppNotificationContent content) {
    Widget icon = const SizedBox(
      height: 40,
      width: 40,
    );
    switch (content.runtimeType) {
      case AppNotifyErrorContent:
        icon = Assets.icons.buttonStateError.lottie(
          height: 40,
          width: 40,
          repeat: false,
        );
        break;
      case AppNotifyInfoContent:
        icon = Assets.icons.buttonStateInfo.lottie(
          height: 40,
          width: 40,
          repeat: false,
        );
        break;
      case AppNotifySuccessContent:
        icon = Assets.icons.buttonStateSuccess.lottie(
          height: 40,
          width: 40,
          repeat: false,
        );
        break;
      case AppNotifyWarningContent:
        icon = Assets.icons.buttonStateWarning.lottie(
          height: 40,
          width: 40,
          repeat: false,
        );
        break;
    }
    return Container(
      width: 300,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(topLeft: Radius.circular(15), bottomLeft: Radius.circular(15)),
        boxShadow: [
          BoxShadow(
            offset: Offset(0, 1),
            blurRadius: 2,
            spreadRadius: 0.1,
            color: Colors.black54,
          ),
        ],
      ),
      padding: const EdgeInsets.all(5),
      child: Row(
        children: [
          icon,
          SizedBox(
            width: 250,
            child: AutoSizeText(
              content.getMessageContent(),
              maxLines: 4,
              minFontSize: 10,
              overflow: TextOverflow.ellipsis,
              style: AppTextStyles.textBodyLarge.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
