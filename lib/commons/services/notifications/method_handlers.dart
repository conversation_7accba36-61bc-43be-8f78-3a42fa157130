import 'package:barge_connect/commons/services/notifications/models.dart';
import 'package:barge_connect/commons/services/notifications/toast_message_widget_builder.dart';
import 'package:barge_connect/commons/utils/app_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

import '../../components/animation/slide_animation.dart';

abstract class AppNotificationMethod {
  late final AppNotificationMethodStyle methodStyle;

  Future<void> notifyMessage(AppNotificationMessage message);
}

class AppNotificationMethodToastHandler extends AppNotificationMethod {
  AppNotificationMethodToastHandler() {
    super.methodStyle = AppNotificationMethodStyle.SIMPLE_TOAST;
  }

  @override
  Future<void> notifyMessage(AppNotificationMessage message) async {
    SmartDialog.showToast(
      message.content.getMessageContent(),
      alignment: Alignment.topCenter,
      useAnimation: true,
      builder: (_) {
        return SafeArea(
          child: Align(
            alignment: Alignment.topRight,
            child: ToastMessageWidgetBuilder.build(message.content),
          ),
        ).slideAnimation(
          delay: const Duration(milliseconds: 300),
          direction: SlideDirection.RightToLeft,
          widgetWidth: 300,
        );
      },
    );
  }
}

class AppNotificationMethodInAppHandler extends AppNotificationMethod {
  AppNotificationMethodInAppHandler() {
    super.methodStyle = AppNotificationMethodStyle.IN_APP_NOTIFY;
  }

  @override
  Future<void> notifyMessage(AppNotificationMessage message) async {
    SmartDialog.showToast('AppNotificationMethodInAppHandler');
  }
}

class AppNotificationMethodPlatformHandler extends AppNotificationMethod {
  AppNotificationMethodPlatformHandler() {
    super.methodStyle = AppNotificationMethodStyle.PLATFORM_NOTIY;
  }

  @override
  Future<void> notifyMessage(AppNotificationMessage message) {
    // TODO: implement notifyMessage
    throw UnimplementedError();
  }
}
