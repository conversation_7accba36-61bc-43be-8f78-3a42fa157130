import 'dart:convert';

import 'package:barge_connect/commons/services/clients/rest/locations/models/dtos.dart';
import 'package:barge_connect/commons/utils/app_extension.dart';
import 'package:drift/drift.dart' as drift;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../../domains/entities/database.dart';
import '../../../domains/entities/locations/models.dart';
import '../../../pages/home/<USER>/waterways/state.dart';
import '../../components/custom_marker/marker_icon.dart';
import '../../constants/colors.dart';
import '../../repository/drift_repository.dart';

class LocationsService extends GetxService {
  final _driftLocalDatabase = Get.find<DriftLocalDatabaseRepository>();

  Future<List<LocationEntity>> getLocations({int? callExternalId}) {
    return _driftLocalDatabase.driftLocalDatabase.locations.select().get();
  }

  Future<List<LocationViewModel>> getMapMarkerLocations() async {
    final viewModels = await Future.wait((await getLocations())
        .where((e) =>
            e.position != null &&
            e.position!.longitude != 0 &&
            e.position!.longitude != null &&
            e.position!.latitude != 0 &&
            e.position!.latitude != null)
        .map((e) async {
      BitmapDescriptor icon = BitmapDescriptor.defaultMarker;
      var descriptionCheck = e.description?.toLowerCase();
      if (descriptionCheck != null && (descriptionCheck.contains("terminal") || descriptionCheck.contains("port"))) {
        icon = await MarkerIcon.circleCanvasWithText(
          size: const Size(100, 100),
          text: String.fromCharCode(Icons.anchor_rounded.codePoint),
          fontSize: 80,
          circleColor: Colors.lightBlue.shade700,
          fontColor: AppColors.colorPrimary12,
          fontWeight: FontWeight.w600,
          fontFamily: Icons.anchor_rounded.fontFamily,
        );
      }

      if (descriptionCheck != null && descriptionCheck.contains("icd")) {
        icon = await MarkerIcon.circleCanvasWithText(
          size: const Size(100, 100),
          text: String.fromCharCode(Icons.anchor_rounded.codePoint),
          fontSize: 60,
          circleColor: Colors.yellow.shade300,
          fontColor: Colors.grey.shade800.withOpacity(0.8),
          fontWeight: FontWeight.w600,
          fontFamily: Icons.anchor_rounded.fontFamily,
        );
      }

      if (descriptionCheck != null && (descriptionCheck.contains("river"))) {
        icon = await MarkerIcon.circleCanvasWithText(
          size: const Size(100, 100),
          text: String.fromCharCode(Icons.waves_rounded.codePoint),
          fontSize: 80,
          circleColor: Colors.blueAccent.shade100,
          fontColor: AppColors.colorPrimary12,
          fontWeight: FontWeight.w600,
          fontFamily: Icons.anchor_rounded.fontFamily,
        );
      }

      return LocationViewModel(
          id: e.id!, code: e.code ?? "Unknown", descriptions: e.description ?? "Unknown", position: e.position, icon: icon);
    }).toList());
    return viewModels;
  }

  saveLocations(List<LocationDto> locations) async {
    final existingMap = (await _driftLocalDatabase.driftLocalDatabase
            .getLocationsByPredicate(predicate: (location) => location.externalId.isIn(locations.map((dto) => dto.id)))
            .get())
        .toMapWhereKey((location) => location.externalId);

    locations
        .where((location) => existingMap.containsKey(location.id))
        .map((locationDto) {
          final existing = existingMap[locationDto.id];
          return existing!.copyWith(
            externalId: locationDto.id,
            code: drift.Value(locationDto.code),
            description: drift.Value(locationDto.description),
            address: drift.Value(json.encode(locationDto.address?.toJson())),
            position: drift.Value(VintaLatLngModel(locationDto.position?.latitude, locationDto.position?.longitude)),
            updatedAt: drift.Value(DateTime.now()),
          );
        })
        .toList()
        .forEach((element) async {
          await _driftLocalDatabase.driftLocalDatabase.locations.replaceOne(element);
        });

    await _driftLocalDatabase.driftLocalDatabase.locations
        .insertAll(locations.where((e) => !existingMap.containsKey(e.id)).map((locationDto) {
      return LocationEntity(
          externalId: locationDto.id,
          code: locationDto.code,
          description: locationDto.description,
          address: json.encode(locationDto.address?.toJson()),
          position: VintaLatLngModel(locationDto.position?.latitude, locationDto.position?.longitude),
          updatedAt: DateTime.now(),
          createdAt: DateTime.now());
    }).toList());
  }
}
