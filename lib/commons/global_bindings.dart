import 'dart:convert';
import 'dart:developer';

import 'package:barge_connect/commons/repository/audit_log_repository.dart';
import 'package:barge_connect/commons/repository/drift_repository.dart';
import 'package:barge_connect/commons/repository/sailing_log_repository.dart';
import 'package:barge_connect/commons/repository/sailing_log_template_repository.dart';
import 'package:barge_connect/commons/repository/simple_repository.dart';
import 'package:barge_connect/commons/services/calls/calls_service.dart';
import 'package:barge_connect/commons/services/cargos/cargos_action_service.dart';
import 'package:barge_connect/commons/services/cargos/cargos_service.dart';
import 'package:barge_connect/commons/services/clients/rest/calls/bos_calls_api.dart';
import 'package:barge_connect/commons/services/clients/rest/cargos/bos_cargos_api.dart';
import 'package:barge_connect/commons/services/clients/rest/cargos/bos_cargos_service.dart';
import 'package:barge_connect/commons/services/clients/rest/locations/bos_locations_api.dart';
import 'package:barge_connect/commons/services/clients/rest/locations/bos_locations_service.dart';
import 'package:barge_connect/commons/services/clients/rest/routes/bos_route_api.dart';
import 'package:barge_connect/commons/services/clients/rest/ships/bos_ships_api.dart';
import 'package:barge_connect/commons/services/clients/rest/ships/bos_ships_service.dart';
import 'package:barge_connect/commons/services/clients/rest/statuses/statuses_api.dart';
import 'package:barge_connect/commons/services/clients/rest/statuses/statuses_service.dart';
import 'package:barge_connect/commons/services/clients/rest/voyages/bos_voyages_api.dart';
import 'package:barge_connect/commons/services/clients/rest/voyages/bos_voyages_service.dart';
import 'package:barge_connect/commons/services/couchbase/couchbase_provider.dart';
import 'package:barge_connect/commons/services/internet_connectivity/internet_connectivity_service.dart';
import 'package:barge_connect/commons/services/language/vinta_language_service.dart';
import 'package:barge_connect/commons/services/locations/locations_service.dart';
import 'package:barge_connect/commons/services/my_locations/my_location_service.dart';
import 'package:barge_connect/commons/services/object_status/object_statuses_service.dart';
import 'package:barge_connect/commons/services/retryer/vinta_retryer_service.dart';
import 'package:barge_connect/commons/services/sailing_logs/sailing_log_service.dart';
import 'package:barge_connect/commons/services/sailing_logs_template/sailing_log_template_service.dart';
import 'package:barge_connect/commons/services/security/user_auth_service.dart';
import 'package:barge_connect/commons/services/ships/ships_service.dart';
import 'package:barge_connect/commons/services/stowage_sharing/stowage_sharing_service.dart';
import 'package:barge_connect/commons/services/stowage_template/stowage_template_service.dart';
import 'package:barge_connect/commons/services/synctask/services/sync_flow_provider.dart';
import 'package:barge_connect/commons/services/synctask/services/sync_task_provider.dart';
import 'package:barge_connect/commons/services/trips/services/trips_service.dart';
import 'package:barge_connect/commons/services/voyages/voyage_service.dart';
import 'package:barge_connect/config/api_hosts.dart';
import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../domains/entities/database.dart';
import 'app_page/controller.dart';
import 'global_logger.dart';
import 'services/clients/rest/calls/bos_calls_service.dart';
import 'services/clients/rest/index.dart';
import 'services/clients/rest/routes/bos_route_service.dart';
import 'services/clients/rest/utils.dart';
import 'services/event_bus/event_bus_service.dart';
import 'services/locker/vinta_locker_service.dart';
import 'services/notifications/index.dart';
import 'services/stowage_board/stowage_board_service.dart';

class GlobalBindings extends Bindings {
  @override
  Future<void> dependencies() async {
    // Initialization
    Get.put<InternalEventBusService>(InternalEventBusService(), permanent: true);
    Get.put<VintaLockerService>(VintaLockerService(), permanent: true);

    GlobalLogger().logger.d("SharedPreferences initialized");

    final SharedPreferences prefs = await SharedPreferences.getInstance();
    // Repositories
    Get.put<SimpleRepository>(SimpleRepository(prefs), permanent: true);

    Get.put<ApiHostProvider>(ApiHostProvider(repository: Get.find<SimpleRepository>()), permanent: true);
    Get.find<ApiHostProvider>().init();

    final driftDB = Get.find<DriftLocalDatabase>();
    Get.put<DriftLocalDatabaseRepository>(DriftLocalDatabaseRepository(driftDB), permanent: true);

    Get.put<DioManagementService>(DioManagementService(Get.find<SimpleRepository>()), permanent: true);
    Get.put<SailingLogTemplateRepository>(SailingLogTemplateRepository(couchbaseDBProvider: CouchbaseDBProvider()),
        permanent: true);
    Get.put<SailingLogRepository>(SailingLogRepository(couchbaseDBProvider: CouchbaseDBProvider()), permanent: true);
    Get.put<AuditLogRepository>(AuditLogRepository(couchbaseDBProvider: CouchbaseDBProvider()), permanent: true);

    GlobalLogger().logger.d("Repositories initialized");

    // Services
    Get.put<RetryerService>(RetryerService(), permanent: true);
    Get.put<VintaLanguageService>(VintaLanguageService(), permanent: true);
    Get.put<InternetConnectivityService>(InternetConnectivityService(), permanent: true);

    Get.put<BosStatusesClientService>(
        BosStatusesClientService(
            () => RestLazyClientProvider().lazyClient(() => StatusesApiClient(Get.find<DioManagementService>().vintaDio,
                baseUrl: Get.find<ApiHostProvider>().currentApiHost!.host)),
            Get.find<RetryerService>()),
        permanent: true);

    Get.put<BosCallsClientService>(
        BosCallsClientService(
            () => RestLazyClientProvider().lazyClient(
                  () => BosCallsApiClient(Get.find<DioManagementService>().vintaDio,
                      baseUrl: Get.find<ApiHostProvider>().currentApiHost!.host),
                ),
            Get.find<RetryerService>()),
        permanent: true);

    Get.put<BosLocationsClientService>(
        BosLocationsClientService(
            () => RestLazyClientProvider().lazyClient(
                  () => BosLocationsApiClient(Get.find<DioManagementService>().vintaDio,
                      baseUrl: Get.find<ApiHostProvider>().currentApiHost!.host),
                ),
            Get.find<RetryerService>()),
        permanent: true);

    Get.put<BosShipsClientService>(
        BosShipsClientService(
            () => RestLazyClientProvider().lazyClient(
                  () => BosShipsApiClient(Get.find<DioManagementService>().vintaDio,
                      baseUrl: Get.find<ApiHostProvider>().currentApiHost!.host),
                ),
            Get.find<RetryerService>()),
        permanent: true);

    Get.put<BosVoyagesClientService>(
        BosVoyagesClientService(
            () => RestLazyClientProvider().lazyClient(
                  () => BosVoyagesApiClient(Get.find<DioManagementService>().vintaDio,
                      baseUrl: Get.find<ApiHostProvider>().currentApiHost!.host),
                ),
            Get.find<RetryerService>()),
        permanent: true);

    Get.put<BosCargosClientService>(
        BosCargosClientService(
            () => RestLazyClientProvider().lazyClient(
                  () => BosCargosApiClient(Get.find<DioManagementService>().vintaDio,
                      baseUrl: Get.find<ApiHostProvider>().currentApiHost!.host),
                ),
            Get.find<RetryerService>()),
        permanent: true);

    Get.put<SailingLogTemplateService>(
        SailingLogTemplateService(
            sailingLogTemplateRepository: SailingLogTemplateRepository(couchbaseDBProvider: CouchbaseDBProvider())),
        permanent: true);

    Get.put<SailingLogService>(
        SailingLogService(
          sailingLogRepository: Get.find<SailingLogRepository>(),
          auditLogRepository: Get.find<AuditLogRepository>(),
          sailingLogTemplateService: Get.find<SailingLogTemplateService>(),
        ),
        permanent: true);

    Get.put<UserClientService>(
        UserClientService(
          () => RestLazyClientProvider().lazyClient(() => StatusesApiClient(Get.find<DioManagementService>().vintaDio,
              baseUrl: Get.find<ApiHostProvider>().currentApiHost!.host)),
        ),
        permanent: true);

    Get.put<UserAuthenticationService>(
        UserAuthenticationService(
          Get.find<SimpleRepository>(),
        ),
        permanent: true);

    Get.put<StowageTemplateService>(StowageTemplateService(), permanent: true);
    Get.put<StowageBoardService>(StowageBoardService(), permanent: true);
    Get.lazyPut(() => CallsService());
    Get.put<CargosService>(CargosService(), permanent: true);
    Get.put<CargoActionService>(CargoActionService(), permanent: true);
    Get.put<VoyageService>(VoyageService(), permanent: true);
    Get.put<LocationsService>(LocationsService(), permanent: true);
    Get.put<ShipsService>(ShipsService(), permanent: true);
    Get.put<StowageSharingService>(StowageSharingService(), permanent: true);
    Get.put<BosRouteClientService>(
        BosRouteClientService(
          BosRouteApiClient(Dio(BaseOptions(
            receiveDataWhenStatusError: true,
            connectTimeout: const Duration(minutes: 2),
            receiveTimeout: const Duration(minutes: 2),
            sendTimeout: const Duration(minutes: 2),
          ))
            ..interceptors.addAll([
              InterceptorsWrapper(onRequest: (options, handler) async {
                // TODO: Improve user creds encryption
                var username = "reader1";
                var password = "retc42mGp1gZSVL";
                options.headers['Authorization'] = "Basic ${base64Encode(utf8.encode('$username:$password'))}";
                log("onRequest: ${options.data}");
                return handler.next(options);
              }, onResponse: (response, handler) {
                handler.next(response);
              }, onError: (dioError, handler) {
                log("dioError: ${dioError.error}");
                // dioError.error.osError.errorCode: 61
                handler.next(dioError);
              }),
              LoggingInterceptor()
            ])),
          Get.find<RetryerService>(),
        ),
        permanent: true);
    Get.put<ObjectStatusService>(ObjectStatusService(), permanent: true);

    Get.lazyPut<MyLocationService>(() => MyLocationService());
    Get.lazyPut<TripsService>(() => TripsService());

    Get.put<SyncTaskProviderService>(
        SyncTaskProviderService(
          statusesClientService: Get.find<BosStatusesClientService>(),
          callsClientService: Get.find<BosCallsClientService>(),
          locationsClientService: Get.find<BosLocationsClientService>(),
          shipsClientService: Get.find<BosShipsClientService>(),
          voyagesClientService: Get.find<BosVoyagesClientService>(),
          cargosClientService: Get.find<BosCargosClientService>(),
          sailingLogTemplateService: Get.find<SailingLogTemplateService>(),
          shipsService: Get.find<ShipsService>(),
          locationsService: Get.find<LocationsService>(),
        ),
        permanent: true);

    Get.put<SyncFlowProviderService>(SyncFlowProviderService(syncTaskProviderService: Get.find<SyncTaskProviderService>()),
        permanent: true);

    Get.put<AppNotificationMethodToastHandler>(AppNotificationMethodToastHandler(), permanent: true);
    Get.put<AppNotificationMethodInAppHandler>(AppNotificationMethodInAppHandler(), permanent: true);
    Get.put<AppNotificationMethodPlatformHandler>(AppNotificationMethodPlatformHandler(), permanent: true);

    (Get.put<AppNotifyInstantInbox>(
            AppNotifyInstantInbox(notificationMethods: [
              Get.find<AppNotificationMethodToastHandler>(),
              Get.find<AppNotificationMethodInAppHandler>(),
              Get.find<AppNotificationMethodPlatformHandler>(),
            ]),
            permanent: true))
        .initialize();

    (Get.put<AppNotifySchedulingInbox>(
            AppNotifySchedulingInbox(notificationMethods: [
              Get.find<AppNotificationMethodToastHandler>(),
              Get.find<AppNotificationMethodInAppHandler>(),
              Get.find<AppNotificationMethodPlatformHandler>(),
            ]),
            permanent: true))
        .initialize();

    Get.put<AppNotificationCenterManager>(
        AppNotificationCenterManager(
          instantInbox: Get.find<AppNotifyInstantInbox>(),
          schedulingInbox: Get.find<AppNotifySchedulingInbox>(),
        ),
        permanent: true);

    GlobalLogger().logger.d("Services initialized");
    // Controllers
    Get.put<AppPageController>(AppPageController(), permanent: true);
    GlobalLogger().logger.d("Controllers initialized");

    driftDB.customStatement('PRAGMA journal_mode = WAL');
    driftDB.customStatement('PRAGMA busy_timeout = 5000');
  }
}
